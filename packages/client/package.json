{"private": true, "name": "@global-material-middleoffice/client", "scripts": {"dev": "vite serve", "build": "vite build", "lint": "eslint \"{src,apps,libs,test}/**/*.{ts,tsx,js,jsx}\" --fix"}, "dependencies": {"@ahooksjs/use-url-state": "3.5.1", "@ant-design/charts": "^2.2.1", "@ks-material-middleoffice/measure-sdk": "0.8.0-beta.4", "@mchart/pc-react": "^1.4.17", "@monaco-editor/react": "^4.7.0", "@mybricks/rxui": "^1.0.87", "@mybricks/sdk-for-app": "0.2.0-beta.1", "@uiw/react-json-view": "2.0.0-alpha.30", "ahooks": "^3.8.1", "antd": "^4.21.16", "axios": "0", "clsx": "^2.1.1", "dayjs": "^1.11.7", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "fuse.js": "^7.0.0", "git-url-parse": "^15.0.0", "highlight.js": "^11.10.0", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "number-precision": "^1.6.0", "react": "17.0.2", "react-countup": "^6.5.3", "react-dom": "17.0.2", "react-infinite-scroll-component": "^6.1.0", "react-json-view": "^1.21.3", "react-router": "6.16.0", "react-router-dom": "6.16.0", "react-syntax-highlighter": "^15.5.0", "rxui-t": "^0.0.4", "safe-json-parse-and-stringify": "^0.2.0", "semver": "^7.6.2", "treemate": "^0.3.11"}, "devDependencies": {"@ant-design/icons": "^4.7.0", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.13.0", "@global-material-middleoffice/server-v2": "workspace:*", "@stylistic/eslint-plugin": "^2.10.1", "@teamsupercell/typings-for-css-modules-loader": "^2.5.1", "@types/git-url-parse": "^9.0.3", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.6", "@types/react": "17", "@types/react-dom": "^17", "@types/react-infinite-scroll-component": "^5.0.0", "@types/semver": "^7.5.8", "@uiw/react-markdown-preview": "^5.1.1", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.7", "eslint": "^9.13.0", "eslint-plugin-editorconfig": "^4.0.3", "eslint-plugin-react": "^7.37.2", "globals": "^15.11.0", "less": "^4.1.3", "mobx": "^6.6.1", "mobx-react-lite": "^3.4.0", "mybricks-material-backend": "file:../server", "path": "^0.12.7", "tslib": "^2.4.0", "type-fest": "^4.26.1", "typescript": "^4.7.4", "typescript-eslint": "^8.11.0", "vite": "^5.3.4", "vite-plugin-externals": "^0.6.2", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^5.0.1"}}