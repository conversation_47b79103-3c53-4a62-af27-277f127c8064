import { defineConfig } from 'vite'
import svgr from 'vite-plugin-svgr'
import react from '@vitejs/plugin-react'
import { viteExternalsPlugin } from 'vite-plugin-externals'
import tsconfigPaths from 'vite-tsconfig-paths'

export default defineConfig({
  build: {
    emptyOutDir: true,
    outDir: '../assets',
    rollupOptions: {
      external: [
        'react-dom/client',
        // https://halo.corp.kuaishou.com/devcloud/pipeline/builddetail/742770/60577934?pluginId=311484531&stageIndex=2&showType=log
      ],
    },
  },
  define: {
    __DEV__: process.env.NODE_ENV !== 'production',
  },
  assetsInclude: ['**/*.svg'],
  plugins: [
    react(),
    svgr(),
    tsconfigPaths(),
    viteExternalsPlugin({
      'react': 'React',
      'react-dom': 'ReactDOM',
      'axios': 'axios',
      'antd': 'antd',
      'moment': 'moment',
    }),
  ],
  server: {
    open: true,
    port: 8008,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          'primary-color': '#174ae6',
        },
        javascriptEnabled: true,
      },
    },
    modules: {},
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'axios', 'antd', 'moment', '@monaco-editor/react'],
  },
  resolve: {
    alias: {
      '@monaco-editor/react': '@monaco-editor/react/dist/index.js',
      'react-dom/client': 'react-dom',
    },
  },
})
