import { useCallback, useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom'

import { BusinessMap, Business } from '@/constants'
import { getLocalStorageItem, setLocalStorageItem } from '@/utils/localstorage'

/**
 * 以路由为 Store 的方式管理业务线数据
 */
export function useBusinessFromURLQuery(): [
  Business,
  (business: Business) => void,
] {
  const location = useLocation()

  const setUrlBusiness = useCallback(
    (business: Business) => {
      const searchParams = new URLSearchParams(location.search)
      const urlBusiness = searchParams.get('business')
      if (urlBusiness !== business) {
        searchParams.set('business', business)
        const newSearch = searchParams.toString()
        const newURL = `${location.pathname}?${newSearch}`
        window.history.replaceState({ path: newURL }, '', newURL)
        window.dispatchEvent(new PopStateEvent('popstate', { state: { path: newURL } }))
      }
    },
    [location],
  )

  const setStorageBusiness = useCallback((business: Business) => {
    setLocalStorageItem('business', business)
  }, [])

  // URL Query > Local Storage > Store > 默认值
  const [business, _setBusiness] = useState<Business>(() => {
    const searchParams = new URLSearchParams(location.search)
    const urlBusiness = searchParams.get('business')
    if (searchParams.has('business') && urlBusiness in BusinessMap) {
      setStorageBusiness(urlBusiness as Business)
      return urlBusiness as Business
    }
    else if (getLocalStorageItem('business') in BusinessMap) {
      setUrlBusiness(getLocalStorageItem('business') as Business)
      return getLocalStorageItem('business') as Business
    }
    else {
      setStorageBusiness(Business.KAEL)
      setUrlBusiness(Business.KAEL)
      return Business.KAEL
    }
  })

  // ======================== 监听路由变化 ========================
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search)
    const urlBusiness = searchParams.get('business')
    if (searchParams.has('business') && urlBusiness in BusinessMap) {
      setStorageBusiness(urlBusiness as Business)
      _setBusiness(urlBusiness as Business)
    }

    if (!searchParams.has('business') && business in BusinessMap) {
      setUrlBusiness(business)
    }
  }, [location])

  const setBusiness = (business: Business) => {
    if (business in BusinessMap && business !== business) {
      setUrlBusiness(business)
      setStorageBusiness(business)
      _setBusiness(business)
    }
  }

  return [business, setBusiness]
}
