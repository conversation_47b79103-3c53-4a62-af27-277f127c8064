import { useCallback, useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom'

import { getLocalStorageItem, setLocalStorageItem } from '@/utils/localstorage'
import { useBusinessFromURLQuery } from './useBusinessFromURLQuery'
import { useUpdateEffect } from 'ahooks'

const isValidNumber = (value: number): value is number => {
  return typeof value === 'number' && !isNaN(value)
}

function useDomainFromURLQuery(): [number, (domain: number) => void] {
  const location = useLocation()
  const [currentBusiness] = useBusinessFromURLQuery()

  const setUrlDomain = useCallback(
    (domain: number) => {
      const searchParams = new URLSearchParams(location.search)
      if (domain.toString() !== searchParams.get('domain')) {
        searchParams.set('domain', domain.toString())
        const newSearch = searchParams.toString()
        const newURL = `${location.pathname}?${newSearch}`
        window.history.replaceState({ path: newURL }, '', newURL)
        window.dispatchEvent(new PopStateEvent('popstate', { state: { path: newURL } }))
      }
    },
    [location, currentBusiness],
  )

  const setStorageDomain = useCallback(
    (domain: number) => {
      setLocalStorageItem(`${currentBusiness}.domain`, domain.toString())
    },
    [currentBusiness],
  )

  // URL Query > Local Storage > Store > 默认值
  const [domain, _setDomain] = useState<number>(() => {
    const searchParams = new URLSearchParams(location.search)
    const urlDomain = searchParams.get(`domain`)
    if (searchParams.has('domain') && isValidNumber(+urlDomain)) {
      setStorageDomain(+urlDomain)
      return +urlDomain
    }
    else if (
      isValidNumber(+getLocalStorageItem(`${currentBusiness}.domain`))
    ) {
      const domain = +getLocalStorageItem(`${currentBusiness}.domain`)
      setUrlDomain(domain)
      return domain
    }
    else {
      setStorageDomain(0)
      setUrlDomain(0)
      return 0
    }
  })

  // ======================== business 更新后重新初始化 ========================
  // URL Query > Local Storage > Store > 默认值
  useUpdateEffect(() => {
    const searchParams = new URLSearchParams(location.search)
    const urlDomain = searchParams.get(`domain`)
    if (searchParams.has('domain') && isValidNumber(+urlDomain)) {
      setStorageDomain(+urlDomain)
      _setDomain(+urlDomain)
    }
    else if (
      isValidNumber(+getLocalStorageItem(`${currentBusiness}.domain`))
    ) {
      const domain = +getLocalStorageItem(`${currentBusiness}.domain`)
      setUrlDomain(domain)
      _setDomain(domain)
    }
    else {
      setStorageDomain(0)
      setUrlDomain(0)
      _setDomain(0)
    }
  }, [currentBusiness])

  // ======================== 监听路由变化 ========================
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search)
    const urlDomain = searchParams.get('domain')
    if (searchParams.has('domain') && isValidNumber(+urlDomain)) {
      _setDomain(+urlDomain)
    }
    if (!searchParams.has('domain') && isValidNumber(domain)) {
      setUrlDomain(domain)
    }
  }, [location])

  const setDomain = (newDomain: number) => {
    if (isValidNumber(newDomain) && newDomain !== domain) {
      setUrlDomain(newDomain)
      setStorageDomain(newDomain)
      _setDomain(newDomain)
    }
  }

  return [domain, setDomain]
}

export default Object.assign(useDomainFromURLQuery, {
  getDomainSync: (currentBusiness: string) => {
    const searchParams = new URLSearchParams(location.search)
    const urlDomain = searchParams.get(`domain`)
    if (searchParams.has('domain') && isValidNumber(+urlDomain)) {
      return +urlDomain
    }
    else if (
      isValidNumber(+getLocalStorageItem(`${currentBusiness}.domain`))
    ) {
      const domain = +getLocalStorageItem(`${currentBusiness}.domain`)
      return domain
    }
    else {
      return 0
    }
  },
})
