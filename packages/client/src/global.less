:root {
  --theme-color: #174ae6;
  --ant-primary-color: #174ae6;
  --ant-info-color: #fa6400;
  --ant-primary-color-hover: #fa6400;
  --theme-color-blur: #174ae653;
  --theme-color-focus: #174ae653;
  // --filter-active-bg-color: #e8ebf2;
  --filter-active-bg-color: #6497ee3d;
  --border-color: #0000001a;
  --header-height: 64px;
  --ant-primary-7: #174ae6;
  --tabs-card-active-color: #174ae6;
  --font-color: #505a71;
  --background-color: #f3f5f7;
  --main-background-color: #fff;
  // --com-lib-background-color: #174ae6;
  --cayon-color: #87e8de;
}

@border-color-base: #0000001a;


:global {
  @media (min-width: 600px) {
    .fixed-page-margin {
      margin: 0 5.55555%;
    }
    .fixed-page-width {
      width: 88.8889vw;
    }
  }

  @media (min-width: 1200px) {
    .fixed-page-margin {
      margin: 0 11.1111%;
    }
    .fixed-page-width {
      width: 77.7778vw;
    }
  }

  @media (min-width: 1800px) {
    .fixed-page-margin {
      margin: 0 16.66665%;
    }
    .fixed-page-width {
      width: 66.6667vw;
    }
  }

  @media (min-width: 2400px) {
    .fixed-page-margin {
      margin: 0 22.2222%;
    }
    .fixed-page-width {
      width: 55.5556vw;
    }
  }
}

body {
  overscroll-behavior: none;
}
