import React from 'react'
import { List, Empty, Spin } from 'antd'
import { ListGridType } from 'antd/lib/list'
import InfiniteScroll from 'react-infinite-scroll-component'

import { NOOP } from '@/utils/function'

interface Props<T> {
  // 数据渲染相关属性
  loading?: boolean
  data?: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  itemKey?: keyof T | ((item: T) => string)

  // 无限滚动相关属性
  infinite?: boolean
  hasMore?: boolean
  onLoadMore?: () => void
  scrollableTarget?: string

  // 布局、文案相关属性
  emptyText?: string
  grid?: ListGridType
  style?: React.CSSProperties
  className?: string
}

function ItemizeList<T>(props: Props<T>) {
  const {
    loading = false,
    data = [],
    renderItem,
    itemKey,
    infinite = false,
    hasMore = false,
    onLoadMore = NOOP,
    emptyText = '暂无数据',
    grid = {
      gutter: 24,
      xs: 1,
      sm: 2,
      md: 2,
      lg: 3,
      xl: 3,
      xxl: 4,
    },
    style,
    className,
    scrollableTarget,
  } = props

  const ListContent = (
    <List
      grid={grid}
      dataSource={data}
      renderItem={(item, idx) => (
        <List.Item>
          {renderItem(item, idx)}
        </List.Item>
      )}
      rowKey={itemKey}
      locale={{
        emptyText: <Empty description={emptyText} />,
      }}
    />
  )

  if (!infinite) {
    return (
      <Spin spinning={loading}>
        <div className={className} style={style}>
          {ListContent}
        </div>
      </Spin>
    )
  }
  return (
    <div className={className} style={style}>
      <InfiniteScroll
        dataLength={data.length}
        next={onLoadMore || (() => {})}
        hasMore={hasMore}
        loader={(
          <Spin
            spinning={true}
            style={{ display: 'block', margin: '16px auto' }}
          />
        )}
        scrollableTarget={scrollableTarget}
      >
        {ListContent}
      </InfiniteScroll>
    </div>
  )
}

export default ItemizeList
