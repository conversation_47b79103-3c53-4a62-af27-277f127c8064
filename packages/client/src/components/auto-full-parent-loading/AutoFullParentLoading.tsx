import React from 'react'
import { Spin } from 'antd'

import styles from './style.module.less'

interface Props {
  loading?: boolean
}

const AutoFullParentLoading: React.FC<Props> = (props) => {
  const { loading = false } = props
  return (
    <div
      className={styles['loading-container']}
      style={{ display: loading ? 'flex' : 'none' }}
    >
      <Spin spinning={loading} />
    </div>
  )
}

export default AutoFullParentLoading
