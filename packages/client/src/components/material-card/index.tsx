import React, { FC, useCallback, useMemo } from 'react'
import cx from 'clsx'
import { observer } from 'mobx-react-lite'
import { Popover, Tag, Tooltip } from 'antd'
import { formatTime, getMaterialIntegralType } from '@/utils'
import { commonModel } from '@/stores'
import { MaterialType } from '@/constants'
import { IntegralType, Material } from '@/types'
import { CreatorIcon2, ScoreIcon, ReferIcon } from '../../icons'
import { useNavigate } from 'react-router-dom'

import styles from './index.module.less'

type PictureCardProps = {
  material: Material
  comLib?: Material
  type?: 'component-lib' | 'component'
}

const MaterialItem: FC<PictureCardProps> = (props) => {
  const { material, comLib, type } = props
  const maxTagLength = commonModel.isSelector ? 2 : 4

  const navigator = useNavigate()
  const currentMaterial = useMemo(() => {
    const {
      defaultSelectedComponent,
      defaultSelectedComLib,
      selectedMaterials,
    } = commonModel
    const isComponent = material.type === MaterialType.COMPONENT

    let defaultSelected
    if (isComponent) {
      defaultSelected = Boolean(
        defaultSelectedComponent.find(
          item => item.namespace === material.namespace,
        ),
      )
    }
    else {
      defaultSelected = Boolean(
        defaultSelectedComLib.find(
          item => `_self__${item.fileId}` === material.namespace,
        ),
      )
    }

    return {
      ...material,
      defaultSelected,
      selected: Boolean(
        selectedMaterials.find(item => item.namespace === material.namespace),
      ),
    }
  }, [
    material,
    commonModel.defaultSelectedComLib,
    commonModel.defaultSelectedComponent,
    commonModel.selectedMaterials,
  ])

  const handleClickCard = useCallback(() => {
    const libInfo = comLib
      ? `&lib_id=${comLib.id}&lib_title=${comLib.title}`
      : ''
    if (type === 'component-lib') {
      navigator(`/lib-detail?material_id=${currentMaterial.id}`)
      return
    }
    navigator(`/detail?material_id=${currentMaterial.id}` + libInfo)
  }, [currentMaterial, type])

  const jumpToDetail = useCallback(
    event => event.stopPropagation(),
    [currentMaterial],
  )

  const showTipTitle = useMemo(() => {
    return currentMaterial.title.length > 18
      ? currentMaterial.title
      : undefined
  }, [currentMaterial])
  const isIcon = useMemo(() => {
    return (
      currentMaterial.preview_img
      === 'https://f2.eckwai.com/udata/pkg/eshop/chrome-plugin-upload/2022-08-08/1659945545554.479c68e4609f462f.png'
      || currentMaterial.preview_img?.startsWith('data:')
    )
  }, [currentMaterial])

  return (
    <div
      className={cx({
        [styles['material-item']]: true,
        [styles.selector]: commonModel.isSelector,
        [styles.defaultSelected]: currentMaterial.defaultSelected,
        [styles.currentSelected]: currentMaterial.selected,
      })}
    >
      <div className={styles['material-block']} onClick={handleClickCard}>
        <div className={styles['img-wrapper']}>
          <div
            className={styles['img-content']}
            style={{
              backgroundImage: currentMaterial.preview_img?.startsWith('data:')
                ? `url(${currentMaterial.preview_img})`
                : `url("${currentMaterial.preview_img}")`,
              backgroundSize: isIcon ? '80px' : 'contain',
            }}
          >
            {currentMaterial.type === MaterialType.COM_LIB
              ? (
                <div className={styles['com-lib']}>组件库</div>
              )
              : null}
          </div>
          <div className={styles.mask}></div>
          {commonModel.isSelector
            ? (
              <div className={styles.entry} onClick={jumpToDetail}>
                <svg
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                >
                  <path
                    d="M192 344.64H128V192a96 96 0 0 1 96-96h608a96 96 0 0 1 96 96v640a96 96 0 0 1-96 96H224a96 96 0 0 1-96-96v-126.272h64V832a32 32 0 0 0 32 32h608a32 32 0 0 0 32-32V192a32 32 0 0 0-32-32H224a32 32 0 0 0-32 32v152.64z"
                    fill="#ffffff"
                  />
                  <path
                    d="M538.688 410.848a32 32 0 1 1 39.808-50.112l148.32 117.856c23.744 18.848 10.4 57.056-19.904 57.056H72.832a32 32 0 1 1 0-64h542.368l-76.48-60.8z"
                    fill="#ffffff"
                  />
                </svg>
              </div>
            )
            : null}
          {type === 'component' && (
            <Tooltip
              title={
                getMaterialIntegralType(currentMaterial.business)
                === IntegralType.REFER_COUNT
                  ? `被引用 ${currentMaterial.integral ?? 0} 次`
                  : `组件成熟度: ${currentMaterial.integral ?? 0}分`
              }
            >
              <div className={styles.integral}>
                {!!currentMaterial.integral
                && (getMaterialIntegralType(currentMaterial.business)
                  === IntegralType.REFER_COUNT
                  ? (
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: 4,
                      }}
                    >
                      <ReferIcon />
                      <span style={{ lineHeight: '16px' }}>
                        {currentMaterial.integral}
                      </span>
                    </div>
                  )
                  : (
                    new Array(
                      Math.min(Math.ceil(currentMaterial.integral / 2), 5),
                    )
                      .fill(0)
                      .map((_, index) => <ScoreIcon key={index} />)
                  ))}
              </div>
            </Tooltip>
          )}

          <div className={styles.version}>{currentMaterial.version}</div>
          <Popover
            placement="topLeft"
            zIndex={4}
            content={(
              <div
                style={{
                  width: '280px',
                  display: 'flex',
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}
              >
                {material?.tags?.map(tag => (
                  <div key={tag.id} style={{ marginTop: '4px' }}>
                    <Tag className={styles.tag} color="#174ae6" key={tag.id}>
                      {tag.title}
                    </Tag>
                  </div>
                ))}
              </div>
            )}
          >
            <div className={styles['tag-list']}>
              {material?.tags?.slice(0, maxTagLength)?.map(tag => (
                <Tag className={styles.tag} color="#174ae6" key={tag.id}>
                  {tag.title}
                </Tag>
              ))}
            </div>
          </Popover>
        </div>
        <div className={styles['info-wrapper']}>
          <div className={styles.header}>
            <Tooltip
              style={{ zIndex: 4, flex: 1 }}
              title={showTipTitle}
              trigger={['hover']}
              placement="topLeft"
            >
              <div className={styles.title}>{currentMaterial.title}</div>
            </Tooltip>
          </div>
          <div className={styles.desc} title={currentMaterial.description}>
            {currentMaterial.description}
          </div>
          <div className={styles['detail-info']}>
            <div className={styles.author}>
              {currentMaterial.creator_name && (
                <CreatorIcon2
                  width={18}
                  height={18}
                  style={{ marginRight: 4 }}
                />
              )}
              {currentMaterial.creator_name}
            </div>
            <div className={styles['update-time']}>
              更新于：
              {formatTime(currentMaterial.update_time)}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default observer(MaterialItem)
