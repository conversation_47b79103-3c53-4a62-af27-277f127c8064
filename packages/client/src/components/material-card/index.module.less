.material-item {
  &.selector {
    width: 208px;
    min-width: 208px;

    .material-block {
      .img-wrapper {
        .mask {
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0.3),
            transparent 25%,
            transparent 75%,
            rgba(0, 0, 0, 0.3)
          );
        }
      }

      .detail-info {
        flex-direction: column;
        justify-content: flex-start;
      }

      .info-wrapper {
        height: 100px;

        .desc {
          margin-bottom: 2px;
        }
      }
    }
  }

  &.selector.defaultSelected {
    .material-block {
      overflow: inherit;

      &:after {
        content: attr(data-text);
        position: absolute;
        top: -23px;
        left: 0;
        height: 23px;
        display: none;
        overflow: hidden;
        background: var(--theme-color);
        color: #ffffff;
        padding: 3px 10px 2px 10px;
        font-size: 12px;
      }
    }

    .material-block:hover {
      &:after {
        display: flex;
      }
    }
  }

  &.selector.currentSelected {
    .material-block {
      overflow: inherit;

      &:before {
        border: 1px solid rgba(250, 100, 0, 0.56);
      }

      &:after {
        content: attr(data-text);
        position: absolute;
        top: -23px;
        left: 0;
        height: 23px;
        display: none;
        overflow: hidden;
        outline: 1px solid rgba(255, 255, 255, 0.5);
        background: rgba(250, 100, 0, 0.56);
        color: #ffffff;
        padding: 3px 10px 2px 10px;
        font-size: 12px;
      }
    }

    .material-block:hover {
      &:after {
        display: flex;
      }
    }
  }

  .material-block {
    height: 100%;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    border: 2px solid var(--border-color);

    &:before {
      content: " ";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    &:hover {
      // border: 2px solid var(--theme-color);
      box-shadow:
        rgba(0, 0, 0, 0) 0px 0px 0px 0px,
        rgba(0, 0, 0, 0) 0px 0px 0px 0px,
        rgba(0, 0, 0, 0.1) 0px 4px 6px -1px,
        rgba(0, 0, 0, 0.1) 0px 2px 4px -2px;
      .img-wrapper {
        .mask,
        .preview,
        .entry,
        .version {
          opacity: 1;
        }
      }
      .info-wrapper {
        z-index: 4;
      }
    }

    .img-wrapper {
      position: relative;
      // width: 100%;
      padding-bottom: 200px;
      background: var(--background-color);
      overflow: hidden;

      .img-content {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        border: 10px solid transparent;

        .com-lib {
          position: absolute;
          left: 50%;
          bottom: 20%;
          transform: translateX(-50%);
          background-color: @primary-color;
          padding: 0 10px;
          border-radius: 2px;
          font-size: 12px;
          line-height: 22px;
          color: #ffffff;
        }
      }

      .mask {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        // background: linear-gradient(180deg, rgba(0, 0, 0, 0.3), transparent 30%, transparent 100%);
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
      }

      .preview {
        position: absolute;
        transition: opacity 0.2s ease-in-out;
        opacity: 0;
        left: 10px;
        top: 10px;
        color: #fff;
        font-size: 24px;
        z-index: 1;
      }

      .entry {
        position: absolute;
        right: 10px;
        bottom: 10px;
        transition: opacity 0.2s ease-in-out;
        opacity: 0;
        display: flex;
        align-items: flex-end;
        z-index: 3;
      }

      .integral {
        display: flex;
        align-items: center;
        z-index: 100;
        position: absolute;
        transition: opacity 0.2s ease-in-out;
        // opacity: 0;
        left: 10px;
        bottom: 10px;
        color: #ea582b;
        font-size: 12px;
        font-weight: 600;
        // color: #808080;
        // font-size: 12px;
      }

      .version {
        position: absolute;
        transition: opacity 0.2s ease-in-out;
        // opacity: 0;
        right: 10px;
        bottom: 10px;
        // color: #808080;
        color: #174ae6;
        font-size: 12px;
        font-weight: 600;
      }

      .tag-list {
        position: absolute;
        left: 0;
        top: 10px;
        width: 100%;
        padding: 0 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #531dab;
        z-index: 2;

        .tag {
          margin: 0 3px 3px 0;
          box-sizing: border-box;
          display: inline-block;
          padding: 0 6px;
          font-size: 10px;
          line-height: 18px;
        }
      }
    }

    .info-wrapper {
      position: relative;
      height: 90px;
      padding: 10px 8px;
      color: #999;
      font-size: 12px;

      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 20px;
        max-width: 100%;
        margin-bottom: 6px;

        .title {
          color: #333;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .desc {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 18px;
        margin-bottom: 6px;
      }

      .detail-info {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
      }

      .author {
        flex: 1;
        display: flex;
        align-items: center;
      }

      .update-time {
      }
    }
  }
}
