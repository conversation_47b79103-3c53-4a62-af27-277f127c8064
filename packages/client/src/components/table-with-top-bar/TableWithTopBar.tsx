import { Space, Table, TableProps } from 'antd'
import React from 'react'
import clsx from 'clsx'

import styles from './style.module.less'

interface Props<T extends object> extends TableProps<T> {
  topBar?: {
    total: number
    operations?: React.ReactNode | React.ReactNode[]
  }
}

function TableWithTopBar<T extends object>(props: Props<T>) {
  const { topBar, className, style, ...tableProps } = props
  return (
    <Space
      direction="vertical"
      style={{ width: '100%' }}
      className={clsx({
        [styles['table-with-top-bar']]: true,
        [className]: !!className,
      })}
    >
      {!!topBar && (
        <div className={styles['top-bar']}>
          <div className={styles['total']}>
            共
            <span className={styles['total-count']}>{topBar.total}</span>
            条数据
          </div>
          <div className={styles['operations']}>
            {Array.isArray(topBar.operations)
              ? (
                <Space>{topBar.operations}</Space>
              )
              : topBar.operations}
          </div>
        </div>
      )}
      <Table {...tableProps} />
    </Space>
  )
}

export default TableWithTopBar
