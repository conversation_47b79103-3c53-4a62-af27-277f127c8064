import React, { FC, useCallback, useEffect, useMemo } from 'react'
import { observer } from 'mobx-react-lite'
import { Empty, Spin } from 'antd'
import cx from 'clsx'
import { commonModel } from '@/stores'
import MaterialItem from '../material-card'

import styles from './index.module.less'

let container: HTMLDivElement = null
export interface ScrollQueryListProps {
  scope: 'component' | string
  onSelect: (item: AnyType) => void
  /** 无更多数据文案 */
  noMoreText: string
  /** 无数据，展示文案 */
  emptyText: string
  containerId?: string
  queryMoreFn: () => Promise<void>
  pageStore: {
    loading: boolean
    total: number
    pageIndex: number
    pageSize: number
    list: Array<Record<string, AnyType>>
    getMoreList: (pageIndex, pageSize) => Promise<void>
  } & Record<string, AnyType>
}
const ScrollQueryList: FC<ScrollQueryListProps> = (props) => {
  const {
    noMoreText = '没有更多了',
    containerId = 'material-layout',
    emptyText = '',
    pageStore,
  } = props
  const { loading, total, pageIndex, pageSize, list } = pageStore

  const onScroll = useCallback(() => {
    if (loading || total === 0 || (total && pageIndex * pageSize >= total)) {
      return
    }

    const scrollTop = container.scrollTop || 0
    const clientHeight = container.clientHeight
    const scrollHeight = container.scrollHeight

    if (scrollTop + clientHeight >= scrollHeight - 500) {
      pageStore.getMoreList(pageIndex + 1, pageSize).catch(console.log)
    }
  }, [loading, total, pageIndex, pageSize])

  const onPreview = useCallback((url: string) => {
    commonModel.previewUrl = url
  }, [])

  useEffect(() => {
    container = document.querySelector(`#${containerId}`)
  }, [])

  useEffect(() => {
    const debouncedOnScroll = window._.debounce(onScroll, 50)
    container?.addEventListener('scroll', debouncedOnScroll, false)
    return () => {
      container?.removeEventListener('scroll', debouncedOnScroll, false)
    }
  }, [onScroll])

  const empty = useMemo(() => {
    if (total === 0 && !loading) {
      return (
        <Empty
          className={styles.empty}
          imageStyle={{ height: '152px' }}
          description={emptyText}
        />
      )
    }

    return null
  }, [total, loading])

  const spin = useMemo(() => {
    return (!total || total < 0) && loading
      ? (
        <Spin className={styles.spin} />
      )
      : null
  }, [total, loading])

  const noMore = useMemo(() => {
    if (!loading && pageIndex * pageSize >= total && total > 0) {
      return (
        <div className={styles.noMore}>
          ~~
          {noMoreText}
          {' '}
          ~~
        </div>
      )
    }

    return null
  }, [loading, pageIndex, pageSize, total])

  const loadingEle = useMemo(() => {
    if (total && loading) {
      return <Spin className={styles.loading} />
    }
  }, [total, loading])

  return (
    <div
      className={cx(
        styles.contentBlock,
        commonModel.isSelector ? styles.isSelector : '',
      )}
    >
      {list?.map((item) => {
        return (
          <MaterialItem
            key={item.namespace}
            material={item as AnyType}
            onPreview={onPreview}
          />
        )
      })}
      {empty}
      {spin}
      {loadingEle}
      {noMore}
    </div>
  )
}

export default observer(ScrollQueryList)
