.contentBlock {
  border-radius: 5px;
  width: 100%;
  min-height: 500px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 40px 10px 10px;
  background: rgb(255, 255, 255);

  &.isSelector {
    border: 1px solid var(--border-color);
  }

  :global {
    .ant-spin-dot-item {
      background-color: var(--theme-color);
    }
  }
}

.empty {
  flex: 1;
  margin-top: 160px;
}

.spin {
  flex: 1;
  margin-top: 160px;
}

.noMore {
  width: 100%;
  height: 54px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 400;
}

.loading {
  width: 100%;
  margin-top: 20px;
}
