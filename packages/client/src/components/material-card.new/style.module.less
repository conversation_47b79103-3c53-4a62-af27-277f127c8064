.material-card {
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  transition: all .2s ease-in-out;
  border: 2px solid var(--border-color);
  min-height: 300px;

  padding-top: 80%;

  &:hover {
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  }

  .cover-position {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 30%;
    padding: 8px;
  }

  .content {
    position: absolute;
    top: 70%;
    left: 0;
    right: 0;
    bottom: 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    padding: 8px;
    gap: 8px;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: var(--font-color);
      line-height: 20px;
    }

    .description {
      font-size: 14px;
      line-height: 18px;
      color: #999;
    }

    .footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #999;
    }
  }
}
