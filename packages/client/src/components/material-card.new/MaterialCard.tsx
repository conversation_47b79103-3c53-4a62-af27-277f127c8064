import React from 'react'
import { Space, Typography } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

import styles from './style.module.less'
import { formatTime } from '@/utils'
import MaterialCover from '../material-cover'

interface Props {
  material: Service.Material.MaterialDetail
}

const MaterialCard: React.FC<Props> = (props) => {
  const { material } = props
  const navigator = useNavigate()

  return (
    <div
      className={styles['material-card']}
      onClick={() => navigator(`/detail?material_id=${material.id}&business=${material.business}`)}
    >
      <MaterialCover
        className={styles['cover-position']}
        url={material.currentVersion.preview_img}
      />
      <div className={styles['content']}>
        <div className={styles['title']}>{material.title}</div>
        <div className={styles['description']}>
          <Typography.Paragraph ellipsis={{ rows: 1 }} style={{ marginBottom: 0 }} title={material.description}>
            {material.description}
          </Typography.Paragraph>
        </div>
        <div className={styles['footer']}>
          <Space className={styles['user']}>
            <UserOutlined />
            {material.currentVersion.creator_name}
          </Space>
          <div className={styles['update-time']}>
            更新于：
            {formatTime(material.create_time)}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MaterialCard
