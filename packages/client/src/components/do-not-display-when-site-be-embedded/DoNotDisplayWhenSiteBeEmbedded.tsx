import React, { memo, useEffect, useState } from 'react'
import { isString } from 'lodash-es'

interface Props {}

/**
 * @example
 * ```js
 * const iframe = document.getElementById("my-iframe");
 * iframe.addEventListener('load', () => {
 *   iframe.contentWindow.postMessage({
 *     action: "IFRAME_INIT",
 *     data: {
 *       platform: "lingzhu",
 *     },
 *   }, "*"); // 传递页面状态
 * });
 * ```
 */
const DoNotDisplayWhenSiteBeEmbedded: React.FC<Props> & {
  useBeEmbeddedState: () => UseBeEmbeddedStateReturn
} = (props) => {
  const { children } = props
  const { isBeEmbedded, beEmbeddedPlatform: _ } = DoNotDisplayWhenSiteBeEmbedded.useBeEmbeddedState()
  return (
    <>
      {isBeEmbedded ? null : children}
    </>
  )
}

export type UseBeEmbeddedStateReturn = {
  isBeEmbedded: true
  beEmbeddedPlatform: string
} | {
  isBeEmbedded: false
  beEmbeddedPlatform: undefined
}

DoNotDisplayWhenSiteBeEmbedded.useBeEmbeddedState = (): UseBeEmbeddedStateReturn => {
  const [isBeEmbedded, setIsBeEmbedded] = useState(window['BE_EMBEDDED_PLATFORM'] !== undefined)
  const [beEmbeddedPlatform, setBeEmbeddedPlatform] = useState<string>(window['BE_EMBEDDED_PLATFORM'])
  useEffect(() => {
    const listener = (event: MessageEvent) => {
      const { action, data } = event.data
      if (action === 'IFRAME_INIT') {
        if ('platform' in data && isString(data.platform)) {
          setIsBeEmbedded(true)
          setBeEmbeddedPlatform(data.platform ?? '__UNKNOWN_PLATFORM__')
        }
      }
    }
    window.addEventListener('message', listener)
    return () => {
      window.removeEventListener('message', listener)
    }
  }, [])

  return {
    isBeEmbedded,
    beEmbeddedPlatform,
  } as UseBeEmbeddedStateReturn
}

window.addEventListener('message', (event) => {
  const { action, data } = event.data
  if (action === 'IFRAME_INIT') {
    if ('platform' in data && isString(data.platform)) {
      window['BE_EMBEDDED_PLATFORM'] = data.platform
    }
  }
})

export default memo(DoNotDisplayWhenSiteBeEmbedded)
