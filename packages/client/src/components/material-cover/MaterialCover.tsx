import React, { useEffect, useState } from 'react'
import clsx from 'clsx'

import styles from './style.module.less'
import { EmptyLogo } from '@/icons/logo'
import { checkImageURLValid } from '@/utils/DOM'

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  url?: string
}

const MaterialCover: React.FC<Props> = (props) => {
  const { url, ...otherProps } = props

  const [badUrl, setBadUrl] = useState(false)
  useEffect(() => {
    setBadUrl(false)
    checkImageURLValid(url).catch(() => {
      setBadUrl(true)
    })
  }, [url])

  return (
    <div {...otherProps} className={clsx(styles['material-cover-container'], otherProps.className)}>
      {!badUrl && url
        ? (
          <div
            className={styles['material-cover']}
            style={{
              backgroundImage: `url(${url})`,
            }}
          />
        )
        : <EmptyLogo />}
    </div>
  )
}

export default MaterialCover
