.domain-selector {
  padding: 4px 12px;
  border-radius: 4px;
  color: var(--theme-color);
  background: var(--filter-active-bg-color);
  display: flex;
  width: fit-content;
  cursor: pointer;
  position: relative;

  &--disabled {
    opacity: 0.7;
    pointer-events: none;
  }

  &__path {
    width: fit-content;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  &__popover {
    width: 450px;
    padding: 4px 0px;
    background: #fff;
    box-shadow:
      0 3px 6px -4px rgb(0 0 0 / 12%),
      0 6px 16px 0 rgb(0 0 0 / 8%),
      0 9px 28px 8px rgb(0 0 0 / 5%);

    &__search {
      padding: 8px;
    }

    &__body {
      overflow: auto;
      max-height: 500px;
    }

    .highlight-text {
      font-weight: bold;
      color: var(--theme-color);
    }
  }
}
