import clsx from 'clsx'
import { useDebounceFn } from 'ahooks'
import Fuse, { FuseResult } from 'fuse.js'
import { SearchOutlined } from '@ant-design/icons'
import { Divider, Dropdown, Empty, Input, Menu, Tree } from 'antd'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { createTreeMate, TreeMate, TreeNode } from 'treemate'

import { NOOP } from '@/utils/function'
import { getLingZhuDomainDirectory } from '@/services/v2/lingzhu'

import styles from './style.module.less'
import { useMaterialListContext } from '@/pages/list/MaterialListContext'

interface Props {
  value?: number
  onChange?: (value?: number) => void
  disabled?: boolean
}

export const STATIST_TREE_NODE = {
  label: '全部',
  value: 0,
}

const DomainSelector: React.FC<Props> = (props) => {
  const { value = 0, onChange = NOOP, disabled = false } = props

  // ======================== domain directory data source ========================
  const [loading, setLoading] = useState(false)

  const [_domainDirectory, setDomainDirectory] = useState<
    TreeMate<Service.Forward.LingZhu.DomainDirectory>
  >(createTreeMate([STATIST_TREE_NODE]))
  useEffect(() => {
    if (!materialListContext.domainDirectory) return
    setLoading(true)
    getLingZhuDomainDirectory()
      .then((res) => {
        setDomainDirectory(
          createTreeMate([STATIST_TREE_NODE, ...res.data.data.children], {
            getKey: node => node.value,
          }),
        )
      })
      .finally(() => {
        setLoading(false)
      })
  }, [])

  const materialListContext = useMaterialListContext()

  const domainDirectory = useMemo(() => {
    return materialListContext?.domainDirectory ?? _domainDirectory
  }, [_domainDirectory, materialListContext?.domainDirectory])

  const computedPath = useMemo(() => {
    return domainDirectory.getPath(value).treeNodePath
  }, [value, domainDirectory])

  return (
    <Dropdown
      overlay={(
        <TreeSelectPopoverContent
          value={value}
          onChange={onChange}
          treeMate={domainDirectory}
        />
      )}
    >
      <div className={clsx({
        [styles['domain-selector']]: true,
        [styles['domain-selector--disabled']]: disabled,
      })}
      >
        <div className={styles['domain-selector__path']}>
          {value === 0
            ? (
              <span className={styles['domain-selector__item']}>全部</span>
            )
            : computedPath.length === 0
              ? (
                <span className={styles['domain-selector__item']}>
                  {loading ? '加载中...' : '未找到节点'}
                </span>
              )
              : (
                computedPath.map((item, idx, arr) => {
                  const isLatestOne = idx === arr.length - 1
                  return (
                    <React.Fragment key={item.key}>
                      <span
                        className={styles['domain-selector__item']}
                        onClick={() => {
                          if (!isLatestOne) {
                            onChange(item.rawNode.value)
                          }
                        }}
                      >
                        {item.rawNode.label}
                      </span>
                      {!isLatestOne && (
                        <span className={styles['domain-selector__split']}>/</span>
                      )}
                    </React.Fragment>
                  )
                })
              )}
        </div>
      </div>
    </Dropdown>
  )
}

function TreeSelectPopoverContent<
  T extends { label: string, value: number },
>(props: {
  value?: number
  onChange?: (value?: number) => void
  treeMate: TreeMate<T>
}) {
  const { value = 0, onChange = NOOP, treeMate } = props
  const [searchedResult, setSearchedResult]
    = useState<FuseResult<TreeNode<T, T, T>>[]>()

  const fuseInstance = useMemo(
    () =>
      new Fuse(treeMate.getFlattenedNodes(), {
        keys: ['rawNode.label'],
        includeScore: true,
        includeMatches: true,
        shouldSort: true,
        findAllMatches: true,
      }),
    [treeMate],
  )

  const [searchText, setSearchText] = useState('')
  const handleSearch = useDebounceFn((keyword = '') => {
    keyword = keyword.trim().toLocaleLowerCase()
    if (!keyword) {
      setSearchedResult(undefined)
    }
    else {
      setSearchedResult(fuseInstance.search(keyword))
    }
  })

  const getHighlightText = useCallback(
    (
      match: FuseResult<TreeNode<T, T, T>>,
    ): {
      text: string
      highlight: boolean
    }[] => {
      const { item, matches } = match
      const { label } = item.rawNode
      const path = treeMate.getPath(item.key).treeNodePath
      const result: {
        text: string
        highlight: boolean
      }[]
        = path.length > 1
          ? [{
            text: path.slice(0, -1).map(item => item.rawNode.label).join(' / ') + ' / ',
            highlight: false,
          }]
          : []
      let lastIndex = 0
      for (const { indices } of matches) {
        for (const [start, end] of indices) {
          result.push({
            text: label.slice(lastIndex, start),
            highlight: false,
          })
          result.push({
            text: label.slice(start, end + 1),
            highlight: true,
          })
          lastIndex = end + 1
        }
      }
      result.push({
        text: label.slice(lastIndex),
        highlight: false,
      })
      return result.filter(Boolean)
    },
    [treeMate],
  )

  return (
    <div className={styles['domain-selector__popover']}>
      <div className={styles['domain-selector__popover__search']}>
        <Input
          placeholder="搜索节点"
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => {
            const text = e.target.value
            setSearchText(text)
            handleSearch.run(text)
          }}
        />
      </div>
      <Divider style={{ margin: 0 }} />
      <div className={styles['domain-selector__popover__body']}>
        {searchedResult
          ? searchedResult.length === 0
            ? (
              <Empty description="未找到相关节点" />
            )
            : (
              <Menu>
                {searchedResult.map((item) => {
                  const node = item.item
                  return (
                    <Menu.Item
                      key={node.key}
                      className={styles['domain-selector__popover__body__search-result__item']}
                      onClick={() => {
                        setSearchedResult(undefined)
                        setSearchText('')
                        onChange(node.rawNode.value)
                      }}
                    >
                      {getHighlightText(item).map(({ text, highlight }, idx) => {
                        return (
                          <span
                            key={idx}
                            className={clsx({
                              [styles['highlight-text']]: highlight,
                            })}
                          >
                            {text}
                          </span>
                        )
                      })}
                    </Menu.Item>
                  )
                })}
              </Menu>

            )
          : (
            <div style={{ padding: 8 }}>
              <Tree
                showLine
                showIcon={false}
                selectable
                autoExpandParent
                treeData={treeMate.treeNodes}
                titleRender={node => node.rawNode.label}
                onSelect={(selectedKeys) => {
                  onChange(isNaN(Number(selectedKeys[0])) ? 0 : Number(selectedKeys[0]))
                }}
                selectedKeys={[Number(value)]}
                defaultExpandedKeys={[Number(value)]}
              />
            </div>
          )}
      </div>
    </div>
  )
}

export default DomainSelector
