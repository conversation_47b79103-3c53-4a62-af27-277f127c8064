import { ReactNode, useMemo } from 'react'
import { Button, Col, DatePicker, Form, Input, Row, Select } from 'antd'
import DebounceSelect from '../../components/debounce-select'
import { FormProps } from 'antd/lib/form'
import { AnyType } from '@/types'
import styles from './style.module.less'

const { Item } = Form

export interface BaseSearchForm<Value = AnyType> {
  formProps?: Omit<FormProps<Value>, 'layout' | 'onFinish' | 'onReset'>
  formItemList?: AdvFormItemProps[]

  onReset?: () => void
  onSubmit?: (value: Value) => void

  resetBtnText?: string
  confirmBtnText?: string
}

export interface AdvFormItemProps {
  label: string | ReactNode
  name: string
  renderType?: 'date' | 'input' | 'select' | 'remoteSelect' | string
  extraOptions?: Record<string, AnyType>
  children?: ReactNode
}

function BaseSearchForm<T>(props: BaseSearchForm<T>) {
  const [form] = Form.useForm(props.formProps.form)

  const renderFormItemType = (options: { renderType: string, name: string, extraOptions?: Record<string, AnyType>, label: string, rules?: Array<Record<string, AnyType>> }) => {
    const { renderType, name, extraOptions = {}, rules = [], label } = options
    const child = useMemo(() => {
      if (renderType === 'select') {
        return (
          <Select
            placeholder="请选择"
            {...extraOptions}
          />
        )
      }
      else if (renderType === 'remoteSelect') {
        return <DebounceSelect fetchOptions={extraOptions.fetchOptions} {...extraOptions} />
      }
      else if (renderType === 'date') {
        return <DatePicker style={{ width: '100%' }} />
      }
      return (
        <Input
          placeholder="请输入内容"
          {...extraOptions}
        />
      )
    }, [renderType, extraOptions])
    return (
      <Item
        name={name}
        label={label}
        rules={rules}
      >
        {child}
      </Item>
    )
  }

  return (
    <div className={styles['base-search-form']}>
      <Form
        {...props.formProps}
        form={form}
        labelAlign="left"
        layout="horizontal"
        size="middle"
        onFinish={props.onSubmit}
        labelCol={{ span: 6 }}
      >
        <Row gutter={[16, 16]}>
          {
            props.formItemList.map(item => <Col span={6} key={item.name}>{renderFormItemType(item as AnyType)}</Col>)
          }
          <Col span={6}>
            <Form.Item colon={false}>
              <Button
                htmlType="button"
                onClick={() => {
                  form.resetFields()
                  props.onReset()
                }}
              >
                {props.resetBtnText || '重置'}
              </Button>
              <Button type="primary" htmlType="submit" style={{ marginLeft: '8px' }}>
                {props.confirmBtnText || '查询'}
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  )
}

export default BaseSearchForm
