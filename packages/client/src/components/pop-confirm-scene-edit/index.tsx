import { EditOutlined } from '@ant-design/icons'
import React, { FC, ReactNode, useState } from 'react'
import { Button, Form, Popover, Select, SelectProps } from 'antd'
import { observer } from 'mobx-react-lite'

import styles from './index.module.less'

export interface EditPopoverItemProps {
  title: string
  content?: ReactNode
  record: Record<string, AnyType>
  name?: string
  options: Array<{ label: string, value: AnyType }>
  type: 'scene' | 'tag'
  onAddClick: (type: string) => void
  initValue?: Record<string, AnyType>
  selectProps?: SelectProps
  onSubmit: (values) => AnyType
  defaultValue?: AnyType
}
export const EditPopoverItem: FC<EditPopoverItemProps> = observer(({ title, name = 'editField', options, selectProps = {}, onSubmit, defaultValue }) => {
  const form = Form.useForm()[0]
  const [open, setOpen] = useState(false)

  const handleCancel = () => {
    setOpen(false)
  }

  const onSave = () => {
    const values = form.getFieldsValue()
    onSubmit?.(values)
  }

  return (
    <Popover
      visible={open}
      mouseLeaveDelay={2000}
      placement="left"
      overlayStyle={{ zIndex: 100 }}
      overlayInnerStyle={{ height: '180px' }}
      title={title}
      content={(
        <div className={styles.popConfirmContent}>
          <Form form={form}>
            <Form.Item name={name}>
              <Select
                showSearch
                options={options as AnyType}
                style={{ width: '100%' }}
                filterOption={(input, option: { label: string }) => option.label?.includes(input)}
                defaultValue={defaultValue}
                {...selectProps}
              />
            </Form.Item>
          </Form>
          <div className={styles.popActionBottom}>
            <Button type="primary" onClick={onSave}>提交</Button>
            <Button style={{ marginLeft: '16px' }} onClick={() => handleCancel()}>取消</Button>
          </div>
        </div>
      )}
    >
      <EditOutlined style={{ color: '#174ae6' }} onClick={() => setOpen(true)} />
    </Popover>
  )
})
