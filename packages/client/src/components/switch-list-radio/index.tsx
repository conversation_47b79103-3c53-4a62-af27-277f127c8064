import React, { FC, useEffect, useState } from 'react'
import { Radio, Tooltip } from 'antd'
import { observer } from 'mobx-react-lite'
import { ListIcon, PaginationIcon } from '../../icons'

import styles from './index.module.less'

export interface SwitchListRadioProps {
  value?: string
  defaultValue?: string
  onChange?: (value: string) => void
}
const SwitchListRadio: FC<SwitchListRadioProps> = ({ value, onChange, defaultValue }) => {
  const [innerValue, setInnerValue] = useState(value)

  useEffect(() => {
    if (value !== innerValue) {
      setInnerValue(value)
    }
  }, [value])

  const innerOnchange = (e) => {
    const value = e.target.value
    setInnerValue(value)
    onChange?.(value)
  }

  return (
    <Radio.Group optionType="button" buttonStyle="solid" value={innerValue} defaultValue={defaultValue} onChange={innerOnchange}>
      <Radio.Button value="scroll-list">
        <Tooltip title="滚动加载更多内容">
          <div className={styles.iconRadio}>
            <ListIcon width={18} height={18} />
            {' '}
            滚动列表
          </div>
        </Tooltip>
      </Radio.Button>
      <Tooltip title="分页切换">
        <Radio.Button value="pagination">
          <div className={styles.iconRadio}>
            <PaginationIcon width={18} height={18} />
            {' '}
            分页列表
          </div>
        </Radio.Button>
      </Tooltip>
    </Radio.Group>
  )
}

export default observer(SwitchListRadio)
