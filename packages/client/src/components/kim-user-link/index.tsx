import React from 'react'

import style from './index.module.less'

type KimUserLinkType = {
  userName: string
  displayName?: string
  showUserName?: boolean
}

const KimUserLink: React.FC<KimUserLinkType> = (props) => {
  const { displayName, userName, showUserName } = props

  return (
    <a
      href={`kim://username?username=${userName}`}
      target="_blank"
      className={style.kimLink}
      rel="noreferrer"
    >
      {displayName}
      {showUserName ? `(${userName})` : null}
    </a>
  )
}

export default KimUserLink
