import React, { FC, ReactNode, useEffect } from 'react'
import {
  Button,
  Form,
  FormProps,
  Input,
  InputNumber,
  Modal,
  ModalProps,
  Select,
} from 'antd'
import { observer } from 'mobx-react-lite'
import { FormInstance } from 'antd/es/form/Form'
import { AdvFormItemProps } from '@/types'
import { materialManageModel } from '@/stores/material-manage'
import DebounceSelect from '../debounce-select'

const { Item } = Form

export interface CreateEditModelProps {
  formItemList: AdvFormItemProps[]
  modalProps?: ModalProps
  formProps?: FormProps
  visible?: boolean
  title?: string
  initValues?: Record<string, AnyType>
  onClose?: (form: FormInstance) => void
  onSubmit?: (values, form: FormInstance) => void
}
const CreateEditModal: FC<CreateEditModelProps> = (props) => {
  const {
    formItemList,
    visible,
    onClose,
    onSubmit,
    title,
    modalProps = {},
    initValues = undefined,
    formProps = {},
  } = props
  const {
    commonModalTitle,
    closeCommonEditModal,
    commonModalSubmitLoading,
    commonModalFormInitValues,
  } = materialManageModel
  const [form] = Form.useForm()

  useEffect(() => {
    return () => {
      form.resetFields()
    }
  }, [])

  const handleCancel = () => {
    closeCommonEditModal()
    form.resetFields()
    onClose?.(form)
  }

  useEffect(() => {
    if (initValues) {
      form.setFieldsValue(initValues)
    }
  }, [initValues, visible])

  useEffect(() => {
    if (commonModalFormInitValues) {
      form.setFieldsValue(commonModalFormInitValues)
    }
    else {
      form.resetFields()
    }
  }, [commonModalFormInitValues])

  const renderFormItemType = (options: {
    renderType: string
    name: string
    extraOptions?: Record<string, AnyType>
    label: string
    rules?: Array<Record<string, AnyType>>
    children?: (form) => ReactNode
  }) => {
    const {
      renderType,
      name,
      extraOptions = {},
      rules = [],
      label,
      children,
    } = options

    const child = () => {
      if (renderType === 'select') {
        return <Select placeholder="请选择" {...extraOptions} />
      }
      else if (renderType === 'remoteSelect') {
        return (
          <DebounceSelect
            fetchOptions={extraOptions.fetchOptions}
            {...extraOptions}
          />
        )
      }
      else if (renderType === 'inputNumber') {
        return <InputNumber {...extraOptions} style={{ width: '100%' }} />
      }
      return <Input placeholder="请输入内容" {...extraOptions} />
    }

    if (renderType === 'custom') {
      return <React.Fragment key={name}>{children(form)}</React.Fragment>
    }

    return (
      <Item name={name} label={label} key={name} rules={rules}>
        {child()}
      </Item>
    )
  }

  const handleOk = () => {
    form
      .validateFields()
      .then(res => onSubmit?.(res, form))
      .catch(() => {})
  }

  return (
    <Modal
      title={title ? title : commonModalTitle}
      visible={visible}
      maskClosable={false}
      onCancel={handleCancel}
      footer={[
        <Button
          key="submit"
          type="primary"
          loading={commonModalSubmitLoading}
          onClick={handleOk}
        >
          提交
        </Button>,
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
      ]}
      {...modalProps}
    >
      <Form
        form={form}
        layout="horizontal"
        size="middle"
        labelCol={{ span: 6 }}
        {...formProps}
      >
        {(formItemList || [])?.map(item =>
          renderFormItemType(item as AnyType),
        )}
      </Form>
    </Modal>
  )
}

export default observer(CreateEditModal)
