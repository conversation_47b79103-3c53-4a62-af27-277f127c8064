import ReactDOM from 'react-dom'
import React, { useMemo, useCallback } from 'react'
import { Modal, Form, FormItemProps, FormProps } from 'antd'
import { ModalProps } from 'antd/lib/modal'
import { nextTick } from '@/utils/function'

interface FormModalProps<T> extends Omit<ModalProps, 'onOk' | 'onCancel'> {
  formItems?: FormItemProps[]
  formProps?: FormProps<T>
}

interface FunctionalFormModal {
  open: <T>(config: FormModalProps<T>) => Promise<T>
}

const createContainer = () => {
  const div = document.createElement('div')
  document.body.appendChild(div)
  return div
}

function ModalContainer<T>() {
  const [form] = Form.useForm()
  const [visible, setVisible] = React.useState(false)
  const [config, setConfig] = React.useState<FormModalProps<T> | null>(null)
  const resolveRef = React.useRef<(value: T) => void>()
  const rejectRef = React.useRef<(reason?: unknown) => void>()

  const handleOk = useCallback(async () => {
    try {
      const values = await form.validateFields()
      resolveRef.current?.(values)
      setVisible(false)
      form.resetFields()
    }
    catch (error) {
      rejectRef.current?.(error)
    }
  }, [form])

  const handleCancel = useCallback(() => {
    setVisible(false)
    form.resetFields()
  }, [form])

  // 暴露方法供外部调用
  React.useImperativeHandle(window.modalRef, () => ({
    open: ((modalConfig: FormModalProps<T>) => {
      return new Promise<T>((resolve, reject) => {
        setConfig(modalConfig)
        setVisible(true)
        resolveRef.current = resolve
        rejectRef.current = reject
        nextTick(() => {
          form.resetFields()
        })
      })
    }) as FunctionalFormModal['open'],
  }))

  const formItems = useMemo(() => {
    if (!config) return null

    return config.formItems.map((item, index) => (
      <Form.Item key={item.name || index} {...item} />
    ))
  }, [config?.formItems])

  if (!config) return null

  return (
    <Modal
      {...config}
      visible={visible}
      onOk={handleOk}
      okText="提交"
      onCancel={handleCancel}
      cancelText="取消"
      destroyOnClose={false}
      getContainer={false}
    >
      <Form
        form={form}
        size="middle"
        layout="horizontal"
        labelCol={{ span: 6 }}
        labelAlign="left"
        {...config.formProps}
      >
        {formItems}
      </Form>
    </Modal>
  )
}

// 创建单例
let container: HTMLDivElement | null = null
window.modalRef = { current: null }

const FunctionalFormModal: FunctionalFormModal = {
  open: <T,>(config: FormModalProps<T>): Promise<T> => {
    if (!container) {
      container = createContainer()
      // eslint-disable-next-line react/no-deprecated
      ReactDOM.render(<ModalContainer />, container)
    }

    return window.modalRef.current?.open(config)
  },
}

declare global {
  interface Window {
    modalRef: React.MutableRefObject<null | FunctionalFormModal>
  }
}

export default FunctionalFormModal
