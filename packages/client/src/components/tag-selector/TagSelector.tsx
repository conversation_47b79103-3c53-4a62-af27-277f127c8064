import React from 'react'
import clsx from 'clsx'

import styles from './style.module.less'

export interface Option {
  label: string
  value: string | number
}

interface Props {
  options?: Option[]
  value?: string | number
  onChange?: (value?: string | number) => void
  disabled?: boolean
  emptyOption?:
    | boolean
    | {
      label: string
      value?: string | number
    }
}

const TagSelector: React.FC<Props> = (props) => {
  const {
    options = [],
    value,
    onChange,
    emptyOption = false,
    disabled = false,
  } = props
  const mergedEmptyOption
    = typeof emptyOption === 'object'
      ? emptyOption
      : { label: '全部', value: undefined }

  return (
    <div
      className={clsx({
        [styles['tag-selector']]: true,
        [styles.disabled]: disabled,
      })}
    >
      {emptyOption
        ? (
          <div
            className={clsx({
              [styles['tag-selector__item']]: true,
              [styles.selected]: mergedEmptyOption.value === value,
            })}
            onClick={() => onChange?.(mergedEmptyOption.value)}
          >
            {mergedEmptyOption.label}
          </div>
        )
        : null}
      {options.map(option => (
        <div
          key={option.value}
          className={clsx({
            [styles['tag-selector__item']]: true,
            [styles.selected]: option.value === value,
          })}
          onClick={() => {
            if (option.value === value) {
              onChange?.(mergedEmptyOption?.value ?? undefined)
            }
            else {
              onChange?.(option.value)
            }
          }}
        >
          {option.label}
        </div>
      ))}
    </div>
  )
}

export default TagSelector
