.tag-selector {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  flex-wrap: wrap;

  &__item {
    width: fit-content;
    cursor: pointer;
    gap: 5px;
    padding: 3px 12px;
    border-radius: 2px;
    min-width: 30px;
    transition: all 0.15s;

    &.selected {
      color: var(--theme-color);
      background: var(--filter-active-bg-color);
    }
  }

  &.disabled {
    opacity: 0.8;
    pointer-events: none;
    cursor: wait;
  }
}
