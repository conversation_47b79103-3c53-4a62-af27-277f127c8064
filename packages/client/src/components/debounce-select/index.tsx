import React, { useEffect } from 'react'
import { debounce } from 'lodash-es'
import { Select, SelectProps, Spin } from 'antd'
import cs from 'clsx'

interface DebounceSelectProps<VT = AnyType> extends SelectProps<VT> {
  wrapClassName?: string
  labelClassName?: string
  fetchOptions: (search: string) => Promise<AnyType>
  defaultOption?: () => Promise<AnyType>
  debounceTimeout?: number
  title?: string
  width?: number
  pure?: boolean
  initialOptions?: Array<AnyType>
}

const DebounceSelect: React.FC<DebounceSelectProps> = ({
  wrapClassName,
  labelClassName,
  title,
  fetchOptions,
  defaultOption,
  debounceTimeout = 500,
  width,
  initialOptions,
  pure,
  ...props
}: DebounceSelectProps) => {
  const [fetching, setFetching] = React.useState(false)
  const [options, setOptions] = React.useState<AnyType[]>(initialOptions || [])
  const fetchRef = React.useRef(0)

  const debounceFetcher = React.useMemo(() => {
    const loadOptions = (value: string) => {
      fetchRef.current += 1
      const fetchId = fetchRef.current
      setOptions([])

      value = value.trim()

      if (value === '') {
        return
      }

      setFetching(true)

      fetchOptions(value)
        .then((newOptions) => {
          if (fetchId !== fetchRef.current) {
            // for fetch callback order
            return
          }
          setOptions(newOptions)
        })
        .finally(() => {
          setFetching(false)
        })
    }

    return debounce(loadOptions, debounceTimeout)
  }, [fetchOptions, debounceTimeout])

  useEffect(() => {
    defaultOption?.().then((newOptions) => {
      setOptions(newOptions)
    })
  }, [defaultOption])

  useEffect(() => {
    if (initialOptions) {
      setOptions(initialOptions)
    }
  }, [initialOptions])

  return pure
    ? (
      <Select
        placeholder="请输入查询的ID"
        filterOption={false}
        showSearch
        onSearch={debounceFetcher}
        notFoundContent={fetching ? <Spin size="small" /> : null}
        options={options}
        {...props}
      />
    )
    : (
      <div className={cs('flex-start', wrapClassName)}>
        {title && (
          <div className={cs('secondary-package-cmp-label', labelClassName)}>
            {title}
          </div>
        )}

        <Select
          placeholder="请输入查询的ID"
          style={{ width: width ?? 200 }}
          filterOption={false}
          showSearch
          onSearch={debounceFetcher}
          notFoundContent={fetching ? <Spin size="small" /> : null}
          options={options}
          {...props}
        />
      </div>
    )
}

export default DebounceSelect
