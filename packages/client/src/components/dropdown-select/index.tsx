import React, { FC, CSSProperties, useState, useMemo } from 'react'
import { Dropdown, Checkbox, Radio, Space, Row, Col, Tag } from 'antd'
import { CheckboxValueType } from 'antd/lib/checkbox/Group'
import { DownOutlined } from '@ant-design/icons'
import styles from './index.module.less'

type DropDownSelectType = {
  value?: Array<any> | string | number
  onChange?: (value: any) => void
  style?: CSSProperties
  options?: Array<{ label: string, value: any }>
  label: string
  mode?: 'multiple' | 'single'
}

const DropDownSelect: FC<DropDownSelectType> = (props) => {
  const { style, label, options, onChange, mode = 'single' } = props

  const [innerValue, setInnerValue] = useState([])

  const displayLabel = useMemo(() => {
    if (innerValue !== undefined) {
      const arr = Array.isArray(innerValue) ? innerValue : [innerValue]
      return arr.length === 0
        ? label
        : options
          .filter(item => arr.includes(item.value))
          .map(item => item.label)
          .join('、')
    }
    return label
  }, [label, innerValue])

  const onCheckBoxChange = (checkedValues: CheckboxValueType[]) => {
    onChange?.(checkedValues)
    setInnerValue(checkedValues)
  }

  const inputValueMap = useMemo(() => {
    let arr = innerValue
    if (!Array.isArray(innerValue)) {
      arr = [innerValue]
    }
    const res = {}
    const filterOptions = options.filter(item => arr.includes(item.value))
    filterOptions.forEach((item) => {
      res[item.value] = item.label
    })

    return res
  }, [innerValue])

  const valArr = useMemo(() => {
    return Array.isArray(innerValue)
      ? innerValue
      : innerValue
        ? [innerValue]
        : []
  }, [innerValue])

  const onRadioChange = (value) => {
    onChange?.(value)
    setInnerValue(value)
  }

  const handleDelete = (value) => {
    if (mode !== 'multiple') {
      setInnerValue(undefined)
    }
    else {
      setInnerValue(innerValue.filter(i => i !== value))
    }
  }

  const spanNum = options.length > 16 ? 8 : 12
  return (
    <div className={styles.dropdownSelectWrapper} style={style}>
      <Dropdown
        // visible={label === '所有分类'? true : false}
        menu={{ items: [] }}
        trigger={['click']}
        getPopupContainer={c => c}
        dropdownRender={menu => (
          <div
            className="dropdown-content"
            style={{
              height: 'max-content',
              maxWidth: '400px',
              minWidth: '300px',
              background: '#fff',
            }}
          >
            {menu}
            {/* <Space style={{ padding: 8 }}> */}
            <div style={{ minHeight: '60px', background: '#fff', padding: 8 }}>
              {mode === 'single'
                ? (
                  <Radio.Group
                    value={innerValue}
                    onChange={e => onRadioChange(e.target.value)}
                  >
                    <Row style={{ minWidth: '200px' }}>
                      {options.map(item => (
                        <Col span={spanNum} key={item.label}>
                          <Radio value={item.value}>{item.label}</Radio>
                        </Col>
                      ))}
                    </Row>
                  </Radio.Group>
                )
                : (
                  <Checkbox.Group value={innerValue} onChange={onCheckBoxChange}>
                    <Row>
                      {options.map(item => (
                        <Col span={spanNum} key={item.label}>
                          <Checkbox value={item.value}>{item.label}</Checkbox>
                        </Col>
                      ))}
                    </Row>
                  </Checkbox.Group>
                )}
            </div>
          </div>
        )}
      >
        <a onClick={e => e.preventDefault()}>
          <Space style={{ position: 'relative' }}>
            {displayLabel}
            <DownOutlined />
          </Space>
        </a>
      </Dropdown>
      <div style={{ marginLeft: '30px' }}>
        {valArr.map(item => (
          <Tag
            key={item}
            closable
            color="blue"
            onClose={() => handleDelete(item)}
          >
            {inputValueMap[item]}
          </Tag>
        ))}
      </div>
    </div>
  )
}

export default DropDownSelect
