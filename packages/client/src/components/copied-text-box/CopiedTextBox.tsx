import React from 'react'

import styles from './style.module.less'
import { Button } from 'antd'
import { CopyOutlined } from '@ant-design/icons'
import { clipboard } from '@/utils/BOM'

interface Props {
  label?: string
  value: string
}

const CopiedTextBox: React.FC<Props> = (props) => {
  const { label, value } = props
  return (
    <div className={styles['copied-text-box']}>
      {
        label
          ? (
            <div className={styles['copied-text-box__label']}>
              {
                label
              }
            </div>
          )
          : null
      }
      <div className={styles['copied-text-box__value']}>
        {value}
      </div>
      <div className={styles['copied-text-box__copy']}>
        <Button type="text" icon={<CopyOutlined />} onClick={() => clipboard(value)} />
      </div>
    </div>
  )
}

export default CopiedTextBox
