import axios from 'axios'
export function download({ filename, url }) {
  console.log('download', filename, url)
  const dlLink = document.createElement('a')
  dlLink.download = filename
  dlLink.href = url
  document.body.appendChild(dlLink)
  dlLink.click()
  document.body.removeChild(dlLink)
}

export async function downloadJsonUrl({ filename, url }) {
  const result = await axios.get(url)
  const blob = new Blob([JSON.stringify(result)], { type: 'application/json' })
  const href = window.URL.createObjectURL(blob)
  const dlLink = document.createElement('a')
  dlLink.download = filename
  dlLink.href = href
  document.body.appendChild(dlLink)
  dlLink.click()
  document.body.removeChild(dlLink)
  window.URL.revokeObjectURL(href)
}
