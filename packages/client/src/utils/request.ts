import { message } from 'antd'
import axios from 'axios'

const request = axios.create({
  baseURL: '/api',
  timeout: 200 * 1000,
  headers: {
    'Content-Type': 'application/json', // 设置请求头
  },
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 在发送请求之前做一些处理，例如添加认证信息
    // config.headers.Authorization = 'Bearer token';
    return config
  },
  (error) => {
    // 处理请求错误
    return Promise.reject(error)
  },
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 对响应数据进行处理，例如解析数据
    if (response.status >= 200 && response.status < 400 && response.data.code === 1) {
      return response
    }
    message.error(response.data?.message)
    return Promise.reject(response.data)
  },
  (error) => {
    // 处理响应错误
    return Promise.reject(error)
  },
)

export default request
