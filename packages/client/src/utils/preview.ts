import { isCorp } from '@/utils/env'

export const getKaelPreviewUrl = (
  materialId: string | number,
  version: string,
) => {
  return `https://qianxiang-component-preview-app.jinx.${isCorp() ? 'corp' : 'staging'}.kuaishou.com/designer?material_id=${materialId}&version=${version}`
}

export const getFangZhouPreviewUrl = (
  materialId: string | number,
  version: string,
) => {
  return `https://fangzhou-component-preview.jinx.${isCorp() ? 'corp' : 'staging'}.kuaishou.com/?material_id=${materialId}&version=${version}`
}

export const getFangZhouPreviewUrlByBundle = (bundle: string) => {
  return `https://fangzhou-component-preview.jinx.${isCorp() ? 'corp' : 'staging'}.kuaishou.com/?bundle=${bundle}`
}

export const getKaelPreviewUrlByBundle = (bundle: string) => {
  return `https://qianxiang-component-preview-app.jinx.${isCorp() ? 'corp' : 'staging'}.kuaishou.com/designer?bundle=${bundle}`
}
