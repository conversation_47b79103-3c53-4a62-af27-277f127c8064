import { IntegralType, Material } from '@/types'
import {
  Business,
  defaultComLibIcon,
  defaultComponentIcon,
  MaterialType,
} from '@/constants'

export function parseMaterial(meta: Material) {
  const isComponent = meta.type === MaterialType.COMPONENT

  if (!meta.preview_img) {
    meta.preview_img = isComponent ? defaultComponentIcon : defaultComLibIcon
  }

  return meta
}

export function getMaterialIntegralType(business: Business) {
  return ['fangzhou', 'kael'].includes(business)
    ? IntegralType.REFER_COUNT
    : IntegralType.MASS_SCORE
}
