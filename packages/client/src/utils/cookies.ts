import cookies from 'js-cookie'

export function getCookie(name: string): string | undefined {
  return cookies.get(name)
}

export function setCookie(name: string, value: string) {
  cookies.set(name, value)
}

export function removeCookie(name: string) {
  cookies.remove(name)
}

// ======================== admin cookie ========================
export const admins = ['yingpengsha']
export function isAdmin(): boolean {
  return admins.includes(getCookie('userName') || '')
}

export function getQueryString(name) {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')

  const r = window.location.search.substring(1).match(reg)
  if (r != null) {
    return r[2]
  }
  return null
}
