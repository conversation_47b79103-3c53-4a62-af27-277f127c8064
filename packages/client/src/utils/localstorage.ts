// 从本地存储中获取数据
export function getLocalStorageItem(key: string): string {
  const item = localStorage.getItem(key)
  if (item) {
    try {
      return JSON.parse(item)
    }
    catch (error) {
      console.error(`Error parsing item with key ${key}:`, error)
    }
  }
  return null
}

// 将数据存储到本地存储中
export function setLocalStorageItem(key: string, value: string): void {
  try {
    const serializedValue = JSON.stringify(value)
    localStorage.setItem(key, serializedValue)
  }
  catch (error) {
    console.error(`Error setting item with key ${key}:`, error)
  }
}

// 从本地存储中移除数据
export function removeLocalStorageItem(key: string): void {
  localStorage.removeItem(key)
}

// 清空本地存储
export function clearLocalStorage(): void {
  localStorage.clear()
}
