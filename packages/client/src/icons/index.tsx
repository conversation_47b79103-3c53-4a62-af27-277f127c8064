import React from 'react'

export const HomeIcon = (props: AnyType) => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    {...props}
  >
    <path
      d="M508.63 927.94H348.88a32 32 0 0 1-32-32v-148a32 32 0 0 1 32-32h159.75a32 32 0 0 1 32 32v148a32 32 0 0 1-32 32z m-127.75-64h95.75v-84h-95.75z"
      fill="#1AD8E2"
    >
    </path>
    <path
      d="M575.76 507.66m31.96 0l64.1 0q31.96 0 31.96 31.96l0 0q0 31.96-31.96 31.96l-64.1 0q-31.96 0-31.96-31.96l0 0q0-31.96 31.96-31.96Z"
      fill="#1AD8E2"
    >
    </path>
    <path
      d="M44.764099 570.222025m22.634488-22.634488l418.755707-418.755707q22.634488-22.634488 45.268976 0l-0.007071-0.007071q22.634488 22.634488 0 45.268976l-418.755707 418.755707q-22.634488 22.634488-45.268976 0l0.007071 0.007071q-22.634488-22.634488 0-45.268976Z"
      fill="#2C77E5"
    >
    </path>
    <path
      d="M926.518969 615.044957m-22.945615-22.945615l-418.133453-418.133453q-22.945615-22.945615 0-45.891231l-0.007071 0.007072q22.945615-22.945615 45.89123 0l418.133453 418.133452q22.945615 22.945615 0 45.891231l0.007071-0.007071q-22.945615 22.945615-45.89123 0Z"
      fill="#2C77E5"
    >
    </path>
    <path
      d="M832.42 927.64H192.3a32 32 0 0 1-32-32V473.83a32 32 0 0 1 64 0v389.81h576.12V473.83a32 32 0 0 1 64 0v421.81a32 32 0 0 1-32 32z"
      fill="#2C77E5"
    >
    </path>
  </svg>
)

export const Home2Icon = (props: AnyType) => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    {...props}
  >
    <path
      d="M887.466667 473.6L768 362.666667v-128c0-12.8-8.533333-21.333333-21.333333-21.333334h-132.266667l-85.333333-81.066666c-8.533333-8.533333-21.333333-8.533333-29.866667 0l-362.666667 341.333333c-8.533333 8.533333-8.533333 21.333333 0 29.866667s21.333333 8.533333 29.866667 0L512 179.2l349.866667 328.533333c4.266667 4.266667 8.533333 4.266667 12.8 4.266667 4.266667 0 12.8-4.266667 17.066666-8.533333 8.533333-8.533333 4.266667-21.333333-4.266666-29.866667zM725.333333 256v64L657.066667 256H725.333333z m85.333334 277.333333v273.066667c0 51.2-46.933333 89.6-102.4 89.6h-392.533334c-55.466667 0-102.4-38.4-102.4-89.6V533.333333c0-12.8 8.533333-21.333333 21.333334-21.333333s21.333333 8.533333 21.333333 21.333333v273.066667c0 25.6 25.6 46.933333 59.733333 46.933333H384v-85.333333c0-46.933333 38.4-85.333333 85.333333-85.333333h85.333334c46.933333 0 85.333333 38.4 85.333333 85.333333v85.333333h68.266667c29.866667 0 59.733333-21.333333 59.733333-46.933333V533.333333c0-12.8 8.533333-21.333333 21.333333-21.333333s21.333333 8.533333 21.333334 21.333333z"
      fill=""
    >
    </path>
  </svg>
)

export const MaterialIcon = (props: AnyType) => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    {...props}
  >
    <path
      d="M931.84 675.84h-194.56a35.84 35.84 0 0 0 0 71.68h194.56a35.84 35.84 0 0 0 0-71.68zM931.84 788.48h-194.56a35.84 35.84 0 0 0 0 71.68h194.56a35.84 35.84 0 0 0 0-71.68zM931.84 901.12h-194.56a35.84 35.84 0 0 0 0 71.68h194.56a35.84 35.84 0 0 0 0-71.68z"
      fill="#1A1A1A"
    >
    </path>
    <path
      d="M905.3184 271.36H773.12v-9.3184a145.92 145.92 0 1 0-220.16 0V271.36H338.8416a61.44 61.44 0 0 0-61.44 61.44v158.72h-10.24a145.92 145.92 0 1 0 0 220.16h10.24v199.68a61.44 61.44 0 0 0 61.44 61.44H614.4a35.84 35.84 0 0 0 0-71.68H349.0816v-261.12H250.88a35.84 35.84 0 0 0-30.72 17.664 74.24 74.24 0 1 1 0-112.1792 35.84 35.84 0 0 0 30.72 17.7152h98.2016V343.04H624.64V245.76a35.84 35.84 0 0 0-17.664-30.72 74.24 74.24 0 1 1 112.1792 0 35.84 35.84 0 0 0-17.7152 30.72v97.28h193.6384v240.64a35.84 35.84 0 1 0 71.68 0V332.8a61.44 61.44 0 0 0-61.44-61.44z"
      fill="#1A1A1A"
    >
    </path>
  </svg>
)

export const PlatformIcon = (props: AnyType) => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path
      d="M905.1 541.5l-176.2-89.6c-2.8-1.4-8.1-2.2-11.3-2.2l-2.6-2.2V210.1c0-8.8-2.7-16.4-10.3-19.4l-176.9-89.6c-2.6-1.3-1.7-2.1-8.7-2.2v-2.7l-196.4 94.2c-8 3.1-11.6 11-11.6 19.7V449l-194.3 92.2c-8 3.1-13.7 11-13.7 19.7v244.7l0.1 2.6 0.4 3.9h0.8c1.7 8 5.2 9.2 9.7 11.7l184 101 2.8 1.3 2 0.6 3.2 0.5 1.9 0.1 1.9-0.1 3.2-0.6 2.4-0.6 0.6-0.4 1.2-0.5 1.3-0.8 182.7-100.1c4.2-2.3 7.5-6 9.3-10.5 1.8 4.5 5.1 8.2 9.3 10.5l181.8 99.5 1.1 0.8 1.7 0.9 1.6 0.7 1.6 0.5 1.7 0.4 2.8 0.4h1.9l1.5-0.1 3.3-0.6 2.6-0.7 0.6-0.4 1.2-0.6 1.2-0.8 182.7-100.3c4.6-2.5 8-3.7 9.7-11.7h0.1V560.9c0-8.8-4.3-16.4-11.9-19.4z m-185.5-47.3l24.7 12.6-137.9 71.9-30-16.4 143.2-68.1zM522 142.4l33.6 16.7-138.1 73.7-38.8-20.6L522 142.4zM313.5 494.2l24.7 12.6-137.9 71.9-30-16.4 143.2-68.1zM284 870.7L143 793V596.3l34.7 18.4 0.4 69.6 1 0.4-0.6-0.4s18.6 67 47.2 22.9H224v-65.7l60 32.4v196.8z m23.8-233.1L252.1 607l140-73 54.3 27.6-138.6 76zM472 794.7l-141 77.7V675.8l141-77.7v196.6z m21-269.9l-143-77.7V250.5l35 18.4v47.3l-0.9 0.1c1.7 5.4 20.9 65.4 47.9 23.1v-43.5l61 32.4v196.5z m22.1-238l-55.8-30.6 140.2-74.8 54.4 27.6-138.8 77.8z m24.9 37.7l142-77.7v196.6l-142 77.7V324.5z m147 546.2L546 793V596.3l35 18.4v100.4s22.4 66.8 50.9 23.1l2.1 0.1v-0.3V641.6l53 32.4v196.7z m26.9-233.1L658.2 607l140-73 54.3 27.6-138.6 76zM876 794.7l-141 77.7V675.8l141-77.7v196.6z"
      fill="#4C8EFF"
    >
    </path>
  </svg>
)

export const PlatformIcon2 = (props: AnyType) => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path
      d="M505.958 977.92c-3.89 0-7.833-0.87-11.417-2.662L118.733 788.07a45.798 45.798 0 0 1-25.549-41.267V278.99c0-17.613 9.78-33.383 25.549-41.267l366.643-182.58a46.418 46.418 0 0 1 41.114 0l366.694 182.63a45.798 45.798 0 0 1 25.549 41.268v467.814c0 17.613-9.78 33.383-25.549 41.268L517.376 975.309c-3.635 1.69-7.526 2.611-11.418 2.611zM144.333 743.629l361.574 180.07 361.575-180.07V282.163l-361.524-180.07-361.625 180.07V743.63z"
      fill="#5B90EF"
    >
    </path>
    <path
      d="M419.891 470.784h-134.86c-11.316 0-20.48-9.165-20.48-20.48v-134.86c0-11.316 9.164-20.48 20.48-20.48h134.86c11.315 0 20.48 9.164 20.48 20.48v134.86c0 11.315-9.165 20.48-20.48 20.48z m0 258.867h-134.86c-11.316 0-20.48-9.165-20.48-20.48v-134.86c0-11.316 9.164-20.48 20.48-20.48h134.86c11.315 0 20.48 9.164 20.48 20.48v134.86c0 11.315-9.165 20.48-20.48 20.48z m291.482 0H576.512c-11.315 0-20.48-9.165-20.48-20.48v-134.86c0-11.316 9.165-20.48 20.48-20.48h134.86c11.316 0 20.48 9.164 20.48 20.48v134.86c0 11.315-9.164 20.48-20.48 20.48z"
      fill="#DCEDFF"
    >
    </path>
    <path
      d="M419.891 496.384h-134.86c-25.396 0-46.08-20.685-46.08-46.08v-134.86c0-25.396 20.684-46.08 46.08-46.08h134.86c25.395 0 46.08 20.684 46.08 46.08v134.86c0 25.446-20.685 46.08-46.08 46.08z m-129.74-51.2h124.62v-124.62h-124.62v124.62z m421.222 51.2H576.512c-25.395 0-46.08-20.685-46.08-46.08v-134.86c0-25.396 20.685-46.08 46.08-46.08h134.86c25.396 0 46.08 20.684 46.08 46.08v134.86a46.08 46.08 0 0 1-46.08 46.08z m-129.741-51.2h124.62v-124.62h-124.62v124.62z m-161.74 310.067H285.03c-25.395 0-46.08-20.685-46.08-46.08v-134.86c0-25.396 20.685-46.08 46.08-46.08h134.861c25.395 0 46.08 20.684 46.08 46.08v134.86c0 25.395-20.685 46.08-46.08 46.08z m-129.742-51.2h124.621v-124.62h-124.62v124.62z m421.223 51.2H576.512c-25.395 0-46.08-20.685-46.08-46.08v-134.86c0-25.396 20.685-46.08 46.08-46.08h134.86c25.396 0 46.08 20.684 46.08 46.08v134.86c0 25.395-20.633 46.08-46.08 46.08z m-129.741-51.2h124.62v-124.62h-124.62v124.62z"
      fill="#5B90EF"
    >
    </path>
  </svg>
)

export const CreatorIcon = (props: AnyType) => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path d="M742.362 268.438c0 120.353-97.569 217.923-217.929 217.923-120.348 0-217.915-97.564-217.915-217.923 0-120.353 97.57-217.924 217.915-217.924 120.361 0 217.929 97.564 217.929 217.924zM838.128 784.393c0 149.514-140.451 170.032-313.698 170.032-173.233 0-313.684-20.503-313.684-170.022 0-149.51 140.449-270.721 313.698-270.721 173.245-0.004 313.684 121.204 313.684 270.707z"></path>
  </svg>
)

export const CreatorIcon2 = props => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path d="M512 140.8a228.8 228.8 0 0 0-228.48 228.48A228.736 228.736 0 0 0 512 597.632a228.8 228.8 0 0 0 228.48-228.48A228.8 228.8 0 0 0 512 140.8z"></path>
    <path d="M688.128 592.448A282.752 282.752 0 0 1 512 654.72a283.584 283.584 0 0 1-176.128-62.336c-92.416 58.816-156.736 162.112-165.568 281.728 32.384 4.352 89.92 9.088 172.032 9.088h339.2c82.176 0 139.776-4.736 172.16-9.088-8.768-119.68-73.088-222.912-165.568-281.728z"></path>
  </svg>
)

/** 信息/描述 图标 */
export const DescriptionIcon = props => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path d="M522.1376 990.72c-263.96672 0-478.72-214.75328-478.72-478.72s214.75328-478.72 478.72-478.72 478.72 214.75328 478.72 478.72-214.75328 478.72-478.72 478.72z m0-896c-230.08768 0-417.28 187.19232-417.28 417.28s187.19232 417.28 417.28 417.28 417.28-187.19232 417.28-417.28-187.1872-417.28-417.28-417.28z"></path>
    <path d="M522.1376 290.85696m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z"></path>
    <path d="M568.2176 723.41504c0 28.86144-20.6336 52.26496-46.08 52.26496-25.45152 0-46.08-23.40352-46.08-52.26496V469.54496c0-28.86656 20.62848-52.26496 46.08-52.26496 25.4464 0 46.08 23.3984 46.08 52.26496v253.87008z"></path>
  </svg>
)

/** 滚动列表图标 */
export const ListIcon = props => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    {...props}
  >
    <path d="M928 160h32v704h-32z" fill="#1B8FFF"></path>
    <path
      d="M64 192h160v126.304H64zM800 318.304H320V192h480z"
      fill="#1B8FFF"
    >
    </path>
    <path
      d="M800 368.832v126.336H320v-126.336h480zM800 720.832v126.336H320v-126.336h480zM64 368.832h160v126.336H64zM64 720.832h160v126.336H64z"
      fill="#1BE8FF"
    >
    </path>
    <path
      d="M64 672v-126.304h160V672zM320 672v-126.304h480V672z"
      fill="#1B8FFF"
    >
    </path>
    <path d="M835.2 704h96v160z" fill="#1B8FFF"></path>
  </svg>
)

/** 滚动列表图标 */
export const ListIcon2 = props => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path
      d="M881.92 564.48a30.72 30.72 0 0 0 30.72-30.72 30.72 30.72 0 0 0-8.96-21.76L768 375.04a32 32 0 0 0-48.64 0L580.48 512a28.16 28.16 0 0 0-8.96 21.76 31.36 31.36 0 0 0 8.96 21.76 30.72 30.72 0 0 0 43.52 0l118.4-117.76 117.76 117.76a30.72 30.72 0 0 0 21.76 8.96z"
      fill="#4DAFFF"
    >
    </path>
    <path
      d="M768 858.24l135.68-136.96a30.72 30.72 0 1 0-43.52-43.52l-117.12 117.76-119.04-118.4a31.36 31.36 0 0 0-43.52 0 28.16 28.16 0 0 0-8.96 21.76 30.08 30.08 0 0 0 8.96 21.76l136.32 136.96a36.48 36.48 0 0 0 51.2 0.64z"
      fill="#8FD7FF"
    >
    </path>
    <path
      d="M912.64 239.36H164.48a35.84 35.84 0 0 1 0-71.68h748.16a35.84 35.84 0 1 1 0 71.68zM443.52 453.12H164.48a35.84 35.84 0 1 1 0-69.12h279.04a35.84 35.84 0 1 1 0 71.68zM392.32 819.2H164.48a35.84 35.84 0 1 1 0-71.68h227.84a35.84 35.84 0 1 1 0 71.68z"
      fill="#4DAFFF"
    >
    </path>
  </svg>
)

export const PaginationIcon = props => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    id="mx_n_1708487893295"
    width="16"
    height="16"
    {...props}
  >
    <path
      d="M787.692 157.538a78.77 78.77 0 0 1 78.77 78.77v551.384a78.77 78.77 0 0 1-78.77 78.77H236.308a78.77 78.77 0 0 1-78.77-78.77V236.308a78.77 78.77 0 0 1 78.77-78.77h551.384z m-291.485 31.469l-259.9 0.04a47.262 47.262 0 0 0-47.025 42.416l-0.236 4.845v551.384a47.262 47.262 0 0 0 42.417 47.026l4.845 0.236h259.899V189.007z m291.485 0.04l-259.978-0.04v645.947h259.978a47.262 47.262 0 0 0 47.026-42.417l0.236-4.845V236.308a47.262 47.262 0 0 0-42.417-47.026l-4.845-0.236z m-384.748 234.22a15.754 15.754 0 0 1 2.009 19.81l-1.97 2.481-65.614 65.536 67.465 67.545a15.754 15.754 0 0 1 2.01 19.77l-2.01 2.482a15.754 15.754 0 0 1-19.81 2.009l-2.481-1.97-78.612-78.65a15.754 15.754 0 0 1-2.009-19.811l2.01-2.481 76.72-76.722a15.754 15.754 0 0 1 22.292 0z m218.27 0a15.754 15.754 0 0 1 22.291 0l76.721 76.72 2.01 2.482a15.754 15.754 0 0 1-1.97 19.81l-78.651 78.612-2.481 2.009a15.754 15.754 0 0 1-19.81-1.97l-1.97-2.52a15.754 15.754 0 0 1 1.97-19.771l67.504-67.545-65.614-65.536-2.01-2.481a15.754 15.754 0 0 1 2.01-19.81z"
      fill="#333333"
    >
    </path>
  </svg>
)

export const NpmIcon = props => (
  <svg
    className="icon"
    viewBox="0 0 2048 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path
      d="M768.192 192v506.24H256V192h512.192zM341.536 276v337.664h170.56v-253.12h85.536v253.12h85.568V276H341.536z"
      fill="#C33E36"
    >
    </path>
    <path
      d="M1194.816 192v506.24h-170.56v84.544H682.624V192h512.192zM768.16 276v421.664h170.56v-84h170.56V276h-341.12z m170.56 84.544h85.536v168.576h-85.536v-168.576z"
      fill="#C33E36"
    >
    </path>
    <path
      d="M1109.28 697.664V192H1792v506.24H1109.28v-0.576z m85.536-421.664v337.664h170.56v-253.12h85.536v253.12h85.568v-253.12h85.536v253.12h85.536V276H1194.816z"
      fill="#C33E36"
    >
    </path>
  </svg>
)

export const UserGroup = props => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path d="M674.784 797.072v-7.84c0-7.104-4.576-24.512-13.168-44.32a310.816 310.816 0 0 0-45.536-73.744c-47.168-56.736-108.48-89.76-182.688-89.76-74.192 0-135.52 33.04-182.672 89.808a310.976 310.976 0 0 0-45.552 73.808c-8.576 19.84-13.168 37.264-13.168 44.4v7.648h482.784z m-85.36-290.992c1.2-6.432 7.184-8 10.272-8.272a112.192 112.192 0 0 0 101.696-111.68V335.68a112.192 112.192 0 0 0-96.784-111.12c-5.024-0.704-15.392-0.704-15.392-17.584v-16.512c0-16.256 10.768-14.688 16.032-14.16 80.944 8.032 144.144 76.32 144.144 159.36v50.448c0 57.168-29.952 107.344-75.008 135.68 51.68 21.072 95.68 55.28 130.608 98.48 36.96 45.712 60.992 99.808 60.992 132.416v76.384a16 16 0 0 1-16 16h-16a16 16 0 0 1-16-16v-76.384c0-20.048-19.68-64.336-50.32-102.24-40.56-50.176-94.88-85.856-160.224-98.8a57.952 57.952 0 0 0-5.184-0.736 16 16 0 0 1-14.352-15.92v-20.24c-0.128-3.92 0.48-6.704 1.52-8.672z m-224.672 35.76a150.288 150.288 0 0 1-87.2-136.416V349.76a150.256 150.256 0 1 1 300.528 0v55.664a150.256 150.256 0 0 1-83.472 134.64c156.944 35.152 228.16 199.648 228.16 249.168v39.84a16 16 0 0 1-16 16H160a16 16 0 0 1-16-16v-39.648c0-48.832 68.992-208.992 220.752-247.584z m63.088-294.32a102.256 102.256 0 0 0-102.256 102.256v55.664a102.256 102.256 0 1 0 204.512 0v-55.68a102.256 102.256 0 0 0-102.24-102.24z"></path>
  </svg>
)

export const DepartmentIcon = props => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path
      d="M996.7104 444.928L611.328 48.896a140.9536 140.9536 0 0 0-198.7584 0L27.2896 444.928a96.1536 96.1536 0 0 0-26.88 66.6624v357.9392a144.3328 144.3328 0 0 0 141.7216 146.5344H340.992a41.5744 41.5744 0 0 0 41.2672-41.8816V688.64a61.1328 61.1328 0 0 1 59.136-62.8224h141.1072a61.1328 61.1328 0 0 1 59.136 62.8224v73.0624a41.3184 41.3184 0 1 0 82.5856 0V688.64a144.3328 144.3328 0 0 0-141.7216-146.5344H441.4464a144.3328 144.3328 0 0 0-141.7216 146.5344v243.6608H142.1312a61.0816 61.0816 0 0 1-59.0848-62.8224v-357.888a11.264 11.264 0 0 1 3.072-7.936l385.28-395.8784a57.2416 57.2416 0 0 1 81.2032 0l385.3312 395.9296a11.1616 11.1616 0 0 1 3.072 7.8336v357.9392a61.1328 61.1328 0 0 1-59.136 62.8224H683.008a41.8816 41.8816 0 0 0 0 83.712h198.912a144.3328 144.3328 0 0 0 141.7216-146.5344v-357.888a96.0512 96.0512 0 0 0-26.88-66.7136z m-758.784 7.8848a38.912 38.912 0 1 0 38.912-39.424 39.2192 39.2192 0 0 0-38.912 39.424z"
      fill="#438CFF"
    >
    </path>
  </svg>
)

export const DeleteIcon = props => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    {...props}
  >
    <path
      d="M189.124926 206.817739 834.875074 206.817739C824.056355 206.817739 815.037987 197.278738 815.718488 186.375821L766.845228 969.422054C767.420593 960.203599 774.673955 953.37931 784.03501 953.37931L239.96499 953.37931C249.232543 953.37931 256.586205 960.312638 257.154772 969.422054L208.281512 186.375821C208.961624 197.272664 199.982115 206.817739 189.124926 206.817739ZM186.671228 973.821228C188.422497 1001.879852 211.883661 1024 239.96499 1024L784.03501 1024C811.990051 1024 835.582287 1001.80337 837.328772 973.821228L886.202033 190.774996C888.065218 160.922854 864.68894 136.197049 834.875074 136.197049L189.124926 136.197049C159.269853 136.197049 135.939407 160.996952 137.797967 190.774996L186.671228 973.821228ZM971.034483 206.817739C990.535839 206.817739 1006.344828 191.00875 1006.344828 171.507394 1006.344828 152.00602 990.535839 136.197049 971.034483 136.197049L52.965517 136.197049C33.464161 136.197049 17.655172 152.00602 17.655172 171.507394 17.655172 191.00875 33.464161 206.817739 52.965517 206.817739L971.034483 206.817739ZM358.849148 206.817739 665.150852 206.817739C694.015417 206.817739 717.323123 183.246901 717.323123 154.533323L717.323123 52.284416C717.323123 23.465207 693.830144 0 665.150852 0L358.849148 0C329.984583 0 306.676877 23.570838 306.676877 52.284416L306.676877 154.533323C306.676877 183.352514 330.169856 206.817739 358.849148 206.817739ZM377.297567 52.284416C377.297567 62.397193 369.165877 70.62069 358.849148 70.62069L665.150852 70.62069C654.846093 70.62069 646.702433 62.486652 646.702433 52.284416L646.702433 154.533323C646.702433 144.420529 654.834123 136.197049 665.150852 136.197049L358.849148 136.197049C369.153907 136.197049 377.297567 144.331087 377.297567 154.533323L377.297567 52.284416ZM595.6986 835.467988C595.6986 854.969344 611.507571 870.778333 631.008945 870.778333 650.510301 870.778333 666.319289 854.969344 666.319289 835.467988L666.319289 324.729062C666.319289 305.227705 650.510301 289.418717 631.008945 289.418717 611.507571 289.418717 595.6986 305.227705 595.6986 324.729062L595.6986 835.467988ZM428.3014 324.729062C428.3014 305.227705 412.492429 289.418717 392.991055 289.418717 373.489699 289.418717 357.680711 305.227705 357.680711 324.729062L357.680711 835.467988C357.680711 854.969344 373.489699 870.778333 392.991055 870.778333 412.492429 870.778333 428.3014 854.969344 428.3014 835.467988L428.3014 324.729062Z"
      fill="#389BFF"
    >
    </path>
  </svg>
)

export const MetricsIcon = props => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path
      d="M541.888 271.488a135.744 135.744 0 1 0-135.296-147.456c-0.32 3.84-9.984 44.032-29.888 64.64a182.08 182.08 0 0 1-62.592 40.32c-26.048 9.408-48.32 18.56-95.296 3.84-56.192-17.728-107.968-16.128-152.704 28.608-52.672 52.672-45.632 119.68-24 162.752 21.632 43.008 99.456 105.088 193.472 47.936 36.672-22.336 61.952-67.968 61.952-108.16 0-33.152 30.208-65.28 52.416-79.616 17.6-11.392 45.952-28.992 74.048-32.192 32.256-3.712 63.168 7.936 66.56 9.28 15.808 6.464 33.152 10.048 51.328 10.048zM426.112 953.792a135.744 135.744 0 1 0 60.032-190.848c-3.52 1.664-43.136 13.312-70.912 6.4a182.08 182.08 0 0 1-66.24-33.984c-21.12-17.92-40.192-32.64-50.88-80.64-12.8-57.6-40.128-101.568-101.248-117.952-71.936-19.328-126.464 20.352-152.896 60.544-26.432 40.256-41.28 138.752 55.232 191.552 37.632 20.608 89.856 19.648 124.672-0.448 28.672-16.576 71.616-6.4 95.104 5.632 18.624 9.536 48.128 25.28 64.896 48 19.328 26.112 24.768 58.688 25.216 62.336 2.368 16.896 7.936 33.728 17.024 49.408z"
      fill="#365EDF"
    >
    </path>
    <path
      d="M755.2 766.208a135.744 135.744 0 1 0 50.048-193.728c-3.456 1.792-42.432 15.552-70.528 10.112a182.08 182.08 0 0 1-67.84-30.528c-22.144-16.704-41.92-30.4-55.104-77.888-15.808-56.768-45.376-99.264-107.264-112.448-72.832-15.488-125.248 26.88-149.504 68.48-24.32 41.6-33.984 140.672 65.152 188.416 38.656 18.56 90.752 14.912 124.48-7.04 27.776-17.984 71.168-10.112 95.296 0.64 19.072 8.576 49.344 22.784 67.328 44.544 20.608 25.088 27.776 57.408 28.416 60.928 3.2 16.832 9.6 33.28 19.52 48.512z"
      fill="#00B4FF"
    >
    </path>
    <path
      d="M861.76 354.432m-136.896 0a136.896 136.896 0 1 0 273.792 0 136.896 136.896 0 1 0-273.792 0Z"
      fill="#00B4FF"
    >
    </path>
  </svg>
)

export const PolarStarIcon = props => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    {...props}
  >
    <path
      d="M757.312 85.312h-521.6c-45.248 0-75.712 29.696-75.712 73.728v663.232c0 11.456 2.048 21.888 5.824 31.04h-32.512c-41.408 0-69.312-28.608-69.312-71.04V71.04C64 28.608 91.904 0 133.312 0h554.688c41.472 0 69.312 28.608 69.312 71.104v14.208z"
      fill="#6AA0F2"
    >
    </path>
    <path
      d="M293.312 160h554.688c41.472 0 69.312 27.264 69.312 67.776V892.16c0 40.512-27.84 67.776-69.312 67.776H293.312c-41.408 0-69.312-27.264-69.312-67.776V227.84c0-40.512 27.904-67.776 69.312-67.776z m55.808 616.96h90.048V478.592H349.12v298.368z m346.688 0h90.048V526.08h-90.048v250.88z m-173.76 0h90.048V356.288H522.048v420.672z"
      fill="#6AA0F2"
    >
    </path>
  </svg>
)

export const ReferIcon = () => (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
  >
    <path
      d="M248.832 560.64H97.792V198.656h497.152v361.472H512c-30.208 0-52.736 22.528-52.736 52.736S481.792 665.6 512 665.6h97.792c45.056 0 82.944-37.376 82.944-82.944V190.976c0-45.056-37.376-82.944-82.944-82.944H82.944C37.888 108.032 0 145.92 0 190.976v391.168c0 45.056 37.376 82.944 82.944 82.944h165.376c30.208 0 52.736-22.528 52.736-52.736s-22.528-51.712-52.224-51.712z"
      fill="currentColor"
    >
    </path>
    <path
      d="M941.056 356.864h-165.376c-30.208 0-52.736 22.528-52.736 52.736s22.528 52.736 52.736 52.736h150.528v361.472H429.056V462.848H512c30.208 0 52.736-22.528 52.736-52.736s-22.528-52.736-52.736-52.736H414.208c-45.056 0-82.944 37.376-82.944 82.944v391.168c0 45.056 37.376 82.944 82.944 82.944h526.848c45.056 0 82.944-37.376 82.944-82.944V439.808c0-45.056-37.376-82.944-82.944-82.944z"
      fill="currentColor"
    >
    </path>
  </svg>
)

export const ScoreIcon = () => (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
  >
    <path
      d="M453.12 111.504c25.342-47.34 92.418-47.34 117.76 0l109.318 204.21 225.16 42.956c51.986 9.916 72.636 74.096 36.404 113.138l-157.614 169.836 29.464 231.304c6.79 53.312-47.4 93.072-95.284 69.91L512 843.088l-206.328 99.77c-47.886 23.16-102.074-16.6-95.284-69.91l29.464-231.304-157.614-169.836c-36.232-39.044-15.58-103.22 36.404-113.14l225.16-42.954 109.32-204.21z"
      fill="currentColor"
    >
    </path>
  </svg>
)
