import clsx from 'clsx'
import { observer } from 'mobx-react-lite'
import React, { memo, useEffect } from 'react'
import { Avatar, Divider, Dropdown, Layout, Menu, Space } from 'antd'
import { DownOutlined, UserOutlined } from '@ant-design/icons'
import { Outlet, useLocation, useNavigate } from 'react-router-dom'

import { userModel } from '@/stores'
import { businessOptions } from '@/constants/material'
import { useBusinessFromURLQuery } from '@/hooks/useBusinessFromURLQuery'

import SiteLogo from '@/icons/logo'
import JoinKimGroup from '@/components/join-kim-group/JoinKimGroup'
import DoNotDisplayWhenSiteBeEmbedded from '@/components/do-not-display-when-site-be-embedded'

import styles from './style.module.less'

const { Header, Content, Footer } = Layout

const STATIC_BREADCRUMB = {
  Logo: memo(function Logo() {
    return (
      <span style={{ fontSize: 26 }}>
        <SiteLogo />
      </span>
    )
  }),
  SiteTitle: memo(function SiteTitle() {
    const nav = useNavigate()
    return (
      <div className={styles['site-title']}>
        <div
          className={clsx(styles['name'], styles['text'])}
          onClick={() => nav('/welcome')}
        >
          <span className={styles['shadow-button']}>物料中台</span>
        </div>
      </div>
    )
  }),
  Split: () => <span className={styles['breadcrumb-split']}>/</span>,
  BUSwitch: memo(
    observer(() => {
      const nav = useNavigate()
      const [business, _] = useBusinessFromURLQuery()
      const menu = (
        <Menu>
          {businessOptions.map((item) => {
            return (
              <Menu.Item
                key={item.value}
                onClick={() => nav(`/?business=${item.value}`)}
              >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span>{item.label}</span>
                  {['fangzhou', 'kael'].includes(item.value) && (
                    <div className={styles['low-code-label']}>低码</div>
                  )}
                </div>
              </Menu.Item>
            )
          })}
        </Menu>
      )

      return (
        <div className={styles['bu-switch']}>
          <Dropdown overlay={menu}>
            <span
              style={{ display: 'flex', gap: 5, alignItems: 'center' }}
              onClick={() => nav('/')}
            >
              <div className={styles['text']}>
                <span className={styles['shadow-button']}>
                  {businessOptions.find(item => business === item.value)?.label}
                </span>
              </div>
              <DownOutlined />
            </span>
          </Dropdown>
        </div>
      )
    }),
  ),
}

const Main: React.FC = () => {
  const location = useLocation()

  useEffect(() => {
    if (document && location?.pathname != '/') {
      if (document?.documentElement || document?.body) {
        document.documentElement.scrollTop = document.body.scrollTop = 0
      }
    }
  }, [location.pathname])

  return (
    <Layout className={styles['client-main-layout']}>
      {/* header */}
      <Header className={styles['client-main-layout-header']}>
        <div className={styles['client-main-layout-header-content']}>
          <div className={styles['breadcrumb']}>
            <STATIC_BREADCRUMB.Logo />
            <STATIC_BREADCRUMB.SiteTitle />
            <STATIC_BREADCRUMB.Split />
            <STATIC_BREADCRUMB.BUSwitch />
          </div>

          <Space direction="horizontal">
            <JoinKimGroup />
            <DoNotDisplayWhenSiteBeEmbedded>
              <Divider type="vertical" />
              <Avatar
                src={userModel.user?.avatar}
                size={32}
                icon={<UserOutlined />}
                style={{ marginRight: '10px' }}
              />
              <span>{userModel?.user?.name ?? '未找到用户'}</span>
            </DoNotDisplayWhenSiteBeEmbedded>
          </Space>
        </div>
      </Header>

      {/* content */}
      <Content id="content" className={styles['client-main-layout-content']}>
        <Outlet />
      </Content>

      {/* footer */}
      <DoNotDisplayWhenSiteBeEmbedded>
        <Footer className={styles['client-main-layout-footer']}>
          <div className={styles['footer-content']}>
            <span style={{ fontSize: 22 }}>物料中台</span>
            <div className={styles['links']}>
              <div className={styles['column']}>
                <b>文档链接</b>
                <a href="/schema" target="__blank">
                  通用物料协议
                </a>
                <a
                  href="https://docs.corp.kuaishou.com/d/home/<USER>"
                  target="__blank"
                >
                  接入文档
                </a>
              </div>
            </div>
            <div className={styles['qr-code-area']}>
              <img
                style={{ width: 90, height: 90 }}
                src="https://f2.eckwai.com/kos/nlav12333/fangzhou/imgs/1710159902149.6ccb856e7b30215d.png"
                alt="客服群"
              />
              <p style={{ marginTop: 4, textAlign: 'center' }}>扫码进群</p>
            </div>
          </div>
        </Footer>
      </DoNotDisplayWhenSiteBeEmbedded>
    </Layout>
  )
}

export default observer(Main)
