@import '../../styles/mixin.less';

.client-main-layout {
  &-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    width: 100%;
    height: 72px !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: white !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0;

    &-content {
      .fixed-page-margin();
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 100%;
    }

    .breadcrumb {
      display: flex;
      gap: 5px;

      &:hover {
        cursor: pointer;
      }
    }

    .site-title {
      display: flex;
      align-items: center;
      gap: 5px;
      font-weight: bold;

      .icon {
        width: 36px;
        height: 36px;
        margin-right: 4px;
        background: url('https://f2.eckwai.com/udata/pkg/eshop/chrome-plugin-upload/2022-07-08/1657264552135.131dd5b494f269ff.png')
          no-repeat;
        background-size: 100%;
      }
    }

    .breadcrumb-split,
    .text {
      font-size: 22px;
    }
    .over-view {
      font-size: 22px;
      color: rgba(0, 0, 0, 0.85);
      margin-left: 36px;
      &:hover {
        color: var(--theme-color);
      }
    }
  }

  &-content {
    min-height: calc(100vh - 72px - 210px);
    background-color: white;
  }

  &-footer {
    height: 210px;
    background: black !important;
    padding: 40px 0;

    .footer-content {
      .fixed-page-margin();
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: white;
      height: 100%;
      gap: 200px;

      .links {
        flex-grow: 1;
        display: flex;
        gap: 20px;
        height: 100%;

        .column {
          font-size: 14px;
          height: 100%;
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        a {
          color: white;
          &:hover {
            color: var(--theme-color);
          }
        }
      }
    }
  }
}

.shadow-button {
  padding: 5px 10px;
  box-sizing: border-box;
  border-radius: 4px;
  background-color: white;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f5f5;
  }
}

.low-code-label {
  height: 14px;
  display: flex;
  align-items: center;
  font-size: 10px;
  color: #fff;
  margin-left: 4px;
  background-color: @primary-color;
  border-radius: 2px;
  padding: 2px;
}
