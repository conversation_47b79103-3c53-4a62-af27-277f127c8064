import React from 'react'
import { Space } from 'antd'
import { LeftOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

import styles from './style.module.less'

const BackwardNav: React.FC = () => {
  const navigate = useNavigate()

  const handleBackOrHome = () => {
    if (window.history.length > 2) {
      navigate(-1)
    }
    else {
      navigate('/')
    }
  }

  return (
    <div className={styles['backward-nav']}>
      <div className={styles['backward-nav-content']}>
        <Space onClick={handleBackOrHome}>
          <LeftOutlined />
          <span>返回</span>
        </Space>
      </div>
    </div>
  )
}

export default BackwardNav
