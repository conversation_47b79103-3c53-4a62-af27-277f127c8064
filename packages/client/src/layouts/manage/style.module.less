.manage-layout {
  min-width: 1300px;
  overflow-x: scroll;

  &-header {
    position: sticky;
    top: 0;
    z-index: 1;
    width: 100%;
    height: 63px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: white !important;
    padding: 0 36px;

    & > * {
      min-width: 250px;
    }

    &-logo {
      display: flex;
      align-items: center;
      font-size: 24px;
      gap: 16px;
    }

    &-title {
      display: flex;
      align-items: center;
      font-size: 18px;
      gap: 4px;
      line-height: 20px;

      .hover-mask {
        padding: 6px 6px;
        border-radius: 2px;
        cursor: pointer;
        transition: all 0.15s;

        &:hover {
          background-color: #f0f0f0;
        }
      }
    }

    .user-avatar {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      gap: 16px;
    }
  }

  &-content-container {
    height: calc(100vh - 63px);
    width: 100%;
    padding: 12px;
  }

  &-content {
    background-color: white;
    border-radius: 4px;
    padding: 24px;
    height: 100%;
    overflow: auto;
  }
}
