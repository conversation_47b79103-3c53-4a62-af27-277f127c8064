import { Avatar, Layout, Menu, MenuProps, Tag, Tooltip } from 'antd'
import { FC, useState } from 'react'
import { Outlet, useNavigate } from 'react-router-dom'
import { observer } from 'mobx-react-lite'
import {
  AppstoreOutlined,
  AreaChartOutlined,
  BookOutlined,
  FunctionOutlined,
  HistoryOutlined,
  InfoCircleOutlined,
  Pie<PERSON>hartOutlined,
  ProductOutlined,
  RadarChartOutlined,
  SettingOutlined,
  SlidersOutlined,
  SwapOutlined,
  UserOutlined,
} from '@ant-design/icons'

import { userModel } from '@/stores'

import styles from './style.module.less'
import Logo from '@/icons/logo'

const { Header, Content } = Layout

const TOP_MENUS: MenuProps['items'] = [
  {
    label: '物料中心',
    icon: <AppstoreOutlined />,
    key: '/material',
    children: [
      {
        label: '物料管理',
        icon: <ProductOutlined />,
        key: '/material/list',
      },
      {
        label: '分类管理',
        icon: <BookOutlined />,
        key: '/material/category',
      },
      {
        label: '元信息管理',
        icon: <InfoCircleOutlined />,
        key: '/material/meta',
      },
    ],
  },
  {
    label: '转换中心',
    icon: <SwapOutlined />,
    key: '/transfer',
    children: [
      {
        label: '脚本管理',
        icon: <FunctionOutlined />,
        key: '/transfer/script',
      },
      {
        label: '转换记录',
        icon: <HistoryOutlined />,
        key: '/transfer/history',
      },
    ],
  },
  {
    label: '度量中心',
    icon: <SlidersOutlined />,
    key: '/indicator',
    children: [
      {
        label: '物料成熟度',
        icon: <RadarChartOutlined />,
        key: '/indicator/grade-of-maturity',
      },
      {
        label: (
          <span style={{ display: 'inline-flex', gap: 8, alignItems: 'center' }}>
            物料覆盖率
            <Tag color="processing">低代码</Tag>
          </span>
        ),
        icon: <PieChartOutlined />,
        key: '/indicator/low-code-material-coverage',
      },
      // {
      //   label: (
      //     <span style={{ display: 'inline-flex', gap: 8, alignItems: 'center' }}>
      //       新增代码覆盖率
      //       <Tag color="success">源代码</Tag>
      //     </span>
      //   ),
      //   icon: <AreaChartOutlined />,
      //   key: '/indicator/new-code-coverage',
      // },
    ],
  },
  {
    label: '系统管理',
    icon: <SettingOutlined />,
    key: '/settings',
  },
]

const Main: FC = () => {
  const navigate = useNavigate()
  const [selectedKey, setSelectedKey] = useState(location.pathname.replace('/manage', '') || '/material/list')

  return (
    <Layout className={styles['manage-layout']}>
      <Header className={styles['manage-layout-header']}>
        <div className={styles['manage-layout-header-logo']}>
          <Logo />
          <div className={styles['manage-layout-header-title']}>
            <Tooltip title="返回前台">
              <b className={styles['hover-mask']} onClick={() => navigate('/')}>物料中台</b>
            </Tooltip>
            <b>/</b>
            <span className={styles['hover-mask']} onClick={() => navigate('/manage')}>管理后台</span>
          </div>
        </div>
        <Menu
          mode="horizontal"
          onSelect={({ selectedKeys }) => {
            const nextKey = selectedKeys[0]
            if (nextKey === selectedKey) return
            setSelectedKey(nextKey)
            console.log(`navigate to /manage${nextKey}`)
            navigate(`/manage${nextKey}`)
          }}
          selectedKeys={[selectedKey]}
          items={TOP_MENUS}
        />

        <div className={styles['user-avatar']}>
          <div>
            <Avatar
              src={userModel.user?.avatar}
              size={32}
              icon={<UserOutlined />}
              style={{ marginRight: '10px' }}
            />
            <span>{userModel?.user?.name ?? '未找到用户'}</span>
          </div>
        </div>
      </Header>
      <Content className={styles['manage-layout-content-container']}>
        <div className={styles['manage-layout-content']}>
          <Outlet />
        </div>
      </Content>
    </Layout>
  )
}

export default observer(Main)
