import { ReactNode, ReactElement } from 'react'

export interface TabItemProps {
  label: string | ReactNode
  icon: ReactElement
  href: string
  key: string
}

export interface Scene {
  sceneId: number | null
  sceneTitle: string
  categories: Category[]
}

export interface Category {
  categoryId: number | null
  categoryTitle: string
  tags: Tag[]
}

export interface Tag {
  tagId: number | null
  tagTitle: string
}
