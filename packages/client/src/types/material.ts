import { Business, EffectStatus, MaterialType } from '@/constants'
import { ReactNode } from 'react'
import { CreateMaterialParam } from '@/services/models'
import type { TablePaginationConfig } from 'antd/es/table'
import type { FilterValue } from 'antd/es/table/interface'
import { AnyType } from '.'

export interface TableParams {
  pagination?: TablePaginationConfig
  sortField?: string
  sortOrder?: string
  filters?: Record<string, FilterValue>
}
/** 展示模式：滚动列表或者分页 */
export type DisplayMode = 'scroll-list' | 'pagination'

export interface AdvFormItemProps {
  label: string | ReactNode
  name: string
  renderType?: 'date' | 'input' | 'select' | 'remoteSelect' | string
  extraOptions?: Record<string, AnyType>
  children?: ReactNode
}

export interface Material {
  id: number
  namespace: string
  version: string
  title: string
  readme: string
  description: string
  type: string
  business: string
  git_url: string
  platform: string
  icon?: string
  preview_img?: string
  creator_id: string
  creator_name: string
  create_time: number
  updater_id: string
  updater_name: string
  update_time: number
  commit_info: string
  scenes?: Scene[]
  tags: Tag[]
  meta?: string
  scope_status?: number

}

/** 转换记录 */
export interface Convert {
  id: number
  material_id: number
  version: string
  script: string
  title: string
  creator_id: string
  create_time: number
  status: number
  reason: string
  result: number
}

/** 脚本 */
export interface ScriptItem {
  id: number
  business: string
  script: string
  creator_id: string
  create_time: number
  status: number
}

export interface MaterialVersion {
  id: number
  title: string
  version: string
  create_time: number
  creator_name: string
  creator_id: string
}
export interface MaterialVersions {
  list: Array<MaterialVersion>
  total: number
}

export interface MaterialVersionItem {
  create_time: number
  creator_id: string
  creator_name: string
  creator_username: string
  description: string
  file_pub_id: number
  icon: string
  material_id: number
  namespace: string
  preview_img: string
  scene_id: number
  status: number
  title: string
  type: string
  update_time: number
  updater_id: string
  updater_name: string
  version: string
  /** 前端使用 */
  disabled?: boolean
}

export interface Creator {
  /** 创建者 */
  creator_id: number
  /** 创建时间 */
  create_time: number
}
export interface Status {
  /** 生效状态，-1-删除，0-待上线，1-正常 */
  status: EffectStatus
}

export interface Scene extends Creator, Status {
  id: number | null
  title: string
  order: number
}

export interface Category {
  categoryId: number | null
  categoryTitle: string
  tags: Tag[]
}

export interface Tag extends Creator, Status {
  id: number | null
  title: string
  order: number
}

export interface MaterialModalInfo {
  [key: string]: AnyType
}

export interface ComLibMaterialInfo {
  id: number
  file_pub_id: number
  namespace: string
  version: string
  icon: string
  preview_img: string
  title: string
  description: string
  type: string
  key_info: string
  creator_id: string
  creator_name: string
  create_time: number
  updator_id: string
  updator_name: string
  update_time: number
  file_id: number
  content?: {
    title?: string
    description?: string
    docs?: string
    version?: string
    namespace?: string
    runtimeCode?: string
    editCode?: string
    componentType?: string
    localConfig?: Record<string, unknown>
    onlineConfig?: Record<string, unknown>
    fileId?: number
  }
  file_content_id: number
  commit_info: string
  // env_type: string;
  // com_pub_ids: number[];
  // components: Material[];
  // scene?: {
  //  id: number;
  //  title: string;
  // };
  // tags?: Array<{ tag_id: number; title: string }>;
  // /** 引用次数 */
  // refer_total: number;
  // docs?: string;
}

export interface MaterialReferItem {
  id: number
  page_id: number
  title: string
  material_id: number
  ref_page: string
  type: MaterialType
  file_pub_id: number
  version: string
  namespace: string
  create_time: string
  creator_name: string
  creator_id: string
}

export type ComponentMaterialInfo = Omit<ComLibMaterialInfo, 'components' | 'com_pub_ids'>

export interface ComOrLibMaterialInfo extends CreateMaterialParam {
  id: number
  /** 创建者 */
  creator_id: string
  create_time: number
  /** 更新者id */
  updater_id: string
  /** 更新时间 */
  updater_time: number
  /** 生效状态 */
  status: string
  /** 其他元信息 */
  [key: string]: AnyType
  meta?: Array<{ [key: string]: AnyType }>
  tags: Tag[]
}

export interface Meta {
  id: number
  title: string
  business: Business
  value: string
  /** 创建者 */
  creator_id: string
  create_time: number
  /** 生效状态 */
  status: string
}

/** 报表数据类型 */
export enum ReportType {
  /** 北极星指标 */
  POLAR_STAR = 'polar_star',
  /** 页面覆盖率 */
  PAGE_COVERAGE_RATE = 'page_coverage_rate',
  /** 新增页面覆盖率 */
  NEW_PAGE_COVERAGE_RATE = 'new_page_coverage_rate',
  /** 项目覆盖率 */
  PROJECT_COVERAGE_RATE = 'project_coverage_rate',
  /** 成本效益 */
  COST_EFFECTIVENESS = 'cost_effectiveness',
}

export enum SortingType {
  /** 按引用次数/组件成熟度 */
  INTEGRAL = 'integral',
  /** 按更新时间 */
  UPDATE_TIME = 'update_time',
}

export enum IntegralType {
  /** 引用次数 */
  REFER_COUNT = 'refer_count',
  /** 组件成熟度 */
  MASS_SCORE = 'mass_score',
}
