import React, { useEffect } from 'react'
import { message } from 'antd'

import {
  BrowserRouter,
  Navigate,
  Outlet,
  Route,
  Routes,
} from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/es/locale/zh_CN'

import 'antd/dist/antd.less'
import './global.less'

import { queryUserInfoUsingGET } from './services'
import { userModel } from './stores'

import Portal from '@/layouts/portal'
import MaterialList from '@/pages/list'
import Welcome from '@/pages/welcome'
import Detail from '@/pages/detail'
import MetricsReport from '@/pages/report'
import ComponentSchema from '@/pages/schema'
import PreviewPage from '@/pages/preview'

import Manage from '@/layouts/manage'
import MaterialManage from '@/pages/manage/material/material-manage'
import CategoryManage from '@/pages/manage/material/category-manage'
import MetaManage from '@/pages/manage/material/meta-manage'
import ScriptManage from '@/pages/manage/transfer/script-manage'
import TransferHistory from '@/pages/manage/transfer/transfer-history'
import GradeOfMaturity from '@/pages/manage/indicator/grade-of-maturity'
import Settings from '@/pages/manage/settings'
import LowCodeMaterialCoverage from './pages/manage/indicator/low-code-material-coverage'
import NewCodeCoverage from './pages/manage/indicator/new-code-coverage'

const Entry: React.FC = () => {
  useEffect(() => {
    queryUserInfoUsingGET().then((res) => {
      if (res.code === 1) {
        userModel.setUser(res.data)
      }
      else {
        message.error('登录信息获取失败')
      }
    })
  }, [])

  return (
    <ConfigProvider locale={zhCN}>
      <BrowserRouter>
        <Routes>
          <Route element={<Outlet />}>
            <Route path="/" element={<Portal />}>
              <Route path="/" element={<MaterialList />} />
              <Route path="/welcome" element={<Welcome />} />
              <Route path="/detail" element={<Detail />} />
              <Route path="/new-report" element={<MetricsReport />} />
              <Route path="/schema" element={<ComponentSchema />} />
              <Route path="/lib-detail" element={<Detail />} />
              <Route path="/preview" element={<PreviewPage />} />
            </Route>
            <Route path="manage" element={<Manage />}>
              <Route index element={<Navigate to="/manage/material/list" />} />
              <Route path="home" element={<Navigate to="/manage/material/list" />} />
              <Route path="material" element={<Outlet />}>
                <Route path="list" element={<MaterialManage />} />
                <Route path="category" element={<CategoryManage />} />
                <Route path="meta" element={<MetaManage />} />
              </Route>
              <Route path="transfer" element={<Outlet />}>
                <Route path="script" element={<ScriptManage />} />
                <Route path="history" element={<TransferHistory />} />
              </Route>
              <Route path="indicator" element={<Outlet />}>
                <Route path="grade-of-maturity" element={<GradeOfMaturity />} />
                <Route path="low-code-material-coverage" element={<LowCodeMaterialCoverage />} />
                {/* <Route path="new-code-coverage" element={<NewCodeCoverage />} /> */}
              </Route>
              <Route path="settings" element={<Settings />} />
            </Route>
          </Route>
        </Routes>
      </BrowserRouter>
    </ConfigProvider>
  )
}

export default Entry
