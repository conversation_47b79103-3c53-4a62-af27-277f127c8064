import axios from 'axios'
import * as model from './models'
import { mAxios } from './http'
import { Business } from '@/constants'

/** 对外管理页接口 */
/** 新增物料接口 */
export async function createMaterialInfoUsingPOST(data: model.CreateMaterialParam): Promise<model.CommonResult> {
  return await mAxios.request({
    url: '/material/create',
    method: 'post',
    data,
  })
}

export async function createMaterialLibInfoUsingPOST(data: Record<string, AnyType>): Promise<model.CommonResult> {
  const result = await axios.request({
    url: '/api/website/material/com_lib/create',
    method: 'post',
    data,
  })
  return result.data
}

export async function queryVirtualMaterialDetailUsingGET(payload: Record<string, AnyType>) {
  const result = await axios.request({
    url: '/api/manage/material/virtual_version_detail',
    method: 'get',
    params: payload,
  })
  return result.data
}

export async function publishVirtualMaterialUsingPOST(payload: { material_id: number, creator_id: number, components: Array<Record<string, AnyType>> }): Promise<Record<string, AnyType>> {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/material/publish_virtual_lib_components',
    method: 'post',
    data: payload,
  })
}

export async function queryVersionsByMaterialIdsUsingPOST(payload: { materialIds: number[] }) {
  const result = await mAxios.request({
    url: '/version/getVersionsByMaterialIds',
    method: 'post',
    data: payload,
  })
  return result.data
}
/** 更新物料接口 */
export async function updateMaterialInfoUsingPOST(payload: {
  param: model.UpdateMaterialParam
}) {
  const data = {
    ...payload.param,
  }

  const result = await mAxios.request<Array<model.CommonResult>>({
    url: '/material/update',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 更新物料接口 */
export async function updateMaterialScenesByIdUsingPOST(payload: { param: model.UpdateMaterialScenesParam }) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/material/updateScenes',
    method: 'post',
    data: payload.param,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 更新物料接口 */
export async function updateMaterialTagsByIdUsingPOST(payload: { param: model.UpdateMaterialTagsParam }) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/material/updateTags',
    method: 'post',
    data: payload.param,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** ad-agent-metrics-task-controller 删除物料 */
export async function deleteMaterialInfoUsingPOST(payload: { id: number }): Promise<model.CommonResult> {
  return await mAxios.request({
    url: '/material/delete',
    method: 'post',
    data: { id: payload.id },
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 启用物料 */
export async function enableMaterialInfoUsingPOST(payload: { id: number, updater_id: number }): Promise<model.CommonResult> {
  return await mAxios.request({
    url: '/material/enable',
    method: 'post',
    data: payload,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 查询物料列表 */
export async function viewAllMaterialManageListUsingGET(payload: Record<string, AnyType>) {
  const params = {
    ...payload,
  }

  const result = await mAxios.request<model.CommonPageData>({
    url: '/material/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 更新物料版本接口 */
export async function updateMaterialVersionInfoUsingPOST(payload: { param: model.UpdateMaterialVersionParam }) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/material/version/update',
    method: 'post',
    data: payload.param,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 获取版本记录列表列表 */
export async function queryVersionListByMaterialInfoIdUsingGET(payload: { id: number }) {
  const result = await mAxios.request<Module.Material.Pub[]>({
    url: '/material/version/list',
    method: 'get',
    params: {
      id: payload.id,
    },
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 新增物料场景接口 */
export async function createMaterialSceneUsingPOST(payload: { param: model.CreateMaterialSceneParam }) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/scene/create',
    method: 'post',
    data: payload.param,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 更新物料场景接口 */
export async function updateMaterialSceneUsingPOST(payload: {
  param: model.UpdateMaterialSceneParam
}) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/scene/update',
    method: 'post',
    data: payload.param,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 删除物料场景 */
export async function deleteMaterialSceneUsingPOST(payload: { sceneId?: number }) {
  const data = {
    id: payload.sceneId,
  }

  const result = await mAxios.request<model.IMessageboolean>({
    url: '/scene/delete',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 查询物料场景列表 */
export async function viewAllMaterialScenesUsingGET(payload: Record<string, AnyType>) {
  const params = {
    ...payload,
  }

  const result = await mAxios.request<model.CommonPageData>({
    url: '/scene/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 新增物料标签接口 */
export async function createMaterialTagUsingPOST(payload: { param: model.CreateMaterialTagParam }) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/tag/create',
    method: 'post',
    data: payload.param,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 更新物料标签接口 */
export async function updateMaterialTagUsingPOST(payload: { param: model.UpdateMaterialTagParam }) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/tag/update',
    method: 'post',
    data: payload.param,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 删除物料标签 */
export async function deleteMaterialTagUsingPOST(payload: { tagId?: number }) {
  const data = {
    id: payload.tagId,
  }

  const result = await mAxios.request<model.IMessageboolean>({
    url: '/tag/delete',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 查询物料标签列表 */
export async function viewAllMaterialTagUsingGET(payload: Record<string, AnyType>) {
  const params = {
    ...payload,
  }

  const result = await mAxios.request<model.CommonPageData>({
    url: '/tag/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 新增部门配置接口 */
export async function createBusinessConfigUsingPOST(payload: { param: model.CreateBusinessConfigParam }) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/business/create',
    method: 'post',
    data: payload.param,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 更新物料标签接口 */
export async function updateBusinessConfigUsingPOST(payload: { param: model.UpdateBusinessConfigParam }) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/business/update',
    method: 'post',
    data: payload.param,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 删除部门配置 */
export async function deleteBusinessConfigUsingPOST(payload: { id: number, key: string }) {
  const data = {
    id: payload.id,
    key: payload.key,
  }

  return await mAxios.request<model.IMessageboolean>({
    url: '/business/delete',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 查询部门配置列表 */
export async function viewAllBusinessConfigUsingGET(payload?: Record<string, AnyType>) {
  const result = await mAxios.request<model.CommonPageData>({
    url: '/business/list',
    method: 'get',
    params: payload,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 更新物料接口 */
export async function openAutoConvert(payload: { id: number, updater_id: number, business: Business[] }) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/material/convert/auto/open',
    method: 'post',
    data: payload,
  })
}

/** 更新物料接口 */
export async function closeAutoConvert(payload: { id: number, updater_id: number }) {
  return await mAxios.request<Array<model.CommonResult>>({
    url: '/material/convert/auto/close',
    method: 'post',
    data: payload,
  })
}
