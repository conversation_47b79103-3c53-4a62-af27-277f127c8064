import * as model from './models'
import axios from 'axios'
import { mAxios } from './http'

export const cAxios = axios.create({ baseURL: '/api/cdn', withCredentials: true })

cAxios.interceptors.response.use(
  (response) => {
    if (response.status === 200 || response.status === 201) {
      return response.data
    }
    else {
      return Promise.reject('http status is not 200')
    }
  },
  error => Promise.reject(error),
)
/** 转换脚本相关 */
/** 新增物料接口 */
export async function triggerConvertMaterialUsingPOST(payload: { param: model.TriggerConvertParam }) {
  return await mAxios.request<model.CommonResult>({
    url: '/convert/trigger',
    method: 'post',
    data: payload.param,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 物料转换详情 */
export async function queryMaterialConvertDetailUsingGET(payload: { id?: number }) {
  const params = {
    id: payload.id,
  }

  const result = await mAxios.request<model.IMessageMateriaConvertDetailResp>({
    url: '/convert/detail',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 查询物料转换状态 */
export async function queryConvertStatusUsingGET(payload: { id: string }) {
  const params = {
    id: payload.id,
  }

  const result = await mAxios.request<model.IResponseConvertStatusResp>({
    url: '/convert/status',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 物料转换详情 */
export async function deleteConvertByIdUsingPOST(payload: { id?: number }) {
  const params = {
    id: payload.id,
  }

  const result = await mAxios.request<model.IMessageMateriaConvertInfoResp>({
    url: '/convert/delete',
    method: 'post',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 转换记录 */
export async function viewAllConvertListUsingGET(payload: Record<string, AnyType>) {
  const params = {
    ...payload,
  }

  const result = await mAxios.request<model.CommonPageData>({
    url: '/convert/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 转换记录 */
export async function publishConvertBundleUsingPOST(data: { id: number }) {
  return await mAxios.request<model.IMessageListViewMaterialInfoResp>({
    url: '/convert/publish',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' },
  })
}

/**   转换脚本   */

/** 新增转换脚本接口 */
export async function createScriptUsingPOST(payload: {
  param: model.CreateMaterialParam
}) {
  const data = {
    ...payload.param,
  }

  const result = await axios.request<Array<model.CommonResult>>({
    url: '/api/manage/script/create',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 更新转换脚本接口 */
export async function updateScriptInfoUsingPOST(payload: {
  param: model.UpdateMaterialParam
}) {
  const data = {
    ...payload.param,
  }

  return await mAxios.request<Array<model.CommonResult>>({
    url: '/script/update',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' },
  })
}

/** ad-agent-metrics-task-controller 删除脚本 */
export async function deleteScriptUsingPOST(payload: { id: number }) {
  const data = {
    id: payload.id,
  }

  const result = await mAxios.request<model.IMessageDelete>({
    url: '/script/delete',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data ? result.data : result
}

/** 查询所有脚本列表 */
export async function viewAllScriptListUsingGET(payload: Record<string, AnyType>) {
  const params = {
    ...payload,
  }

  const result = await mAxios.request<model.CommonPageData>({
    url: '/script/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** policy-upload-controller 文件上传接口 */
export async function uploadScriptFileUsingPost(payload: { file: File, path: string, hash?: boolean }) {
  const data = new FormData()
  data.append('file', payload.file)
  data.append('path', payload.path)
  data.append('hash', String(!!payload.hash))

  return await cAxios.request<model.WebResponseScriptUploadResp>({
    url: '/uploadFile',
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' },
  })
}
