import axios from 'axios'

export const mAxios = axios.create({ baseURL: '/api/manage', withCredentials: true })

mAxios.interceptors.response.use(
  (response) => {
    if (response.status === 200 || response.status === 201) {
      return response.data
    }
    else {
      return Promise.reject('http status is not 200')
    }
  },
  error => Promise.reject(error),
)

export const wAxios = axios.create({ baseURL: '/api/website/material', withCredentials: true })

wAxios.interceptors.response.use(
  (response) => {
    if (response.status === 200 || response.status === 201) {
      return response.data
    }
    else {
      return Promise.reject('http status is not 200')
    }
  },
  error => Promise.reject(error),
)
