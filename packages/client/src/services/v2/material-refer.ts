import request from '@/utils/request'
import { CODE_TYPE } from '@global-material-middleoffice/server-v2/shared'

export const fetchTianheMaterialsReferCount = (
  namespaces: string[],
  appKeys: string[],
  invert: boolean,
  beforeTime?: number,
) =>
  request.post('/v2/material/refer/tianhe/materials-refer-count', {
    namespaces,
    appKeys,
    invert,
    beforeTime,
  })

export const fetchTianheMaterialReferPages = (
  namespace: string,
  appKeys: string[] = [],
) =>
  request.post('/v2/material/refer/tianhe/material-refer-pages', {
    namespace,
    appKeys,
  })

export const fetchTianheAppPageReferUsage = (appKeys: string[]) =>
  request.post('/v2/material/refer/tianhe/app-page-refer-usage', { appKeys })

export const fetchTianheUsageGrowth = (
  namespaces: string[] = [],
  appKeys: string[] = [],
) =>
  request.post('/v2/material/refer/tianhe/material-usage-growth', {
    namespaces,
    appKeys,
  })

export const fetchTianheUseRateGrowth = (
  namespaces: string[] = [],
  appKeys: string[] = [],
) =>
  request.post('/v2/material/refer/tianhe/material-use-rate-growth', {
    namespaces,
    appKeys,
  })

export const fetchMaterialReferList = (params: {
  id: number
  source_type?: 'namespace' | 'package'
  code_type?: CODE_TYPE
  pageSize?: number
  pageNum?: number
}) => request.get('/v2/material/refer/list', { params })
