import request from '@/utils/request'

export function fetchMaterialTags(params: Service.Material.Tag.SearchParams) {
  return request.get('/v2/material/tag/list', { params })
}

export function createMaterialTag(params: Service.Material.Tag.CreateParams) {
  return request.post('/v2/material/tag/create', params)
}

export function updateMaterialTag(params: Service.Material.Tag.UpdateParams) {
  return request.post('/v2/material/tag/update', params)
}

export function enableMaterialTag(params: Service.Material.Tag.EnableParams) {
  return request.post('/v2/material/tag/enable', params)
}

export function deleteMaterialTag(id: number) {
  return request.post('/v2/material/tag/delete', { id })
}
