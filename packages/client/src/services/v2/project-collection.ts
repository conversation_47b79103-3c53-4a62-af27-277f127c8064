import request from '@/utils/request'

export function queryProjectCollectionList(listParams: Service.Analysis.ProjectCollection.ListParams) {
  return request({
    method: 'POST',
    url: '/v2/analysis/project-collection/list',
    data: listParams,
  })
}

export function createProjectCollection(data: Service.Analysis.ProjectCollection.CreateParams) {
  return request({
    method: 'POST',
    url: '/v2/analysis/project-collection/create',
    data,
  })
}
export function updateProjectCollection(data: Service.Analysis.ProjectCollection.UpdateParams) {
  return request({
    method: 'POST',
    url: '/v2/analysis/project-collection/update',
    data,
  })
}

export function queryProjectCollectionDetail(id: number) {
  return request({
    method: 'GET',
    url: '/v2/analysis/project-collection/detail',
    params: { id },
  })
}

export function computeNewCodeCoverage(collectionId: number) {
  return request({
    method: 'GET',
    url: '/v2/analysis/project-collection/compute/new-code-coverage',
    params: { id: collectionId },
  })
}
