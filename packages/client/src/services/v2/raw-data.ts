import request from '@/utils/request'

export function queryRawDataList(
  params: Service.Analysis.RawData.FindListRawDataRecordParams,
) {
  return request({
    method: 'POST',
    url: '/v2/analysis/raw-data/list',
    data: params,
  })
}

export function queryRawDataDetail(
  params: Service.Analysis.RawData.SearchOneRawDataRecordParams,
) {
  return request({
    method: 'GET',
    url: '/v2/analysis/raw-data/detail',
    params,
  })
}
