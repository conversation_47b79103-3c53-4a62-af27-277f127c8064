import request from '@/utils/request'

export function fetchMaterialDetail(params: Service.Material.DetailQueries) {
  return request.post<
    Service.StandardJSONResponseWrapper<Service.Material.MaterialDetail>
  >('/v2/material/detail', params)
}

export function __oldFetchMaterialList(params: Service.Material.ListParams) {
  return request.get('/v2/material/old/list', { params })
}

export function fetchMaterialList(params: Service.Material.ListQueries) {
  return request.post<
    Service.StandardJSONResponseWrapper<Service.Material.MaterialListResult>
  >('/v2/material/list', params)
}

export function fetchKaelMaterialBaseInfoByNamespace(namespace: string) {
  return request.get('/v2/material/kael/base-info/namespace', {
    params: { namespace },
  })
}

export function fetchTransformedTags(tags: GeneralMaterialSchema.Tags) {
  return request.post<
    Service.StandardJSONResponseWrapper<Service.Material.MaterialDetail['tags']>
  >('/v2/material/transform/tags', { tags })
}
