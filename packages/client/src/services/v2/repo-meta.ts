import request from '@/utils/request'

export function queryRepoMetaList(
  params: Service.Analysis.RepoMeta.ListParams,
) {
  return request({
    method: 'GET',
    url: '/v2/analysis/repo-meta/list',
    params,
  })
}

export function queryRepoMetaDetail(
  params: Service.Analysis.RepoMeta.FindOneParams,
) {
  return request({
    method: 'GET',
    url: '/v2/analysis/repo-meta',
    params,
  })
}
