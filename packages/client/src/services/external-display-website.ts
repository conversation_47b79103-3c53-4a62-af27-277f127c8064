import * as model from './models'
import axios from 'axios'
import { mAxios, wAxios } from './http'
import { CODE_TYPE } from '@global-material-middleoffice/server-v2/shared'

/** 对内展示列表 */
/** 新增物料接口 */
export async function createMaterialUsingPOST(payload: {
  param: model.CreateMaterialParam
}) {
  const data = {
    ...payload.param,
  }

  const result = await axios.request<Array<model.CommonResult>>({
    url: 'api/manage/material/create',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 更新物料接口 */
export async function updateMaterialUsingPOST(payload: {
  param: model.UpdateMaterialParam
}) {
  const data = {
    ...payload.param,
  }

  const result = await axios.request<Array<model.CommonResult>>({
    url: '/api/material/update',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** ad-agent-metrics-task-controller 删除物料 */
export async function deleteMaterialUsingGET(payload: { materialId?: number }) {
  const params = {
    taskId: payload.materialId,
  }

  const result = await axios.request<model.IMessageboolean>({
    url: '/api/material/delete',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 物料详情 */
export async function queryMaterialDetailUsingGET(payload: {
  namespace?: string
  id?: number
  version?: string
}) {
  const params = {
    id: payload.id,
    namespace: payload.namespace,
    version: payload.version,
  }

  const result = await wAxios.request<model.IMessageMaterialInfoResp>({
    url: '/detail',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 物料详情 */
export async function queryMaterialReuseRateUsingGET(payload: {
  namespace?: string
  id?: number
  business: Business
}) {
  const result = await mAxios.request<model.IMessageMaterialInfoResp>({
    url: '/refer/rate',
    method: 'get',
    params: {
      id: payload.id,
      namespace: payload.namespace,
      business: payload.business,
    },
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 查询物料列表 */
export async function viewAllExternalMaterialListUsingGET(
  payload: Record<string, AnyType>,
) {
  const result = await wAxios.request<model.CommonPageData>({
    url: '/list_optimized',
    method: 'get',
    params: { ...payload },
    headers: { 'Content-Type': 'application/json' },
  })
  return result.data
}

/** 获取引用记录列表 */
export async function queryReferListByMaterialIdUsingGET(payload: {
  id?: number
  pageSize: number
  pageNum: number
  code_type: CODE_TYPE
  source_type: string
}) {
  const result = await wAxios.request<model.IResponseReferListResp>({
    url: '/refer/list',
    method: 'get',
    params: {
      id: payload.id,
      pageSize: payload.pageSize,
      pageNum: payload.pageNum,
      source_type: payload.source_type,
      code_type: payload.code_type,
    },
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 获取版本记录列表列表 */
export async function queryVersionListByMaterialIdUsingGET(payload: {
  id: number
}) {
  const params = {
    id: payload.id,
  }

  const result = await wAxios.request<model.IResponseReferListResp>({
    url: '/version/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 物料详情 */
export async function queryMaterialVersionPubDetailUsingGET(payload: {
  id: number
}): Promise<Module.Material.Pub> {
  const result = await mAxios.request({
    url: '/version/detail',
    method: 'get',
    params: { id: payload.id },
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 查询组件列表 */
export async function queryComponentListByMaterialIdUsingGET(payload: {
  id: number
  version: string
}) {
  const params = {
    id: payload.id,
    version: payload.version,
  }

  const result = await wAxios.request<Record<string, AnyType>>({
    url: '/comlib/component/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

export async function queryUserInfoUsingGET() {
  const result = await axios.request<Record<string, AnyType>>({
    url: '/api/website/user/info',
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 查询BU扩展元信息列表 */
export async function queryComponentMetaListByBusinessUsingGET(payload: {
  business: string
}) {
  const params = {
    business: payload.business,
  }

  const result = await wAxios.request<Record<string, AnyType>>({
    url: '/meta/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

/** 更新物料接口2 */
export async function updateMaterialInfo(payload: {
  param: model.IUpdateMaterialParam
}): Promise<model.CommonResult> {
  return await wAxios.request({
    url: '/update',
    method: 'post',
    data: { ...payload.param },
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function queryMaterialReportOverviewUsingGET(
  params: model.QueryReportOverviewParams,
) {
  const result = await mAxios.request<AnyType>({
    url: '/report/overview',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

export async function queryMaterialReportDataUsingGET(
  params: model.QueryReportParams,
) {
  const result = await mAxios.request<AnyType>({
    url: '/report/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}
