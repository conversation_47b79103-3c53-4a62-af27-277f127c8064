export interface CreateMaterialParam {
  namespace: string
  version: string
  title: string
  readme: string
  description: string
  type: string
  business: string
  git_url: string
  platform: string
}

export interface UpdateMaterialParam extends CreateMaterialParam {
  id: number
  status: string
}

export interface CommonResult {
  code: number
  data: AnyType
  message: string
}

export interface IMessageboolean {
  data: boolean
  code: number
  message: string
  result: number
}

export interface IMessageDelete {
  code: number
  message: string
  data: AnyType
}

export interface IMessageMaterialInfoResp {
  data: UpdateMaterialParam
  error_message: string
  host: string
  result: number
  status: number
  timestamp: string
  total: number
  trace_id: string
}

export interface IMessageMateriaConvertDetailResp {
  id: number
  script: string
  bundle: string
}

export interface CommonPageData {
  dataSource: Array<Record<string, AnyType>>
  pageNum: number
  pageSize: number
  total: number
}

export interface IMessageListViewMaterialInfoResp extends CommonResult {
  data: CommonPageData
}

export interface IResponseSceneListResp extends CommonResult {
  data: {
    dataSource: Array<Record<string, AnyType>>
    pageNum: number
    pageSize: number
    total: number
  }
}

export interface ReferItemData {
  material_id: number
  version: string
  ref_page: string
  ref_business: string
  type: string
  creator_id: string
  create_time: number
  status: number
}
export interface CreateMaterialSceneParam {
  title: string
  order: number
}
export interface UpdateMaterialSceneParam extends CreateMaterialSceneParam {
  id: number
  status: string
}
export interface IResponseReferListResp extends CommonResult {
  data: Array<ReferItemData>
}

export interface IResponseTagListResp extends CommonResult {
  data: CommonPageData
}

export interface CreateMaterialTagParam {
  title: string
  value: string
  order: number
}

export interface CreateBusinessConfigParam {
  business: string
  meta: Record<string, AnyType>
  updater_id: number
}

export interface UpdateBusinessConfigParam extends CreateBusinessConfigParam {
  id: string
}

export interface UpdateMaterialScenesParam {
  id: number
  scenes: number[]
  creator_id: number
}

export interface UpdateMaterialTagsParam {
  id: number
  tags: number[]
  creator_id: number
}

export interface UpdateMaterialTagParam {
  id: number
  status: string
}

export interface UpdateMaterialVersionParam {
  status: string
  id: number
}

export interface WebResponseScriptUploadResp {
  data: {
    url: string
  }
  code: number
}

export interface IResponseConvertStatusResp {
  result: number
  text: string
}

export interface IUpdateMaterialParam {
  /** 更新者 */
  updater: string
  /**
   * 物料命名空间，拼接规则如下：
   * componentBundleType === 'SLMC' ? `${packageName}/${componentName}` : packageName
   */
  namespace: string
  version?: string
  readme?: string
  preview_img?: string
  title?: string
  description?: string
  git_url?: string
  platform?: string
  schema: {
    props?: Array<AnyType>
  }
}

export interface QueryReportParams {
  business: string
  code_type: string
}

export interface QueryReportOverviewParams {
  business: string
  code_type: string
  start_time: number
  end_time: number
}

export interface TriggerConvertParam {
  material_id: number
  version: string
  business: Business
  creator_id: number
}

export interface IMessageMateriaConvertInfoResp {
  id: number
}
