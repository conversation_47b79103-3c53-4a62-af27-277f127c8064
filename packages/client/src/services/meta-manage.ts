import * as model from './models'
import { Business, CommonResult } from './models'
import axios from 'axios'
import { AnyType } from '@/types'
import { EffectStatus } from '@/constants'

const cAxios = axios.create({ baseURL: '/api/manage/meta', withCredentials: true })

cAxios.interceptors.response.use(
  (response) => {
    if (response.status === 200 || response.status === 201) {
      return response.data
    }
    else {
      return Promise.reject('http status is not 200')
    }
  },
  error => Promise.reject(error),
)

/** 查询物料列表 */
export async function viewAllMetaManageListUsingGET(payload: Record<string, AnyType>) {
  const params = {
    ...payload,
  }

  const result = await cAxios.request<model.CommonPageData>({
    url: '/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

export async function createMetaUsingPOST(payload: { title: string, value: string, business: Business, creator_id: number, meta: string }): Promise<CommonResult> {
  return await cAxios.request({
    url: '/create',
    method: 'post',
    data: payload,
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function updateMetaUsingPOST(payload: { title: string, value: string, id: number }): Promise<CommonResult> {
  return await cAxios.request({
    url: '/update',
    method: 'post',
    data: payload,
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function updateMetaStatusUsingPOST(payload: { status: EffectStatus, id: number }): Promise<CommonResult> {
  return await cAxios.request({
    url: '/update',
    method: 'post',
    data: payload,
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function deleteMetaUsingPOST(id: number): Promise<CommonResult> {
  return await cAxios.request({
    url: '/delete',
    method: 'post',
    data: { id },
    headers: { 'Content-Type': 'application/json' },
  })
}

/** 查询物料列表 */
export async function viewAllMetaValueManageListUsingGET(payload: Record<string, AnyType>) {
  const params = {
    ...payload,
  }

  const result = await cAxios.request<model.CommonPageData>({
    url: '/value/list',
    method: 'get',
    params,
    headers: { 'Content-Type': 'application/json' },
  })

  return result.data
}

export async function createMetaValueUsingPOST(payload: { title: string, value: string, order: number, meta_id: number, creator_id: number }): Promise<CommonResult> {
  return await cAxios.request({
    url: '/value/create',
    method: 'post',
    data: payload,
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function updateMetaValueUsingPOST(payload: { title: string, value: string, order: number, id: number }): Promise<CommonResult> {
  return await cAxios.request({
    url: '/value/update',
    method: 'post',
    data: payload,
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function updateMetaValueStatusUsingPOST(payload: { status: EffectStatus, id: number }): Promise<CommonResult> {
  return await cAxios.request({
    url: '/value/update',
    method: 'post',
    data: payload,
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function deleteMetaValueUsingPOST(id: number): Promise<CommonResult> {
  return await cAxios.request({
    url: '/value/delete',
    method: 'post',
    data: { id },
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function getMetaInfoByMaterialIdUsingGET(id: number): Promise<CommonResult> {
  return await cAxios.request({
    url: '/list/material_id',
    params: { id },
  })
}

export async function updateMetaInfoByMaterialIdUsingPOST(data: Module.Meta.UpdateMetaRelationByMaterialIdParams): Promise<CommonResult> {
  return await cAxios.request({
    url: '/update/material_id',
    data,
    method: 'post',
  })
}
