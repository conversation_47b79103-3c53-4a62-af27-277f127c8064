import React from 'react'
import { Image, Tag } from 'antd'
import { Button, Form, Upload, message } from 'antd'
import { uploadScriptFileUsingPost } from '@/services'
import {
  BusinessMap,
  businessOptions,
  EffectStatusOptions,
  MaterialType,
  MaterialTypeMap,
  MaterialTypeOptions,
  PlatformOptions,
} from '@/constants'
import { CommonModalType } from '@/stores/material-manage'
import { AdvFormItemProps } from '@/types'
import { formatTime } from '@/utils'
import { EditPopoverItem } from '@/components/pop-confirm-scene-edit'
import styles from './index.module.less'

const fallbackImage
  = 'https://cdnfile.corp.kuaishou.com/kc/files/a/kael-mui-demo/chrome-plugin-upload/2024-03-20/1710901567689.85f131258d385280.svg'
const requiredRules = [{ required: true, message: '请输入内容' }]
export const normFile = (e: AnyType) => {
  if (Array.isArray(e)) {
    return e
  }
  return e && e.fileList
}

const genSceneOrTagConfig = (commonModalType: CommonModalType) => [
  {
    label: `${commonModalType === 'scene' ? '场景' : '标签'}标题`,
    name: 'title',
    renderType: 'input',
    extraOptions: {
      placeholder: `请输入${commonModalType === 'scene' ? '场景' : '标签'}标题`,
    },
    rules: [
      { required: true, message: '请输入内容' },
      { max: 20, message: '不能超过20字符' },
      { whitespace: true },
    ],
  },
  {
    label: `${commonModalType === 'scene' ? '场景' : '标签'}值`,
    name: 'value',
    renderType: 'input',
    extraOptions: {
      placeholder: `请输入${commonModalType === 'scene' ? '场景' : '标签'}值`,
    },
    rules: [
      { required: true, message: '请输入内容' },
      { max: 20, message: '不能超过20字符' },
      { whitespace: true },
    ],
  },
  {
    label: '展示顺序',
    name: 'order',
    renderType: 'inputNumber',
    extraOptions: {
      placeholder: '请输入展示顺序',
      step: 1,
      min: 0,
      max: 100,
    },
  },
]

export const tagEditConfig = genSceneOrTagConfig('tag') as AdvFormItemProps[]

export const searchFormItemsConfig = ({ tagOptions = [] }) => [
  {
    label: '物料名称',
    name: 'keyword',
    renderType: 'input',
  },
  {
    label: '物料类型',
    name: 'type',
    renderType: 'select',
    extraOptions: {
      options: MaterialTypeOptions,
      allowClear: true,
    },
  },
  {
    label: '运行端',
    name: 'platform',
    renderType: 'select',
    extraOptions: {
      options: PlatformOptions,
      showSearch: false,
      placeholder: '选择运行端',
      allowClear: true,
    },
  },
  {
    label: '分类',
    name: 'tagId',
    renderType: 'select',
    extraOptions: {
      options: tagOptions,
      placeholder: '选择分类',
      showSearch: false,
      allowClear: true,
    },
  },
  {
    label: '物料生产方',
    name: 'business',
    renderType: 'select',
    extraOptions: {
      options: businessOptions,
      allowClear: true,
    },
  },
  {
    label: '生效状态',
    name: 'status',
    renderType: 'select',
    extraOptions: {
      options: EffectStatusOptions,
      allowClear: true,
    },
  },
]

export const defaultColumns = (config: {
  tagAddClick: (record) => void
  tagOptions
  submitMaterialCategoryById: (values) => void
}) => {
  return [
    {
      title: '物料id',
      dataIndex: 'id',
      key: 'id',
      width: 200,
    },
    {
      title: '预览图',
      dataIndex: 'preview_img',
      key: 'preview_img',
      width: 100,
      render: (val) => {
        return <Image width={100} src={val} fallback={fallbackImage} />
      },
    },
    {
      title: '物料名称',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (val, record) => {
        let isVirtual = false
        try {
          isVirtual = record.meta ? JSON.parse(record.meta)?.virtual : false
        }
        catch {
          return null
        }
        return (
          <span>
            {val}
            {isVirtual
              ? (
                <span className={styles['virtual_text']}>虚拟</span>
              )
              : null}
          </span>
        )
      },
    },
    {
      title: 'NameSpace',
      dataIndex: 'namespace',
      key: 'namespace',
      width: 240,
    },
    {
      title: '当前版本',
      dataIndex: 'version',
      key: 'version',
      width: 140,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render(value) {
        return MaterialTypeMap[value] ?? '-'
      },
    },
    {
      title: '物料生产方',
      dataIndex: 'business',
      key: 'business',
      width: 200,
      render: value => BusinessMap[value],
    },
    {
      title: '分类',
      dataIndex: 'tags',
      key: 'tags',
      render: (_, record) => {
        return (
          <div>
            {(record.tags || []).map(tag => (
              <Tag color="#174ae6" key={tag.id}>
                {tag.title}
              </Tag>
            ))}
            <EditPopoverItem
              type="tag"
              name="tags"
              title="编辑物料分类"
              selectProps={{ mode: 'multiple' }}
              onAddClick={() => config.tagAddClick(record)}
              record={record}
              defaultValue={(record.tags || []).map(item => item.id)}
              options={config.tagOptions || []}
              onSubmit={values =>
                config.submitMaterialCategoryById?.({
                  id: record.id,
                  tags: values.tags,
                })}
            />
          </div>
        )
      },
      width: 200,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 250,
    },
    {
      title: '创建者',
      dataIndex: 'creator_name',
      key: 'creator_name',
      render: creatorName => creatorName ?? '-',
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: val => formatTime(val),
    },
  ]
}

export const getMaterialFormList = (
  versionList = [],
  business: string,
  material_type: string,
) => [
  {
    label: '物料名称',
    name: 'title',
    renderType: 'input',
    extraOptions: {
      placeholder: '请输入物料名称',
    },
    rules: [
      { required: true, message: '请输入内容' },
      { max: 100, message: '不能超过 100 字符' },
      { whitespace: true },
    ],
  },
  {
    label: '适用端',
    name: 'platform',
    renderType: 'select',
    extraOptions: {
      mode: 'multiple',
      placeholder: '请选择类型',
      options: PlatformOptions,
    },
    rules: requiredRules,
  },
  {
    label: '版本',
    name: 'version',
    renderType: 'select',
    extraOptions: {
      placeholder: '请选择版本',
      options: versionList.map(v => ({ label: v.version, value: v.version })),
    },
    rules: requiredRules,
  },
  {
    label: 'Git地址',
    name: 'git_url',
    renderType: 'input',
    extraOptions: {
      placeholder: '请输入仓库地址',
    },
  },
  {
    label: '描述信息',
    name: 'description',
    renderType: 'input',
    extraOptions: {
      placeholder: '请输入描述信息',
    },
  },
  ...(material_type === MaterialType.COMPONENT
    ? [
      {
        label: '文档地址',
        name: 'readme',
        renderType: 'input',
        extraOptions: {
          placeholder: '请输入readme文档地址',
        },
      },
    ]
    : []),
  {
    label: '预览图',
    name: 'preview_img',
    renderType: 'custom',
    extraOptions: {
      placeholder: '输入预览图CDN地址',
    },
    rules: [
      { required: true, message: '请输入预览图地址' },
      { whitespace: true },
    ],
    children: form => (
      <>
        <Form.Item name={['preview_img']} noStyle></Form.Item>
        <Form.Item
          name={['upload']}
          label="预览图"
          valuePropName="fileList"
          getValueFromEvent={normFile}
        >
          <Upload
            name="logo"
            listType="picture"
            multiple={false}
            maxCount={1}
            showUploadList={{ showRemoveIcon: false }}
            withCredentials={true}
            accept=".svg,.gif,.png,.jpeg,.jpg"
            beforeUpload={() => {
              return false
            }}
            onChange={(info: AnyType) => {
              uploadScriptFileUsingPost({
                file: info.file,
                path: `/script/${business}`,
              })
                .then((res: AnyType) => {
                  if (res.data.url) {
                    form.setFieldsValue({
                      preview_img: res.data.url,
                    })
                    message.success('更新预览图片地址')
                  }
                })
                .catch((err) => {
                  console.log('err', err)
                  message.error(`${info.file.name}上传失败`)
                })
            }}
          >
            <Button>上传</Button>
          </Upload>
        </Form.Item>
      </>
    ),
  },
]
