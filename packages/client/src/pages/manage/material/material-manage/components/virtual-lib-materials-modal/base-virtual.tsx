import React, { FC, useCallback, useEffect, useState } from 'react'
import { Button, message, Row, Table, Tree } from 'antd'
import { observer } from 'mobx-react-lite'
import { cloneDeep } from 'lodash-es'
import { userModel } from '@/stores'
import { publishVirtualMaterialUsingPOST, queryVirtualMaterialDetailUsingGET } from '@/services'
import { Material } from '@/types'
import { materialManageModel } from '@/stores/material-manage'
import TreeNodeTitle, { TreeNodeType } from './tree-title'
import { defaultMaterialColumns, formatCompTreeData, getComponentNamespacesByComLib } from './config'

import styles from './index.module.less'

interface BaseVirtualLibEditProps {
  material: Material & { children?: Material[] }
  onPublish: () => void
}

const CategoryPrefix = 'CATEGORY'
const checkIsCategory = (key) => {
  return !!key.includes(CategoryPrefix)
}

const checkCanNest = (from<PERSON>ey, toKey) => {
  const isFromCategory = checkIsCategory(fromKey)
  const isToCategory = checkIsCategory(toKey)
  return !isFromCategory && isToCategory
}
export type BaseType = { namespace: string, version: string, key: string }
export type CategoryType = { title: string, children: BaseType [], key: string }
export type ComponentType = BaseType | CategoryType

const BaseVirtualLibEdit: FC<BaseVirtualLibEditProps> = (props) => {
  const { material, onPublish } = props

  const {
    materialSelectTableLoading,
    materialSelectTableData,
    materialSelectTableParams,
    updateMaterialSelectTableParams,
    getMaterialSelectTableData,
    getMaterialsVersions,
  } = materialManageModel
  const [componentTree, setComponentTree] = useState([])
  const [materialVersionsMap, setMaterialVersionsMap] = useState(undefined)
  const [libNamespaces, setLibNamespaces] = useState([])
  const [expandedKeys, setExpandedKeys] = useState([])
  const [gData, setGData] = useState([])
  const [tempFirst, setTempFirst] = useState(true)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [tempSelectedComps, setTempSelectComps] = useState([])
  const [materialInfo, setMaterialInfo] = useState<Record<string, AnyType> | null>(null)

  useEffect(() => {
    if (material) {
      getMaterialSelectTableData(material.business)
    }

    return () => {
      setMaterialInfo(undefined)
      setComponentTree([])
    }
  }, [material])

  const handleDelete = useCallback((options: { key: string }) => {
    const { key } = options
    const data = cloneDeep(componentTree)
    loop(data, key, (_, index) => {
      data.splice(index, 1)
    })
    setComponentTree(data)
  }, [componentTree])

  const handleUpdateTitle = useCallback((options: { key: string, value: string }) => {
    const { key, value } = options
    const data = cloneDeep(componentTree)
    loop(data, key, (item) => {
      item.title = value
    })
    setComponentTree(data)
  }, [componentTree])

  const handleUpdateVersion = useCallback((options: { key: string, value: string }) => {
    const { key, value } = options
    const data = cloneDeep(componentTree)
    loop(data, key, (item) => {
      item.version = value
    })
    setComponentTree(data)
  }, [componentTree])

  const generateTreeData = (data: Array<ComponentType>, path: string[]) => {
    return data.map((item) => {
      if ((item as CategoryType).title && !(item as BaseType)?.namespace) {
        return {
          key: item.key,
          title: (
            <TreeNodeTitle
              info={item as AnyType}
              mapMaterialIdToVersions={materialVersionsMap}
              type={TreeNodeType.CATEGORY}
              delete={handleDelete}
              updateTitle={handleUpdateTitle}
              updateVersion={handleUpdateVersion}
            />
          ),
          children: (item as CategoryType).children?.length
            ? generateTreeData((item as CategoryType).children, [...path, 'children'])
            : undefined,
        }
      }
      else {
        return {
          key: item.key,
          title: (
            <TreeNodeTitle
              info={item}
              type={TreeNodeType.COMPONENT}
              mapMaterialIdToVersions={materialVersionsMap}
              delete={handleDelete}
              updateTitle={handleUpdateTitle}
              updateVersion={handleUpdateVersion}
            />
          ),
        }
      }
    })
  }

  useEffect(() => {
    if (componentTree) {
      const data = generateTreeData(componentTree, [])
      const allLibNamespaces = getComponentNamespacesByComLib(componentTree)
      setLibNamespaces(allLibNamespaces)
      if (tempFirst) {
        setTempFirst(false)
      }
      setGData(data)
      const keys = componentTree.map(item => item.key)
      setExpandedKeys(keys)
    }
  }, [componentTree, materialVersionsMap])

  useEffect(() => {
    if (material?.id) {
      const materialId = Number(material.id)

      queryVirtualMaterialDetailUsingGET({ id: materialId, version: material.version }).then(async (materialDetailRes) => {
        setMaterialInfo(materialDetailRes.data)
        const materialInfo = materialDetailRes.data
        const firstMaterialIds = []
        if (materialInfo?.components) {
          materialInfo.components.forEach((element) => {
            if (!element.namespace && element.title) {
              element.key = CategoryPrefix + element.title
              element.children = element.children.map((item) => {
                firstMaterialIds.push(item.id)
                return { ...item, key: item.namespace }
              })
            }
            else {
              element.key = element.namespace
              firstMaterialIds.push(element.id)
            }
          })
          const materialVersionsMap = await getMaterialsVersions(firstMaterialIds)
          setSelectedRowKeys(firstMaterialIds)
          setMaterialVersionsMap(materialVersionsMap)
          setComponentTree(materialInfo.components)
        }
      })
    }
  }, [material])

  const handleAddCategory = () => {
    const title = '分类' + 1 + (componentTree.filter(item => item.title && !item.namespace)?.length || 0)
    const key = CategoryPrefix + title
    const categoryItem = { title, key, children: [] }
    setComponentTree(val => [...val, categoryItem])
  }

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: Record<string, AnyType>[]) => {
      setSelectedRowKeys(selectedRowKeys)
      setTempSelectComps(selectedRows.filter(Boolean).map(item => ({ namespace: item.namespace, title: item.title, key: item.namespace, version: item.version, id: item.id })))
    },
    getCheckboxProps: (record: Record<string, AnyType>) => ({
      disabled: libNamespaces.includes(record.namespace), // Column configuration not to be checked
      name: record.name,
    }),
    selectedRowKeys,
  }

  const handleTableChange = (pagination, filters) => {
    updateMaterialSelectTableParams({
      pagination,
      filters,
      business: material?.business,
    })
  }

  const handleAddSelectedComps = async () => {
    if (selectedRowKeys && tempSelectedComps) {
      const materialVersionsMap = await getMaterialsVersions(selectedRowKeys)
      setMaterialVersionsMap(materialVersionsMap)
      const originNamespaces = getComponentNamespacesByComLib(componentTree)
      const addedComp = tempSelectedComps.filter(item => !originNamespaces.includes(item.namespace))

      setComponentTree(val => [...val, ...addedComp])
    }
  }

  const loop = (
    data: Record<string, AnyType>[],
    key: React.Key,
    callback: (node: Record<string, AnyType>, i: number, data: Record<string, AnyType>[]) => void,
    customJudge?: (dataKey, key) => boolean,
  ) => {
    for (let i = 0; i < data.length; i++) {
      if (data[i].key === key || customJudge?.(data[i].key, key)) {
        return callback(data[i], i, data)
      }
      if (data[i].children) {
        loop(data[i].children!, key, callback, customJudge)
      }
    }
  }
  const onDrop = (info) => {
    const {
      dragNode: { key: fromId },
      node: { key: toId, pos },
      dropPosition,
    } = info
    const dropPos = pos.split('-')
    const calDropPosition = dropPosition - Number(dropPos[dropPos.length - 1])

    // 移动到下面作为子集
    if (calDropPosition === 0) {
      const canNest = checkCanNest(fromId, toId)
      if (!canNest) {
        return
      }
    }
    const data = [...componentTree]
    let dragObj: AnyType
    loop(data, fromId, (item, index, arr) => {
      arr.splice(index, 1)
      dragObj = item
    })

    loop(data, toId, (item, index, arr) => {
      if (calDropPosition === 0) {
        if (item.children) {
          item.children.unshift(dragObj)
        }
        else {
          item.children = [dragObj]
        }
      }
      else {
        const offset = (calDropPosition === 1 ? 1 : 0) + index
        arr.splice(offset, 0, dragObj)
      }
    }, (dataKey, key) => {
      if (key.includes(CategoryPrefix)) {
        return key.includes(dataKey)
      }
      return dataKey === key
    })

    setComponentTree(data)
  }

  const handlePublish = async () => {
    const formatted = formatCompTreeData(componentTree)
    const res = await publishVirtualMaterialUsingPOST({
      material_id: materialInfo.id,
      creator_id: userModel.user.id,
      components: formatted,
    })
    if (res?.code === 1) {
      message.success('发布成功')
      onPublish?.()
    }
    else {
      message.error(res?.message)
    }
  }

  const handleExpand = (expended) => {
    setExpandedKeys(expended)
  }

  return (
    <div>
      <Row>
        <Button onClick={handleAddCategory}>新增分类</Button>
        <Button style={{ marginLeft: '24px' }} type="primary" onClick={handlePublish}>发布</Button>
      </Row>
      <div className={styles['main-material-operate-area']}>
        <div className={styles['material-tree-body']}>
          <Tree
            className={styles['material-tree-self']}
            draggable
            showLine
            showIcon
            onExpand={handleExpand}
            expandedKeys={expandedKeys}
            defaultExpandAll
            autoExpandParent
            onDrop={onDrop}
            treeData={gData}
          />
        </div>
        <div className={styles['material-table-select']}>
          <Button type="primary" onClick={handleAddSelectedComps}> 添加至左侧</Button>
          <Table
            rowSelection={{
              type: 'checkbox',
              preserveSelectedRowKeys: true,
              ...rowSelection,
            }}
            pagination={materialSelectTableParams.pagination}
            size="small"
            rowKey="id"
            loading={materialSelectTableLoading}
            columns={defaultMaterialColumns}
            dataSource={materialSelectTableData}
            onChange={handleTableChange}
          />
        </div>
      </div>
    </div>
  )
}

export default observer(BaseVirtualLibEdit)
