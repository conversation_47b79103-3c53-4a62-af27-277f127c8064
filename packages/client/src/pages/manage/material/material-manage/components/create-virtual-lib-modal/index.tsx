import React, { FC, useCallback, useEffect, useState } from 'react'
import { Alert, Form, Modal, message } from 'antd'
import { observer } from 'mobx-react-lite'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { materialManageModel } from '@/stores/material-manage'
import CustomUpload from '../pub-material-modal/custom-upload'

interface CreateVirtualLibModalProps {
  visible: boolean
  onCancel(): void
  title?: string
}

const INIT_SCHEMA = {
  packageName: '@es/pc-common-lib',
  componentBundleType: 'SL',
  componentChineseName: 'PC 通用组件库',
  description: '这是一个PC 通用组件库',
  gitUrl: 'https://git/',
  assetDownloadType: 'cdn',
  assetDownloadUrl: 'https://cdn',
  author: 'xxx',
  thumbnailUrl: 'https://ali-ec.static.yximgs.com/udata/pkg/eshop/chrome-plugin-upload/2024-04-29/1714373381608.ebe50d93b4905123.svg',
  version: '0.0.1',
  publishTime: '2021-09-10 20:30:33',
  updateTime: '2021-10-10 20: 59: 59',
  tags: {
    platform: 'pc',
    business: 'es',
    category: 'dataDisplay',
    meta: {},
  },
  components: [{ title: '分类1', children: [] }],
}
const CreateVirtualLibModal: FC<CreateVirtualLibModalProps> = (props) => {
  const { visible, onCancel, title = '创建虚拟组件库' } = props
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const onOk = useCallback(() => {
    form.validateFields().then((values) => {
      setLoading(true)
      materialManageModel.createMaterialLibMaterial({ meta: { virtual: true }, ...values })
        .then(() => {
          message.success('创建虚拟组件库物料成功')
          materialManageModel.resetAndGetMaterialTableList()
          onCancel()
        })
        .catch((error) => {
          message.error(error.message)
        })
        .finally(() => setLoading(false))
    })
  }, [onCancel])
  const initSchema = useCallback(() => {
    if (form.getFieldValue('schema')) {
      Modal.confirm({
        title: '当前输入框存在修改，确认要替换默认协议吗？',
        icon: <ExclamationCircleOutlined />,
        okText: '确认',
        cancelText: '取消',
        onOk: () => form.setFieldsValue({ schema: JSON.stringify(INIT_SCHEMA, null, 2) }),
      })
    }
    else {
      form.setFieldsValue({ schema: JSON.stringify(INIT_SCHEMA, null, 2) })
    }
  }, [form])

  useEffect(() => {
    if (visible) {
      form.resetFields()
    }
  }, [visible])

  return (
    <Modal
      title={title}
      visible={visible}
      onCancel={onCancel}
      onOk={onOk}
      width={600}
      okButtonProps={{ loading }}
    >
      <Alert style={{ marginBottom: 16 }} message="支持上传 JSON 文件解析、填写 JSON 字符串" type="info" showIcon />
      <Form form={form}>
        <Form.Item
          name="schema"
          label="组件库标准协议"
          required
          extra={(
            <>
              物料标准协议，参考：https://components.corp.kuaishou.com/schema
              <a onClick={initSchema}>填充默认协议</a>
            </>
          )}
          rules={[{ required: true, message: '标准协议不能为空' }]}
        >
          <CustomUpload rows={6} />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default observer(CreateVirtualLibModal)
