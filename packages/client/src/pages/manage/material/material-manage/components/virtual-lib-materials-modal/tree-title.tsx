import React, { useCallback, useMemo } from 'react'
import classNames from 'clsx'
import { observer } from 'mobx-react-lite'
import { debounce } from 'lodash-es'
import { Button, Input, Select } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'

import styles from './index.module.less'

export interface TreeNodeTitleProps {
  /** 组件信息 */
  info: Record<string, AnyType>
  type: TreeNodeType
  mapMaterialIdToVersions: Record<string, AnyType>
  updateTitle?: (options: { key: string, value: string }) => void
  updateVersion?: (options: { key: string, value: string }) => void
  delete?: (options: { key: string }) => void
}

export enum TreeNodeType {
  COMPONENT = 'component',
  CATEGORY = 'category',
}
const TreeNodeTitle: React.FC<TreeNodeTitleProps> = observer((props) => {
  const { type, info, mapMaterialIdToVersions, updateVersion } = props

  const versionOptions = useMemo(() => {
    if (type === TreeNodeType.COMPONENT) {
      if (info.id) {
        return (mapMaterialIdToVersions[String(info.id)] || []).map(item => ({ label: item.version, value: item.version }))
      }
    }
    return []
  }, [type, info, mapMaterialIdToVersions])

  const handleDelete = useCallback(() => props.delete?.({ key: info.key }), [props.delete])

  const handleUpdate = debounce(e => props.updateTitle?.({ key: info.key, value: e.target.value }), 50)

  const handleBlur = e => props.updateTitle?.({ key: info.key, value: e.target.value })

  const selectEle = useMemo(() => {
    return type === TreeNodeType.CATEGORY
      ? null
      : (
        <Select
          size="small"
          defaultValue={info.version}
          onChange={value => updateVersion?.({ key: info.key, value })}
          options={versionOptions || []}
          style={{ width: '140px' }}
        />
      )
  }, [type, versionOptions, updateVersion])

  return (
    <div className={classNames(styles['material-tree-item'])} key={info.key}>
      <div className={styles['material-tree-item-title']}>
        {type === TreeNodeType.CATEGORY
          ? (
            <Input
              style={{ width: '140px' }}
              size="small"
              defaultValue={info.title}
              onChange={handleUpdate}
              onBlur={handleBlur}
            />
          )
          : null}
        {type === TreeNodeType.COMPONENT ? info.title : null}
      </div>
      <div className={styles['material-tree-item-action']}>
        {selectEle}
        <Button
          danger
          size="small"
          onClick={handleDelete}
          icon={<DeleteOutlined />}
          style={{ marginLeft: '16px' }}
        />
      </div>
    </div>
  )
})

export default TreeNodeTitle
