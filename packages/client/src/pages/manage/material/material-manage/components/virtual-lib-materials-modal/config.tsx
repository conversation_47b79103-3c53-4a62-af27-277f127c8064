import { Tooltip } from 'antd'
import React from 'react'
import { pick } from 'lodash-es'
import { businessOptions, BusinessMap } from '@/constants'
import { formatTime } from '@/utils'

import styles from './index.module.less'

export const defaultMaterialColumns = [
  {
    title: '物料名称',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: 'NameSpace',
    dataIndex: 'namespace',
    key: 'namespace',
    render: value => (
      <Tooltip title={value}>
        <span className={styles['ellipse-text']}>{value}</span>
      </Tooltip>
    ),
  },
  {
    title: '物料生产方',
    dataIndex: 'business',
    key: 'business',
    render: value => BusinessMap[value],
    filters: businessOptions.map(item => ({ text: item.label, value: item.value })),
    filterMultiple: false,
  },
  {
    title: '最近版本',
    dataIndex: 'version',
    key: 'version',
  },
  {
    title: '创建者',
    dataIndex: 'creator_name',
    key: 'creator_name',
    render: creatorName => creatorName ?? '-',
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
    render: val => formatTime(val),
  },
]

/** 获取所有组件的 namespace */
export const getComponentNamespacesByComLib = (data: Array<Record<string, AnyType>>) => {
  const namespaces = []

  data.forEach((item) => {
    if (item.children?.length) {
      namespaces.push(...getComponentNamespacesByComLib(item.children ?? []))
    }
    else {
      namespaces.push(item.namespace)
    }
  })

  return namespaces
}

export const formatCompTreeData = (data: Array<Record<string, AnyType>>) => {
  const result = []

  data.forEach((item) => {
    if (item.children) {
      const current = pick(item, ['title', 'children'])

      if (item.children?.length) {
        current.children = formatCompTreeData(current.children)
      }
      result.push(current)
    }
    else {
      result.push(pick(item, ['namespace', 'version']))
    }
  })

  return result
}
