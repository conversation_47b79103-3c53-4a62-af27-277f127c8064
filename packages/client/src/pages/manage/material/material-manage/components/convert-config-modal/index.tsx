import React, { FC, useEffect, useMemo } from 'react'
import { Alert, Form, message, Modal, Select, Switch } from 'antd'
import { observer } from 'mobx-react-lite'
import { AnyType, Material } from '@/types'
import { safeParse } from '@/utils'
import { businessOptions } from '@/constants'
import { closeAutoConvert, openAutoConvert } from '@/services'
import { scriptModel, userModel } from '@/stores'

interface ConvertConfigModalProps {
  material: Material
  onCancel(refresh?: boolean): void
}

const ConvertConfigModal: FC<ConvertConfigModalProps> = (props) => {
  const { material, onCancel } = props
  const { scriptList, init } = scriptModel

  const [form] = Form.useForm()
  const meta = useMemo(() => safeParse(material?.meta), [material])
  const curBusinessOptions = useMemo(() => {
    return businessOptions.map(item => ({ ...item, disabled: !scriptList.find(s => s.business === item.value && !!s.script) || item.value === material?.business }))
  }, [businessOptions, scriptList, material])

  useEffect(() => {
    form.setFieldsValue({
      convert: meta.autoConvert?.length,
      business: meta.autoConvert?.filter(bu => businessOptions.find(b => b.value === bu)),
    })
    init()
  }, [])

  const onOK = () => {
    form.validateFields()
      .then((values) => {
        const promise = values.convert
          ? openAutoConvert({ id: material?.id, business: values.business, updater_id: userModel.user?.id })
          : closeAutoConvert({ id: material?.id, updater_id: userModel.user?.id })

        promise
          .then((data: AnyType) => {
            if (data.code === 1) {
              message.success('编辑成功')
              onCancel(true)
            }
            else {
              message.error(data.message || '编辑失败')
            }
          })
          .catch(error => message.error(error.message || error))
      })
      .catch(_ => _)
  }

  return material
    ? (
      <Modal visible title="物料转换配置" onCancel={onCancel as (() => void)} maskClosable onOk={onOK}>
        <Alert showIcon type="info" message="组件库类型物料开启自动转换，其所含组件物料将跟随组件库发版自动转换" style={{ marginBottom: 16 }} />
        <Form form={form}>
          <Form.Item label="自动转换" valuePropName="checked" name="convert">
            <Switch />
          </Form.Item>
          <Form.Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.convert !== curValues.convert}>
            {({ getFieldValue }) => {
              const convert = getFieldValue('convert')

              return convert
                ? (
                  <Form.Item label="转换到" name="business">
                    <Select options={curBusinessOptions} mode="multiple" />
                  </Form.Item>
                )
                : null
            }}
          </Form.Item>
        </Form>
      </Modal>
    )
    : null
}

export default observer(ConvertConfigModal)
