import { Button, Input, message, Upload } from 'antd'
import React, { FC, useCallback } from 'react'
import { UploadOutlined } from '@ant-design/icons'

interface CustomUploadProps {
  value?: AnyType
  rows?: number
  onChange?: (value: AnyType) => void
}

const CustomUpload: FC<CustomUploadProps> = (props) => {
  const { value, rows = 4, onChange } = props

  const onChangeInput = useCallback((e) => {
    try {
      onChange(JSON.parse(e.target.value))
    }
    catch {
      onChange(e.target.value)
    }
  }, [onChange])

  return (
    <>
      <Upload
        listType="text"
        multiple={false}
        maxCount={1}
        accept=".json"
        fileList={[]}
        beforeUpload={(file) => {
          const reader = new FileReader()
          reader.readAsText(file)
          reader.onload = () => {
            try {
              onChange(JSON.parse(reader.result as string))
            }
            catch (_) {
              message.error('文件解析失败，请检查文件内容是否为合法 JSON')
            }
          }

          return false
        }}
      >
        <Button>
          <UploadOutlined />
&nbsp;上传
        </Button>
      </Upload>

      <Input.TextArea
        style={{ marginTop: 10 }}
        rows={rows}
        value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value}
        onChange={onChangeInput}
      />
    </>
  )
}

export default CustomUpload
