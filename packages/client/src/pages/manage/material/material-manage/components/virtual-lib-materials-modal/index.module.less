.main-material-operate-area {
  margin-top: 24px;
  display: flex;
  height: 696px;
}

.material-tree-body {
  flex: 0 0 400px;
  border-right: 1px solid #E6E9ED;;
  overflow-y: scroll;
}

.material-table-select {
  flex: 1 0 500px;
  margin-left: 16px;
  overflow-y: scroll;
}

.material-tree-self {
  :global {
    .ant-tree-title {
      font-size: 14px;
      color: #434343;
      flex: 1;
      line-height: 20px;
      display: block;
      overflow: hidden;
    }

    .ant-tree-treenode {
      width: 100%;
      padding: 4px 0 !important;

      &:hover {
        background-color: #f5f7f9;
      }

      .ant-tree-show-line .ant-tree-switcher {
        background-color: transparent !important;
      }
    }

    .ant-tree-list-holder-inner:first-child {
      .ant-tree-switcher {
        margin-left: 16px !important;
      }
    }

    .ant-tree-treenode.ant-tree-treenode-selected {
      background-color: #e6f6ff !important;

      &:hover {
        background-color: #e6f6ff !important;
      }
    }

    .ant-tree-treenode-motion {
      width: 100%;
    }

    .ant-tree-switcher {
      width: 12px;
      background: transparent !important;
      align-self: center;

      .ant-tree-switcher-icon {
        vertical-align: middle;
      }
    }

    .ant-tree-indent-unit {
      width: 12px;

      &::before {
        right: 6px !important;
        top: -4px !important;
        z-index: 1;
        transform: translateY(-1px);
        border-right: 1px solid transparent !important;
      }

      &::after {
        border-bottom: 1px solid transparent !important;
      }
    }

    .ant-tree-switcher-leaf-line {
      &::after,
      &::before {
        content: unset;
        background-color: transparent;
      }
    }

    .ant-tree-node-content-wrapper {
      white-space: nowrap;
      line-height: 20px !important;
      min-height: 20px !important;
      flex: 1;
      width: 0;
      margin-right: 8px;
      padding: 0 8px;
      display: inline-flex;
      background-color: transparent !important;

      &:hover {
        background-color: transparent !important;
      }

      .ant-tree-icon__customize {
        vertical-align: middle;
      }
    }

    .ant-tree-node-selected {
      border-radius: 4px;

      .ant-tree-title {
        color: #0075ff;
      }
    }

    .ant-tree-draggable-icon {
      display: none;
    }
  }
}

.ellipse-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  width: 120px;
}

.material-tree-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  border-radius: 6px;
  padding: 6px 6px;
  border: 1px solid #f0f0f0d2;
  height: 100%;

  .material-tree-item-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .material-tree-item-title-input {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;

    .material-tree-item-title-input {
      width: 100%;
      z-index: 1000;
      line-height: 24px;
    }
  }

  .material-tree-item-operate {
    text-align: right;
    min-width: 26px;

    .ant-tree-drop-indicator {
      z-index: 100;
    }
  }
}
