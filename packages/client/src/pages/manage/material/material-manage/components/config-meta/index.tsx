import React, { FC, useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { Form, message, Modal, Select } from 'antd'
import { Material } from '@/types'
import { getMetaInfoByMaterialIdUsingGET, updateMetaInfoByMaterialIdUsingPOST } from '@/services'
import { userModel } from '@/stores'

interface ConfigMetaProps {
  material: Material
  visible: boolean
  onClose: () => void
}

type MetaItem = Module.Meta.Meta & { items: Module.Meta.MetaValue[] }
const ConfigMeta: FC<ConfigMetaProps> = (props) => {
  const { material, visible, onClose } = props
  const [metas, setMetas] = useState<MetaItem[]>([])
  const [loading, setLoading] = useState(false)
  const [metaValue, setMetaValue] = useState<Record<string, number[]>>({})
  const [form] = Form.useForm()

  const onOk = () => {
    form.validateFields().then((value) => {
      setLoading(true)
      updateMetaInfoByMaterialIdUsingPOST({ value, id: material.id, creator_id: userModel.user?.id })
        .then((res) => {
          if (res.code === 1) {
            message.success('扩展元信息保存成功')
            form.resetFields()
            onClose()
          }
          else {
            throw Error(res.message)
          }
        })
        .catch(err => message.error(err.message))
        .finally(() => setLoading(false))
    })
  }

  useEffect(() => {
    if (visible) {
      getMetaInfoByMaterialIdUsingGET(material.id)
        .then((res) => {
          if (res.code === 1) {
            setMetas(res.data.metas)
            setMetaValue(res.data.value)
          }
          else {
            throw Error(res.message)
          }
        })
        .catch(err => message.error(err.message))
    }
  }, [visible])

  useEffect(() => {
    if (visible && Object.keys(metaValue ?? {}).length) {
      form.setFieldsValue(metaValue)
    }
  }, [visible, metaValue])

  return (
    <Modal title="扩展元信息" visible={visible} onCancel={onClose} width={600} onOk={onOk} okButtonProps={{ loading }}>
      <Form form={form} labelCol={{ span: 5 }}>
        {metas.map((meta) => {
          return (
            <Form.Item label={meta.title} name={meta.value} key={meta.id}>
              <Select
                allowClear
                mode="multiple"
                placeholder={`请选择${meta.title}`}
                options={meta.items.map(v => ({ label: v.title, value: v.id }))}
              >
              </Select>
            </Form.Item>
          )
        })}
      </Form>
    </Modal>
  )
}

export default observer(ConfigMeta)
