import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Alert, Button, Form, message, Modal, ModalProps, Select, Switch } from 'antd'
import { observer } from 'mobx-react-lite'
import { FormInstance } from 'antd/es/form/Form'
import { scriptModel, userModel } from '@/stores'
import { Business, businessOptions } from '@/constants'
import { Material } from '@/types'
import {
  publishConvertBundleUsingPOST,
  queryMaterialConvertDetailUsingGET,
  queryVersionListByMaterialInfoIdUsingGET,
  triggerConvertMaterialUsingPOST,
} from '@/services'
import { getFangZhouPreviewUrlByBundle, safeParse } from '@/utils'

export interface TransferScriptModalProps {
  modalProps?: ModalProps
  visible?: boolean
  title?: string
  id?: number
  material: Material
  initValues?: Record<string, AnyType>
  onClose?: (form: FormInstance) => void
}

enum ConvertStatus {
  INIT = 'INIT',
  CONVERTING = 'CONVERTING',
  WAIT_PUBLISH = 'WAIT_PUBLISH',
  END = 'END',
}
const TransferScriptModal: FC<TransferScriptModalProps> = (props) => {
  const { material, visible, onClose, title, modalProps = {}, initValues = undefined } = props
  const [form] = Form.useForm()
  const { scriptList, scriptModalLoading, setScriptModalLoading, queryTransferStatus } = scriptModel
  const [versionOptions, setVersionOptions] = useState([])
  const [convertStatus, setConvertStatus] = useState(ConvertStatus.INIT)
  const [transfer_id, setTransferId] = useState(undefined)
  const nextBusiness = useRef('')
  const hiddenTransformFormItem = useMemo(() => material?.business === Business.FANG_ZHOU && !safeParse(material?.meta).isCloudComponent, [material])
  let timer = null
  useEffect(() => {
    return () => {
      form.resetFields()
    }
  }, [])
  const initTimer = useCallback((transfer_id, autoPublish) => {
    timer = setInterval(async () => {
      const val = await queryTransferStatus({ transfer_id })
      if (val === true) {
        clearInterval(timer)
        if (autoPublish) {
          message.success('转换成功，正在发布中...')
          handlePublish(transfer_id)
        }
        else {
          setConvertStatus(ConvertStatus.WAIT_PUBLISH)
          setScriptModalLoading(false)
          message.success('转换成功，请确认发布...')
        }
      }
      else if (val === false) {
        setScriptModalLoading(false)
        setConvertStatus(ConvertStatus.END)
        clearInterval(timer)
        throw Error('转换失败，请重试')
      }
    }, 1000)
  }, [])

  const queryTransferDetail = async (transfer_id) => {
    try {
      const result = await queryMaterialConvertDetailUsingGET({ id: transfer_id })
      if (result?.bundle) {
        if (nextBusiness.current === Business.FANG_ZHOU) {
          window.open(getFangZhouPreviewUrlByBundle(result.bundle), '_blank')
        }
        else if (nextBusiness.current === Business.KAEL) {
          window.open(getFangZhouPreviewUrlByBundle(result.bundle), '_blank')
        }
        else {
          window.open(result.bundle, '_blank')
        }
      }
    }
    catch (_) {}
  }

  useEffect(() => {
    if (visible && material?.version) {
      form.setFieldsValue({ version: material.version })
    }
    setTransferId(undefined)
    setConvertStatus(undefined)
  }, [material, visible])

  useEffect(() => {
    try {
      if (visible) {
        queryVersionListByMaterialInfoIdUsingGET({ id: material?.id }).then((res) => {
          const options = (res || []).map(item => ({ label: item.version, value: item.version }))
          setVersionOptions(options)
        })
      }
    }
    catch (_) {}
  }, [material?.id, visible])

  const handleCancel = () => {
    form.resetFields()
    onClose?.(form)
    nextBusiness.current = ''
    setConvertStatus(ConvertStatus.INIT)
  }

  useEffect(() => {
    if (initValues && visible) {
      form.setFieldsValue(initValues)
    }
  }, [initValues, visible])

  useEffect(() => {
    if (visible) {
      scriptModel.init()
    }

    return () => scriptModel.reset()
  }, [initValues, visible])

  const handleOk = () => {
    form.validateFields().then((res) => {
      setScriptModalLoading(true)
      setConvertStatus(ConvertStatus.CONVERTING)
      nextBusiness.current = res.business
      triggerConvertMaterialUsingPOST({
        param: {
          version: res.version,
          business: res.business,
          creator_id: userModel.user?.id,
          material_id: material.id,
        },
      })
        .then((result: AnyType) => {
          if (result?.code === -1) {
            message.error(result.message)
            setScriptModalLoading(false)
            return
          }
          setTransferId(result.data.id)
          initTimer(result.data.id, res.publish)
        })
        .catch(() => setScriptModalLoading(false))
        .finally(() => {
          setConvertStatus(ConvertStatus.END)
        })
    }).catch(() => {})
  }

  const handlePublish = (curTransferId) => {
    setScriptModalLoading(true)
    publishConvertBundleUsingPOST({ id: typeof curTransferId === 'number' ? curTransferId : transfer_id })
      .then((res: AnyType) => {
        if (res.data) {
          message.success(res.message)
          handleCancel()
        }
        else {
          if (res.code === -1) {
            message.error(res.message)
          }
        }
      })
      .finally(() => {
        setConvertStatus(ConvertStatus.END)
        setScriptModalLoading(false)
      })
  }
  const curBusinessOptions = useMemo(() => {
    return businessOptions.map(item => ({ ...item, disabled: !scriptList.find(s => s.business === item.value && !!s.script) }))
  }, [businessOptions, scriptList])

  return !material
    ? null
    : (
      <Modal
        title={title}
        visible={visible}
        onCancel={handleCancel}
        footer={hiddenTransformFormItem
          ? null
          : ([
            convertStatus === ConvertStatus.WAIT_PUBLISH
              ? (
                <Button key="submit" onClick={() => queryTransferDetail(transfer_id)}>
                  预览产物
                </Button>
              )
              : null,
            convertStatus === ConvertStatus.WAIT_PUBLISH
              ? (
                <Button key="submit" type="primary" loading={scriptModalLoading} onClick={handlePublish}>
                  发布
                </Button>
              )
              : (
                <Button key="submit" type="primary" loading={scriptModalLoading} onClick={handleOk}>
                  转换
                </Button>
              ),
            <Button
              key="cancel"
              disabled={scriptModalLoading}
              onClick={handleCancel}
            >
              取消
            </Button>,
          ].filter(Boolean))}
        {...modalProps}
      >
        <Form form={form} size="middle" labelCol={{ span: 5 }}>
          <Form.Item label="选择版本" name="version">
            <Select defaultValue={material.version} disabled={hiddenTransformFormItem} options={versionOptions} />
          </Form.Item>
          {
            hiddenTransformFormItem
              ? (
                <Alert
                  style={{ marginBottom: '16px' }}
                  message={(
                    <>
                      方舟原子物料无法直接转换使用，请参考
                      <a
                        href="https://docs.corp.kuaishou.com/d/home/<USER>"
                        target="_blank"
                        rel="noreferrer"
                        style={{ marginLeft: '8px' }}
                      >
                        文档
                      </a>
                    </>
                  )}
                  type="info"
                />
              )
              : (
                <>
                  <Form.Item label="目标使用方" name="business">
                    <Select options={curBusinessOptions} />
                  </Form.Item>
                  <Form.Item label="自动发布" name="publish" extra="转换后自动发布，请谨慎选择" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </>
              )
          }
        </Form>
        {
          convertStatus === ConvertStatus.WAIT_PUBLISH
            ? (
              <Alert showIcon type="success" message="转换完成，可预览产物或直接发布转换结果" />
            )
            : null
        }
      </Modal>
    )
}

export default observer(TransferScriptModal)
