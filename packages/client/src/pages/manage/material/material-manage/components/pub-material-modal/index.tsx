import React, { FC, useCallback, useEffect, useState } from 'react'
import { Alert, Form, Modal, message } from 'antd'
import { observer } from 'mobx-react-lite'
import CustomUpload from './custom-upload'
import { materialManageModel } from '@/stores/material-manage'

interface PubMaterialModalProps {
  visible: boolean
  onCancel(): void
}

const PubMaterialModal: FC<PubMaterialModalProps> = (props) => {
  const { visible, onCancel } = props
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const onOk = useCallback(() => {
    form.validateFields().then((values) => {
      setLoading(true)
      materialManageModel.createMaterial(values)
        .then(() => {
          message.success('发布物料成功')
          materialManageModel.resetAndGetMaterialTableList()
          onCancel()
        })
        .catch((error) => {
          message.error(error.message)
        })
        .finally(() => setLoading(false))
    })
  }, [onCancel])

  useEffect(() => {
    if (visible) {
      form.resetFields()
    }
  }, [visible])

  return (
    <Modal title="发布物料" visible={visible} onCancel={onCancel} onOk={onOk} width={600} okButtonProps={{ loading }}>
      <Alert style={{ marginBottom: 16 }} message="支持上传 JSON 文件解析、填写 JSON 字符串" type="info" showIcon />
      <Form form={form}>
        <Form.Item
          name="schema"
          label="标准协议"
          required
          extra="物料标准协议，参考：https://components.corp.kuaishou.com/schema"
          rules={[{ required: true, message: '标准协议不能为空' }]}
        >
          <CustomUpload />
        </Form.Item>
        <Form.Item name="content" label="发布内容" extra="用于存储各 BU 自定义的发布内容(可不传)">
          <CustomUpload />
        </Form.Item>
        <Form.Item name="meta" label="附加信息" extra="用于扩展物料附加信息(可不传)">
          <CustomUpload />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default observer(PubMaterialModal)
