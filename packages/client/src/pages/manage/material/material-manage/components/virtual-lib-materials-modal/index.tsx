import React, { FC, useEffect, useState } from 'react'
import { Modal } from 'antd'
import { observer } from 'mobx-react-lite'
import { Material } from '@/types'
import BaseVirtualLibEdit from './base-virtual'

interface VirtualLibMaterialsModalProps {
  visible: boolean
  material: Material | null
  onCancel(val): void
  title?: string
}

const VirtualLibMaterialsModal: FC<VirtualLibMaterialsModalProps> = (props) => {
  const { visible, onCancel, title = '编辑虚拟组件库组件信息', material } = props
  const [published, setPublished] = useState(false)

  useEffect(() => {
    if (visible) {
      setPublished(false)
    }
  }, [visible])

  return (
    <Modal
      title={title}
      visible={visible}
      onCancel={() => onCancel(published)}
      width={1200}
      bodyStyle={{ height: '800px' }}
      footer={null}
      destroyOnClose
    >
      <BaseVirtualLibEdit material={material} onPublish={() => setPublished(true)} />
    </Modal>
  )
}

export default observer(VirtualLibMaterialsModal)
