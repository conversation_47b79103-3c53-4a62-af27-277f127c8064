import React, { FC, useEffect, useState } from 'react'
import { Button, message, Popconfirm, Table } from 'antd'
import { observer } from 'mobx-react-lite'
import { EffectStatus, EffectStatusMap } from '@/constants'
import { queryVersionListByMaterialInfoIdUsingGET } from '@/services'
import { materialManageModel } from '@/stores/material-manage'
import { formatTime } from '@/utils'

export interface VersionManageProps {
  material_id: number | string
}
const VersionManage: FC<VersionManageProps> = ({ material_id }) => {
  const { materialSceneLoading, updateMaterialVersionStatus } = materialManageModel

  const [versionList, setVersionList] = useState([])

  const handleDiscard = (id, status) => {
    if (versionList.length === 1 && (status === EffectStatus.DISABLED || status === EffectStatus.DELETE)) {
      message.warning('唯一版本不支持废弃/删除')
      return
    }
    updateMaterialVersionStatus({ id, status }, submitCallback)
  }

  const submitCallback = (flag: boolean) => {
    if (flag === true) {
      queryVersionListByMaterialInfoIdUsingGET({ id: material_id as number }).then((res) => {
        setVersionList(res)
      })
    }
  }

  const handleEnable = (id, status) => {
    updateMaterialVersionStatus({ id, status }, submitCallback)
  }

  const columns = [
    {
      title: '物料id',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '版本号',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '创建者',
      dataIndex: 'creator_name',
      key: 'creator_name',
    },
    {
      title: '发布时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: val => formatTime(val),
    },
    {
      title: '生效状态',
      dataIndex: 'status',
      key: 'status',
      render: val => EffectStatusMap[val],
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      render: (_, record) => {
        return record.status === EffectStatus.EFFECT
          ? (
            <Popconfirm title="确定废弃吗?" onConfirm={() => handleDiscard(record.id, EffectStatus.DISABLED)}>
              <Button type="link" danger>废弃</Button>
            </Popconfirm>
          )
          : (
            <Popconfirm title="确定启用吗?" onConfirm={() => handleEnable(record.id, EffectStatus.EFFECT)}>
              <Button type="link">启用</Button>
            </Popconfirm>
          )
      },
    },
  ]

  useEffect(() => {
    if (material_id) {
      queryVersionListByMaterialInfoIdUsingGET({ id: material_id as number }).then((res) => {
        setVersionList(res)
      })
    }
  }, [material_id])

  return (

    <Table
      columns={columns}
      rowKey={record => record.id}
      dataSource={versionList}
      loading={materialSceneLoading}
    />
  )
}

export default observer(VersionManage)
