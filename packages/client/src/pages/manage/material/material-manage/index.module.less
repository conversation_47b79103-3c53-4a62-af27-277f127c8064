.materialManagerMain {
  height: 100%;

  .materialManagerSearchArea {
    background-color: var(--main-background-color);
  }
  .common {
    background-color: var(--main-background-color);
    padding: 24px;
    border-radius: 5px;
  }

  .materialManagerContainer {
    margin-top: 16px;
  }
  :global {
    .ant-form-item {
      margin-bottom: 10px;
    }
    .ant-form-item-label {
      white-space: break-spaces;
    }
  }

}

.selectAddActionArea {
  height: 32px;
  cursor: pointer;
  padding: 5px 12px;
  font-weight: 400;
  color: #1d59f2;
}

.popConfirmContent {
  width: 280px;
  height: 50px;
  .popActionBottom {
    margin-top: 16px;
    padding: 10px 12px 10px 12px;
    border-top: 1px solid var(--border-color);
    float: right;
    width: 100%;
    height: 48px;
  }
}

.operationBox {
  & > a {
    margin-right: 16px;
  }

  & > a:last-of-type {
    margin-right: 0;
  }
}

.danger {
  color: #ff4d4f!important;
}

.virtual_text {
  background-color: #174ae6;
  padding: 2px 4px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
 }
