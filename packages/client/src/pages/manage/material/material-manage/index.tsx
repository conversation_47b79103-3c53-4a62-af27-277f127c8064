import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { But<PERSON>, Drawer, Form, Popconfirm, Space, Typography } from 'antd'
import { omit } from 'lodash-es'
import { observer } from 'mobx-react-lite'
import { materialManageModel } from '@/stores/material-manage'
import { EffectStatus, MaterialType } from '@/constants'
import { queryVersionListByMaterialInfoIdUsingGET } from '@/services'
import { safeParse } from '@/utils'
import { userModel } from '@/stores'
import CreateEditModal from '@/components/create-edit-modal'
import BaseSearchForm from '@/components/base-search-form'
import { defaultColumns, getMaterialFormList, searchFormItemsConfig } from './config'
import VersionManage from './components/version-manage'
import TransferScriptModal from './components/transfer-script-modal'
import ConfigMeta from './components/config-meta'
import PubMaterialModal from './components/pub-material-modal'
import CreateVirtualLibModal from './components/create-virtual-lib-modal'
import VirtualLibMaterialsModal from './components/virtual-lib-materials-modal'
import ConvertConfigModal from './components/convert-config-modal'

import { PlusOutlined } from '@ant-design/icons'
import TableWithTopBar from '@/components/table-with-top-bar'

import styles from './index.module.less'
import ManageTitle from '@/components/manage-title/ManageTitle'

const { Link } = Typography
const ManagePage: FC = () => {
  const [form] = Form.useForm()
  const {
    materialTableLoading,
    tagList,
    submitEditMaterial,
    deleteMaterial,
    enableMaterial,
    resetAndGetMaterialTableList,
    submitMaterialCategoryById,
    materialTableData,
    materialTableParams,
    materialTableTotal,
    refreshMaterialTableList,
  } = materialManageModel
  const [materialModalVisible, setMaterialModalVisible] = useState(false)
  const [currentMaterial, setCurrentMaterial] = useState(undefined)
  const [drawerTitle, setDrawerTitle] = useState<string | undefined>(undefined)
  const [drawerType, setDrawerType] = useState<string | undefined>(undefined)
  const [showMetaDrawer, setShowMetaDrawer] = useState(false)
  const [showPubModal, setShowPubModal] = useState(false)
  const [showCreVirtualLib, setShowCreVirtualLib] = useState(false)
  const [showEditVirMaterials, setShowEditVirMaterials] = useState(false)
  const [versionList, setVersionList] = useState([])
  const [showConvertConfigModal, setShowConvertConfigModal] = useState(false)

  const tagOptions = useMemo(() => tagList.map(item => ({ label: item.title, value: item.id })), [tagList])
  const searchFormItems = useMemo(() => searchFormItemsConfig({ tagOptions }), [tagOptions])

  useEffect(() => {
    materialManageModel.init()
    return () => materialManageModel.reset()
  }, [])

  const openMetaDrawer = (material) => {
    setShowMetaDrawer(true)
    setCurrentMaterial(material)
  }

  const closeMetaDrawer = () => {
    setShowMetaDrawer(false)
    setCurrentMaterial(null)
  }

  const toggleCreateVirtualLibModal = () => {
    setShowCreVirtualLib(p => !p)
  }

  const toggleEditVirMaterialsModal = (published: boolean) => {
    setShowEditVirMaterials(p => !p)
    if (published) {
      refreshMaterialTableList()
    }
  }

  const openDrawer = (material, type: string) => {
    setDrawerTitle(material.title)
    setCurrentMaterial(material)
    setDrawerType(type)
  }

  const closeDrawer = () => {
    setDrawerTitle(undefined)
    setCurrentMaterial(undefined)
    setDrawerType(undefined)
  }

  const openConvertConfigModal = (material) => {
    setCurrentMaterial(material)
    setShowConvertConfigModal(true)
  }

  const closeConvertConfigModal = (refresh?: boolean) => {
    setCurrentMaterial(undefined)
    setShowConvertConfigModal(false)

    if (refresh === true) {
      refreshMaterialTableList()
    }
  }

  const onMaterialModalClose = () => {
    setMaterialModalVisible(false)
  }

  const onReset = () => {
    form.resetFields()
    resetAndGetMaterialTableList({})
  }

  const onSearch = () => {
    const searchParams = form.getFieldsValue()
    resetAndGetMaterialTableList(searchParams)
  }

  const handleDelete = (id) => {
    deleteMaterial(id)
  }

  const handleEnable = (id) => {
    enableMaterial(id, userModel.user?.id)
  }

  const handleEditComLib = (record) => {
    if (safeParse(record.meta)?.virtual) {
      setCurrentMaterial(record)
      setShowEditVirMaterials(true)
    }
  }

  const handleEdit = (record) => {
    queryVersionListByMaterialInfoIdUsingGET({ id: record.id }).then((res) => {
      setVersionList(res)
      setCurrentMaterial(record)
      setMaterialModalVisible(true)
    })
  }

  const initMaterialValue = useMemo(() => {
    const values = omit(currentMaterial, ['id', 'tags', 'update_time', 'updater_id', 'creator_id', 'creator_name', 'create_time'])
    if (currentMaterial?.preview_img) {
      values.upload = [
        { uid: '-1', name: '预览图', status: 'done', url: currentMaterial?.preview_img },
      ]
    }
    return values
  }, [currentMaterial])

  const createSceneOrTag = (type: 'scene' | 'tag') => {
    materialManageModel.openCommonEditModal(`新建${type === 'scene' ? '场景' : '标签'}`, type)
  }

  const tagAddClick = () => {
    createSceneOrTag('tag')
  }

  const columns = useMemo(() => {
    return defaultColumns({ tagAddClick, tagOptions, submitMaterialCategoryById })
  }, [tagOptions])

  const togglePubModal = useCallback(() => setShowPubModal(p => !p), [])

  const submitCallback = (flag: boolean) => {
    if (flag) {
      setMaterialModalVisible(false)
      refreshMaterialTableList()
    }
  }

  const handleSubmitMaterial = (values) => {
    const submitValue = omit(values, 'upload')
    submitEditMaterial({ id: currentMaterial.id, ...submitValue }, submitCallback)
  }

  const operateColumns = [
    {
      title: '物料转换',
      dataIndex: 'script-run',
      key: 'script-run',
      render: (_, record) => {
        const isLogic = record.tags?.find(tag => tag.title === '逻辑')
        return isLogic
          ? <>逻辑物料不支持转换</>
          : (
            record.type === MaterialType.COMPONENT
              ? (
                <div className={styles.operationBox}>
                  <a onClick={() => openDrawer(record, 'script-modal')}>转换</a>
                  <a href={`/manage/transfer/history?material_id=${record.id}`} target="_blank" rel="noreferrer">转换记录</a>
                  <a onClick={() => openConvertConfigModal(record)}>配置</a>
                </div>
              )
              : <a onClick={() => openConvertConfigModal(record)}>配置</a>
          )
      },
      width: 148,
      fixed: 'right',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      render: (_, record) => {
        return (
          <div className={styles.operationBox}>
            <a onClick={() => handleEdit(record)}>编辑</a>
            {record.type === MaterialType.COM_LIB
              ? <a onClick={() => handleEditComLib(record)}>组件排版</a>
              : null}
            <a onClick={() => openDrawer(record, 'version')}>版本管理</a>
            <a onClick={() => openMetaDrawer(record)}>扩展元信息</a>
            {record.status === EffectStatus.EFFECT
              ? (
                <Popconfirm title="确定删除吗?" onConfirm={() => handleDelete(record.id)}>
                  <a className={styles.danger}>删除</a>
                </Popconfirm>
              )
              : (
                <Popconfirm title="确定启用吗?" onConfirm={() => handleEnable(record.id)}>
                  <a>启用</a>
                </Popconfirm>
              )}
          </div>
        )
      },
      width: 220,
    },
  ]

  const handleTableChange = (pagination) => {
    materialManageModel.updateTableParams('pagination', pagination)
  }

  const handleCloseScriptRunModal = () => {
    setDrawerType(undefined)
  }

  const editFormList = useMemo(() => {
    if (materialModalVisible === false) {
      return []
    }
    return getMaterialFormList(versionList, currentMaterial?.business, currentMaterial.type)
  }, [currentMaterial, versionList, materialModalVisible])

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <ManageTitle>物料管理</ManageTitle>
      <BaseSearchForm formProps={{ form }} formItemList={searchFormItems} onReset={onReset} onSubmit={onSearch} />
      <TableWithTopBar
        topBar={{
          total: materialTableTotal,
          operations: [
            <Button key="add" icon={<PlusOutlined />} type="primary" onClick={toggleCreateVirtualLibModal}>
              新增虚拟组件库
            </Button>,
            <Button key="publish" icon={<PlusOutlined />} type="primary" onClick={togglePubModal}>
              发布物料
            </Button>,
            <Button key="record" type="primary">
              <Link style={{ color: '#fff' }} href="/manage/transfer/history">物料转换记录</Link>
            </Button>,
          ],
        }}
        columns={[...columns, ...operateColumns]}
        dataSource={materialTableData}
        rowKey={record => record.id}
        scroll={{ x: 'max-content' }}
        pagination={materialTableParams.pagination}
        loading={materialTableLoading}
        onChange={handleTableChange}
      />
      {drawerType === 'version'
        ? (
          <Drawer title={drawerTitle} visible onClose={closeDrawer} width={1000}>
            <VersionManage material_id={currentMaterial?.id} />
          </Drawer>
        )
        : null}
      <CreateEditModal
        initValues={initMaterialValue}
        visible={materialModalVisible}
        title="编辑物料"
        formItemList={editFormList as AnyType}
        onClose={onMaterialModalClose}
        onSubmit={handleSubmitMaterial}
      />
      <PubMaterialModal visible={showPubModal} onCancel={togglePubModal} />
      <CreateVirtualLibModal visible={showCreVirtualLib} onCancel={toggleCreateVirtualLibModal} />
      <VirtualLibMaterialsModal
        material={currentMaterial}
        visible={showEditVirMaterials}
        onCancel={val => toggleEditVirMaterialsModal(val)}
      />
      <ConfigMeta visible={showMetaDrawer} onClose={closeMetaDrawer} material={currentMaterial} />
      <TransferScriptModal
        onClose={handleCloseScriptRunModal}
        title={`物料转换-${currentMaterial?.title || '-'}`}
        visible={drawerType === 'script-modal'}
        material={currentMaterial}
        id={currentMaterial?.id}
      />
      {showConvertConfigModal
        ? (
          <ConvertConfigModal
            material={currentMaterial}
            onCancel={closeConvertConfigModal}
          />
        )
        : null}
    </Space>
  )
}

export default observer(ManagePage)
