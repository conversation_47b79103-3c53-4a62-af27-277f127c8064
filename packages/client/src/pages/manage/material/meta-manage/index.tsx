import { FC, useEffect, useRef, useState } from 'react'
import { Button, Drawer, Form, Input, message, Modal, Popconfirm, Select, Space } from 'antd'
import { observer } from 'mobx-react-lite'
import { formatTime, safeParse, safeStringify } from '@/utils'
import { BusinessMap, businessOptions, EffectStatus, EffectStatusMap, EffectStatusOptions } from '@/constants'
import { metaManageModel } from '@/stores/meta-manage'
import { queryComponentMetaListByBusinessUsingGET } from '@/services'
import BaseSearchForm from '@/components/base-search-form'
import MetaValueManage from '../meta-value-manage'

import { PlusOutlined } from '@ant-design/icons'
import TableWithTopBar from '@/components/table-with-top-bar'
import ManageTitle from '@/components/manage-title/ManageTitle'

enum Action {
  CREATE = 'create',
  EDIT = 'edit',
}
export interface MetaItem {
  id: number
  title: string
  value: string
  items: Array<{ id: number, title: string, value: string }>
}
const MetaManage: FC = () => {
  const [action, setAction] = useState('')
  const [showMetaValues, setShowMetaValues] = useState(false)
  const [form] = Form.useForm()
  const [actionForm] = Form.useForm()
  const { meta } = metaManageModel
  const [businessMetas, setBusinessMetas] = useState<MetaItem[]>([])
  const currentMeta = useRef(null)

  const handleCreate = () => {
    setAction(Action.CREATE)
  }

  const handleEditMetaValues = (record) => {
    setShowMetaValues(true)
    currentMeta.current = record
  }

  const onCloseMetaValues = () => {
    setShowMetaValues(false)
    currentMeta.current = null
  }

  const handleUpdateStatus = async (record, status) => {
    try {
      await metaManageModel.updateMetaStatus({ id: record.id, status })
      metaManageModel.getMetas()
    }
    catch (error) {
      message.error(error.message)
    }
  }

  const handleEdit = (record) => {
    setAction(Action.EDIT)
    const meta = safeParse(record.meta)
    actionForm.setFieldsValue({ ...record, ...meta })
    currentMeta.current = record
    onChangeBusiness(record.business)
  }
  const onCancel = () => {
    actionForm.resetFields()
    setAction('')
    currentMeta.current = null
    setBusinessMetas([])
  }

  const onSubmit = () => {
    actionForm.validateFields().then(async (values) => {
      try {
        if (Action.CREATE === action) {
          await metaManageModel.createMeta({ ...values, meta: safeStringify({ condition: values.condition }) })
          message.success('创建成功')
        }
        else {
          const meta = safeParse(currentMeta.current?.meta)
          await metaManageModel.updateMeta({
            ...values,
            id: currentMeta.current?.id,
            meta: safeStringify({ ...meta, condition: values.condition }),
          })
          message.success('更新成功')
        }

        metaManageModel.getMetas()
        onCancel()
      }
      catch (error) {
        message.error(error.message)
      }
    })
  }
  const columns = [
    {
      title: '元信息id',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '元信息名',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '物料生产方',
      dataIndex: 'business',
      key: 'business',
      render: val => BusinessMap[val] ?? '-',
    },
    {
      title: '值',
      dataIndex: 'value',
      key: 'value',
    },
    {
      title: '创建者',
      dataIndex: 'creator_name',
      key: 'creator_name',
      render: creatorName => creatorName ?? '-',
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: val => formatTime(val),
    },
    {
      title: '生效状态',
      dataIndex: 'status',
      key: 'status',
      render: val => EffectStatusMap[val],
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 250,
      render: (_, record) => {
        const effected = record.status === EffectStatus.EFFECT

        return (
          <div>
            <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
            <Button type="link" onClick={() => handleEditMetaValues(record)}>扩展值</Button>
            <Popconfirm title={`确定${effected ? '删除' : '启用'}吗?`} onConfirm={() => handleUpdateStatus(record, effected ? EffectStatus.DELETE : EffectStatus.EFFECT)}>
              <Button type="link" danger={effected}>{effected ? '删除' : '启用'}</Button>
            </Popconfirm>
          </div>
        )
      },
    },
  ]

  useEffect(() => {
    metaManageModel.getMetas()
  }, [])
  const onReset = () => {
    metaManageModel.meta.searchParams = { title: '', status: undefined }
    metaManageModel.getMetas()
  }

  const onSearch = () => {
    metaManageModel.meta.searchParams = form.getFieldsValue()
    metaManageModel.getMetas()
  }

  const searchFormItems = [
    {
      label: '元信息名',
      name: 'title',
      renderType: 'input',
      extraOptions: {
        placeholder: '输入元信息名',
        allowClear: true,
      },
    },
    {
      label: '状态',
      name: 'status',
      renderType: 'select',
      extraOptions: {
        options: EffectStatusOptions,
        allowClear: true,
      },
    },
  ]
  const onChangeBusiness = async (business) => {
    const dataSource = await queryComponentMetaListByBusinessUsingGET({ business })
    setBusinessMetas((dataSource as MetaItem[]).filter(meta => meta.value !== currentMeta.current?.value))
  }

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <ManageTitle>元信息管理</ManageTitle>
      <BaseSearchForm formProps={{ form }} formItemList={searchFormItems} onReset={onReset} onSubmit={onSearch} />
      <TableWithTopBar
        topBar={{
          total: meta.dataSource.length,
          operations: (
            <Button type="primary" onClick={handleCreate} icon={<PlusOutlined />}>
              新建元信息
            </Button>
          ),
        }}
        columns={columns}
        rowKey="id"
        dataSource={meta.dataSource}
        pagination={meta.pagination}
        loading={meta.loading}
      />
      <Modal
        width={600}
        visible={!!action}
        title={action === Action.CREATE ? '新建元信息' : '编辑元信息'}
        onOk={onSubmit}
        onCancel={onCancel}
      >
        <Form form={actionForm} labelCol={{ span: 6 }}>
          <Form.Item label="名称" name="title" required rules={[{ required: true, message: '请输入名称' }]}>
            <Input placeholder="请输入名称" />
          </Form.Item>
          <Form.Item label="值" name="value" required rules={[{ required: true, message: '请输入值' }]}>
            <Input placeholder="请输入值" />
          </Form.Item>
          {action === Action.CREATE
            ? (
              <Form.Item label="物料生产方" name="business" required rules={[{ required: true, message: '请选择所属BU' }]}>
                <Select placeholder="请选择物料生产方" onChange={onChangeBusiness}>
                  {businessOptions.map((b) => {
                    return <Select.Option key={b.value} value={b.value}>{b.label}</Select.Option>
                  })}
                </Select>
              </Form.Item>
            )
            : null}
          <Form.Item label="显示条件" name="condition" extra="配置元信息在列表页的展示逻辑，需保证选项完整条件才会生效">
            <Condition list={businessMetas} />
          </Form.Item>
        </Form>
      </Modal>

      {showMetaValues
        ? (
          <Drawer visible title="编辑元信息扩展值" width={900} closable onClose={onCloseMetaValues}>
            <MetaValueManage metaId={currentMeta.current?.id} />
          </Drawer>
        )
        : null}
    </Space>
  )
}

const Condition: FC<{ value?: AnyType, onChange?(value: AnyType): void, list: MetaItem[] }> = (props) => {
  const { value, onChange, list } = props
  const [metaValues, setMetaValues] = useState<MetaItem['items']>([])
  const onChangeMeta = (meta) => {
    setMetaValues(list.find(l => l.value === meta)?.items ?? [])
    onChange({ meta, meta_value: [] })
  }

  const onChangeMetaValue = (metaValue) => {
    onChange({ ...(value ?? {}), meta_value: metaValue })
  }

  useEffect(() => {
    setMetaValues(list.find(l => l.value === value?.meta)?.items ?? [])
  }, [list])

  return (
    <Space>
      <Select
        style={{ width: '184px' }}
        options={list.map(l => ({ label: l.title, value: l.value }))}
        allowClear
        value={value?.meta}
        placeholder="请选择关联的元信息"
        onChange={onChangeMeta}
      />
      等于
      <Select
        style={{ width: '184px' }}
        options={metaValues.map(l => ({ label: l.title, value: l.value }))}
        allowClear
        mode="multiple"
        value={value?.meta_value}
        placeholder="请选择关联的元信息值"
        onChange={onChangeMetaValue}
      />
    </Space>
  )
}

export default observer(MetaManage)
