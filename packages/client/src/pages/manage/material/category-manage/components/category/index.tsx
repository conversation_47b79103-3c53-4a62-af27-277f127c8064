import { FC, useCallback, useEffect, useState } from 'react'
import { Button, message, Space, Switch, Tag } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import { ColumnsType } from 'antd/lib/table'

import {
  EffectStatusOptions,
  EffectStatus,
  EFFECT_STATUS_TAG,
} from '@/constants'
import { formatTime } from '@/utils'

import { enableMaterialTag, fetchMaterialTags } from '@/services/v2/material-tag'

import BaseSearchForm from '@/components/base-search-form'
import TableWithTopBar from '@/components/table-with-top-bar'
import ManageTitle from '@/components/manage-title/ManageTitle'
import { createCategoryFormModal, editCategoryFormModal } from './create-edit-functions'

const SEARCH_FORM_ITEMS = [
  {
    label: '分类名',
    name: 'title',
    renderType: 'input',
    extraOptions: {
      placeholder: '输入分类名',
      allowClear: true,
    },
  },
  {
    label: '状态',
    name: 'status',
    renderType: 'select',
    extraOptions: {
      options: EffectStatusOptions,
      allowClear: true,
    },
  },
]

const SEARCH_FORM_INITIAL_VALUES = {
  title: '',
  status: 1,
}

const CategoryManage: FC = () => {
  const columns: ColumnsType<Service.Material.Tag.TagRecord> = [
    {
      title: '分类id',
      dataIndex: 'id',
      key: 'id',
      width: 200,
    },
    {
      title: '分类名',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '分类值',
      dataIndex: 'value',
      key: 'value',
      render: value => <Tag>{value}</Tag>,
    },
    {
      title: '顺序',
      dataIndex: 'order',
      key: 'order',
      width: 150,
    },
    {
      title: '生效状态',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      render: val => EFFECT_STATUS_TAG[val],
    },
    {
      title: '创建者',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 150,
      render: creatorName => creatorName ?? '-',
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 150,
      render: val => formatTime(val),
    },
    {
      title: '是否启用',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      render: (val, record) => {
        return (
          <Switch
            checked={val === EffectStatus.EFFECT}
            onChange={
              async (next) => {
                const nextStatus = next ? EffectStatus.EFFECT : EffectStatus.DISABLED
                await enableMaterialTag({
                  id: record.id,
                  target_status: nextStatus,
                })
                message.success('操作成功')
                setCategoryList(pre => pre.map((item) => {
                  if (item.id === record.id) {
                    return {
                      ...item,
                      status: nextStatus,
                    }
                  }
                  return item
                }))
              }
            }
          />
        )
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 90,
      align: 'center',
      render: (_, record) => (
        <Button
          type="link"
          onClick={() => editCategoryFormModal(record, () => {
            message.success('编辑成功')
            getCategoryList(categorySearchParams)
          })}
        >
          编辑
        </Button>
      ),
    },
  ]

  const [loading, setLoading] = useState(false)
  const [categorySearchParams, setCategorySearchParams] = useState<Service.Material.Tag.SearchParams>(SEARCH_FORM_INITIAL_VALUES)
  const [categoryList, setCategoryList] = useState<Service.Material.Tag.TagRecord[]>([])
  const getCategoryList = useCallback(async (params: Service.Material.Tag.SearchParams) => {
    setLoading(true)
    try {
      const res = await fetchMaterialTags(params)
      setCategoryList(res.data.data)
    }
    finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    getCategoryList(categorySearchParams)
  }, [])

  const onReset = useCallback(() => {
    setCategorySearchParams(SEARCH_FORM_INITIAL_VALUES)
    getCategoryList(SEARCH_FORM_INITIAL_VALUES)
  }, [])

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <ManageTitle>分类管理</ManageTitle>
      <BaseSearchForm
        formProps={{
          initialValues: SEARCH_FORM_INITIAL_VALUES,
        }}
        formItemList={SEARCH_FORM_ITEMS}
        onReset={onReset}
        onSubmit={(value) => {
          setCategorySearchParams(value)
          getCategoryList(value)
        }}
      />
      <TableWithTopBar
        topBar={{
          total: categoryList.length,
          operations: (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => createCategoryFormModal(() => {
                message.success('新建成功')
                getCategoryList(categorySearchParams)
              })}
            >
              新建分类
            </Button>
          ),
        }}
        columns={columns}
        rowKey={record => record.id}
        dataSource={categoryList}
        loading={loading}
        pagination={false}
      />
    </Space>
  )
}

export default observer(CategoryManage)
