import { FormItemProps, Input, InputNumber } from 'antd'
import FunctionalFormModal from '@/components/functional-form-modal'
import { createMaterialTag, updateMaterialTag } from '@/services/v2/material-tag'
import { userModel } from '@/stores'

const CategoryFormItems: FormItemProps[] = [
  {
    label: `分类名称`,
    name: 'title',
    rules: [
      { required: true, message: '请输入内容' },
      { max: 20, message: '不能超过20字符' },
      { whitespace: true },
    ],
    children: <Input placeholder="请输入分类名称" />,
  },
  {
    label: `分类值`,
    name: 'value',
    rules: [
      { required: true, message: '请输入内容' },
      { max: 20, message: '不能超过20字符' },
      { whitespace: true },
    ],
    children: <Input placeholder="请输入分类值" />,
  },
  {
    label: '展示顺序',
    name: 'order',
    children: (
      <InputNumber
        placeholder="请输入展示顺序"
        style={{ width: '100%' }}
        step={1}
        min={0}
        max={100}
      />
    ),
  },
]

interface CategoryFormValues {
  title: string
  value: string
  order: number
}

export function createCategoryFormModal(callback: () => void) {
  console.log(userModel)
  return FunctionalFormModal.open<CategoryFormValues>({
    title: '新建分类',
    formItems: CategoryFormItems,
    formProps: {
      initialValues: {
        order: 0,
      },
    },
  }).then(values =>
    createMaterialTag({
      ...values,
      creator_id: userModel.user.id,
    }),
  ).then(() => {
    callback()
  })
}

export function editCategoryFormModal(record: Service.Material.Tag.TagRecord, callback: (newRecord: Service.Material.Tag.TagRecord) => void) {
  return FunctionalFormModal.open<CategoryFormValues>({
    title: '编辑分类',
    formItems: CategoryFormItems,
    formProps: {
      initialValues: {
        ...record,
      },
    },
  }).then(values =>
    updateMaterialTag({
      id: record.id,
      ...values,
    }),
  ).then((res) => {
    callback(res.data.data)
  })
}
