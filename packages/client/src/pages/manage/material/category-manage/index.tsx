import React, { useMemo } from 'react'
import { observer } from 'mobx-react-lite'
import Category from './components/category'
import { materialManageModel } from '@/stores/material-manage'
import CreateEditModal from '@/components/create-edit-modal'
import { tagEditConfig } from '../material-manage/config'

const CategoryManage = () => {
  const {
    commonModalType,
    commonModalTitle,
    submitCategory,
    commonModalCurrentRecord,
  } = materialManageModel
  const subModelEditConfig = useMemo(() => {
    return commonModalType === 'tag' ? tagEditConfig : []
  }, [commonModalType])

  const handleSubmit = (values) => {
    submitCategory(values, commonModalCurrentRecord ? 'edit' : 'create')
  }

  return (
    <>
      <Category />
      <CreateEditModal
        visible={!!commonModalTitle}
        title={commonModalTitle}
        formItemList={subModelEditConfig}
        onSubmit={handleSubmit}
      />
    </>
  )
}
export default observer(CategoryManage)
