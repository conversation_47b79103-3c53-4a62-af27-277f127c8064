import React, { FC, useEffect, useRef, useState } from 'react'
import { Button, Form, Input, InputNumber, message, Modal, Popconfirm, Space } from 'antd'
import { observer } from 'mobx-react-lite'
import { EffectStatus, EffectStatusMap, EffectStatusOptions } from '@/constants'
import { formatTime } from '@/utils'
import { metaValueManageModel } from '@/stores/meta-value-manage'
import BaseSearchForm from '@/components/base-search-form'

import TableWithTopBar from '@/components/table-with-top-bar'
import { PlusOutlined } from '@ant-design/icons'

interface MetaValueManageProps {
  metaId: number
}

enum Action {
  CREATE = 'create',
  EDIT = 'edit',
}
const MetaValueManage: FC<MetaValueManageProps> = (props) => {
  const { metaId } = props
  const [action, setAction] = useState('')
  const [form] = Form.useForm()
  const [actionForm] = Form.useForm()
  const { metaValue } = metaValueManageModel
  const currentMeta = useRef(null)

  const handleCreate = () => {
    setAction(Action.CREATE)
  }

  const handleUpdateStatus = async (record, status) => {
    try {
      await metaValueManageModel.updateMetaValueStatus({ id: record.id, status })
      metaValueManageModel.getMetaValues()
    }
    catch (error) {
      message.error(error.message)
    }
  }

  const handleEdit = (record) => {
    setAction(Action.EDIT)
    actionForm.setFieldsValue(record)
    currentMeta.current = record
  }
  const onCancel = () => {
    actionForm.resetFields()
    setAction('')
  }

  const onSubmit = () => {
    actionForm.validateFields().then(async (values) => {
      try {
        if (Action.CREATE === action) {
          await metaValueManageModel.createMetaValue(values)
          message.success('创建成功')
        }
        else {
          await metaValueManageModel.updateMetaValue({ ...values, id: currentMeta.current?.id })
          message.success('更新成功')
        }

        metaValueManageModel.getMetaValues()
        onCancel()
      }
      catch (error) {
        message.error(error.message)
      }
    })
  }
  const columns = [
    {
      title: '元信息值id',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '元信息值名',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '值',
      dataIndex: 'value',
      key: 'value',
    },
    {
      title: '顺序',
      dataIndex: 'order',
      key: 'order',
    },
    {
      title: '创建者',
      dataIndex: 'creator_name',
      key: 'creator_name',
      render: creatorName => creatorName ?? '-',
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: val => formatTime(val),
    },
    {
      title: '生效状态',
      dataIndex: 'status',
      key: 'status',
      render: val => EffectStatusMap[val],
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      render: (_, record) => {
        const effected = record.status === EffectStatus.EFFECT

        return (
          <div>
            <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
            <Popconfirm title={`确定${effected ? '删除' : '启用'}吗?`} onConfirm={() => handleUpdateStatus(record, effected ? EffectStatus.DELETE : EffectStatus.EFFECT)}>
              <Button type="link" danger={effected}>{effected ? '删除' : '启用'}</Button>
            </Popconfirm>
          </div>
        )
      },
    },
  ]

  const onReset = () => {
    metaValueManageModel.metaValue.searchParams = { title: '', status: undefined }
    metaValueManageModel.getMetaValues()
  }

  const onSearch = () => {
    metaValueManageModel.metaValue.searchParams = form.getFieldsValue()
    metaValueManageModel.getMetaValues()
  }

  const searchFormItems = [
    {
      label: '名称',
      name: 'title',
      renderType: 'input',
      extraOptions: {
        placeholder: '输入元信息值名称',
        allowClear: true,
      },
    },
    {
      label: '状态',
      name: 'status',
      renderType: 'select',
      extraOptions: {
        options: EffectStatusOptions,
        allowClear: true,
      },
    },
  ]

  useEffect(() => {
    metaValueManageModel.metaId = metaId
    metaValueManageModel.getMetaValues()
  }, [metaId])

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <BaseSearchForm formProps={{ form }} formItemList={searchFormItems} onReset={onReset} onSubmit={onSearch} />
      <TableWithTopBar
        topBar={{
          total: metaValue.dataSource.length,
          operations: (
            <Button type="primary" onClick={handleCreate} icon={<PlusOutlined />}>
              新建元信息值
            </Button>
          ),
        }}
        columns={columns}
        rowKey="id"
        dataSource={metaValue.dataSource}
        pagination={false}
        loading={metaValue.loading}
      />
      <Modal
        visible={!!action}
        title={action === Action.CREATE ? '新建元信息值' : '编辑元信息值'}
        onOk={onSubmit}
        onCancel={onCancel}
      >
        <Form form={actionForm} labelCol={{ span: 6 }}>
          <Form.Item label="名称" name="title" required rules={[{ required: true, message: '请输入名称' }]}>
            <Input placeholder="请输入名称" />
          </Form.Item>
          <Form.Item label="值" name="value" required rules={[{ required: true, message: '请输入值' }]}>
            <Input placeholder="请输入值" />
          </Form.Item>
          <Form.Item label="展示顺序" name="order" required rules={[{ required: true, message: '请填写展示顺序' }]}>
            <InputNumber placeholder="请填写展示顺序" step={1} />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  )
}

export default observer(MetaValueManage)
