import React from 'react'
import { Typography, Tag, Popconfirm } from 'antd'
import { EffectStatusMap, BusinessMap, ConvertColorMap, ConvertStatusMap, ConvertStatus, Business } from '@/constants'
import { formatTime } from '@/utils'
import { getFangZhouPreviewUrlByBundle, getKaelPreviewUrlByBundle } from '@/utils'

const { Paragraph } = Typography

export const getTransferColumns = ({ publish }) => [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id',
    width: 140,
  },
  {
    title: '物料',
    dataIndex: 'material_id',
    key: 'material_id',
    width: 140,
    render: (_, record) => {
      return `${record.material_title}(${record.material_id})`
    },
  },
  {
    title: '物料生产方',
    dataIndex: 'business',
    key: 'business',
    render: val => BusinessMap[val] ?? '-',
    width: 140,
  },
  {
    title: '物料版本',
    dataIndex: 'version',
    key: 'version',
    width: 160,
  },
  {
    title: '转换脚本',
    dataIndex: 'script',
    key: 'script',
    render: val => (
      <Paragraph copyable={{ text: val }} style={{ width: '400px' }}>
        <a href={val} target="_blank" rel="noreferrer">{val}</a>
      </Paragraph>
    ),
    width: 200,
  },
  {
    title: '结果标识',
    dataIndex: 'result',
    key: 'result',
    render: (val) => {
      const color = ConvertColorMap[val] || '#808080'
      return ConvertColorMap[val] ? <Tag color={color}>{ConvertStatusMap[val]}</Tag> : '-'
    },
    width: 160,
  },
  {
    title: '描述信息',
    dataIndex: 'reason',
    key: 'reason',
    width: 140,
  },
  {
    title: '创建者',
    dataIndex: 'creator_name',
    key: 'creator_name',
    width: 160,
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
    render: val => formatTime(val),
    width: 160,
  },
  {
    title: '生效状态',
    dataIndex: 'status',
    key: 'status',
    render: val => EffectStatusMap[val] ?? '-',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    fixed: 'right',
    render: (_, record) => {
      const onPreview = () => {
        if (record.business === Business.FANG_ZHOU) {
          window.open(getFangZhouPreviewUrlByBundle(record.bundle), '_blank')
        }
        else if (record.business === Business.KAEL) {
          window.open(getKaelPreviewUrlByBundle(record.bundle), '_blank')
        }
        else {
          window.open(record.bundle, '_blank')
        }
      }

      return (
        <div>
          {[ConvertStatus.SUCCESS, ConvertStatus.PUBLISHED].includes(record.result)
            ? (
              <a onClick={onPreview}>预览产物</a>
            )
            : null}
          {record.result === ConvertStatus.SUCCESS
            ? (
              <Popconfirm title="确定发布吗?" onConfirm={() => publish(record.id)}>
                <a onClick={publish} style={{ marginLeft: 16 }}>发布</a>
              </Popconfirm>
            )
            : null}
        </div>
      )
    },
    width: 200,
  },
]
