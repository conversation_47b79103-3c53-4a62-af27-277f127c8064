import { FC, useEffect } from 'react'
import { Form, message, Space, Table } from 'antd'
import { observer } from 'mobx-react-lite'

import { transferModel } from '@/stores'
import { getUrlParam } from '@/utils'
import { businessOptions, EffectStatusOptions } from '@/constants'
import { publishConvertBundleUsingPOST } from '@/services'

import BaseSearchForm from '@/components/base-search-form'
import ManageTitle from '@/components/manage-title/ManageTitle'

import { getTransferColumns } from './config'

const TransferHistory: FC = () => {
  const [form] = Form.useForm()
  const material_id = getUrlParam('material_id')
  const { loading, transferList, resetAndGetTransferList, transferTableParams } = transferModel

  const columns = getTransferColumns({
    publish: (id: number) => {
      publishConvertBundleUsingPOST({ id }).then((res: AnyType) => {
        if (res.code === 1) {
          message.success(res.message)
          resetAndGetTransferList()
        }
        else {
          message.error(res.message)
        }
      })
    },
  })

  useEffect(() => {
    transferModel.init(material_id ? { material_id } : {})

    return () => transferModel.reset()
  }, [])
  const onReset = () => {
    form.resetFields()
    resetAndGetTransferList({})
  }

  const handleTableChange = (pagination) => {
    transferModel.updateTableParams('pagination', pagination)
  }

  const onSearch = () => {
    const reqParams = form.getFieldsValue()
    resetAndGetTransferList({ ...reqParams, material_id })
  }

  const searchFormItems = [
    {
      label: '物料生产方',
      name: 'business',
      renderType: 'select',
      extraOptions: {
        placeholder: '选择部门',
        options: businessOptions,
        allowClear: true,
      },
    },
    {
      label: '生效状态',
      name: 'status',
      renderType: 'select',
      extraOptions: {
        options: EffectStatusOptions,
        allowClear: true,
      },
    },
  ]

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <ManageTitle>转换记录</ManageTitle>
      <BaseSearchForm formProps={{ form: form }} formItemList={searchFormItems} onReset={onReset} onSubmit={onSearch} />
      <Table
        columns={columns as AnyType}
        rowKey={record => record.id}
        dataSource={transferList}
        pagination={transferTableParams.pagination}
        loading={loading}
        onChange={handleTableChange}
      />
    </Space>
  )
}

export default observer(TransferHistory)
