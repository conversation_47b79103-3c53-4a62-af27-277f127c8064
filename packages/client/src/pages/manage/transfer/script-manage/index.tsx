import { observer } from 'mobx-react-lite'
import { PlusOutlined } from '@ant-design/icons'
import { FC, useEffect, useState, useMemo } from 'react'
import { Button, Form, Popconfirm, Typography, Input, Upload, message, Tooltip, Space } from 'antd'

import { EffectStatusOptions, EffectStatusMap, businessOptions, BusinessMap } from '@/constants'
import { formatTime } from '@/utils'
import { uploadScriptFileUsingPost } from '@/services'
import { scriptModel } from '@/stores'
import BaseSearchForm from '@/components/base-search-form'
import CreateEditModal from '@/components/create-edit-modal'
import TableWithTopBar from '@/components/table-with-top-bar'
import ManageTitle from '@/components/manage-title/ManageTitle'

const { Paragraph, Link } = Typography

const requiredRules = [
  { required: true, message: '请输入内容' },
]

export const normFile = (e: AnyType) => {
  if (Array.isArray(e)) {
    return e
  }
  return e && e.fileList
}

const ScriptManage: FC = () => {
  const [form] = Form.useForm()

  const { loading, scriptList, submitScript, deleteScript, getScriptTableData } = scriptModel
  const [scriptModalVisible, setScriptModalVisible] = useState(false)
  const [initScriptValue, setInitScriptValue] = useState(undefined)
  const [scriptModalType, setScriptModalType] = useState<'create' | 'edit' | undefined>('create')
  const [currentScript, setCurrentScript] = useState(undefined)
  const [uploading, setUploading] = useState(false)
  const handleEdit = (record) => {
    setCurrentScript(record)
    setInitScriptValue({
      business: record.business,
      script: {
        url: record.script,
      },
    })
    setScriptModalType('edit')
    setScriptModalVisible(true)
  }
  const handleDelete = (record) => {
    deleteScript(record.id)
  }

  const handleCreate = () => {
    setScriptModalType('create')
    setScriptModalVisible(true)
    setInitScriptValue({})
  }

  const columns = [
    {
      title: '脚本id',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '物料生产方',
      dataIndex: 'business',
      key: 'business',
      render: val => BusinessMap[val] ?? '-',
      width: 160,
    },
    {
      title: '转换脚本地址',
      dataIndex: 'script',
      key: 'script',
      render: (val) => {
        return (
          <Paragraph copyable={{ text: val }}>
            <Link href={val} target="_blank">{val}</Link>
          </Paragraph>
        )
      },
    },
    {
      title: '创建者',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 140,
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: val => formatTime(val),
      width: 140,
    },
    {
      title: '生效状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: val => EffectStatusMap[val] ?? '-',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      render: (_, record) => {
        return (
          <div>
            <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
            <Popconfirm title="确定删除吗?" onConfirm={() => handleDelete(record)}>
              <Button type="link" danger>删除</Button>
            </Popconfirm>
          </div>
        )
      },
    },
  ]

  useEffect(() => {
    scriptModel.init()

    return () => scriptModel.reset()
  }, [])
  const onReset = () => {
    form.resetFields()
  }

  const onSearch = () => {
    const params = form.getFieldsValue()
    getScriptTableData(params)
  }

  const submitCallback = (flag) => {
    if (flag) {
      setScriptModalVisible(false)
      setInitScriptValue(undefined)
    }
  }

  const handleSubmitScript = (values) => {
    const reqparams = {
      business: values.business,
      script: values.script.url,
      ...(currentScript ? { id: currentScript.id } : {}),
    }

    submitScript(reqparams, scriptModalType, submitCallback)
  }

  const onScriptModalClose = () => {
    setScriptModalType('create')
    setScriptModalVisible(false)
  }

  const scriptFormList = useMemo(() => [
    {
      label: '物料生产方',
      name: 'business',
      renderType: 'select',
      extraOptions: {
        placeholder: '请选择类型',
        options: businessOptions,
        disabled: scriptModalType === 'edit',
      },
      rules: [
        ...requiredRules,
      ],
    },
    {
      label: '脚本转换地址',
      name: 'title',
      renderType: 'custom',
      extraOptions: {
        placeholder: '输入脚本CDN地址',
      },
      rules: [
        { required: true, message: '请输入地址' },
        { whitespace: true },
      ],
      children: form => (
        <>
          <Form.Item label="脚本转换地址">
            <Input.Group compact>
              <Form.Item
                name={['script', 'url']}
                noStyle
                rules={[{ required: true, message: '输入脚本cdn链接' }]}
              >
                <Input style={{ width: 'calc(100% - 120px)' }} placeholder="输入脚本CDN链接" />
              </Form.Item>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                  prevValues.business !== curValues.business}
              >
                {({ getFieldValue }) => {
                  const canNotUpload = getFieldValue('business') === undefined
                  return (
                    <Form.Item
                      name={['script', 'upload']}
                      noStyle
                      valuePropName="fileList"
                      getValueFromEvent={normFile}
                    >
                      <Upload
                        name="logo"
                        listType="text"
                        multiple={false}
                        maxCount={1}
                        disabled={canNotUpload}
                        withCredentials={true}
                        accept=".js"
                        beforeUpload={() => {
                          return false
                        }}
                        onChange={(info: AnyType) => {
                          const business = form.getFieldValue('business')
                          setUploading(true)
                          uploadScriptFileUsingPost({ file: info.file, hash: true, path: `/script/${business}` })
                            .then((res: AnyType) => {
                              if (res.data.url) {
                                form.setFieldsValue({
                                  script: {
                                    url: res.data.url,
                                  },
                                })
                                message.success('脚本文件上传成功成功')
                              }
                              else {
                                throw Error(res.data?.message ?? '脚本文件上传失败')
                              }
                            })
                            .catch((err) => {
                              console.log('err', err)
                              message.error('脚本文件上传失败')
                            })
                            .finally(() => setUploading(false))
                        }}
                      >
                        <Tooltip title={canNotUpload ? '请选择业务部门' : ''}>
                          <Button disabled={canNotUpload} loading={uploading}>上传脚本文件</Button>
                        </Tooltip>
                      </Upload>
                    </Form.Item>
                  )
                }}
              </Form.Item>
            </Input.Group>
          </Form.Item>
          <Form.Item
            label=" "
            shouldUpdate={(prevValues, curValues) =>
              prevValues?.script?.url !== curValues?.script?.url}
          >
            {({ getFieldValue }) => {
              const scriptUrl = getFieldValue(['script', 'url'])
              if (!scriptUrl) {
                return null
              }
              return (
                <Paragraph copyable={{ text: scriptUrl }}>
                  链接
                  <Link href={scriptUrl} target="_blank">
                    {' '}
                    {scriptUrl}
                  </Link>
                </Paragraph>
              )
            }}
          </Form.Item>
        </>
      ),
    },
  ], [scriptModalType])

  const searchFormItems = [
    {
      label: '业务部门',
      name: 'business',
      renderType: 'select',
      extraOptions: {
        placeholder: '输入场景名',
        options: businessOptions,
        allowClear: true,
      },
    },
    {
      label: '状态',
      name: 'status',
      renderType: 'select',
      extraOptions: {
        options: EffectStatusOptions,
        allowClear: true,
      },
    },
  ]

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <ManageTitle>脚本管理</ManageTitle>
      <BaseSearchForm formProps={{ form: form }} formItemList={searchFormItems} onReset={onReset} onSubmit={onSearch} />
      <TableWithTopBar
        topBar={{
          total: scriptList.length,
          operations: (
            <Button type="primary" onClick={handleCreate} icon={<PlusOutlined />}>
              新建脚本
            </Button>
          ),
        }}
        columns={columns}
        rowKey={record => record.id}
        dataSource={scriptList}
        loading={loading}
      />
      <CreateEditModal
        modalProps={{ width: 720 }}
        initValues={initScriptValue}
        visible={scriptModalVisible}
        title={scriptModalType === 'edit' ? '编辑脚本' : '新增脚本'}
        formItemList={scriptFormList as AnyType}
        formProps={{ colon: false }}
        onClose={onScriptModalClose}
        onSubmit={handleSubmitScript}
      />
    </Space>
  )
}

export default observer(ScriptManage)
