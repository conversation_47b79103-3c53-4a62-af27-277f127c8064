import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { <PERSON><PERSON>, Button, Card, Empty, message, Space, Table, Tabs, Typography } from 'antd'
import { EditTwoTone } from '@ant-design/icons'
import { formatTime } from '@/utils'
import { BusinessMap, businessOptions } from '@/constants'
import { DeleteIcon, DepartmentIcon } from '@/icons'
import { deleteBusinessConfigUsingPOST, viewAllBusinessConfigUsingGET } from '@/services'
import EditSubscription from './components/edit-subscription-modal'
import EditCodeScanReport from './components/edit-code-scan-modal'
import EditMaterialScan from './components/edit-material-scan-modal'

import buStyle from './index.module.less'
import ManageTitle from '@/components/manage-title/ManageTitle'

const { Paragraph } = Typography
export interface SubscriptionCardProps {
  business: string
  metaKey?: string
  dataSource: Array<{
    id: number
    business: string
    creator_name: string
    update_time: number
    meta: {
      subscription: Record<string, AnyType>
      status: string
      report: { projects?: Array<AnyType> }
    }
  }>
  handleDelete?: (item, key) => Promise<void>
  handleEdit: (business, item) => void
}

const columns = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: 'git地址',
    dataIndex: 'git_url',
    key: 'git_url',
    width: '240px',
    render: (val) => {
      return (
        <Paragraph
          ellipsis={{ rows: 2, expandable: true }}
          style={{ display: 'inline' }}
          copyable={{ text: val }}
        >
          {val}
        </Paragraph>
      )
    },
  },
  {
    title: '路由入口',
    dataIndex: 'router_entry',
    key: 'router_entry',
    width: '180px',
    render: val => <span style={{ whiteSpace: 'pre-wrap' }}>{val?.join(';\n')}</span>,
  },
]

const SubscriptionCard: FC<SubscriptionCardProps> = (props) => {
  const { business, dataSource, handleDelete, handleEdit } = props
  const businessConfig = useMemo(() => {
    return dataSource.filter(item => item.business === business)?.[0]
  }, [dataSource])
  return (
    <Card
      title={(
        <div className={buStyle.scanHeaderTitle}>
          <DepartmentIcon width={16} height={16} style={{ marginRight: 12 }} />
          {BusinessMap[business]}
        </div>
      )}
      bordered={false}
      bodyStyle={{ minHeight: 200 }}
      style={{ width: 400, border: '1px solid #f0f0f0d2', borderRadius: '5px' }}
      actions={[
        <EditTwoTone key={business} onClick={() => handleEdit(business, businessConfig)} />,
        <DeleteIcon key={business} className={!businessConfig?.meta.subscription ? buStyle.disabledIcon : ''} disable={!businessConfig} onClick={() => handleDelete?.(businessConfig, 'subscription')} />,
      ]}
    >
      {
        !businessConfig?.meta.subscription
          ? <Empty description="请添加订阅相关信息" />
          : (
            <div key={businessConfig?.id} className={buStyle.detailWrapper}>
              <div className={buStyle.detailRow}>
                <span className={buStyle.detailLabel}>BU: </span>
                <span className={buStyle.detailText}>{BusinessMap[businessConfig?.business]}</span>
              </div>
              <div className={buStyle.detailRow}>
                <span className={buStyle.detailLabel}>接口地址: </span>
                <span className={buStyle.detailText}>{businessConfig?.meta?.subscription?.path}</span>
              </div>
              <div className={buStyle.detailRow}>
                <span className={buStyle.detailLabel}>创建人:</span>
                <span className={buStyle.detailText}>{businessConfig?.creator_name}</span>
              </div>
              <div className={buStyle.detailRow}>
                <span className={buStyle.detailLabel}>更新时间:</span>
                <span className={buStyle.detailText}>{formatTime(businessConfig?.update_time)}</span>
              </div>
            </div>
          )
      }
    </Card>
  )
}

const ScanReportCard: FC<SubscriptionCardProps> = (props) => {
  const { business, dataSource, handleEdit, metaKey } = props
  const businessConfig = useMemo(() => {
    return dataSource.filter(item => item.business === business)?.[0]
  }, [dataSource])
  return (
    <Card
      title={(
        <div className={buStyle.scanHeader}>
          <div className={buStyle.scanHeaderTitle}>
            <DepartmentIcon width={16} height={16} style={{ marginRight: 12 }} />
            {BusinessMap[business]}
          </div>
          <Button size="small" type="primary" onClick={() => handleEdit(business, businessConfig)}>编辑</Button>
        </div>
      )}
      bordered={false}
      bodyStyle={{ minHeight: 200 }}
      style={{ width: '720px', border: '1px solid #f0f0f0d2', borderRadius: '5px' }}
    >
      {
        !businessConfig
          ? <Empty description="请添加代码扫描相关信息" />
          : (
            <Table
              columns={columns}
              scroll={{ y: 300 }}
              size="small"
              dataSource={businessConfig?.meta?.[metaKey]?.projects || []}
              pagination={false}
            />
          )
      }
    </Card>
  )
}
const Settings: FC = () => {
  const [subscription, setSubscription] = useState(undefined)
  const [dataSource, setDataSource] = useState<AnyType[]>([])

  const [modalVisible, setModalVisible] = useState(false)
  const [scanModalVisible, setScanModalVisible] = useState(false)
  const [materialModalVisible, setMaterialModalVisible] = useState(false)
  const [type, setType] = useState<'create' | 'edit'>('create')

  const handleEdit = useCallback((business, item) => {
    setType(item ? 'edit' : 'create')
    setSubscription({ ...(item ?? {}), business })
    setModalVisible(true)
  }, [])

  const handleEditScanConfig = useCallback((business, item) => {
    setType(item ? 'edit' : 'create')
    setSubscription({ ...(item ?? {}), business })
    setScanModalVisible(true)
  }, [])

  const handleEditMaterialConfig = useCallback((business, item) => {
    setType(item ? 'edit' : 'create')
    setSubscription({ ...(item ?? {}), business })
    setMaterialModalVisible(true)
  }, [])

  useEffect(() => {
    getBusinessConfig()
  }, [])

  const getBusinessConfig = () => {
    viewAllBusinessConfigUsingGET().then((res) => {
      if (res.dataSource) {
        setDataSource(res.dataSource)
      }
    })
  }

  const handleClose = () => {
    setType('create')
    setModalVisible(false)
    setScanModalVisible(false)
    setMaterialModalVisible(false)
  }

  const handleOk = () => {
    // 获取subscription
    setModalVisible(false)
    setScanModalVisible(false)
    setMaterialModalVisible(false)
    getBusinessConfig()
  }

  const handleDelete = useCallback(async (subscription, key: string) => {
    const res: AnyType = await deleteBusinessConfigUsingPOST({ id: subscription.id, key })
    if (res.code === 1) {
      message.success(res.message)
      getBusinessConfig()
    }
    else {
      message.error(res.message)
    }
  }, [])

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <ManageTitle>系统设置</ManageTitle>
      <Tabs defaultActiveKey="material-subscribe">
        <Tabs.TabPane tab="变更订阅" key="material-subscribe">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert type="info" message="管理订阅变更消息" showIcon />
            <div className={buStyle.subscriptionRowWrapper}>
              {
                businessOptions.map((item) => {
                  return (
                    <SubscriptionCard
                      handleDelete={handleDelete}
                      handleEdit={handleEdit}
                      key={item.value}
                      business={item.value}
                      dataSource={dataSource}
                    />
                  )
                })
              }
            </div>
            <EditSubscription subscription={subscription} modalVisible={modalVisible} mode={type} onClose={handleClose} onOK={handleOk} />
          </Space>
        </Tabs.TabPane>
        <Tabs.TabPane tab="项目扫描配置" key="gitlab_scan_manage">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert type="info" message="管理代码扫描信息" showIcon />
            <div className={buStyle.scanReportRowWrapper}>
              {
                businessOptions.map((item) => {
                  return (
                    <ScanReportCard
                      handleDelete={handleDelete}
                      handleEdit={handleEditScanConfig}
                      key={item.value}
                      business={item.value}
                      metaKey="report"
                      dataSource={dataSource}
                    />
                  )
                })
              }
            </div>
            <EditCodeScanReport
              dataSource={subscription}
              business={subscription?.business}
              modalVisible={scanModalVisible}
              mode={type}
              onClose={handleClose}
              onOK={handleOk}
            />
          </Space>
        </Tabs.TabPane>
        <Tabs.TabPane tab="物料扫描配置" key="material_scan_manage">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert type="info" message="管理物料扫描信息" showIcon />
            <div className={buStyle.scanReportRowWrapper}>
              {
                businessOptions.map((item) => {
                  return (
                    <ScanReportCard
                      handleEdit={handleEditMaterialConfig}
                      key={item.value}
                      business={item.value}
                      metaKey="material"
                      dataSource={dataSource}
                    />
                  )
                })
              }
            </div>
            <EditMaterialScan
              dataSource={subscription}
              business={subscription?.business}
              modalVisible={materialModalVisible}
              mode={type}
              onClose={handleClose}
              onOK={handleOk}
            />
          </Space>
        </Tabs.TabPane>
      </Tabs>
    </Space>

  )
}

export default Settings
