.cardBorder {
  border-radius: 5px;
  box-shadow: 0 0 0 2px var(--theme-color-blur);
  margin: 16px 0;
  padding: 16px;
  position: relative;

  .operateArea {
    position: absolute;
    bottom: 10px;
    right: 40px;
  }
}

.disabledIcon {
  cursor: not-allowed;
  color: #00000040;
  border-color: #d9d9d9;
  text-shadow: none;
  box-shadow: none;
}

.detailLabel {
  color: #646B73;
  font-family: PingFang SC, serif;
  flex: 0 0 auto;
}


.detailRow {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin-top: 8px;
  display: inline-flex;
}

.detailText {
  color: #1D2126;
  margin-left: 12px;
  word-break: break-all;
}

.detailWrapper {
  display: flex;
  flex-direction: column;
}

.scanHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.scanHeaderTitle {
  line-height: 25px;
  vertical-align: middle;
}

.subscriptionRowWrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;

  > :global(.ant-card) {
    margin-right: 36px;
    margin-bottom: 16px;
  }

  > :global(.ant-card):last-child {
    margin-right: 0;
  }
}

.scanReportRowWrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  > :global(.ant-card) {
    margin-right: 36px;
    margin-bottom: 16px;
  }
}

.headerInfo {
  margin-bottom: 16px;
}