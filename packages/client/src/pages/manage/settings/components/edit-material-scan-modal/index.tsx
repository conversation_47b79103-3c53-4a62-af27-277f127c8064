import React, { FC, useEffect, useState } from 'react'
import { Form, Input, message, Modal, Typography } from 'antd'
import { every, has, isEmpty } from 'lodash-es'
import { userModel } from '@/stores'
import { createBusinessConfigUsingPOST, updateBusinessConfigUsingPOST } from '@/services'

const { Paragraph } = Typography
export interface EditMaterialScanProps {
  dataSource?: { business: string, id: number, status: number, meta: AnyType } & Record<string, AnyType>
  modalVisible?: boolean
  business?: string
  mode?: 'create' | 'edit'
  onClose?: () => void
  onOK?: () => void
  onError?: (values) => void
}

const example = {
  projects: [
    {
      title: '标题',
      namespace: 'comlib-pc-normal',
      git_url: 'https://github.com/mybricks/comlib-pc-normal.git',
      router_entry: ['./src/*/', '所有组件的相对路径', '这里面是 glob 匹配语法'],
      component_folder_map: {
        _pick: 'mybricks.normal-pc.pick',
        _cookie: 'mybricks.normal-pc.cookie',
        组件文件夹名: '物料中台创建物料传递的 namespace',
      },
    },
  ],
}

const checkIsValid = (content: Record<string, AnyType>) => {
  if (!content.projects || !Array.isArray(content.projects)) {
    return false
  }

  /** 以下属性必填 */
  const propertiesToCheck = ['title', 'git_url', 'namespace']

  return every(content.projects, project => propertiesToCheck.every(property => has(project, property) && !isEmpty(project[property])))
}

const EditMaterialScan: FC<EditMaterialScanProps> = (props) => {
  const { modalVisible, dataSource, onClose, onOK, mode, business } = props
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    if (modalVisible === false) {
      form.setFieldsValue({})
    }
  }, [modalVisible, mode])

  const onCancel = () => {
    form.resetFields()
    onClose?.()
  }

  useEffect(() => {
    if (dataSource && mode === 'edit') {
      form.setFieldsValue({
        projectList: JSON.stringify(dataSource.meta?.material ?? {}, null, 2),
      })
    }
    else {
      form.setFieldsValue({
        projectList: undefined,
      })
    }
  }, [dataSource, mode])

  const handleOk = () => {
    form.validateFields().then(async (values) => {
      setLoading(true)
      const parsedContent = JSON.parse(values.projectList)
      const validContent = checkIsValid(parsedContent)

      if (!validContent) {
        setLoading(false)
        return message.error('格式不正确或者属性值为空，请检查')
      }
      // 创建时，如果dataSource存在更新原BU数据配置
      if (mode === 'create') {
        const createParams = { updater_id: userModel.user?.id, business: business, meta: { material: parsedContent } }

        createBusinessConfigUsingPOST({ param: createParams }).then((res: AnyType) => {
          if (res.code === 1) {
            message.success(res.message)
            onOK?.()
          }
          else {
            message.error(res.message)
          }
        })
      }
      else {
        const updateParams = { ...dataSource, meta: { ...dataSource.meta, material: parsedContent }, updater_id: userModel.user?.id }
        updateBusinessConfigUsingPOST({ param: updateParams as AnyType }).then((res: AnyType) => {
          if (res.code === 1) {
            message.success(res.message)
            onOK?.()
          }
          else {
            message.error(res.message)
          }
        })
      }
      setLoading(false)
    }).catch(() => {
      setLoading(false)
    })
  }

  return (
    <Modal title="代码仓库扫描配置" visible={modalVisible} onCancel={onCancel} width={600} onOk={handleOk} okButtonProps={{ loading }}>
      <Form form={form} labelCol={{ span: 5 }}>
        <Form.Item
          label="代码仓库列表"
          name="projectList"
          required
          rules={[{ required: true, message: '请填写', whitespace: false }]}
          extra={(
            <Paragraph copyable={{ text: JSON.stringify(example) }}>
              JSON数据结构(可拷贝示例数据):
              <br />
              {' '}
              {JSON.stringify(example, null, 2)}
            </Paragraph>
          )}
        >
          <Input.TextArea
            style={{ marginTop: 10 }}
            rows={8}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default EditMaterialScan
