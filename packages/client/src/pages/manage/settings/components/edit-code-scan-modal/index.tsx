import { FC, useEffect, useState } from 'react'
import { Form, Input, message, Modal, Typography } from 'antd'
import { every, has, isEmpty } from 'lodash-es'
import { userModel } from '@/stores'
import { createBusinessConfigUsingPOST, updateBusinessConfigUsingPOST } from '@/services'

const { Paragraph } = Typography
export interface EditCodeScanReportsProps {
  dataSource?: { business: string, id: number, status: number, meta: AnyType } & Record<string, AnyType>
  modalVisible?: boolean
  business?: string
  mode?: 'create' | 'edit'
  onClose?: () => void
  onOK?: () => void
  onError?: (values) => void
}

const example = {
  projects: [
    {
      title: '标题',
      namespace: 'tianhe-designer',
      git_url: 'https://git.corp.kuaishou.com/plateco-dev-fe/kwaishop-tech/tianhe/tianhe-designer.git',
      router_entry: ['./packages/components'],
    },
  ],
}

const checkIsValid = (content: Record<string, AnyType>) => {
  if (!content.projects || !Array.isArray(content.projects)) {
    return false
  }

  /** 以下属性必填 */
  const propertiesToCheck = ['title', 'git_url', 'namespace']

  return every(content.projects, project => propertiesToCheck.every(property => has(project, property) && !isEmpty(project[property])))
}

const EditCodeScanReport: FC<EditCodeScanReportsProps> = (props) => {
  const { modalVisible, dataSource, onClose, onOK, mode, business } = props
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    if (modalVisible === false) {
      form.setFieldsValue({})
    }
  }, [modalVisible, mode])

  const onCancel = () => {
    form.resetFields()
    onClose?.()
  }

  useEffect(() => {
    if (dataSource && mode === 'edit') {
      const reports = dataSource.meta?.report
      form.setFieldsValue({
        projectList: JSON.stringify(reports, null, 2),
      })
    }
    else {
      form.setFieldsValue({
        projectList: undefined,
      })
    }
  }, [dataSource, mode])

  const handleOk = () => {
    form.validateFields().then(async (values) => {
      setLoading(true)
      const parsedContent = JSON.parse(values.projectList)
      const validContent = checkIsValid(parsedContent)

      if (!validContent) {
        setLoading(false)
        return message.error('格式不正确或者属性值为空，请检查')
      }
      // 创建时，如果dataSource存在更新原BU数据配置
      if (mode === 'create') {
        const createParams = { updater_id: userModel.user?.id, business: business, meta: { report: parsedContent } }

        createBusinessConfigUsingPOST({ param: createParams }).then((res: AnyType) => {
          if (res.code === 1) {
            message.success(res.message)
            onOK?.()
          }
          else {
            message.error(res.message)
          }
        })
      }
      else {
        const updateParams = { ...dataSource, meta: { ...dataSource.meta, report: parsedContent }, updater_id: userModel.user?.id }
        updateBusinessConfigUsingPOST({ param: updateParams as AnyType }).then((res: AnyType) => {
          if (res.code === 1) {
            message.success(res.message)
            onOK?.()
          }
          else {
            message.error(res.message)
          }
        })
      }
      setLoading(false)
    }).catch(() => {
      setLoading(false)
    })
  }

  return (
    <Modal title="代码仓库扫描配置" visible={modalVisible} onCancel={onCancel} width={600} onOk={handleOk} okButtonProps={{ loading }}>
      <Form form={form} labelCol={{ span: 5 }}>
        <Form.Item
          label="代码仓库列表"
          name="projectList"
          required
          rules={[{ required: true, message: '请填写', whitespace: false }]}
          extra={(
            <Paragraph copyable={{ text: JSON.stringify(example) }}>
              JSON数据结构(可拷贝示例数据):
              <br />
              {JSON.stringify(example, null, 2)}
            </Paragraph>
          )}
        >
          <Input.TextArea
            style={{ marginTop: 10 }}
            rows={8}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default EditCodeScanReport
