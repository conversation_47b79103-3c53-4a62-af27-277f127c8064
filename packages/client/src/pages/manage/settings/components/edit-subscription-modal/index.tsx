import { FC, useEffect, useState } from 'react'
import { Form, Input, message, Modal, Select } from 'antd'
import { businessOptions } from '@/constants'
import { userModel } from '@/stores'
import { createBusinessConfigUsingPOST, updateBusinessConfigUsingPOST } from '@/services'

export interface EditSubscriptionProps {
  subscription?: Record<string, AnyType>
  modalVisible?: boolean
  mode?: 'create' | 'edit'
  onClose?: () => void
  onOK?: () => void
  onError?: (values) => void
}

const EditSubscription: FC<EditSubscriptionProps> = (props) => {
  const { modalVisible, subscription, onClose, onOK, mode } = props
  const [loading, setLoading] = useState(false)

  const [form] = Form.useForm()
  const urlPattern = /^(http|https):\/\/[^ "]+$/

  useEffect(() => {
    if (modalVisible === false) {
      form.setFieldsValue({})
    }
  }, [modalVisible, mode])

  useEffect(() => {
    if (subscription && mode === 'edit') {
      form.setFieldsValue({
        business: subscription?.business,
        path: subscription?.meta?.subscription?.path,
      })
    }
    else {
      form.setFieldsValue({
        business: subscription?.business,
        path: undefined,
      })
    }
  }, [subscription, mode])

  const handleOk = () => {
    form.validateFields().then(async (values) => {
      setLoading(true)
      const params = mode === 'create' ? values : { ...subscription, business: values.business, meta: { subscription: { path: values.path } }, updater_id: userModel.user?.id }
      if (mode === 'create') {
        const createParams = { updater_id: userModel.user?.id, business: values.business, meta: { subscription: { path: values.path } } }
        createBusinessConfigUsingPOST({ param: createParams }).then((res: AnyType) => {
          if (res.code === 1) {
            message.success(res.message)
            onOK?.()
          }
          else {
            message.error(res.message)
          }
        })
      }
      else {
        updateBusinessConfigUsingPOST({ param: params }).then((res: AnyType) => {
          if (res.code === 1) {
            message.success(res.message)
            onOK?.()
          }
          else {
            message.error(res.message)
          }
        })
      }
      setLoading(false)
    }).catch(() => {
      setLoading(false)
    })
  }

  return (
    <Modal title="扩展订阅接口" visible={modalVisible} onCancel={onClose} width={600} onOk={handleOk} okButtonProps={{ loading }}>
      <Form form={form} labelCol={{ span: 5 }}>
        <Form.Item label="物料生产方" name="business" required rules={[{ required: true, message: '请选择所属BU' }]}>
          <Select placeholder="请选择物料生产方" disabled={true}>
            {businessOptions.map((b) => {
              return <Select.Option key={b.value} value={b.value}>{b.label}</Select.Option>
            })}
          </Select>
        </Form.Item>
        <Form.Item
          name="path"
          label="接口地址"
          rules={[{ required: true, message: '接口地址不能为空' }, {
            pattern: urlPattern,
            message: '请输入有效的URL地址',
          }]}
        >
          <Input placeholder="输入接收消息的接口地址" />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default EditSubscription
