import React, { useMemo } from 'react'
import ReactECharts, { EChartsOption } from 'echarts-for-react'
import { SOURCE_CODE_BUSINESS } from '@global-material-middleoffice/server-v2/shared'

import { MaterialListWithTime } from './GradeOfMaturity'
import { MATERIAL_POINTS } from './MaterialPointsDialog'

import styles from './style.module.less'

interface Props {
  business: SOURCE_CODE_BUSINESS
  rawData: MaterialListWithTime[]
}

const BusinessLineChart: React.FC<Props> = (props) => {
  const { business, rawData } = props
  const currentBusinessChartData = useMemo(() => {
    const pointsBooks: { department: string, count: number, points: number, time: number }[] = []
    for (const item of rawData) {
      if (item.materials.some(material => material.business === business)) {
        const currentBusinessItems = item.materials.filter(
          material => material.business === business,
        )
        currentBusinessItems.forEach((material) => {
          const currentDepartment = material.department || '未知部门'
          const sumCodeLines = material.codeLines * material.count
          const point = MATERIAL_POINTS.find(
            point =>
              sumCodeLines >= point.NC.min
              && sumCodeLines < (point.NC.max || Infinity),
          )
          if (point) {
            const department = pointsBooks.find(
              b => b.department === currentDepartment && b.time === item.time,
            )
            if (department) {
              department.points += point.points
              department.count += 1
            }
            else {
              pointsBooks.push({
                department: currentDepartment,
                count: 1,
                points: point.points,
                time: item.time,
              })
            }
          }
        })
      }
    }
    return pointsBooks
  }, [rawData, business])

  const chartOptions = useMemo<EChartsOption>(() => {
    const departments = Array.from(new Set(currentBusinessChartData.map(item => item.department)))
    const series: EChartsOption['series'] = departments.map(dept => ({
      name: dept,
      type: 'line',
      smooth: true,
      symbol: 'circle', // 数据点的形状
      symbolSize: 8, // 数据点的大小
      label: {
        show: true,
        position: 'top',

        formatter: function (params) {
          return params.value[1]
        },
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        padding: [4, 6],
        borderRadius: 4,
      },
      data: currentBusinessChartData
        .filter(item => item.department === dept)
        .sort((a, b) => a.time - b.time)
        .map(item => [item.time, item.points]),
    }))

    return {
      title: {
        text: '各小组成熟度趋势图',
        left: 'center',
        top: 20, // 增加顶部距离
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
        },
        padding: [0, 0, 20, 0], // 上右下左的内边距
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          const date = new Date(params[0].value[0])
          let result = `${date.toLocaleString()}<br/>`
          params.forEach((param) => {
            result += `${param.seriesName}: ${param.value[1]}分<br/>`
          })
          return result
        },
      },
      legend: {
        type: 'scroll', // 启用滚动
        bottom: 0, // 位于底部
        left: 'center', // 水平居中
        height: 50, // 限制图例区域高度
        padding: [0, 20], // 增加左右内边距
        itemGap: 20, // 图例项之间的间距
        pageButtonPosition: 'end', // 翻页按钮位置
        data: departments,
        formatter: function (name: string) {
          // 如果名称过长，截断并添加省略号
          return name.length > 10 ? name.slice(0, 10) + '...' : name
        },
      },
      grid: {
        left: 50,
        right: 50,
        bottom: 100,
        top: 100,
        containLabel: true,
      },
      xAxis: {
        type: 'time',
        name: '时间',
        axisLabel: {
          formatter: function (value: number) {
            const date = new Date(value)
            return `${date.getMonth() + 1}/${date.getDate()}`
          },
        },
      },
      yAxis: {
        type: 'value',
        name: '成熟度',
      },
      series,
    }
  }, [currentBusinessChartData])

  return (
    <div className={styles['line-chart']}>
      <ReactECharts
        notMerge={true}
        style={{ height: '100%' }}
        option={chartOptions}
      />
    </div>
  )
}

export default BusinessLineChart
