import React, { useEffect, useState } from 'react'
import { Divide<PERSON>, Tabs } from 'antd'
import axios from 'axios'
import dayjs from 'dayjs'
import { SOURCE_CODE_BUSINESS } from '@global-material-middleoffice/server-v2/shared'

import BusinessLineChart from './BusinessLineChart'
import BusinessTimeDetailPanel from './BusinessTimeDetailPanel'
import { fetchMaterialPointsList } from '../../../../services/v2/material-points'
import ManageTitle from '@/components/manage-title/ManageTitle'

import styles from './style.module.less'
import AutoFullParentLoading from '@/components/auto-full-parent-loading'

export type MaterialItem = {
  id: number
  library: string
  business: string
  businessName: string
  materialName: string
  codeLines: number
  count: number
  department: string
  color: string
  [key: string]: string | number | object
}

export type MaterialListWithTime = {
  time: number
  timeDisplay: string
  materials: MaterialItem[]
}

type CDNRawData = {
  id: number
  start_time: number
  end_time: number
  display_txt: string
  usage: Array<{
    library: string
    business: string
    materials: Array<{
      materialId: number
      name: string
      codeLines: number
      count: number
      directUseCount: number
      indirectUseCount: number
      thirdPartyUsage: object
      department: string
    }>
  }>
}

const BUSINESS_MAPPER = {
  [SOURCE_CODE_BUSINESS.ES]: '电商',
  [SOURCE_CODE_BUSINESS.BIZ]: '商业化',
  [SOURCE_CODE_BUSINESS.LOCAL_LIFE]: '本地前端',
  [SOURCE_CODE_BUSINESS.LOCAL_LIFE_CLIENT]: '本地生活客户端',
}

const BUSINESS_COLORS = {
  [SOURCE_CODE_BUSINESS.ES]: '#5B8FF9',
  [SOURCE_CODE_BUSINESS.BIZ]: '#5AD8A6',
  [SOURCE_CODE_BUSINESS.LOCAL_LIFE]: '#E0A2FF',
  [SOURCE_CODE_BUSINESS.LOCAL_LIFE_CLIENT]: '#F6BD16',
}

const GradeOfMaturity: React.FC = () => {
  // ======================== 原始数据 ========================
  const [loading, setLoading] = useState(false)
  const [normalizeMaterialDataWithDate, setNormalizeMaterialDataWithDate]
    = useState<MaterialListWithTime[]>([])
  useEffect(() => {
    setLoading(true)
    fetchMaterialPointsList()
      .then((res) => {
        const resources = res.data.data
        Promise.all(
          resources.map(item =>
            axios.get(item.content).then(res => res.data),
          ),
        ).then((list: CDNRawData[]) => {
          const result: MaterialListWithTime[] = []
          for (let i = 0; i < list.length; i++) {
            const analysisResultWithData = list[i]
            const currentDateResultItem = {
              time: analysisResultWithData.start_time,
              timeDisplay: `${dayjs(analysisResultWithData.start_time).format('YYYY-MM-DD')} ~ ${dayjs(
                analysisResultWithData.end_time,
              ).format('YYYY-MM-DD')}`,
              materials: [] as MaterialItem[],
            }
            analysisResultWithData.usage.forEach((item) => {
              const business = item.business as keyof typeof BUSINESS_MAPPER
              item.materials.forEach((material) => {
                currentDateResultItem.materials.push({
                  ...material,
                  id: material.materialId,
                  library: item.library,
                  business: item.business,
                  businessName: BUSINESS_MAPPER[business],
                  materialName: material.name,
                  fullName: `${item.library}/${material.name}`,
                  limitCodeLines: material.codeLines > 5000 ? 5000 : material.codeLines,
                  limitCount: material.count > 1000 ? 1000 : material.count,
                  color: BUSINESS_COLORS[business],
                })
              })
            })
            result.push(currentDateResultItem)
          }
          setNormalizeMaterialDataWithDate(result)
        })
      })
      .finally(() => {
        setTimeout(() => setLoading(false), 1500)
      })
  }, [])

  // ======================== 当前业务数据 ========================
  const [currentBusiness, setCurrentBusiness] = useState<SOURCE_CODE_BUSINESS>(SOURCE_CODE_BUSINESS.ES)

  // ======================== 详情看板时间戳 ========================
  const [detailPanelTimeStamp, setDetailPanelTimeStamp] = useState<number>()

  return (
    <div className={styles['page-container']}>
      <ManageTitle>物料成熟度</ManageTitle>
      <Tabs activeKey={currentBusiness} onChange={value => setCurrentBusiness(value as SOURCE_CODE_BUSINESS)}>
        {Array.from(Object.entries(BUSINESS_MAPPER)).map(([value, label]) => (
          <Tabs.TabPane key={value} tab={label} />
        ))}
      </Tabs>
      <div className={styles['business-content']}>
        <AutoFullParentLoading loading={loading} />
        <BusinessLineChart
          business={currentBusiness}
          rawData={normalizeMaterialDataWithDate}
        />
        <Divider type="vertical" style={{ height: '100%' }} />
        <BusinessTimeDetailPanel
          business={currentBusiness}
          rawData={normalizeMaterialDataWithDate}
          timestamp={detailPanelTimeStamp}
          onTimestampChange={setDetailPanelTimeStamp}
        />
      </div>
    </div>
  )
}

export default GradeOfMaturity
