import { EyeOutlined } from '@ant-design/icons'
import { Button, Modal, Table } from 'antd'
import React, { useState } from 'react'

export const MATERIAL_POINTS = [
  { NC: { min: 0, max: 2 }, points: 0 },
  { NC: { min: 2, max: 2500 }, points: 1 },
  { NC: { min: 2500, max: 5000 }, points: 2 },
  { NC: { min: 5000, max: 10000 }, points: 3 },
  { NC: { min: 10000, max: 20000 }, points: 4 },
  { NC: { min: 20000, max: 50000 }, points: 5 },
  { NC: { min: 50000, max: 100000 }, points: 6 },
  { NC: { min: 100000, max: 200000 }, points: 7 },
  { NC: { min: 200000, max: 300000 }, points: 8 },
  { NC: { min: 300000, max: 400000 }, points: 9 },
  { NC: { min: 400000, max: 500000 }, points: 10 },
  { NC: { min: 500000 }, points: 11 },
]
const MaterialPointsDialog: React.FC = () => {
  const [visible, setVisible] = useState(false)
  return (
    <>
      <Button icon={<EyeOutlined />} onClick={() => setVisible(true)}>指标分计算表</Button>
      <Modal title="指标分计算表" visible={visible} onCancel={() => setVisible(false)} footer={null}>
        <Table
          size="small"
          pagination={false}
          dataSource={MATERIAL_POINTS}
          columns={[
            {
              title: 'NC',
              dataIndex: 'NC',
              key: 'NC',
              render: value => `${value.min} - ${value.max || '∞'}`,
            },
            { title: 'Points', dataIndex: 'points', key: 'points' },
          ]}
        />
      </Modal>
    </>
  )
}

export default MaterialPointsDialog
