import React, { useMemo } from 'react'
import { Select, Table, Typography } from 'antd'
import { SOURCE_CODE_BUSINESS } from '@global-material-middleoffice/server-v2/shared'
import { NOOP } from '@/utils/function'

import { MATERIAL_POINTS } from './MaterialPointsDialog'
import { MaterialListWithTime } from './GradeOfMaturity'

import styles from './style.module.less'

interface Props {
  business: SOURCE_CODE_BUSINESS
  timestamp?: number
  onTimestampChange?: (timestamp: number) => void
  rawData: MaterialListWithTime[]
}

const BusinessTimeDetailPanel: React.FC<Props> = (props) => {
  const { business, rawData, timestamp = rawData?.[0]?.time, onTimestampChange = NOOP } = props
  const currentBusinessAndTimeData = useMemo(() => {
    const currentTimeData = rawData.find(item => item.time === timestamp)
    if (!currentTimeData) return []
    const currentBusinessItems = currentTimeData.materials.filter(
      item => item.business === business,
    )
    const pointsBooks: { department: string, count: number, points: number }[] = []
    currentBusinessItems.forEach((item) => {
      const currentDepartment = item.department || '未知部门'
      const sumCodeLines = item.codeLines * item.count
      const point = MATERIAL_POINTS.find(
        point =>
          sumCodeLines >= point.NC.min
          && sumCodeLines < (point.NC.max || Infinity),
      )
      if (point) {
        const department = pointsBooks.find(
          b => b.department === currentDepartment,
        )
        if (department) {
          department.points += point.points
          department.count += 1
        }
        else {
          pointsBooks.push({
            department: currentDepartment,
            count: 1,
            points: point.points,
          })
        }
      }
    })
    return pointsBooks.sort((a, b) => b.department === '未知部门' ? -1 : b.points - a.points)
  }, [rawData, timestamp, business])

  return (
    <div className={styles['detail-panel']}>
      <Typography.Title level={5}>单周详情</Typography.Title>
      <Select
        style={{ width: 400 }}
        value={timestamp}
        onChange={(value) => {
          onTimestampChange(value)
        }}
      >
        {rawData.map(option => (
          <Select.Option key={option.time} value={option.time}>
            {option.timeDisplay}
          </Select.Option>
        ))}
      </Select>

      <div className={styles['table']}>
        <Table
          key={timestamp}
          size="small"
          pagination={false}
          dataSource={currentBusinessAndTimeData}
          columns={[
            { title: '小组', dataIndex: 'department', key: 'department' },
            { title: '个数', dataIndex: 'count', key: 'count', width: 80 },
            { title: '成熟度', dataIndex: 'points', key: 'points', width: 80 },
          ]}
        />
      </div>
    </div>
  )
}

export default BusinessTimeDetailPanel
