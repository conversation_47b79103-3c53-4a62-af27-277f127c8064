.page-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  overflow: hidden;
  min-height: 600px;
}

.business-content {
  display: flex;
  gap: 12px;
  height: 100%;
  overflow: hidden;
  position: relative;

  .line-chart {
    flex: 1
  }

  .detail-panel {
    width: 400px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 100%;
    overflow: hidden;

    .table {
      overflow: auto;
    }
  }
}
