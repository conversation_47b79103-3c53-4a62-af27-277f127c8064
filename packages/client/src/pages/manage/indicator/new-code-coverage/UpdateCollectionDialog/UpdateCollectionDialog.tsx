import {
  Button,
  Form,
  Modal,
  Input,
  message,
  DatePicker,
  DatePickerProps,
  Switch,
} from 'antd'
import React, { useState, useEffect, useMemo } from 'react'
import dayjs from 'dayjs'
import { updateProjectCollection } from '@/services/v2/project-collection'
import { ButtonProps } from 'antd/lib'
import {
  EditOutlined,
  MinusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import {
  MaterialSelect,
  ProjectSelect,
} from '../AddCollectionDialog/AddCollectionDialog'
import { SOURCE_CODE_BUSINESS } from '@global-material-middleoffice/server-v2/shared'

interface Props extends ButtonProps {
  business: SOURCE_CODE_BUSINESS
  initialValues: Service.Analysis.ProjectCollection.ProjectCollection
  onUpdate?: () => void
}

const UpdateCollectionDialog: React.FC<Props> = (props) => {
  const { onUpdate, initialValues, business, ...buttonProps } = props

  const realInitialValues = useMemo(() => {
    return {
      ...initialValues,
      startTime: initialValues?.start_time
        ? dayjs(initialValues.start_time)
        : undefined,
      endTime: initialValues?.end_time
        ? dayjs(initialValues.end_time)
        : undefined,
      projects: initialValues?.projects ?? [],

      payload: Object.assign(
        {},
        { realTimeLimit: false },
        initialValues?.payload,
      ),
    }
  }, [initialValues])

  // 表单
  const [formInstance] = Form.useForm()
  const [submitting, setSubmitting] = useState(false)
  const handleSubmit = async () => {
    setSubmitting(true)
    try {
      const values = await formInstance.validateFields()
      const {
        name,
        projects = [],
        materials = [],
        startTime,
        endTime,
        payload,
      } = values

      await updateProjectCollection({
        id: initialValues.id,
        name,
        tag: ['source_code'],
        business,
        startTime: startTime?.valueOf(),
        endTime: endTime?.valueOf(),
        projects,
        materials,
        payload,
      })
      message.success('更新成功')
      onUpdate?.()
      setOpen(false)
    }
    finally {
      setSubmitting(false)
    }
  }

  // 弹窗
  const [open, setOpen] = useState(false)
  useEffect(() => {
    if (open) {
      setTimeout(() => formInstance.resetFields())
    }
  }, [open, formInstance, realInitialValues])

  const disabledStartDate: DatePickerProps['disabledDate'] = (current) => {
    const dayjsCurrent = dayjs(current.valueOf())
    return dayjsCurrent && dayjsCurrent.isBefore(dayjs('2024-01-01'))
  }

  const disabledEndDate: DatePickerProps['disabledDate'] = (current) => {
    const startDate = formInstance.getFieldValue('startTime')
    return startDate && current && current.isBefore(startDate, 'day')
  }

  return (
    <>
      <Button
        icon={<EditOutlined />}
        onClick={() => setOpen(true)}
        {...buttonProps}
      >
        编辑
      </Button>
      <Modal
        title="更新源码项目看板"
        width={600}
        visible={open}
        onCancel={() => setOpen(false)}
        onOk={handleSubmit}
        confirmLoading={submitting}
        cancelButtonProps={{ disabled: submitting }}
        closable={submitting}
        cancelText="取消"
        okText="更新"
      >
        <Form
          form={formInstance}
          labelAlign="left"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          initialValues={realInitialValues}
        >
          <Form.Item
            name="name"
            label="项目集合名称"
            rules={[{ required: true, message: '请输入项目集合名称' }]}
          >
            <Input disabled={submitting} placeholder="请输入项目集合名称" />
          </Form.Item>
          <Form.Item
            name="startTime"
            label="看板开始时间"
            initialValue={dayjs('2024-01-01')}
            rules={[{ required: true, message: '请选择开始时间' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              disabled={submitting}
              placeholder="开始时间"
              disabledDate={disabledStartDate}
            />
          </Form.Item>
          <Form.Item name="endTime" label="结束时间">
            <DatePicker
              style={{ width: '100%' }}
              disabled={submitting}
              placeholder="请选择结束时间，如果为空则不限定结束时间"
              disabledDate={disabledEndDate}
            />
          </Form.Item>
          <Form.Item name="projects" label="项目列表">
            <ProjectSelect placeholder="请选择项目，如果为空则默认选择所有项目" />
          </Form.Item>
          <Form.Item name="materials" label="物料列表">
            <MaterialSelect placeholder="请选择物料，如果为空则默认选择所有物料" />
          </Form.Item>
          <Form.List name={['payload', 'ignoredLibraries']} initialValue={['']}>
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, fieldKey, ...restField }) => (
                  <Form.Item
                    key={key}
                    label={name === 0 ? '忽略的库' : ''}
                    required={false}
                    {...(name === 0
                      ? { labelCol: { span: 8 }, wrapperCol: { span: 16 } }
                      : { wrapperCol: { offset: 8, span: 16 } })}
                  >
                    <Form.Item
                      {...restField}
                      noStyle
                      name={[name]}
                      fieldKey={[fieldKey]}
                      rules={[
                        {
                          required: name !== 0,
                          message: '请输入库名称或删除此字段',
                        },
                      ]}
                    >
                      <Input
                        placeholder="请输入库名称"
                        style={{ width: '80%' }}
                      />
                    </Form.Item>
                    {fields.length > 1
                      ? (
                        <Button
                          type="link"
                          onClick={() => remove(name)}
                          icon={<MinusCircleOutlined />}
                        />
                      )
                      : null}
                  </Form.Item>
                ))}
                <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    icon={<PlusOutlined />}
                  >
                    添加忽略的库
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
          <Form.Item
            name={['payload', 'realTimeLimit']}
            label="是否开启物料行数上限"
            tooltip="开启后，在周期内，物料行数会存在上限。反之则无上限限制"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default UpdateCollectionDialog
