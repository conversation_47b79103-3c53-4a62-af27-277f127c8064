import dayjs from 'dayjs'
import { EChartsOption } from 'echarts'
import ReactECharts, { EChartsInstance } from 'echarts-for-react'
import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import { isArray, isObject } from '@global-material-middleoffice/server-v2/shared'

interface ChartDataItem {
  scanId: number
  time: string
  value: number
  newCodeLines: number
  newMaterialCodeLines: number
  cumulativeNewCodeLines: number
  cumulativeNewMaterialCodeLines: number
}

const DualAxesChart: React.FC<{
  chartData: ChartDataItem[]
  selectedScanId?: number
  onSelectChange: (scanId: number) => void
}> = (props) => {
  const { chartData, selectedScanId, onSelectChange } = props
  const chartRef = useRef<EChartsInstance>()

  useEffect(() => {
    const instance = chartRef.current?.getEchartsInstance()
    if (instance) {
      if (selectedScanId === undefined) {
        instance.setOption({
          series: [
            {
              itemStyle: {
                color: () => '#2989FF',
              },
            },
          ],
        })
      }
      else {
        const colors = chartData.map((_, idx) =>
          chartData[idx].scanId === selectedScanId ? '#2989FF' : '#2989FF33',
        )
        instance.setOption({
          series: [
            {
              // Bar series
              itemStyle: {
                color: (params: { dataIndex: number }) => colors[params.dataIndex],
              },
            },
          ],
        })
      }
    }
  }, [selectedScanId, chartData])

  const onChartClick = useCallback(
    (params: { componentType: string, dataIndex: number }) => {
      if (params.componentType === 'series') {
        const newIndex = params.dataIndex
        const currentSelectedScanId = chartData[newIndex].scanId
        onSelectChange(currentSelectedScanId)
      }
    },
    [chartData, onSelectChange],
  )

  const option: EChartsOption = useMemo<EChartsOption>(() => {
    const times = chartData.map(item => item.time)
    const values = chartData.map(item => item.value)
    const newCodeLines = chartData.map(item => item.newCodeLines)
    const newMaterialCodeLines = chartData.map(
      item => item.newMaterialCodeLines,
    )
    const cumulativeNewCodeLines = chartData.map(
      item => item.cumulativeNewCodeLines,
    )
    const cumulativeNewMaterialCodeLines = chartData.map(
      item => item.cumulativeNewMaterialCodeLines,
    )
    return {
      legend: {
        data: [
          '新增代码覆盖率',
          '每周新增业务代码行数',
          '每周新增物料代码行数',
        ],
        top: 0,
      },
      xAxis: {
        type: 'category',
        data: times,
        name: '时间',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontWeight: 'bold',
          color: 'black',
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '新增代码覆盖率',
          min: 0,
          max: 100,
          position: 'left',
          axisLabel: {
            formatter: '{value} %',
          },
          nameTextStyle: {
            fontWeight: 'bold',
            color: 'black',
          },
          nameGap: 20,
        },
        {
          type: 'value',
          name: '代码行数',
          min: 0,
          position: 'right',
          axisLabel: {
            formatter: '{value}',
          },
          nameTextStyle: {
            fontWeight: 'bold',
            color: 'black',
          },
          nameGap: 20,
        },
      ],
      series: [
        {
          name: '新增代码覆盖率（柱状图）',
          type: 'bar',
          data: values,
          barMaxWidth: 50,
          barWidth: '60%',
          itemStyle: {
            color: '#2989FF',
          },
          z: 10,
        },
        {
          name: '新增代码覆盖率',
          type: 'line',
          data: values,
          smooth: true,
          itemStyle: {
            color: '#2989FF',
          },
          lineStyle: {
            width: 3,
            color: '#2989FF',
          },
          z: 11,
        },
        {
          name: '每周新增物料代码行数',
          type: 'line',
          data: newMaterialCodeLines,
          yAxisIndex: 1,
          smooth: true,
          itemStyle: {
            color: '#67c23a',
          },
          lineStyle: {
            width: 2,
            color: '#67c23a',
          },
          z: 11,
        },
        {
          name: '每周新增业务代码行数',
          type: 'line',
          data: newCodeLines,
          yAxisIndex: 1,
          smooth: true,
          itemStyle: {
            color: '#f56c6c',
          },
          lineStyle: {
            width: 1,
            color: '#f56c6c',
          },
          z: 11,
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params) {
          if (
            isArray(params)
            && isObject(params[0])
            && 'axisValue' in params[0]
            && 'data' in params[0]
            && 'dataIndex' in params[0]
          ) {
            const items = params as unknown as { axisValue: string, data: number, dataIndex: number }[]
            return `
              <span>${dayjs(items[0].axisValue)
                .add(-6, 'day')
                .format('YYYY-MM-DD')} ~</span>
              <div style="font-size: 17px; font-weight: bold;">${items[0].axisValue}</div>
              <div style="margin: 0; margin-top: 6px;">
                <div style="font-size: 18px; font-weight: bold; margin: 16px 0 10px 0;">新增代码覆盖率: &nbsp;<span style="color: #2989FF;">${items[0].data}%</span></div>
                <span>---</span><br />
                <b>当周新增物料代码行数: &nbsp;<span style="color: #67c23a;">+ ${items[2].data} 行</span></b><br />
                <b>当周新增业务代码行数: &nbsp;<span style="color: #f56c6c;">+ ${items[3].data} 行</span></b><br />
                <span>---</span><br />
                <b>累计新增物料代码行数: &nbsp;<span style="color: #67c23a;">${cumulativeNewMaterialCodeLines?.[items[2].dataIndex] ?? 0} 行</span></b><br />
                <b>累计新增业务代码行数: &nbsp;<span style="color: #f56c6c;">${cumulativeNewCodeLines?.[items[3].dataIndex] ?? 0} 行</span></b><br />
              </div>
              `
          }
          return ''
        },
      },
      grid: {
        top: '60px',
        left: '10px',
        right: '40px',
        bottom: '50px',
        containLabel: true,
      },
      responsive: true,
      maintainAspectRatio: false,
    }
  }, [chartData])

  return (
    <ReactECharts
      ref={chartRef}
      option={option}
      style={{ height: '400px', width: '100%' }}
      opts={{ renderer: 'svg' }}
      onEvents={{ click: onChartClick }}
    />
  )
}

export default DualAxesChart
