import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import axios from 'axios'
import clsx from 'clsx'
import dayjs from 'dayjs'
import { get } from 'lodash-es'
import NP from 'number-precision'
import { Alert, Card, Radio, Select, Space, Spin } from 'antd'
import { BUSINESS, SOURCE_CODE_BUSINESS } from '@global-material-middleoffice/server-v2/shared'

import { isAdmin } from '@/utils'
import {
  computeNewCodeCoverage,
  queryProjectCollectionDetail,
} from '@/services/v2/project-collection'

import DualAxesChart from './DualAxesChart'
import ProjectOverview from '../ProjectOverview'
import UpdateCollectionDialog from '../UpdateCollectionDialog'

import styles from './style.module.less'

interface Props {
  business: SOURCE_CODE_BUSINESS
  collectionId: number
  refreshList?: () => void
}

const KanbanDetail: React.FC<Props> = ({ collectionId, business, refreshList }) => {
  const [loading, setLoading] = useState(false)
  const [collectionDetail, setCollectionDetail]
    = useState<Service.Analysis.ProjectCollection.ProjectCollection>()
  const realTimeLimit = useMemo<boolean>(() => !!get(collectionDetail, 'payload.realTimeLimit', false), [collectionDetail])

  const fetchProjectCollectionDetail = async (collectionId: number) => {
    return queryProjectCollectionDetail(collectionId).then((res) => {
      setCollectionDetail(res.data.data)
    })
  }

  useEffect(() => {
    fetchProjectCollectionDetail(collectionId)
  }, [collectionId])

  const [newCodeCoverages, setNewCodeCoverages] = useState<
    Service.Analysis.ProjectCollection.CollectionNewCodeCoverageItem[]
  >([])
  useEffect(() => {
    setLoading(true)
    computeNewCodeCoverage(collectionId)
      .then(async (res) => {
        let data: Service.Analysis.ProjectCollection.CollectionNewCodeCoverageItem[]
          = []
        if (
          typeof res.data.data === 'string'
          && res.data.data.startsWith('http')
        ) {
          data = (await axios.get(res.data.data)).data
        }
        else {
          data = res.data.data
        }
        setNewCodeCoverages(
          data.sort((a, b) => a.scan.start_time! - b.scan.start_time!),
        )
      })
      .finally(() => {
        setLoading(false)
      })
  }, [collectionId])

  // ======================== dual axes chart data ========================
  const [selectedScanId, setSelectedScanId] = useState<number>()
  const onSelectedScanIdChange = useCallback(
    (nextScanId: number) => {
      setSelectedScanId((pre) => {
        if (nextScanId === pre) {
          return undefined
        }
        else {
          return nextScanId
        }
      })
    },
    [setSelectedScanId],
  )
  const chartData = useMemo(() => {
    const chartData: {
      scanId: number
      time: string
      value: number
      newCodeLines: number
      newMaterialCodeLines: number
      cumulativeNewCodeLines: number
      cumulativeNewMaterialCodeLines: number
    }[] = []

    let cumulativeNewCodeLines = 0
    let cumulativeNewMaterialCodeLines = 0
    for (const item of newCodeCoverages) {
      cumulativeNewCodeLines += item.totalNewCodeLines
      cumulativeNewMaterialCodeLines += item.totalNewMaterialCodeLines
      const value = NP.round(
        NP.divide(
          cumulativeNewMaterialCodeLines,
          NP.plus(cumulativeNewCodeLines, cumulativeNewMaterialCodeLines),
        ) * 100,
        2,
      )
      chartData.push({
        scanId: item.scan.id,
        time: dayjs(item.scan.end_time).format('YYYY-MM-DD'),
        value: isNaN(value) ? 0 : value,
        newCodeLines: item.totalNewCodeLines,
        newMaterialCodeLines: item.totalNewMaterialCodeLines,
        cumulativeNewCodeLines: cumulativeNewCodeLines,
        cumulativeNewMaterialCodeLines: cumulativeNewMaterialCodeLines,
      })
    }
    return chartData
  }, [newCodeCoverages])

  // ======================== detail select options ========================
  const detailSelectOptions = useMemo(
    () =>
      [{ label: '截至目前', value: -1 }].concat(
        newCodeCoverages
          .slice()
          .reverse()
          .map(item => ({
            label: `${dayjs(item.scan.start_time).format('YYYY-MM-DD')} ~ ${dayjs(item.scan.end_time).format('YYYY-MM-DD')}`,
            value: item.scan.id,
          })),
      ),
    [newCodeCoverages],
  )

  const isUserPickedMaterial = useCallback(
    (namespace: string) => {
      if (
        Array.isArray(collectionDetail?.materials)
        && collectionDetail.materials.length > 0
      ) {
        return collectionDetail.materials.some(
          material => material.name === namespace,
        )
      }
      else {
        return true
      }
    },
    [collectionDetail],
  )

  // ======================== ranks ========================
  // 项目新增物料行数
  const projectNewMaterialCodeLinesRank = useMemo(() => {
    const projectNewMaterialCodeLines: {
      id: number
      name: string
      value: number
    }[] = []
    for (const item of newCodeCoverages.filter(
      item => item.scan.id === selectedScanId || !selectedScanId,
    )) {
      const { projectNewLibraryMaterialCodeLiens } = item
      const filteredProjectNewLibraryMaterialCodeLiens
        = Array.isArray(collectionDetail?.projects)
        && collectionDetail?.projects.length > 0
          ? projectNewLibraryMaterialCodeLiens.filter(project =>
            collectionDetail.projects.some(p => p.id === project.projectId),
          )
          : projectNewLibraryMaterialCodeLiens
      for (const projectNewLibraryMaterialCodeLine of filteredProjectNewLibraryMaterialCodeLiens) {
        const projectIdx = projectNewMaterialCodeLines.findIndex(
          item => item.id === projectNewLibraryMaterialCodeLine.projectId,
        )
        let currentNewCodeLines = 0
        for (const [libraryName, materialCodeLines] of Object.entries(
          projectNewLibraryMaterialCodeLine.materialUsage,
        )) {
          for (const [materialName, codeLines] of Object.entries(
            materialCodeLines,
          )) {
            if (isUserPickedMaterial(`${libraryName}/${materialName}`)) {
              currentNewCodeLines += codeLines
            }
          }
        }
        if (projectIdx !== -1) {
          projectNewMaterialCodeLines[projectIdx].value += currentNewCodeLines
        }
        else {
          projectNewMaterialCodeLines.push({
            id: projectNewLibraryMaterialCodeLine.projectId,
            name: projectNewLibraryMaterialCodeLine.projectName,
            value: currentNewCodeLines,
          })
        }
      }
    }

    return projectNewMaterialCodeLines.sort((a, b) => b.value - a.value)
  }, [
    newCodeCoverages,
    collectionDetail,
    selectedScanId,
    isUserPickedMaterial,
  ])

  // 物料新增使用行数
  const [materialRankNumberOption, setMaterialRankNumberOption] = useState('行数')
  const materialNewCodeLinesRank = useMemo(() => {
    const materialNewCodeLines: {
      id: string
      name: string
      codeLines: number
      count: number
    }[] = []
    for (const item of newCodeCoverages.filter(
      item => item.scan.id === selectedScanId || !selectedScanId,
    )) {
      const { newLibraryMaterialUsage } = item
      for (const [library, materialCodeLines] of Object.entries(
        newLibraryMaterialUsage,
      )) {
        for (const [material, { totalCodeLines, totalCount }] of Object.entries(
          materialCodeLines,
        )) {
          if (isUserPickedMaterial(`${library}/${material}`) === false) {
            continue
          }
          const materialIdx = materialNewCodeLines.findIndex(
            item => item.id === `${library}/${material}`,
          )
          if (materialIdx !== -1) {
            materialNewCodeLines[materialIdx].codeLines += totalCodeLines
            materialNewCodeLines[materialIdx].count += totalCount
          }
          else {
            materialNewCodeLines.push({
              id: `${library}/${material}`, // FIXME: 物料唯一标识
              name: `${library}/${material}`,
              codeLines: totalCodeLines,
              count: totalCount,
            })
          }
        }
      }
    }
    return materialNewCodeLines.sort((a, b) => b.codeLines - a.codeLines)
  }, [
    newCodeCoverages,
    selectedScanId,
    isUserPickedMaterial,
  ])

  // 项目新增物料行数
  const projectNewCodeLinesRank = useMemo(() => {
    const projectNewCodeLines: {
      id: number
      name: string
      value: number
    }[] = []
    const isUserPicked = (projectId: number) => {
      if (
        Array.isArray(collectionDetail?.projects)
        && collectionDetail.projects.length > 0
      ) {
        return collectionDetail.projects.some(
          project => project.id === projectId,
        )
      }
      else {
        return true
      }
    }
    for (const item of newCodeCoverages.filter(
      item => item.scan.id === selectedScanId || !selectedScanId,
    )) {
      const { professionProjectNewCodeLines } = item
      for (const professionProjectNewCodeLine of professionProjectNewCodeLines) {
        if (
          isUserPicked(professionProjectNewCodeLine.repo_project_id) === false
        ) {
          continue
        }
        const projectIdx = projectNewCodeLines.findIndex(
          item => item.id === professionProjectNewCodeLine.repo_project_id,
        )
        if (projectIdx !== -1) {
          projectNewCodeLines[projectIdx].value
            += professionProjectNewCodeLine.new_code_lines ?? 0
        }
        else {
          projectNewCodeLines.push({
            id: professionProjectNewCodeLine.repo_project_id,
            name: professionProjectNewCodeLine.repo_name,
            value: professionProjectNewCodeLine.new_code_lines ?? 0,
          })
        }
      }
    }
    return projectNewCodeLines.sort((a, b) => b.value - a.value)
  }, [newCodeCoverages, collectionDetail, selectedScanId])

  // ======================== project kanban ========================
  const normalizedSelectedScanId = useMemo(() => {
    if (!selectedScanId || selectedScanId === -1) {
      return newCodeCoverages?.[newCodeCoverages.length - 1]?.scan.id
    }
    else {
      return selectedScanId
    }
  }, [selectedScanId, newCodeCoverages])

  const normalizedScanOptions = useMemo(() => {
    if (detailSelectOptions.length > 1) {
      return detailSelectOptions.slice(1)
    }
    else {
      return []
    }
  }, [detailSelectOptions])

  // ======================== selectedProjectId ========================
  const [selectedProjectId, setSelectedProjectId] = useState<number>()

  const projectNewMaterialCodeLinesRankDOM = useRef<HTMLDivElement>(null)
  const projectsNewMaterialCodeLinesDOMMapper = useRef<
    Record<number, HTMLDivElement | null>
  >({})
  useEffect(() => {
    if (
      selectedProjectId
      && projectsNewMaterialCodeLinesDOMMapper.current[selectedProjectId]
    ) {
      projectsNewMaterialCodeLinesDOMMapper.current[
        selectedProjectId
      ].scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })
    }
  }, [selectedProjectId])

  const projectNewCodeLinesRankDOM = useRef<HTMLDivElement>(null)
  const projectsNewCodeLinesDOMMapper = useRef<
    Record<number, HTMLDivElement | null>
  >({})
  useEffect(() => {
    if (
      selectedProjectId
      && projectsNewCodeLinesDOMMapper.current[selectedProjectId]
    ) {
      projectsNewCodeLinesDOMMapper.current[selectedProjectId].scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })
    }
  }, [selectedProjectId])

  return (
    <Spin spinning={loading}>
      <div className={styles['kanban-detail']}>
        {collectionDetail && (isAdmin() || __DEV__) && (
          <Space direction="vertical">
            <UpdateCollectionDialog
              business={business}
              initialValues={collectionDetail}
              onUpdate={() => {
                refreshList?.()
                fetchProjectCollectionDetail(collectionId)
              }}
            />
          </Space>
        )}
        <Alert
          showIcon
          type={realTimeLimit ? 'warning' : 'info'}
          message={realTimeLimit
            ? `该看板在指定时间周期内存在物料行数上限。即在观测时间范围内，单一物料使用超过指定上限后，新增加的行数将被忽略。`
            : `该看板在指定时间周期内不存在物料行数上限。仅忽略在指定时间前就超出指定行数上限的物料。`}
        />
        <Card>
          <h3 style={{ textAlign: 'center' }}>新增代码覆盖率</h3>
          <DualAxesChart
            chartData={chartData}
            selectedScanId={selectedScanId}
            onSelectChange={onSelectedScanIdChange}
          />
        </Card>
        <Card
          title={(
            <Space align="center">
              <h3 style={{ textAlign: 'center', marginBottom: 0 }}>指标详情</h3>
              <Select
                style={{ width: 250, justifySelf: 'right' }}
                options={detailSelectOptions}
                value={selectedScanId ?? -1}
                onChange={(value) => {
                  setSelectedScanId(value === -1 ? undefined : value)
                }}
              />
            </Space>
          )}
        >
          <div className={styles['ranks']}>
            <div className={styles['rank']} ref={projectNewMaterialCodeLinesRankDOM}>
              <div className={styles['title']}>新增物料代码行数（项目维度）</div>
              <div className={styles['items']}>
                {projectNewMaterialCodeLinesRank.map((item, _, arr) => (
                  <div
                    key={item.id}
                    ref={(el) => {
                      projectsNewMaterialCodeLinesDOMMapper.current[item.id] = el
                    }}
                    className={clsx({
                      [styles['item']]: true,
                      [styles['selected']]: selectedProjectId === item.id,
                    })}
                    onClick={() => {
                      setSelectedProjectId((pre) => {
                        if (pre === item.id) {
                          return undefined
                        }
                        else {
                          return item.id
                        }
                      })
                    }}
                  >
                    <div
                      className={clsx(styles['processing'], styles['green-bg'])}
                      style={{
                        width: `${(item.value / arr[0].value) * 100}%`,
                      }}
                    />
                    <div className={styles['name']}>{item.name}</div>
                    <div className={styles['value']}>{item.value}</div>
                  </div>
                ))}
              </div>
            </div>
            <div className={styles['rank']}>
              <div className={styles['header']}>
                <div className={styles['title']}>
                  新增物料代码行数（物料维度）
                </div>
                <Radio.Group
                  optionType="button"
                  options={['行数', '次数'].map(item => ({
                    label: item,
                    value: item,
                  }))}
                  value={materialRankNumberOption}
                  onChange={e => setMaterialRankNumberOption(e.target.value)}
                />
              </div>
              <div className={styles['items']}>
                {materialNewCodeLinesRank.map((item, _, arr) => (
                  <div
                    key={item.id}
                    className={styles['item']}
                    onClick={() => {
                      window.open(
                        `https://components.corp.kuaishou.com/detail?namespace=${item.id}`,
                      )
                    }}
                  >
                    <div
                      className={clsx(styles['processing'], styles['green-bg'])}
                      style={{
                        width: `${(item.codeLines / arr[0].codeLines) * 100}%`,
                      }}
                    />
                    <div className={styles['name']}>{item.name}</div>
                    <div className={styles['value']}>
                      {materialRankNumberOption === '行数'
                        ? item.codeLines
                        : item.count}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className={styles['rank']} ref={projectNewCodeLinesRankDOM}>
              <div className={styles['title']}>新增业务代码行数</div>
              <div className={styles['items']}>
                {projectNewCodeLinesRank.map((item, _, arr) => (
                  <div
                    key={item.id}
                    ref={(el) => {
                      projectsNewCodeLinesDOMMapper.current[item.id] = el
                    }}
                    className={clsx({
                      [styles['item']]: true,
                      [styles['selected']]: selectedProjectId === item.id,
                    })}
                    onClick={() => {
                      setSelectedProjectId((pre) => {
                        if (pre === item.id) {
                          return undefined
                        }
                        else {
                          return item.id
                        }
                      })
                    }}
                  >
                    <div
                      className={clsx(styles['processing'], styles['red-bg'])}
                      style={{
                        width: `${(item.value / arr[0].value) * 100}%`,
                      }}
                    />
                    <div className={styles['name']}>{item.name}</div>
                    <div className={styles['value']}>{item.value}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Card>
        <Card
          title={(
            <Space align="center">
              <h3 style={{ textAlign: 'center', marginBottom: 0 }}>项目详情</h3>
              <Select
                style={{ width: 250, justifySelf: 'right' }}
                value={normalizedSelectedScanId}
                options={normalizedScanOptions}
                onChange={(value) => {
                  setSelectedScanId(value === -1 ? undefined : value)
                }}
              />
            </Space>
          )}
        >
          <ProjectOverview
            scanId={normalizedSelectedScanId}
            forceSelectedProjectId={selectedProjectId}
            clearSelected={() => setSelectedProjectId(undefined)}
            business={collectionDetail?.business as BUSINESS}
            projects={collectionDetail?.projects ?? []}
          />
        </Card>
      </div>
    </Spin>
  )
}

export default KanbanDetail
