.kanban-detail {
  display: flex;
  flex-direction: column;
  gap: 12px;
  // max-height: 100%;
  flex: 1;
  position: relative;

  .title-card {
    border: 2px solid var(--border-color);
    border-radius: 2px;
    box-sizing: border-box;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
  }

  .ranks {
    display: flex;
    gap: 12px;

    .rank {
      flex: 1;
      border: 2px solid var(--border-color);
      border-radius: 2px;
      padding: 22px;
      box-sizing: border-box;
      max-height: 450px;
      display: flex;
      flex-direction: column;
      gap: 12px;

      .header {
        display: flex;
        justify-content: space-between;
      }

      .title {
        font-weight: bold;
        font-size: 18px;
      }

      .items {
        overflow: auto;
        display: flex;
        font-size: 18px;
        flex-direction: column;
        gap: 8px;
        height: 400px;

        .project-item {
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background-color: #f5f5f5f5;
          }
        }

        .item.selected {
          border: 2px solid #2f55eb;
        }

        .item {
          display: flex;
          padding: 10px 10px;
          justify-content: space-between;
          position: relative;
          border-radius: 2px;
          cursor: pointer;
          transition: all 0.3s;
          border: 2px solid transparent;

          &:hover {
            background-color: #f5f5f5f5;
          }

          .name,
          .value {
            z-index: 1;
          }

          .processing {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            border-radius: 2px;
          }

          .red-bg {
            background-color: #f56c6c76;
          }

          .green-bg {
            background-color: #67c23a6a;
          }
        }
      }
    }
  }

  .project-list {
    background-color: white;
    max-height: 100%;
    width: 27%;
    border-radius: 2px;
    padding: 12px;
    box-sizing: border-box;
    position: relative;
    height: 100;

    .fixed-container {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .list {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .project-item {
      padding: 8px 12px;
      border-radius: 2px;
      cursor: pointer;
      // 文字过长显示省略号，纵向不压缩
      white-space: nowrap;
      text-overflow: ellipsis;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    .selected {
      background-color: rgba(231, 231, 231, 0.6);
    }

    .code-changed {
      border-left: 3px solid #2f55eb;
    }

    .total {
      font-size: 12px;
      color: #999;
    }
  }
}
