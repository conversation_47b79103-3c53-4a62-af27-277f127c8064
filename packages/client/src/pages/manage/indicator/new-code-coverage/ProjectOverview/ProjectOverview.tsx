import React, { useEffect, useState } from 'react'
import { Card, Empty, Input } from 'antd'
import clsx from 'clsx'
import {
  BUSINESS,
  REPO_TYPE,
} from '@global-material-middleoffice/server-v2/shared'

import { queryRawDataList } from '@/services/v2/raw-data'

import AutoFullParentLoading from '@/components/auto-full-parent-loading'
import ProjectDetail from './ProjectDetail'

import styles from './style.module.less'
import gitUrlParse from 'git-url-parse'

interface Props {
  scanId: number
  business: BUSINESS
  forceSelectedProjectId?: number
  clearSelected: () => void
  projects: Service.Analysis.ProjectCollection.IdNameObject[]
}

const ProjectOverview: React.FC<Props> = (props) => {
  const { scanId, business, projects, clearSelected, forceSelectedProjectId } = props

  // ======================== 项目列表 ========================
  const [searchText, setSearchText] = useState('')
  const [projectList, setProjectList]
    = useState<Service.Analysis.RawData.ListResponse>([])
  const [loading, setLoading] = useState(false)
  useEffect(() => {
    if (scanId && business) {
      setLoading(true)
      queryRawDataList({
        scanId,
        businesses: business,
        repoTypes: REPO_TYPE.PROFESSION,
      })
        .then((res) => {
          const result = res.data.data.list.filter(
            (item: Service.Analysis.RawData.ListResponse[0]) => {
              return (
                item.repo_meta
                && !item.repo_meta?.clone_url.includes(
                  'plateco-dev-fe/kwaishop-tech/tianhe/',
                )
              )
            },
          )
          setProjectList(result)
          if (scanId === 583815140216901) {
            setSelectedProjectId(80192)
          }
          else {
            setSelectedProjectId(result?.[0].repo_project_id)
          }
        })
        .finally(() => setLoading(false))
    }
  }, [scanId, business])

  const [selectedProjectId, _setSelectedProjectId] = useState<
    number | undefined
  >(forceSelectedProjectId)
  useEffect(() => {
    _setSelectedProjectId(forceSelectedProjectId)
  }, [forceSelectedProjectId])

  const setSelectedProjectId = (id: number) => {
    if (id !== selectedProjectId) {
      clearSelected()
      _setSelectedProjectId(id)
      clearSelected()
    }
  }

  return (
    <div className={styles['project-overview']}>
      <Card className={styles['project-list']}>
        <div className={styles['fixed-container']}>
          <Input.Search
            value={searchText}
            onChange={value => setSearchText(value.target.value)}
          />
          <div className={styles['list']}>
            {projectList
              .filter(item => item.repo_meta.name.includes(searchText))
              .filter((item) => {
                if (!projects?.length) {
                  return true
                }
                else {
                  return projects
                    .map(item => item.id)
                    .includes(item.repo_project_id)
                }
              })
              .map((item: Service.Analysis.RawData.ListResponse[0]) => {
                const fistIdx = item.repo_meta.name.indexOf(searchText)
                const name = gitUrlParse(item.repo_meta.clone_url).name

                return (
                  <div
                    key={item.id}
                    onClick={() => setSelectedProjectId(item.repo_project_id)}
                    className={clsx({
                      [styles['project-item']]: true,
                      [styles['selected']]:
                        selectedProjectId === item.repo_project_id,
                      [styles['code-changed']]: item.repo_last_update_time,
                    })}
                  >
                    {name.slice(0, fistIdx)}
                    <b>{searchText}</b>
                    {name.slice(fistIdx + searchText.length)}
                  </div>
                )
              })}
          </div>
          <div className={styles['total']}>
            共计：
            {projectList.length}
          </div>
        </div>
      </Card>

      <Card className={styles['project-detail']}>
        <div className={styles['fixed-container']}>
          {selectedProjectId
            ? (
              <ProjectDetail scanId={scanId} projectId={selectedProjectId} />
            )
            : (
              <Empty description="请在左侧选择项目" />
            )}
        </div>
      </Card>

      <AutoFullParentLoading loading={loading} />
    </div>
  )
}

export default ProjectOverview
