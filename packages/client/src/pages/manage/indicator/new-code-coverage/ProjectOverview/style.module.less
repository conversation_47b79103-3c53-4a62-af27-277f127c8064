.project-overview {
  display: flex;
  gap: 12px;
  max-height: 100%;
  flex: 1;
  position: relative;
  min-height: 800px;

  .project-list {
    background-color: white;
    max-height: 100%;
    width: 27%;
    border-radius: 2px;
    padding: 12x;
    box-sizing: border-box;
    position: relative;

    .fixed-container {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .list {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .project-item {
      padding: 8px 12px;
      border-radius: 2px;
      cursor: pointer;
      // 文字过长显示省略号，纵向不压缩
      white-space: nowrap;
      text-overflow: ellipsis;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    .selected {
      background-color: rgba(231, 231, 231, 0.6);
    }

    .code-changed {
      border-left: 3px solid #2f55eb;
    }

    .total {
      font-size: 12px;
      color: #999;
    }
  }

  .project-detail {
    background-color: white;
    max-height: 100%;
    flex: 1;
    border-radius: 2px;
    padding: 12px;
    box-sizing: border-box;
    position: relative;
    display: flex;
    flex-direction: column;

    .detail {
      display: flex;
      flex-direction: column;
      gap: 12px;
      height: 100%;
      position: relative;
    }

    .page-info {
      position: relative;
      overflow: hidden;
      flex: 1;
      display: flex;
      border-radius: 12px;
      padding: 12px;
      box-sizing: border-box;
      width: 100%;
      background-color: #f5f5f5;
      display: flex;
      gap: 12px;
      height: 0;

      .drawer-mask {
        background-color: rgba(0, 0, 0, 0);
      }

      .page-drawer-content {
        display: flex;
        flex-direction: column;
        gap: 30px;
      }

      .package-list {
        width: 24%;
        display: flex;
        flex-direction: column;
        gap: 4px;
        padding: 4px 0;
        overflow: auto;

        .package-item {
          padding: 6px 12px;
          border-radius: 8px;
          cursor: pointer;

          // 文字过长显示省略号，纵向不压缩
          white-space: nowrap;
          text-overflow: ellipsis;

          &:hover {
            background-color: rgba(255, 255, 255, 0.7);
          }
        }

        .selected {
          background-color: white;
        }
      }

      .page-list {
        flex: 1;
        background-color: white;
        border-radius: 12px;
        padding: 12px;
        overflow: auto;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .page-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 12px;

          .extra {
            display: flex;
            gap: 10px;
            align-items: center;
          }

          .update-date {
            font-size: 12px;
            color: #999;
          }
          .statuses {
            min-width: 130px;
            display: flex;
            justify-content: space-between;
            gap: 16px;
          }
        }

        .page-item + .page-item {
          border-top: 1px solid #f5f5f5;
        }
      }
    }
  }

  .fixed-container {
    position: absolute;
    padding: 12px;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
  }
}
