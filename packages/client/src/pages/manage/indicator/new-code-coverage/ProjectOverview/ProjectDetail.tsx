import { git2httpLink } from '@/utils/git'
import {
  FileTextOutlined,
  GitlabOutlined,
  RightOutlined,
} from '@ant-design/icons'
import {
  Badge,
  Button,
  Collapse,
  Descriptions,
  Drawer,
  Modal,
  Result,
  Select,
  Space,
  Table,
  Typography,
} from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import dayjs from 'dayjs'
import clsx from 'clsx'
import {
  APPModeReport,
  PackageReport,
} from '@ks-material-middleoffice/measure-sdk'
import JsonView from '@uiw/react-json-view'
import gitUrlParse from 'git-url-parse'

import { queryRawDataDetail } from '@/services/v2/raw-data'
import { queryProjectFileNewMaterialUsage } from '@/services/v2/new-material-usage'
import { queryProjectFileMaterialUsage } from '@/services/v2/material-usage'
import AutoFullParentLoading from '@/components/auto-full-parent-loading'

import styles from './style.module.less'

interface Props {
  scanId: number
  projectId: number
}

const CONTENT_OPTIONS = [
  { value: 'base-info', label: '基本信息' },
  { value: 'page-info', label: '页面信息' },
]

const MATERIAL_USAGE_COLUMNS = [
  {
    title: '库名',
    dataIndex: 'libraryName',
    key: 'libraryName',
  },
  {
    title: '物料名',
    dataIndex: 'materialName',
    key: 'materialName',
  },
  {
    title: '使用次数',
    dataIndex: 'count',
    key: 'count',
  },
]

const ProjectDetail: React.FC<Props> = (props) => {
  const { scanId, projectId } = props
  const [selectedContentTab, setSelectedContentTab] = useState('base-info')

  // ======================== project info ========================
  const [currentProjectInfo, setCurrentProjectInfo]
    = useState<Service.Analysis.RawData.DetailResponse>()
  const projectGitHttpURL = useMemo(() => {
    return currentProjectInfo && currentProjectInfo.repo_meta.clone_url
      ? git2httpLink(currentProjectInfo.repo_meta.clone_url)
      + `/-/tree/${(currentProjectInfo.content as APPModeReport)?.endCommitHash}`
      : ''
  }, [currentProjectInfo])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (scanId && projectId) {
      setLoading(true)
      queryRawDataDetail({ scanId, repoProjectId: projectId })
        .then((res) => {
          setCurrentProjectInfo(res.data.data)
        })
        .finally(() => setLoading(false))
    }
  }, [scanId, projectId])

  // ======================== package info ========================
  const [selectedPackagePath, setSelectedPackagePath] = useState<string>()
  const packageList = useMemo(() => {
    if (currentProjectInfo?.content) {
      const list = (currentProjectInfo.content as APPModeReport).packages
        .filter(
          item =>
            item.pages.filter(page => page.files.length).length > 0,
        )
        .sort((a, b) => a.pages.length - b.pages.length)
      setSelectedPackagePath(list[0]?.path)
      return list
    }
  }, [currentProjectInfo])

  // ======================== page list ========================
  const pageList = useMemo(() => {
    if (selectedPackagePath) {
      return (
        packageList
          ?.find(item => item.path === selectedPackagePath)
          ?.pages.filter(page => page.files.length)
          .sort((a, b) => b.lastUpdateTime - a.lastUpdateTime) ?? []
      )
    }
    return []
  }, [packageList, selectedPackagePath])

  // ======================== file material usage ========================
  const [projectFileMaterialUsage, setProjectFileMaterialUsage]
    = useState<Service.Analysis.MaterialUsage.SerializeAnalysisMaterialUsage[]>()
  useEffect(() => {
    if (scanId && projectId) {
      queryProjectFileMaterialUsage({ scanId, repoProjectId: projectId }).then(
        (res) => {
          setProjectFileMaterialUsage(res.data.data.result)
        },
      )
    }
  }, [scanId, projectId])

  // ======================== new material usage ========================
  const [projectFileNewMaterialUsage, setProjectFileNewMaterialUsage]
    = useState<
      Service.Analysis.NewMaterialUsage.SerializeAnalysisNewMaterialUsage[]
    >()
  useEffect(() => {
    if (scanId && projectId) {
      queryProjectFileNewMaterialUsage({
        scanId,
        repoProjectId: projectId,
      }).then((res) => {
        setProjectFileNewMaterialUsage(res.data.data.result)
      })
    }
  }, [scanId, projectId])

  // ======================== route detail ========================
  const [drawerVisible, setDrawerVisible] = useState(false)
  const [selectedRouteReport, setSelectedPageReport]
    = useState<PackageReport['pages'][0]>()
  const currentRoutePagesReport: {
    filePath: string
    newMaterialUsage: {
      libraryName: string
      materialName: string
      count: number
    }[]
    materialUsage: {
      libraryName: string
      materialName: string
      count: number
    }[]
  }[] = useMemo(() => {
    if (selectedRouteReport) {
      return selectedRouteReport.files.map((file) => {
        const materialUsageObj
          = projectFileMaterialUsage?.find(item => item.file_path === file.path)
            ?.filter_material_info ?? {}
        const materialUsage: {
          libraryName: string
          materialName: string
          count: number
        }[] = []
        for (const libraryName in materialUsageObj) {
          if (
            Object.prototype.hasOwnProperty.call(materialUsageObj, libraryName)
          ) {
            const usage = materialUsageObj[libraryName]
            materialUsage.push(
              ...Object.entries(usage).map(([materialName, count]) => ({
                libraryName,
                materialName,
                count,
              })),
            )
          }
        }

        const newMaterialUsageObj
          = projectFileNewMaterialUsage?.find(
            item => item.file_path === file.path,
          )?.material_info ?? {}

        const newMaterialUsage: {
          libraryName: string
          materialName: string
          count: number
        }[] = []
        for (const libraryName in newMaterialUsageObj) {
          if (
            Object.prototype.hasOwnProperty.call(
              newMaterialUsageObj,
              libraryName,
            )
          ) {
            const usage = newMaterialUsageObj[libraryName]
            newMaterialUsage.push(
              ...Object.entries(usage).map(([materialName, count]) => ({
                libraryName,
                materialName,
                count,
              })),
            )
          }
        }
        return {
          filePath: file.path,
          materialUsage,
          newMaterialUsage,
        }
      })
    }
    return []
  }, [
    selectedRouteReport,
    projectFileNewMaterialUsage,
    projectFileMaterialUsage,
  ])

  const viewRawData = () => {
    Modal.info({
      title: '底表信息',
      width: 800,
      content: (
        <div style={{ maxHeight: 800, overflow: 'auto' }}>
          <JsonView value={currentProjectInfo?.content ?? {}} />
        </div>
      ),
    })
  }

  return (
    <div className={styles['detail']}>
      <Space>
        <Select
          options={CONTENT_OPTIONS}
          value={selectedContentTab}
          onChange={value => setSelectedContentTab(value)}
        />
        <Button
          type="link"
          icon={<GitlabOutlined />}
          href={projectGitHttpURL}
          target="_blank"
        />
        <Button
          icon={<FileTextOutlined />}
          onClick={viewRawData}
          size="small"
          type="link"
        />
      </Space>
      <div
        style={{
          display: selectedContentTab !== 'base-info' ? 'none' : undefined,
        }}
      >
        <Descriptions
          bordered
          size="small"
          column={24}
        >
          {[
            {
              label: '项目名称',
              span: 24,
              children: currentProjectInfo
                ? gitUrlParse(currentProjectInfo.repo_meta.clone_url).name
                : '',
            },
            {
              label: '本周最后更新时间',
              span: 24,
              children: currentProjectInfo?.repo_last_update_time
                ? dayjs(currentProjectInfo.repo_last_update_time).format(
                  'YYYY-MM-DD HH:mm:ss',
                )
                : '-',
            },
            {
              label: '代码行数',
              children: currentProjectInfo?.content?.codeLines ?? '-',
            },
            {
              label: '新增代码行数',
              children: (currentProjectInfo?.content as APPModeReport)?.newCodeLines ?? '-',
            },
          ].map(item => (<Descriptions.Item key={item.label} {...item} />))}
        </Descriptions>
      </div>

      <div
        key={currentProjectInfo?.repo_meta.clone_url}
        className={styles['page-info']}
        style={{
          display: selectedContentTab !== 'page-info' ? 'none' : undefined,
        }}
      >
        {!!packageList?.length && (
          <>
            <div className={styles['package-list']}>
              {packageList.map(item => (
                <div
                  key={item.path}
                  className={clsx({
                    [styles['package-item']]: true,
                    [styles['selected']]: selectedPackagePath === item.path,
                  })}
                  onClick={() => setSelectedPackagePath(item.path)}
                >
                  <div className={styles['package-item-header']}>
                    <div>
                      {item.path === '.'
                        ? currentProjectInfo?.repo_meta.name
                        : item.path}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className={styles['page-list']}>
              {pageList.map((item, idx) => {
                const filePaths = item.files.map(file => file.path)
                const usedMaterial = !!projectFileMaterialUsage?.find(
                  item =>
                    item.file_path && filePaths.includes(item.file_path),
                )
                const usedNewMaterial = !!projectFileNewMaterialUsage?.find(
                  item =>
                    item.file_path && filePaths.includes(item.file_path),
                )
                return (
                  <div
                    key={item.browserPath + idx}
                    className={styles['page-item']}
                    onClick={() => {
                      setDrawerVisible(true)
                      setSelectedPageReport(item)
                    }}
                  >
                    <div>{item.browserPath}</div>
                    <div className={styles['extra']}>
                      <div className={styles['update-date']}>
                        {dayjs(item.lastUpdateTime).format(
                          'YYYY-MM-DD HH:mm:ss',
                        )}
                      </div>
                      <div className={styles['statuses']}>
                        {usedNewMaterial && (
                          <Badge status="success" text="使用了新物料" />
                        )}
                        {usedMaterial && !usedNewMaterial && (
                          <Badge color="blue" text="使用了物料" />
                        )}
                        <RightOutlined style={{ fontSize: 12 }} />
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </>
        )}

        {!packageList?.length && (
          <Result
            status="warning"
            style={{ margin: '0 auto' }}
            title={(
              <Space direction="vertical">
                <Typography.Text type="warning">
                  该项目未扫描到有效页面信息，请检查扫描配置是否正确或者代码是否存在问题。
                </Typography.Text>
                <Typography.Text type="secondary">
                  如有问题请联系技术支持 @yingpengsha。
                </Typography.Text>
              </Space>
            )}
            extra={(
              <Space>
                <Button icon={<FileTextOutlined />} onClick={viewRawData}>
                  底表信息
                </Button>
                <Button
                  type="primary"
                  icon={<GitlabOutlined />}
                  onClick={() => window.open(projectGitHttpURL, '_blank')}
                >
                  代码仓库
                </Button>
              </Space>
            )}
          />
        )}

        <Drawer
          title="路由详情"
          placement="right"
          closable={false}
          visible={drawerVisible}
          getContainer={false}
          onClose={() => setDrawerVisible(false)}
          maskClosable
          width="40%"
          maskClassName={styles['drawer-mask']}
          // styles={{
          //   header: { padding: '10px 14px' },
          //   body: { padding: '14px 14px' },
          // }}
        >
          <div className={styles['page-drawer-content']}>
            <Descriptions column={4}>
              {[
                {
                  label: '路由路径',
                  span: 4,
                  children: selectedRouteReport?.browserPath,
                },
                {
                  label: '创建时间',
                  span: 4,
                  children: dayjs(selectedRouteReport?.createTime).format(
                    'YYYY-MM-DD HH:mm:ss',
                  ),
                },
              ].map(item => (<Descriptions.Item key={item.label} {...item} />))}
            </Descriptions>

            <Collapse bordered={false}>
              {currentRoutePagesReport.map(pageReport => ({
                key: pageReport.filePath,
                label: (
                  <Space>
                    {pageReport.filePath}
                    <Button
                      type="link"
                      onClick={(e) => {
                        window.open(
                          projectGitHttpURL + `/${pageReport.filePath}`,
                        )
                        e.stopPropagation()
                      }}
                      size="small"
                      icon={<GitlabOutlined />}
                    />
                  </Space>
                ),
                children: (
                  <div>
                    <Typography.Text strong>新物料使用</Typography.Text>
                    <Table
                      size="small"
                      dataSource={pageReport.newMaterialUsage}
                      columns={MATERIAL_USAGE_COLUMNS}
                    >
                    </Table>

                    <Typography.Text strong>物料使用</Typography.Text>
                    <Table
                      size="small"
                      dataSource={pageReport.materialUsage}
                      columns={MATERIAL_USAGE_COLUMNS}
                    >
                    </Table>
                  </div>
                ),
              })).map(item => (<Descriptions.Item key={item.key} {...item} />))}
            </Collapse>
          </div>
        </Drawer>
      </div>

      <AutoFullParentLoading loading={loading} />
    </div>
  )
}

export default ProjectDetail
