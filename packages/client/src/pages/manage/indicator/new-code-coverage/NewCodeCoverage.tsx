import React, { useCallback, useEffect, useState } from 'react'
import { Select, Space, Tabs, Tag } from 'antd'
import useUrlState from '@ahooksjs/use-url-state'
import { SOURCE_CODE_BUSINESS } from '@global-material-middleoffice/server-v2/shared'

import { BusinessMap } from '@/constants'
import ManageTitle from '@/components/manage-title/ManageTitle'
import { queryProjectCollectionList } from '@/services/v2/project-collection'
import AddCollectionDialog from './AddCollectionDialog'
import KanbanDetail from './KanbanDetail'

const BUSINESS_OPTIONS = [
  {
    label: BusinessMap[SOURCE_CODE_BUSINESS.ES],
    value: SOURCE_CODE_BUSINESS.ES,
  },
  {
    label: BusinessMap[SOURCE_CODE_BUSINESS.BIZ],
    value: SOURCE_CODE_BUSINESS.BIZ,
  },
]

const NewCodeCoverage: React.FC = () => {
  // ======================== 事业部选项 ========================
  const [business, setBusiness] = useState<SOURCE_CODE_BUSINESS>(SOURCE_CODE_BUSINESS.BIZ)

  // ======================== 看板集合 ========================
  const [projectCollectionList, setProjectCollectionList] = useState<Service.Analysis.ProjectCollection.IdNameObject[]>([])
  const fetchProjectCollectionList = useCallback(() => {
    return queryProjectCollectionList({
      tag: ['source_code'],
      business,
    }).then((res) => {
      setProjectCollectionList(res.data.data)
      return res
    })
  }, [business])

  useEffect(() => {
    fetchProjectCollectionList().then((res) => {
      setSelectedCollectionId((pre: number | undefined) => {
        if (
          pre
          && res.data.data.find(
            (item: Service.Analysis.ProjectCollection.IdNameObject) =>
              item.id === pre,
          )
        ) {
          return pre
        }
        return res.data.data?.[0]?.id
      })
    })
  }, [fetchProjectCollectionList, business])

  // ======================== 选中的看板 ========================
  const [{ collectionId: selectedCollectionId }, _setSelectedCollectionId] = useUrlState<{
    collectionId?: number
  }>({ collectionId: undefined })
  const setSelectedCollectionId = (changeValueOrFunc: number | ((pre?: number) => number | undefined)) => {
    _setSelectedCollectionId(({ collectionId }) => {
      if (typeof changeValueOrFunc === 'number') {
        return {
          collectionId: changeValueOrFunc,
        }
      }
      return { collectionId: changeValueOrFunc(+collectionId) }
    })
  }

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <Space>
        <ManageTitle>
          新增代码覆盖率
        </ManageTitle>
        <Tag color="success">源代码</Tag>
        <Select
          size="small"
          style={{ width: 120 }}
          value={business}
          onChange={setBusiness}
          options={BUSINESS_OPTIONS}
        />
      </Space>

      <Tabs
        size="large"
        type="card"
        activeKey={selectedCollectionId?.toString()}
        onChange={key => setSelectedCollectionId(+key)}
        tabBarExtraContent={(
          <AddCollectionDialog
            business={business}
            onAdd={fetchProjectCollectionList}
          />
        )}
      >
        {projectCollectionList.map(item => (
          <Tabs.TabPane key={item.id.toString()} tab={item.name}>
            <KanbanDetail
              key={item.id}
              business={business}
              collectionId={item.id}
              refreshList={fetchProjectCollectionList}
            />
          </Tabs.TabPane>
        ))}
      </Tabs>
    </Space>
  )
}

export default NewCodeCoverage
