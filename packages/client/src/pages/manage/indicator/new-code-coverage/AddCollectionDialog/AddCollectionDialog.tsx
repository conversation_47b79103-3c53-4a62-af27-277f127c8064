import {
  Button,
  Form,
  Modal,
  Input,
  Select,
  message,
  DatePicker,
  SelectProps,
  DatePickerProps,
  Switch,
} from 'antd'
import React, { useState, useCallback, useEffect } from 'react'
import dayjs from 'dayjs'
import { OverrideProperties } from 'type-fest'
import { SOURCE_CODE_BUSINESS } from '@global-material-middleoffice/server-v2/shared'

import { NOOP } from '@/utils/function'
import { queryRepoMetaList } from '@/services/v2/repo-meta'
import { createProjectCollection } from '@/services/v2/project-collection'
import { __oldFetchMaterialList } from '@/services/v2/material'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'

interface Props {
  business: SOURCE_CODE_BUSINESS
  onAdd?: () => void
}

const AddCollectionDialog: React.FC<Props> = (props) => {
  const { onAdd, business } = props
  // 表单
  const [formInstance] = Form.useForm()
  const [submitting, setSubmitting] = useState(false)
  const handleSubmit = async () => {
    setSubmitting(true)
    try {
      const values = await formInstance.validateFields()
      const {
        name,
        projects = [],
        materials = [],
        startTime,
        endTime,
        payload,
      } = values

      await createProjectCollection({
        name,
        tag: ['source_code'],
        business,
        startTime: startTime?.valueOf(),
        endTime: endTime?.valueOf(),
        projects,
        materials,
        payload,
      })
      message.success('创建成功')
      onAdd?.()
      setOpen(false)
    }
    finally {
      setSubmitting(false)
    }
  }

  // 弹窗
  const [open, setOpen] = useState(false)
  useEffect(() => {
    if (open) {
      formInstance.resetFields()
    }
  }, [open, formInstance])

  const disabledStartDate: DatePickerProps['disabledDate'] = (current) => {
    const dayjsCurrent = dayjs(current.valueOf())
    return dayjsCurrent && dayjsCurrent.isBefore(dayjs('2024-01-01'))
  }

  const disabledEndDate: DatePickerProps['disabledDate'] = (current) => {
    const startDate = formInstance.getFieldValue('startTime')
    return startDate && current && current.isBefore(startDate, 'day')
  }

  return (
    <>
      <Button icon={<PlusOutlined />} onClick={() => setOpen(true)}>
        创建看板
      </Button>
      <Modal
        title="创建源码项目看板"
        width={600}
        visible={open}
        onCancel={() => setOpen(false)}
        onOk={handleSubmit}
        confirmLoading={submitting}
        cancelButtonProps={{ disabled: submitting }}
        closable={submitting}
        cancelText="取消"
        okText="创建"
      >
        <Form
          form={formInstance}
          labelAlign="left"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <Form.Item
            name="name"
            label="项目集合名称"
            rules={[{ required: true, message: '请输入项目集合名称' }]}
          >
            <Input disabled={submitting} placeholder="请输入项目集合名称" />
          </Form.Item>
          <Form.Item
            name="startTime"
            label="看板开始时间"
            initialValue={dayjs('2024-01-01')}
            rules={[{ required: true, message: '请选择开始时间' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              disabled={submitting}
              placeholder="开始时间"
              disabledDate={disabledStartDate}
            />
          </Form.Item>
          <Form.Item name="endTime" label="结束时间">
            <DatePicker
              style={{ width: '100%' }}
              disabled={submitting}
              placeholder="请选择结束时间，如果为空则不限定结束时间"
              disabledDate={disabledEndDate}
            />
          </Form.Item>
          <Form.Item name="projects" label="项目列表">
            <ProjectSelect placeholder="请选择项目，如果为空则默认选择所有项目" />
          </Form.Item>
          <Form.Item name="materials" label="物料列表">
            <MaterialSelect placeholder="请选择物料，如果为空则默认选择所有物料" />
          </Form.Item>
          <Form.List name={['payload', 'ignoredLibraries']} initialValue={['']}>
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, fieldKey, ...restField }) => (
                  <Form.Item
                    key={key}
                    label={name === 0 ? '忽略的库' : ''}
                    required={false}
                    {...(name === 0
                      ? { labelCol: { span: 8 }, wrapperCol: { span: 16 } }
                      : { wrapperCol: { offset: 8, span: 16 } })}
                  >
                    <Form.Item
                      {...restField}
                      noStyle
                      name={[name]}
                      fieldKey={[fieldKey]}
                      rules={[
                        {
                          required: name !== 0,
                          message: '请输入库名称或删除此字段',
                        },
                      ]}
                    >
                      <Input
                        placeholder="请输入库名称"
                        style={{ width: '80%' }}
                      />
                    </Form.Item>
                    {fields.length > 1
                      ? (
                        <Button
                          type="link"
                          onClick={() => remove(name)}
                          icon={<MinusCircleOutlined />}
                        />
                      )
                      : null}
                  </Form.Item>
                ))}
                <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    icon={<PlusOutlined />}
                  >
                    添加忽略的库
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
          <Form.Item
            name={['payload', 'realTimeLimit']}
            label="是否打开物料实时上限"
            tooltip="开启后，在周期内，物料行数会存在上限。反之则无上限限制"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export function ProjectSelect(
  props: OverrideProperties<
    Omit<SelectProps, 'options'>,
    {
      value?: Service.Analysis.ProjectCollection.IdNameObject[]
      onChange?: (
        value: Service.Analysis.ProjectCollection.IdNameObject[],
      ) => void
    }
  >,
) {
  const { value = [], onChange = NOOP, ...otherProps } = props
  const [projectOptions, setProjectOptions] = useState<
    Service.Analysis.RepoMeta.RepoMetaRecord[]
  >([])
  const [loading, setLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [totalCount, setTotalCount] = useState(0)

  const fetchProjects = useCallback(
    async (searchValue: string = '') => {
      if (!hasMore || loading) return
      setLoading(true)
      try {
        const result: Service.Analysis.RepoMeta.FindAllResult = (
          await queryRepoMetaList({
            query: searchValue,
            page,
            pageSize: 20,
          })
        ).data.data
        setProjectOptions(prev => [...prev, ...result.data])
        setPage(prev => prev + 1)
        setHasMore(
          result.pagination.currentPage < result.pagination.totalPages,
        )
        setTotalCount(result.pagination.totalCount)
      }
      finally {
        setLoading(false)
      }
    },
    [page, hasMore, loading],
  )

  const handleSearch = (value: string) => {
    setProjectOptions([])
    setPage(1)
    setHasMore(true)
    fetchProjects(value)
  }

  useEffect(() => {
    handleSearch('')
  }, [])

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { target } = event
    if (target) {
      const { scrollTop, clientHeight, scrollHeight }
        = target as HTMLDivElement
      if (scrollHeight - scrollTop === clientHeight) {
        fetchProjects()
      }
    }
  }

  return (
    <Select
      mode="multiple"
      placeholder="请选择项目"
      options={projectOptions.map(item => ({
        label: item.name,
        value: +item.project_id,
      }))}
      onSearch={handleSearch}
      onPopupScroll={handleScroll}
      filterOption={false}
      loading={loading}
      value={value.map(item => +item.id)}
      onChange={next =>
        onChange(
          next.map((id: number) => {
            const project = projectOptions.find(
              item => item.project_id === +id,
            )
            if (!project) {
              return value.find(item => item.id === id)!
            }
            return {
              id: +project.project_id,
              name: project.name,
            }
          }),
        )}
      {...otherProps}
    >
      {projectOptions.length > 0 && !hasMore && (
        <Select.Option disabled value="loaded" style={{ textAlign: 'center' }}>
          已加载全部
          {totalCount}
          个项目
        </Select.Option>
      )}
    </Select>
  )
}

export function MaterialSelect(
  props: OverrideProperties<
    SelectProps,
    {
      value?: Service.Analysis.ProjectCollection.IdNameObject[]
      onChange?: (
        value: Service.Analysis.ProjectCollection.IdNameObject[],
      ) => void
    }
  >,
) {
  const { value = [], onChange = NOOP, ...otherProps } = props
  const [materialOptions, setMaterialOptions] = useState<
    Service.Material.SerializeMaterialRecord[]
  >([])
  const [loading, setLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [totalCount, setTotalCount] = useState(0)

  const fetchMaterials = useCallback(
    async (searchValue: string = '') => {
      if (!hasMore || loading) return
      setLoading(true)
      try {
        const result: Service.Material.ListQueryResult = (
          await __oldFetchMaterialList({
            query: searchValue,
            page,
            pageSize: 20,
          })
        ).data.data
        setMaterialOptions(prev => [...prev, ...result.data])
        setPage(prev => prev + 1)
        setHasMore(
          result.pagination.currentPage < result.pagination.totalPages,
        )
        setTotalCount(result.pagination.totalCount)
      }
      finally {
        setLoading(false)
      }
    },
    [page, hasMore, loading],
  )

  const handleSearch = (value: string) => {
    setMaterialOptions([])
    setPage(1)
    setHasMore(true)
    fetchMaterials(value)
  }

  useEffect(() => {
    handleSearch('')
  }, [])

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { target } = event
    if (target) {
      const { scrollTop, clientHeight, scrollHeight }
        = target as HTMLDivElement
      if (scrollHeight - scrollTop === clientHeight) {
        fetchMaterials()
      }
    }
  }

  const options: SelectProps['options'] = materialOptions.map(item => ({
    label: item.namespace,
    value: item.id,
  }))

  return (
    <Select
      mode="multiple"
      placeholder="请选择物料"
      options={options}
      onSearch={handleSearch}
      onPopupScroll={handleScroll}
      filterOption={false}
      loading={loading}
      value={value.map(item => item.id)}
      onChange={next =>
        onChange(
          next.map((id: number) => {
            const material = materialOptions.find(item => item.id === id)
            if (!material) {
              return value.find(item => item.id === id)!
            }
            return {
              id,
              name: material.namespace,
            }
          }),
        )}
      {...otherProps}
    >
      {materialOptions.length > 0 && !hasMore && (
        <Select.Option disabled value="loaded" style={{ textAlign: 'center' }}>
          已加载全部
          {' '}
          {totalCount}
          {' '}
          个物料
        </Select.Option>
      )}
    </Select>
  )
}

export default AddCollectionDialog
