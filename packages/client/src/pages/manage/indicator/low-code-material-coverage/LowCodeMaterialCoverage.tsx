import useUrlState from '@ahooksjs/use-url-state'
import { Select, Space, Tabs, Tag } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'

import { queryProjectCollectionList } from '@/services/v2/project-collection'
import ManageTitle from '@/components/manage-title/ManageTitle'

import AddCollectionDialog from './AddCollectionDialog'
import KanbanDetail from './KanbanDetail'

const LowCodeMaterialCoverage: React.FC = () => {
  // ======================== 看板集合 ========================
  const [projectCollectionList, setProjectCollectionList] = useState<Service.Analysis.ProjectCollection.IdNameObject[]>([])
  const fetchProjectCollectionList = useCallback(() => {
    return queryProjectCollectionList({
      tag: ['tianhe'],
    }).then((res) => {
      setProjectCollectionList(res.data.data)
      return res
    })
  }, [])

  useEffect(() => {
    fetchProjectCollectionList().then((res) => {
      setSelectedCollectionId((pre: number | undefined) => {
        if (
          pre && res.data.data.find(
            (item: Service.Analysis.ProjectCollection.IdNameObject) =>
              item.id === pre,
          )
        ) {
          return pre
        }
        return res.data.data?.[0]?.id
      })
    })
  }, [fetchProjectCollectionList])

  // ======================== 选中的看板 ========================
  const [{ collectionId: selectedCollectionId }, _setSelectedCollectionId] = useUrlState<{
    collectionId?: number
  }>({ collectionId: undefined })
  const setSelectedCollectionId = (changeValueOrFunc: number | ((pre?: number) => number | undefined)) => {
    _setSelectedCollectionId(({ collectionId }) => {
      if (typeof changeValueOrFunc === 'number') {
        return {
          collectionId: changeValueOrFunc,
        }
      }
      return { collectionId: changeValueOrFunc(+collectionId) }
    })
  }

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <Space>
        <ManageTitle>
          物料覆盖率
        </ManageTitle>
        <Tag color="processing">低代码</Tag>
        <Select
          size="small"
          style={{ width: 120 }}
          defaultActiveFirstOption
          defaultValue="tianhe"
          options={[{
            label: '天河',
            value: 'tianhe',
          }]}
        />
      </Space>
      <Tabs
        size="large"
        type="card"
        activeKey={selectedCollectionId?.toString()}
        onChange={key => setSelectedCollectionId(+key)}
        tabBarExtraContent={<AddCollectionDialog onAdd={fetchProjectCollectionList} />}
      >
        {projectCollectionList.map(item => (
          <Tabs.TabPane key={item.id.toString()} tab={item.name}>
            <KanbanDetail
              key={item.id}
              collectionId={item.id}
              refreshList={fetchProjectCollectionList}
            />
          </Tabs.TabPane>
        ))}
      </Tabs>
    </Space>
  )
}

export default LowCodeMaterialCoverage
