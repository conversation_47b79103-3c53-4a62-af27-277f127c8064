import {
  Button,
  Form,
  Modal,
  Input,
  Select,
  message,
  DatePicker,
  SelectProps,
  Checkbox,
  DatePickerProps,
} from 'antd'
import React, { useState, useCallback, useEffect } from 'react'
import dayjs from 'dayjs'
import { OverrideProperties } from 'type-fest'

import { NOOP } from '@/utils/function'
import { __oldFetchMaterialList } from '@/services/v2/material'
import { createProjectCollection } from '@/services/v2/project-collection'
import { fetchTianheAppList } from '@/services/v2/page-info'
import { PlusOutlined } from '@ant-design/icons'

interface Props {
  onAdd?: () => void
}

const AddCollectionDialog: React.FC<Props> = (props) => {
  const { onAdd } = props
  // 表单
  const [formInstance] = Form.useForm()
  const [submitting, setSubmitting] = useState(false)
  const handleSubmit = async () => {
    setSubmitting(true)
    try {
      const values = await formInstance.validateFields()
      const {
        name,
        projects = [],
        materials = [],
        startTime,
        endTime,
        payload,
      } = values

      await createProjectCollection({
        name,
        tag: ['tianhe'],
        startTime: startTime?.valueOf(),
        endTime: endTime?.valueOf(),
        projects: projects.map((item: { appKey: string, appName: string }) => ({
          id: item.appKey,
          name: item.appName,
        })),
        materials,
        payload,
      })
      message.success('创建成功')
      onAdd?.()
      setOpen(false)
    }
    finally {
      setSubmitting(false)
    }
  }

  // 弹窗
  const [open, setOpen] = useState(false)
  useEffect(() => {
    if (open) {
      formInstance.resetFields()
    }
  }, [open, formInstance])

  const disabledStartDate: DatePickerProps['disabledDate'] = (current) => {
    const dayjsCurrent = dayjs(current.valueOf())
    return dayjsCurrent && dayjsCurrent.isBefore(dayjs('2024-01-01'))
  }

  const disabledEndDate: DatePickerProps['disabledDate'] = (current) => {
    const startDate = formInstance.getFieldValue('startTime')
    return startDate && current && current.isBefore(startDate, 'day')
  }

  return (
    <>
      <Button icon={<PlusOutlined />} onClick={() => setOpen(true)}>
        新建看板
      </Button>
      <Modal
        title="创建低代码下钻看板"
        width={600}
        visible={open}
        onCancel={() => setOpen(false)}
        onOk={handleSubmit}
        confirmLoading={submitting}
        cancelButtonProps={{ disabled: submitting }}
        closable={submitting}
        cancelText="取消"
        okText="创建"
      >
        <Form
          form={formInstance}
          labelAlign="left"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
        >
          <Form.Item
            name="name"
            label="应用集合名称"
            rules={[{ required: true, message: '请输入应用集合名称' }]}
          >
            <Input disabled={submitting} placeholder="请输入应用集合名称" />
          </Form.Item>
          <Form.Item
            name="startTime"
            label="看板开始时间"
            initialValue={dayjs('2024-01-01')}
            rules={[{ required: true, message: '请选择开始时间' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              disabled={submitting}
              placeholder="开始时间"
              disabledDate={disabledStartDate}
            />
          </Form.Item>
          <Form.Item name="endTime" label="结束时间">
            <DatePicker
              style={{ width: '100%' }}
              disabled={submitting}
              placeholder="请选择结束时间，如果为空则不限定结束时间"
              disabledDate={disabledEndDate}
            />
          </Form.Item>
          <Form.Item name={['payload', 'materialLibrary']} label="预设物料库">
            <Checkbox.Group>
              <Checkbox value="@es/tianhe-basic-materials">
                天河基础物料
              </Checkbox>
              <Checkbox value="@es/tianhe-pro-materials">天河高级物料</Checkbox>
            </Checkbox.Group>
          </Form.Item>
          <Form.Item name="materials" label="低代码物料列表">
            <MaterialSelect placeholder="请选择物料，如果为空则默认选择所有物料" />
          </Form.Item>
          <Form.Item name="projects" label="天河应用列表">
            <TianheAppSelect placeholder="请选择应用，如果为空则默认选择所有应用" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export function TianheAppSelect(
  props: OverrideProperties<
    SelectProps,
    {
      value?: { appKey: string, appName: string }[]
      onChange?: (value: { appKey: string, appName: string }[]) => void
    }
  >,
) {
  const { value = [], onChange = NOOP, ...otherProps } = props
  const [tianheAppOptions, setTianheAppOptions] = useState<
    { appKey: string, appName: string }[]
  >([])
  const [loading, setLoading] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  useEffect(() => {
    setLoading(true)
    fetchTianheAppList()
      .then((res) => {
        setTianheAppOptions(res.data.data)
      })
      .finally(() => {
        setLoading(false)
      })
  }, [])

  const options: SelectProps['options'] = tianheAppOptions
    .filter(
      item =>
        item.appName.includes(searchValue)
        || item.appKey.includes(searchValue),
    )
    .map(item => ({
      label: item.appName,
      value: item.appKey,
    }))

  return (
    <Select
      mode="multiple"
      placeholder="请选择应用"
      options={options}
      loading={loading}
      onSearch={setSearchValue}
      value={value.map(item => item.appKey)}
      onChange={next =>
        onChange(
          next.map((appKey: string) => {
            const app = tianheAppOptions.find(item => item.appKey === appKey)
            if (!app) {
              return value.find(item => item.appKey === appKey)!
            }
            return {
              appKey,
              appName: app.appName,
            }
          }),
        )}
      {...otherProps}
    />
  )
}

export function MaterialSelect(
  props: OverrideProperties<
    SelectProps,
    {
      value?: Service.Analysis.ProjectCollection.IdNameObject[]
      onChange?: (
        value: Service.Analysis.ProjectCollection.IdNameObject[],
      ) => void
    }
  >,
) {
  const { value = [], onChange = NOOP, ...otherProps } = props
  const [materialOptions, setMaterialOptions] = useState<
    Service.Material.SerializeMaterialRecord[]
  >([])
  const [loading, setLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [totalCount, setTotalCount] = useState(0)

  const fetchMaterials = useCallback(
    async (searchValue: string = '') => {
      if (!hasMore || loading) return
      setLoading(true)
      try {
        const result: Service.Material.ListQueryResult = (
          await __oldFetchMaterialList({
            query: searchValue,
            business: 'kael',
            page,
            pageSize: 20,
          })
        ).data.data
        setMaterialOptions(prev => [...prev, ...result.data])
        setPage(prev => prev + 1)
        setHasMore(
          result.pagination.currentPage < result.pagination.totalPages,
        )
        setTotalCount(result.pagination.totalCount)
      }
      finally {
        setLoading(false)
      }
    },
    [page, hasMore, loading],
  )

  useEffect(() => {
    handleSearch('')
  }, [])

  const handleSearch = (value: string) => {
    setMaterialOptions([])
    setPage(1)
    setHasMore(true)
    fetchMaterials(value)
  }

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { target } = event
    if (target) {
      const { scrollTop, clientHeight, scrollHeight }
        = target as HTMLDivElement
      if (scrollHeight - scrollTop === clientHeight) {
        fetchMaterials()
      }
    }
  }

  return (
    <Select
      mode="multiple"
      placeholder="请选择物料"
      options={materialOptions.map(item => ({
        label: item.namespace,
        value: item.id,
      }))}
      onFocus={() => handleSearch('')}
      onSearch={handleSearch}
      onPopupScroll={handleScroll}
      filterOption={false}
      loading={loading}
      value={value.map(item => item.id)}
      onChange={next =>
        onChange(
          next.map((id: number) => {
            const material = materialOptions.find(item => item.id === id)
            if (!material) {
              return value.find(item => item.id === id)!
            }
            return {
              id,
              name: material.namespace,
            }
          }),
        )}
      {...otherProps}
    >
      {materialOptions.length > 0 && !hasMore && (
        <Select.Option disabled value="loaded" style={{ textAlign: 'center' }}>
          已加载全部
          {totalCount}
          个物料
        </Select.Option>
      )}
    </Select>
  )
}

export default AddCollectionDialog
