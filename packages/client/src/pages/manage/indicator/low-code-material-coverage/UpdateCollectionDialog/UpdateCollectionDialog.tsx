import {
  Form,
  Modal,
  Input,
  message,
  DatePicker,
  Button,
  Checkbox,
  ButtonProps,
  DatePickerProps,
} from 'antd'
import React, { useState, useEffect, useMemo } from 'react'
import dayjs from 'dayjs'
import { EditOutlined } from '@ant-design/icons'

import { updateProjectCollection } from '@/services/v2/project-collection'
import {
  MaterialSelect,
  TianheAppSelect,
} from '../AddCollectionDialog/AddCollectionDialog'

interface Props extends ButtonProps {
  initialValues: Service.Analysis.ProjectCollection.ProjectCollection
  onUpdate?: () => void
}

const UpdateCollectionDialog: React.FC<Props> = (props) => {
  const { onUpdate, initialValues, ...buttonProps } = props

  const realInitialValues = useMemo(() => {
    return {
      ...initialValues,
      startTime: initialValues?.start_time
        ? dayjs(initialValues.start_time)
        : undefined,
      endTime: initialValues?.end_time
        ? dayjs(initialValues.end_time)
        : undefined,
      projects: initialValues?.projects.map(
        (item: Service.Analysis.ProjectCollection.IdNameObject) => ({
          appKey: item.id,
          appName: item.name,
        }),
      ),
      payload: Object.assign(
        {},
        { materialLibrary: [] },
        initialValues?.payload,
      ),
    }
  }, [initialValues])

  // 表单
  const [formInstance] = Form.useForm()
  const [submitting, setSubmitting] = useState(false)
  const handleSubmit = async () => {
    if (!initialValues) return
    setSubmitting(true)
    try {
      const values = await formInstance.validateFields()
      const {
        name,
        projects = [],
        materials = [],
        startTime,
        endTime,
        payload,
      } = values

      await updateProjectCollection({
        id: initialValues.id,
        name,
        tag: ['tianhe'],
        startTime: startTime?.valueOf(),
        endTime: endTime?.valueOf(),
        projects: projects.map((item: { appKey: string, appName: string }) => ({
          id: item.appKey,
          name: item.appName,
        })),
        materials,
        payload,
      })
      message.success('更新成功')
      onUpdate?.()
      setOpen(false)
    }
    finally {
      setSubmitting(false)
    }
  }

  // 弹窗
  const [open, setOpen] = useState(false)
  useEffect(() => {
    if (open) {
      setTimeout(() => formInstance.resetFields())
    }
  }, [open, formInstance, realInitialValues])

  const disabledStartDate: DatePickerProps['disabledDate'] = (current) => {
    const dayjsCurrent = dayjs(current.valueOf())
    return dayjsCurrent && dayjsCurrent.isBefore(dayjs('2024-01-01'))
  }

  const disabledEndDate: DatePickerProps['disabledDate'] = (current) => {
    const startDate = formInstance.getFieldValue('startTime')
    return startDate && current && current.isBefore(startDate, 'day')
  }

  return (
    <>
      <Button
        icon={<EditOutlined />}
        onClick={() => setOpen(true)}
        {...buttonProps}
      >
        编辑
      </Button>
      <Modal
        title="更新低代码下钻看板"
        width={600}
        visible={open}
        onCancel={() => setOpen(false)}
        onOk={handleSubmit}
        confirmLoading={submitting}
        cancelButtonProps={{ disabled: submitting }}
        closable={submitting}
        cancelText="取消"
        okText="更新"
      >
        <Form
          form={formInstance}
          labelAlign="left"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          initialValues={realInitialValues}
        >
          <Form.Item
            name="name"
            label="应用集合名称"
            rules={[{ required: true, message: '请输入应用集合名称' }]}
          >
            <Input disabled={submitting} placeholder="请输入应用集合名称" />
          </Form.Item>
          <Form.Item
            name="startTime"
            label="看板开始时间"
            initialValue={dayjs('2024-01-01')}
            rules={[{ required: true, message: '请选择开始时间' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              disabled={submitting}
              placeholder="开始时间"
              disabledDate={disabledStartDate}
            />
          </Form.Item>
          <Form.Item name="endTime" label="结束时间">
            <DatePicker
              style={{ width: '100%' }}
              disabled={submitting}
              placeholder="请选择结束时间，如果为空则不限定结束时间"
              disabledDate={disabledEndDate}
            />
          </Form.Item>
          <Form.Item
            name={['payload', 'materialLibrary']}
            label="预设物料库"
            initialValue={[]}
          >
            <Checkbox.Group>
              <Checkbox value="@es/tianhe-basic-materials">
                天河基础物料
              </Checkbox>
              <Checkbox value="@es/tianhe-pro-materials">天河高级物料</Checkbox>
            </Checkbox.Group>
          </Form.Item>
          <Form.Item name="materials" label="低代码物料列表">
            <MaterialSelect
              disabled={submitting}
              placeholder="请选择物料，如果为空则默认选择所有物料"
            />
          </Form.Item>
          <Form.Item name="projects" label="天河应用列表">
            <TianheAppSelect
              disabled={submitting}
              placeholder="请选择应用，如果为空则默认选择所有应用"
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default UpdateCollectionDialog
