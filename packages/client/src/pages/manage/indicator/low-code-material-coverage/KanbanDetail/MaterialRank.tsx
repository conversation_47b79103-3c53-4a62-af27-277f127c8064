import { useEffect, useMemo, useState } from 'react'
import { <PERSON><PERSON>, Drawer, Input, Select, Table } from 'antd'
import { DownOutlined, PieChartFilled, UpOutlined } from '@ant-design/icons'
import { Pie, PieConfig } from '@ant-design/charts'
import clsx from 'clsx'

import { NOOP } from '@/utils/function'

import { fetchTianheMaterialReferPages } from '@/services/v2/material-refer'

import MaterialBaseInfo from './MaterialBaseInfo'

import styles from './style.module.less'

interface Props {
  materialRanks: { namespace: string, count: number, addedCount: number }[]
  appKeys: string[]
}

const MATERIAL_COUNT_OPTIONS = [
  { label: '新增引用次数', value: 'addedCount' },
  { label: '总计引用次数', value: 'count' },
  { label: '增长率相较占有率排名变化', value: 'growthRate' },
]

function MaterialRank({ materialRanks, appKeys }: Props) {
  const [loading, setLoading] = useState(false)
  const [selectedMaterial, setSelectedMaterial] = useState<string | null>(null)
  const [referPages, setReferPages] = useState<Service.Material.Refer.Refer[]>(
    [],
  )

  useEffect(() => {
    setSelectedMaterial(materialRanks?.[0]?.namespace)
  }, [materialRanks])

  useEffect(() => {
    if (selectedMaterial) {
      let thenFunc = (data: Service.Material.Refer.Refer[]) => {
        setReferPages(
          data.sort((a, b) => (b.refer_count ?? 0) - (a.refer_count ?? 0)),
        )
      }
      let finalFunc = () => {
        setLoading(false)
      }
      setLoading(true)
      fetchTianheMaterialReferPages(selectedMaterial, appKeys)
        .then((res) => {
          thenFunc(res.data.data)
        })
        .finally(() => {
          finalFunc()
        })
      return () => {
        thenFunc = NOOP
        finalFunc = NOOP
      }
    }
  }, [selectedMaterial, appKeys])

  // ======================== 排名字段 ========================
  const [selectedCountOption, setSelectedCountOption] = useState('addedCount')

  // ======================== 搜索 ========================
  const [searchValue, setSearchValue] = useState('')
  const filteredAndSortedMaterialRanks = useMemo(() => {
    const filteredMaterialRanks = materialRanks.filter(item =>
      item.namespace.toLowerCase().includes(searchValue.toLowerCase()),
    )
    if (selectedCountOption !== 'growthRate') {
      return filteredMaterialRanks.sort((a, b) => {
        if (selectedCountOption === 'count') {
          return b.count - a.count
        }
        else {
          return b.addedCount - a.addedCount
        }
      })
    }
    else {
      const shareRanking = filteredMaterialRanks
        .slice()
        .sort((a, b) => b.count - a.count)
      const growthRateRanking = filteredMaterialRanks
        .slice()
        .sort((a, b) => b.addedCount / b.count - a.addedCount / b.count)
      return growthRateRanking.map((item, index) => {
        const shareRank = shareRanking.findIndex(
          shareItem => shareItem.namespace === item.namespace,
        )
        return {
          ...item,
          count: shareRank - index,
        }
      })
    }
  }, [materialRanks, searchValue, selectedCountOption])

  // ======================== pie chart ========================
  const [drawerOpen, setDrawerOpen] = useState(false)
  const pieConfig = useMemo<PieConfig>(() => {
    // 计算总的 count 值
    const total: number = filteredAndSortedMaterialRanks.reduce(
      (sum: number, item: { count: number }) => sum + item.count,
      0,
    )

    // 排序并筛选出占比最高的 90% 的条目
    const sortedData: { namespace: string, count: number }[] = [
      ...filteredAndSortedMaterialRanks,
    ].sort((a, b) => b.count - a.count)
    let cumulative: number = 0
    const threshold: number = total * 0.9
    const mainItems: { namespace: string, count: number }[] = []
    const otherItems: { namespace: string, count: number }[] = []

    sortedData.forEach((item) => {
      if (cumulative + item.count <= threshold) {
        mainItems.push({ namespace: item.namespace, count: item.count })
        cumulative += item.count
      }
      else {
        otherItems.push({ namespace: item.namespace, count: item.count })
      }
    })

    // 将最后的 10% 条目合并为 "其他"
    const otherCount: number = otherItems.reduce(
      (sum: number, item: { count: number }) => sum + item.count,
      0,
    )
    if (otherCount > 0) {
      mainItems.push({
        namespace: '其他',
        count: otherCount,
      })
    }

    // 返回 Pie 图配置
    const config: PieConfig = {
      data: mainItems,
      angleField: 'count', // 对应角度的字段
      colorField: 'namespace', // 用于区分颜色的字段
      labels: [
        {
          text: (data: { namespace: string, count: number }) =>
            data.namespace.split('/').pop(),
          style: { fontSize: 10, fontWeight: 'bold' },
        },
        {
          text: 'count',
          style: {
            fontSize: 9,
            dy: 12,
          },
        },
      ],
      legend: false,
      interaction: {
        elementHighlight: true,
      },
      tooltip: false,
    }
    return config
  }, [filteredAndSortedMaterialRanks])

  return (
    <div className={styles['content']}>
      <div className={styles['rank']}>
        <Drawer
          width="100%"
          title="图表统计"
          placement="left"
          visible={drawerOpen}
          getContainer={false}
          onClose={() => setDrawerOpen(false)}
          style={{
            position: 'absolute',
            height: '100%',
            overflow: 'hidden',
          }}
        >
          <Pie {...pieConfig} />
        </Drawer>
        <div className={styles['fixed-container']}>
          <div className={styles['filter-bar']}>
            <Button
              type="primary"
              icon={<PieChartFilled />}
              onClick={() => setDrawerOpen(true)}
            />
            <Select
              style={{ width: 250 }}
              options={MATERIAL_COUNT_OPTIONS}
              value={selectedCountOption}
              onChange={setSelectedCountOption}
            />
          </div>
          <div className={styles['input-bar']}>
            <Input.Search
              allowClear
              placeholder="搜索物料"
              value={searchValue}
              onChange={e => setSearchValue(e.target.value)}
            />
          </div>
          <div className={styles['list']}>
            {filteredAndSortedMaterialRanks.map(item => (
              <div
                key={item.namespace}
                onClick={() => setSelectedMaterial(item.namespace)}
                className={clsx({
                  [styles['item']]: true,
                  [styles['selected']]: selectedMaterial === item.namespace,
                })}
              >
                <div className={styles['name']} title={item.namespace}>
                  {item.namespace}
                </div>
                {selectedCountOption === 'count' && (
                  <div className={styles['count']}>{item.count}</div>
                )}
                {selectedCountOption === 'addedCount' && (
                  <div
                    className={
                      item.addedCount < 0
                        ? styles['negative-count']
                        : item.addedCount > 0
                          ? styles['positive-count']
                          : undefined
                    }
                  >
                    {item.addedCount > 0
                      ? `+ ${item.addedCount}`
                      : item.addedCount}
                  </div>
                )}
                {selectedCountOption === 'growthRate' && (
                  <div
                    className={
                      item.count < 0
                        ? styles['negative-count']
                        : item.count > 0
                          ? styles['positive-count']
                          : undefined
                    }
                  >
                    {item.count > 0
                      ? (
                        <>
                          <UpOutlined />
                          {item.count}
                        </>
                      )
                      : item.count < 0
                        ? (
                          <>
                            <DownOutlined />
                            {item.count}
                          </>
                        )
                        : item.count}
                  </div>
                )}
              </div>
            ))}
          </div>
          <div className={styles['total']}>
            共计
            {' '}
            {filteredAndSortedMaterialRanks.length}
            {' '}
            个物料
          </div>
        </div>
      </div>
      <div className={styles['detail']}>
        <div className={styles['fixed-container']}>
          <div className={styles['header']}>
            <div className={styles['title']}>物料信息</div>
          </div>
          <MaterialBaseInfo namespace={selectedMaterial ?? ''} />
          <div className={styles['header']}>
            <div className={styles['title']}>使用的页面列表</div>
            <div className={styles['subtitle']}>
              （仅包含选中的应用范围的页面）
            </div>
          </div>
          <Table
            loading={loading}
            rowKey="id"
            size="small"
            columns={[
              {
                title: '页面',
                dataIndex: 'ref_page',
                key: 'ref_page',
                render: text => (
                  <a href={text} target="_blank" rel="noreferrer">
                    {text}
                  </a>
                ),
              },
              {
                title: '引用次数',
                dataIndex: 'refer_count',
                key: 'refer_count',
              },
            ]}
            dataSource={referPages}
            pagination={{
              pageSize: 10,
              hideOnSinglePage: true,
              total: referPages.length,
            }}
          />
        </div>
      </div>
    </div>
  )
}

export default MaterialRank
