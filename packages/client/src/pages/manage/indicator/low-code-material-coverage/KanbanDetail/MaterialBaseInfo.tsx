import { fetchKaelMaterialBaseInfoByNamespace } from '@/services/v2/material'
import { Descriptions, Typography } from 'antd'
import React, { useEffect, useState } from 'react'

interface Props {
  namespace: string
}

const MaterialBaseInfo: React.FC<Props> = (props) => {
  const { namespace } = props

  const [baseInfo, setBaseInfo] = useState<Service.Material.MaterialBaseInfo>()
  useEffect(() => {
    fetchKaelMaterialBaseInfoByNamespace(namespace).then((res) => {
      setBaseInfo(res.data.data)
    })
  }, [namespace])

  return baseInfo
    ? (
      <Descriptions bordered>
        <Descriptions.Item label="名称">{baseInfo.name}</Descriptions.Item>
        <Descriptions.Item label="最新版本号">
          {baseInfo.newestVersion}
        </Descriptions.Item>
        <Descriptions.Item label="详情页地址">
          <a href={baseInfo.previewURL} target="_blank" rel="noopener noreferrer">
            物料详情页
          </a>
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">
          {new Date(baseInfo.createTime).toLocaleString()}
        </Descriptions.Item>
        <Descriptions.Item label="更新时间">
          {new Date(baseInfo.updateTime).toLocaleString()}
        </Descriptions.Item>
        <Descriptions.Item label="创建者">
          {baseInfo.creator.name}
          (
          {baseInfo.creator.username}
          )
        </Descriptions.Item>
      </Descriptions>
    )
    : (
      <Typography.Text type="secondary">
        暂未在物料中台找到该物料，请及时添加。或联系维护者（@yingpengsha）检查。
      </Typography.Text>
    )
}

export default MaterialBaseInfo
