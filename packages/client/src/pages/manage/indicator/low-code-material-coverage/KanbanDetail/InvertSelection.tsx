const InvertSelection = () => {
  return (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="1em"
      height="1em"
    >
      <path
        d="M844.8 251.5h-72.1v-26.3c0-82.5-67.1-149.6-149.6-149.6H213.6C131.1 75.7 64 142.8 64 225.3v409.5c0 82.5 67.1 149.6 149.6 149.6h49.6v48.7c0 63.5 51.7 115.2 115.2 115.2h466.4c63.5 0 115.2-51.7 115.2-115.2V366.7c0-63.5-51.7-115.2-115.2-115.2z m-631.2 436c-29.1 0-52.7-23.6-52.7-52.7V225.3c0-29.1 23.7-52.7 52.7-52.7h409.5c29.1 0 52.7 23.6 52.7 52.7v26.3H378.4c-63.5 0-115.2 51.7-115.2 115.2v320.8h-49.6z m617.8-181.3L602 762c-3.8 4.2-7.8 6.7-10.5 8.3-9.3 5.7-18.7 8.4-28.2 8.4-11.9 0-23-4.1-32.1-11.8L394.7 651.2c-10.4-8.8-16.7-21.1-17.9-34.7-1.1-13.6 3.1-26.8 11.9-37.1 9.7-11.5 23.9-18 38.9-18 12 0 23.7 4.3 32.9 12l98.7 83.6 196.3-218.9c9.7-10.8 23.5-16.9 37.9-16.9 12.6 0 24.7 4.6 34 13 21 18.8 22.8 51.1 4 72z"
        fill="currentColor"
      >
      </path>
    </svg>
  )
}

export default InvertSelection
