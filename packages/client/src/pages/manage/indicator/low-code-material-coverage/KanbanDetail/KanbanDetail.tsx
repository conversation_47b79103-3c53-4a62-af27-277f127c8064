import { <PERSON><PERSON>, Card, Space, Spin, Tooltip } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import {
  isArray,
  isObject,
  isStringArray,
} from '@global-material-middleoffice/server-v2/shared'
import Icon from '@ant-design/icons'
import clsx from 'clsx'

import {
  fetchTianheAppPageReferUsage,
  fetchTianheMaterialsReferCount,
  fetchTianheUsageGrowth,
  fetchTianheUseRateGrowth,
} from '@/services/v2/material-refer'
import { queryProjectCollectionDetail } from '@/services/v2/project-collection'
import AutoFullParentLoading from '@/components/auto-full-parent-loading'
import { isAdmin } from '@/utils'

import MaterialRank from './MaterialRank'
import ProjectRank from './ProjectRank'
import UpdateCollectionDialog from '../UpdateCollectionDialog'

import InvertSelectionIcon from './InvertSelection'

import styles from './style.module.less'
import LineChart from './LineChart'

interface Props {
  collectionId: number
  refreshList?: () => void
}

const dimensionOptions = [
  { label: '物料维度', value: 'material' },
  { label: '应用维度', value: 'app' },
]

const KanbanDetail: React.FC<Props> = ({ collectionId, refreshList }) => {
  const [loading, setLoading] = useState(false)
  const [kanbanData, setKanbanData]
    = useState<Service.Analysis.ProjectCollection.ProjectCollection>()

  const fetchKanbanData = () => {
    if (collectionId) {
      setLoading(true)
      queryProjectCollectionDetail(collectionId)
        .then((res) => {
          setKanbanData(res.data.data)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }
  useEffect(fetchKanbanData, [collectionId])

  // ======================== Segmented Options ========================
  const [selectedOption, setSelectedOption] = useState('material')

  const noneBracketMaterialNamespaces = useMemo<string[]>(() => {
    return (
      kanbanData?.materials?.map(
        (item: Service.Analysis.ProjectCollection.IdNameObject) => {
          return item.name.replace(/\([^)]*\)/g, '').trim()
        },
      ) || []
    )
  }, [kanbanData])

  const materialNamespaceFilterItems = useMemo<string[]>(() => {
    if (
      isObject(kanbanData?.payload)
      && 'materialLibrary' in kanbanData.payload
      && isArray(kanbanData.payload.materialLibrary)
      && isStringArray(kanbanData.payload.materialLibrary)
    ) {
      return noneBracketMaterialNamespaces.concat(
        kanbanData?.payload?.materialLibrary?.map(
          (item: string) => item + '%',
        ) ?? [],
      )
    }
    else {
      return noneBracketMaterialNamespaces
    }
  }, [noneBracketMaterialNamespaces, kanbanData])

  const userTargetAppKeys = useMemo<string[]>(() => {
    return (
      kanbanData?.projects?.map(
        (item: Service.Analysis.ProjectCollection.IdNameObject) => item.id + '',
      ) ?? []
    )
  }, [kanbanData])

  // ======================== invert-selection ========================
  const [inverted, setInverted] = useState(false)

  // ======================== Material Rank ========================
  const [materialRank, setMaterialRank] = useState<
    { namespace: string, count: number, addedCount: number }[]
  >([])
  useEffect(() => {
    if (kanbanData) {
      setLoading(true)
      fetchTianheMaterialsReferCount(
        materialNamespaceFilterItems,
        userTargetAppKeys,
        inverted,
      )
        .then(async (res) => {
          const currentDataRank: { namespace: string, count: number }[]
            = res.data.data
          const sinceDateRank: { namespace: string, count: number }[] = (
            await fetchTianheMaterialsReferCount(
              materialNamespaceFilterItems,
              userTargetAppKeys,
              inverted,
              kanbanData.start_time!,
            )
          ).data.data
          setMaterialRank(
            currentDataRank.map((item) => {
              const sinceDateItem = sinceDateRank.find(
                sinceDateItem => sinceDateItem.namespace === item.namespace,
              )
              return {
                ...item,
                addedCount: sinceDateItem
                  ? item.count - sinceDateItem.count
                  : item.count,
              }
            }),
          )
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }, [kanbanData, inverted, materialNamespaceFilterItems, userTargetAppKeys])

  // ======================== app ========================
  const [appMaterialUsage, setAppMaterialUsage] = useState<
    Service.Material.Refer.TianheAppPageReferUsage[]
  >([])
  useEffect(() => {
    if (kanbanData) {
      setLoading(true)
      fetchTianheAppPageReferUsage(userTargetAppKeys)
        .then((res) => {
          setAppMaterialUsage(res.data.data)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }, [kanbanData, userTargetAppKeys])

  // ======================== line chart ========================
  const [fetchingChartData, setFetchingChartData] = useState({
    usage: false,
    useRate: false,
  })
  const [usageGrowth, setUsageGrowth] = useState<
    Service.Material.Refer.DailyMaterialUsageGrowth[]
  >([])
  useEffect(() => {
    if (kanbanData) {
      setFetchingChartData(prev => ({ ...prev, usage: true }))
      fetchTianheUsageGrowth(materialNamespaceFilterItems, userTargetAppKeys)
        .then((res) => {
          if (Array.isArray(res.data.data)) {
            const data: Service.Material.Refer.DailyMaterialUsageGrowth[]
              = res.data.data
            setUsageGrowth(
              data.filter(
                item =>
                  item.time > kanbanData.start_time!
                  && item.time < (kanbanData.end_time || +Date.now()),
              ),
            )
          }
        })
        .finally(() => {
          setFetchingChartData(prev => ({ ...prev, usage: false }))
        })
    }
  }, [kanbanData, materialNamespaceFilterItems, userTargetAppKeys])

  const [useRateGrowth, setUseRateGrowth] = useState<
    Service.Material.Refer.DailyMaterialUsageGrowth[]
  >([])
  useEffect(() => {
    if (kanbanData) {
      setFetchingChartData(prev => ({ ...prev, useRate: true }))
      fetchTianheUseRateGrowth(materialNamespaceFilterItems, userTargetAppKeys)
        .then((res) => {
          if (Array.isArray(res.data.data)) {
            const data: Service.Material.Refer.DailyMaterialUsageGrowth[]
              = res.data.data
            setUseRateGrowth(
              data
                .filter(
                  item =>
                    item.time > kanbanData.start_time!
                    && item.time < (kanbanData.end_time || +Date.now()),
                )
                .map((item) => {
                  item.value = Math.floor(item.value * 10000) / 100
                  return item
                }),
            )
          }
        })
        .finally(() => {
          setFetchingChartData(prev => ({ ...prev, useRate: false }))
        })
    }
  }, [kanbanData, materialNamespaceFilterItems, userTargetAppKeys])

  return (
    <div
      className={clsx({
        [styles['kanban-detail']]: true,
        [styles['invert']]: inverted,
      })}
    >
      <Space>
        {(isAdmin() || __DEV__) && kanbanData && (
          <UpdateCollectionDialog
            initialValues={kanbanData}
            loading={loading}
            onUpdate={() => {
              fetchKanbanData()
              refreshList?.()
            }}
          />
        )}
      </Space>

      <Card title="物料覆盖率">
        <Spin spinning={fetchingChartData.usage && fetchingChartData.useRate}>
          <h3 style={{ textAlign: 'center' }}>物料覆盖率</h3>
          <LineChart useRateGrowth={useRateGrowth} usageGrowth={usageGrowth} />
        </Spin>
      </Card>
      <Card
        title={(
          <Space>
            <span>覆盖详情</span>
            {!!materialNamespaceFilterItems.length && (
              <Tooltip title="指定范围以外的物料使用情况">
                <Button
                  icon={<Icon component={InvertSelectionIcon} />}
                  onClick={() => setInverted(pre => !pre)}
                  type={inverted ? 'primary' : 'default'}
                >
                  反选物料
                </Button>
              </Tooltip>
            )}
          </Space>
        )}
        onTabChange={setSelectedOption}
        activeTabKey={selectedOption}
        tabList={dimensionOptions.map(item => ({
          key: item.value,
          tab: item.label,
        }))}
      >
        {selectedOption === 'material' && (
          <MaterialRank
            materialRanks={materialRank}
            appKeys={userTargetAppKeys}
          />
        )}
        {selectedOption === 'app' && (
          <ProjectRank
            appMaterialUsage={appMaterialUsage}
            namespaces={materialRank.map(item => item.namespace)}
          />
        )}
      </Card>

      <AutoFullParentLoading loading={loading} />
    </div>
  )
}

export default KanbanDetail
