import React, { useMemo } from 'react'
import ReactECharts from 'echarts-for-react' // 确保导入正确
import dayjs from 'dayjs'

type ChartProps = {
  usageGrowth: Service.Material.Refer.DailyMaterialUsageGrowth[]
  useRateGrowth: Service.Material.Refer.DailyMaterialUsageGrowth[]
}

const LineChart: React.FC<ChartProps> = ({ usageGrowth, useRateGrowth }) => {
  const chartOptions = useMemo(() => {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 使用指针
          type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
        },
      },
      legend: {
        data: ['物料覆盖率 (%)', '物料使用次数'],
      },
      xAxis: {
        name: '时间',
        type: 'category',
        nameLocation: 'middle',
        data: usageGrowth.map(item => dayjs(item.time).format('YYYY-MM-DD')),
        nameGap: 20,
        nameTextStyle: {
          fontWeight: 'bold',
          color: 'black',
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '物料覆盖率 (%)',
          min: 0,
          max: 100,
          position: 'left',
          axisLabel: {
            formatter: '{value}%',
          },
          nameTextStyle: {
            fontWeight: 'bold',
            color: 'black',
          },
        },
        {
          type: 'value',
          name: '物料使用次数',
          min: 0,
          position: 'right',
          axisLabel: {
            formatter: '{value}',
          },
          nameTextStyle: {
            fontWeight: 'bold',
            color: 'black',
          },
        },
      ],
      series: [
        {
          name: '物料覆盖率 (%)',
          data: useRateGrowth.map(item => item.value),
          type: 'bar', // 修改这里将覆盖率的显示方式改为柱状图
          yAxisIndex: 0,
          barWidth: '60%', // 可以调整柱状图的宽度
          itemStyle: {
            color: '#2989FF',
          },
        },
        {
          name: '物料使用次数',
          data: usageGrowth.map(item => item.value),
          type: 'line',
          yAxisIndex: 1,
          smooth: true, // 使线条平滑显示
          itemStyle: {
            color: '#67c23a',
          },
          lineStyle: {
            width: 2,
            color: '#67c23a',
          },
        },
      ],
      grid: {
        top: '60px',
        left: '10px',
        right: '40px',
        bottom: '30px',
        containLabel: true,
      },
      responsive: true,
      maintainAspectRatio: false,
    }
  }, [usageGrowth, useRateGrowth])

  return (
    <ReactECharts
      option={chartOptions}
      style={{ height: '400px', width: '100%' }}
    />
  )
}

export default LineChart
