import React, { useMemo, useState } from 'react'
import { Input, Table } from 'antd'
import clsx from 'clsx'

import styles from './style.module.less'

interface Props {
  appMaterialUsage: Service.Material.Refer.TianheAppPageReferUsage[]
  namespaces: string[]
}

const ProjectRank: React.FC<Props> = (props) => {
  const { appMaterialUsage, namespaces } = props

  const [selectedProject, setSelectedProject]
    = useState<Service.Material.Refer.TianheAppPageReferUsage>()
  const appRank = useMemo(() => {
    const apps = appMaterialUsage
      .map((app) => {
        let sumMaterialCount = 0
        let targetMaterialCount = 0
        for (const page of app.pages) {
          for (const material of page.materials) {
            if (namespaces.includes(material.namespace)) {
              targetMaterialCount += material.count
            }
            sumMaterialCount += material.count
          }
        }
        return {
          appKey: app.appKey,
          appName: app.appName,
          targetMaterialCount:
            namespaces.length === 0 ? sumMaterialCount : targetMaterialCount,
          sumMaterialCount,
        }
      })
      .sort(
        (a, b) =>
          b.targetMaterialCount - a.targetMaterialCount
          || b.targetMaterialCount / b.sumMaterialCount
          - a.targetMaterialCount / a.sumMaterialCount
          || b.sumMaterialCount - a.sumMaterialCount,
      )
    setSelectedProject(
      appMaterialUsage.find(item => item.appKey === apps[0].appKey),
    )

    return apps
  }, [appMaterialUsage, namespaces])

  const [searchValue, setSearchValue] = useState('')
  const filteredMaterialRanks = useMemo(
    () => appRank.filter(item => item.appName.includes(searchValue)),
    [appRank, searchValue],
  )

  const pageRank = useMemo(() => {
    return (
      selectedProject?.pages
        .map((item) => {
          let sumMaterialCount = 0
          let targetMaterialCount = 0
          for (const material of item.materials) {
            if (namespaces.includes(material.namespace)) {
              targetMaterialCount += material.count
            }
            sumMaterialCount += material.count
          }
          return {
            ...item,
            targetMaterialCount:
              namespaces.length === 0 ? sumMaterialCount : targetMaterialCount,
            sumMaterialCount,
            materials: item.materials.sort((a, b) => b.count - a.count),
          }
        })
        .sort(
          (a, b) =>
            b.targetMaterialCount - a.targetMaterialCount
            || b.targetMaterialCount / b.sumMaterialCount
            - a.targetMaterialCount / a.sumMaterialCount
            || b.sumMaterialCount - a.sumMaterialCount,
        ) || []
    )
  }, [selectedProject, namespaces])

  return (
    <div className={styles['content']}>
      <div className={styles['rank']}>
        <div className={styles['fixed-container']}>
          <Input.Search
            allowClear
            placeholder="搜索应用"
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
          />
          <div className={styles['list']}>
            {filteredMaterialRanks.map(app => (
              <div
                key={app.appKey}
                className={clsx({
                  [styles['item']]: true,
                  [styles['selected']]: selectedProject?.appKey === app.appKey,
                })}
                onClick={() =>
                  setSelectedProject(
                    appMaterialUsage.find(item => item.appKey === app.appKey),
                  )}
              >
                <div className={styles['name']}>{app.appName || '-'}</div>
                <div className={styles['count']}>
                  {app.targetMaterialCount}
                  /
                  {app.sumMaterialCount}
                </div>
              </div>
            ))}
          </div>
          <div className={styles['total']}>
            共计
            {' '}
            {filteredMaterialRanks.length}
            {' '}
            个应用
          </div>
        </div>
      </div>
      <div className={styles['detail']}>
        <div className={styles['fixed-container']}>
          <div className={styles['header']}>
            <div className={styles['title']}>页面列表</div>
          </div>
          <div className={styles['table']}>
            <Table
              rowKey="pageUrl"
              size="small"
              columns={[
                {
                  title: '页面',
                  dataIndex: 'pageName',
                  key: 'pageName',
                },
                {
                  title: '地址',
                  dataIndex: 'pageUrl',
                  key: 'pageUrl',
                  render: (pageUrl: string) => {
                    return (
                      <a href={pageUrl} target="_blank" rel="noreferrer">
                        {pageUrl}
                      </a>
                    )
                  },
                },
                {
                  title: '目标物料总数',
                  dataIndex: 'targetMaterialCount',
                },
                {
                  title: '物料总数',
                  dataIndex: 'sumMaterialCount',
                },
              ]}
              dataSource={pageRank}
              expandable={{
                expandedRowRender: record => (
                  <>
                    <Table
                      dataSource={
                        namespaces.length > 0
                          ? record.materials.filter(item =>
                            namespaces.includes(item.namespace),
                          )
                          : record.materials
                      }
                      rowKey="namespace"
                      size="small"
                      columns={[
                        {
                          title: '物料',
                          dataIndex: 'namespace',
                          key: 'namespace',
                        },
                        {
                          title: '引用次数',
                          dataIndex: 'count',
                          key: 'count',
                        },
                      ]}
                    />
                  </>
                ),
                rowExpandable: () => true,
              }}
              pagination={{
                pageSize: 10,
                hideOnSinglePage: true,
                total: pageRank.length,
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProjectRank
