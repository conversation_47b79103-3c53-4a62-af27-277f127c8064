.spin-container {
  flex: 1;
  ::global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.input-bar {
  display: flex;
  gap: 12px;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
}

.kanban-detail {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  position: relative;

  .chart {
    background-color: white;
    padding: 12px;
    border-radius: 2px;
    border: 2px solid var(--border-color);
  }

  .content {
    flex: 1;
    display: flex;
    gap: 24px;
    min-height: 700px;

    .rank {
      border: 2px solid var(--border-color);
      width: 32%;
      display: flex;
      background-color: white;
      padding: 24px;
      border-radius: 2px;
      position: relative;
      overflow: hidden;

      .list {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        max-height: 100%;

        .item {
          display: flex;
          justify-content: space-between;
          padding: 10px 14px;
          transition: 0.3;
          border-radius: 2px;
          box-sizing: border-box;
          cursor: pointer;

          .name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &:hover {
            background-color: #f5f5f5;
          }
        }

        .selected {
          background-color: #f1f1f1;
        }
      }

      .total {
        font-size: 12px;
        color: #999;
      }
    }

    .detail {
      border: 2px solid var(--border-color);
      position: relative;
      width: 68%;
      display: flex;
      flex-direction: column;
      gap: 24px;
      background-color: white;
      padding: 24px;
      border-radius: 2px;

      .header {
        display: flex;
        gap: 12px;
        align-items: center;

        .title {
          font-size: 20px;
          font-weight: 600;
        }

        .sub-title {
          font-size: 14px;
          color: #666;
        }
      }

      .table {
        flex: 1;
        overflow: auto;
      }
    }
  }
}

.invert {
  .rank {
    border: 2px #2f54eb solid !important;
  }

  .detail {
    border: 2px #2f54eb solid !important;
  }
}

.fixed-container {
  position: absolute;
  top: 24px;
  left: 24px;
  right: 24px;
  bottom: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow: auto;
}

.negative-count {
  color: green;
  text-wrap: nowrap;
}

.positive-count {
  color: red;
  text-wrap: nowrap;
}


.detail-bar {
  display: flex;
  align-items: center;

  :global {
    .ant-tabs-nav {
      margin-bottom: 0px;
    }
  }
}
