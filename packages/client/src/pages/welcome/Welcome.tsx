import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

import Features from './Features'
import Material from './Material'
import Guide from './Guide'
import Situation from './Situation'

import StarSVG from './assets/star.svg?react'
import ALotSVG from './assets/alot.svg?react'
import GuideSVG from './assets/guide.svg?react'
import MetricsSVG from './assets/metrics.svg?react'

import styles from './style.module.less'

const Welcome: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const query = new URLSearchParams(location.search)

  return (
    <div className={styles['welcome']}>
      <div className={styles['cover']}>
        <div className={styles['title']}>
          <h1>物料中台</h1>
          <p>整合物料链路，助力业务高效运行</p>
          <div
            className={styles['entry-button']}
            onClick={() =>
              navigate('/', { state: { business: query.get('business') } })}
          >
            进入组件市场
          </div>
        </div>
        <div>
          <img
            height="100%"
            src="https://w1.beckwai.com/kos/nlav12333/fangzhou/imgs/cover.png"
            alt="cover"
          />
        </div>
      </div>
      <div className={styles['quick-jump']}>
        <div
          className={styles['jump-item']}
          onClick={() => (window.location.hash = 'features')}
        >
          <div className={styles['img']}>
            <StarSVG width="100%" height="100%" />
          </div>
          功能介绍
        </div>
        <div
          className={styles['jump-item']}
          onClick={() => (window.location.hash = 'materials')}
        >
          <div className={styles['img']}>
            <ALotSVG width="100%" height="100%" />
          </div>
          丰富物料
        </div>
        <div
          className={styles['jump-item']}
          onClick={() => (window.location.hash = 'guide')}
        >
          <div className={styles['img']}>
            <GuideSVG width="100%" height="100%" />
          </div>
          接入指南
        </div>
        <div
          className={styles['jump-item']}
          onClick={() => (window.location.hash = 'situation')}
        >
          <div className={styles['img']}>
            <MetricsSVG width="100%" height="100%" />
          </div>
          接入情况
        </div>
      </div>
      <Features />
      <Material />
      <Guide />
      <Situation />
    </div>
  )
}

export default Welcome
