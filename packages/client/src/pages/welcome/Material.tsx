import React, { useState, useEffect } from 'react'
import { Statistic, StatisticProps, Typography } from 'antd'
import { RightOutlined } from '@ant-design/icons'
import CountUp from 'react-countup'
import { useNavigate } from 'react-router-dom'

import styles from './style.module.less'
import { BusinessMap } from '@/constants'
import { queryMaterialCount } from '@/services/material'

const formatter: StatisticProps['formatter'] = value => (
  <CountUp end={value as number} separator="," />
)

const Material: React.FC = () => {
  return (
    <div id="materials" className={styles['materials']}>
      <Typography.Title level={3}>丰富物料</Typography.Title>
      <div className={styles['businesses']}>
        {Object.entries(BusinessMap).map(([key]) => (
          <BusinessItem key={key} business={key as Business} />
        ))}
      </div>
    </div>
  )
}

const BusinessItem = (props: { business: Business }) => {
  const { business } = props
  const [count, setCount] = useState(0)
  const navigate = useNavigate()

  useEffect(() => {
    queryMaterialCount({ business }).then((res) => {
      setCount(res.data?.[0].total)
    })
  }, [])

  return (
    <div key={business} className={styles['business']}>
      <div
        className={styles['title']}
        onClick={() => navigate('/', { state: { business } })}
      >
        <h1>{BusinessMap[business]}</h1>
        <RightOutlined />
      </div>
      <Statistic title="物料数量" value={count} formatter={formatter} />
    </div>
  )
}

export default Material
