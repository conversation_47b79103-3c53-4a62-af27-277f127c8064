@import '../../styles/mixin.less';

.welcome {
  .small-fixed-page-margin();
  padding: 80px 0;
  display: flex;
  flex-direction: column;
  gap: 40px;

  .entry-button {
    display: flex;
    justify-content: center;
    gap: 20px;
    width: fit-content;
    background: #174ae6;
    border-radius: 26px;
    font-size: 18px;
    line-height: 20px;
    padding: 16px 40px;
    color: white;
    cursor: pointer;
  }

  .cover {
    width: 100%;
    height: 400px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    overflow: hidden;

    .title {
      display: flex;
      flex-direction: column;
      padding: 60px;

      h1 {
        font-size: 64px;
        font-weight: 700;
      }

      p {
        font-size: 24px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }

  .quick-jump {
    width: 100%;
    display: flex;
    justify-content: space-around;
    gap: 20px;

    .jump-item {
      flex: 1;
      text-align: center;
      line-height: 50px;
      border-radius: 4px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      font-weight: 700;
      position: relative;
      overflow: hidden;
      cursor: pointer;

      .img {
        position: absolute;
        top: -10px;
        left: -10px;
        width: 50px;
        height: 50px;
        opacity: 0.6;
      }
    }
  }
}

.features {
  display: flex;
  flex-direction: column;
  gap: 40px;

  .feature-list {
    // grid layout, 2 columns, gap 20px
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;

    .feature-item {
      position: relative;
      padding: 20px;
      border-radius: 4px;
      box-shadow: 0 0 1px rgba(0, 0, 0, 0.4);
      background: linear-gradient(to right, #ebf4ff, #ffffff);
      overflow: hidden;
      p {
        max-width: 63%;
      }

      .img {
        position: absolute;
        top: -5px;
        right: -5px;
        height: 80px;
        width: 80px;
        opacity: 0.3;
      }
    }
  }
}

.materials {
  display: flex;
  flex-direction: column;
  gap: 40px;

  .businesses {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    .business {
      padding: 20px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      border-left: 4px solid #4C7CEE;

      .title {
        display: flex;
        justify-content: space-between;
        cursor: pointer;
      }
    }
  }
}

.guide {
  display: flex;
  flex-direction: column;
  gap: 40px;

  .guides {
    display: flex;
    gap: 20px;

    .guide-item {
      height: 300px;
      box-shadow: 0 0 1px rgba(0, 0, 0, 0.4);
      padding: 20px;
      display: flex;
      flex-direction: column;
      flex: 1;
      border-radius: 4px;
      position: relative;
      box-sizing: border-box;
    }
  }
}

.situation {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.deep-blue-linear {
  background: linear-gradient(to right, #174ae6, #7eacf7);
  h1 {
    color: white;
  }
}

.simple-blue-linear {
  background: linear-gradient(to right, #7eacf7, #a7c6f1);
  h1 {
    color: white;
  }
}

.blue-green-linear {
  background: linear-gradient(to right, #a7c6f1, #93ddee);
  h1 {
    color: white;
  }
}

.deep-green-linear {
  background: linear-gradient(to right, #93ddee, #3bc6e5);
  h1 {
    color: white;
  }
}
