import React from 'react'
import { Typography } from 'antd'

import styles from './style.module.less'
import clsx from 'clsx'

const Guide: React.FC = () => {
  return (
    <div id="guide" className={styles['guide']}>
      <Typography.Title level={3}>接入指南</Typography.Title>
      <div className={styles['guides']}>
        <div
          className={clsx(styles['guide-item'], styles['deep-blue-linear'])}
          onClick={() => window.open('/manage', '_blank')}
        >
          <h1>物料管理</h1>
        </div>
        <div
          className={clsx(styles['guide-item'], styles['simple-blue-linear'])}
          onClick={() =>
            window.open(
              'https://docs.corp.kuaishou.com/d/home/<USER>',
              '_blank',
            )}
        >
          <h1>开放 APIs</h1>
        </div>
        <div
          className={clsx(styles['guide-item'], styles['blue-green-linear'])}
          onClick={() => window.open('/schema', '_blank')}
        >
          <h1>通用物料协议</h1>
        </div>
        <div
          className={clsx(styles['guide-item'], styles['deep-green-linear'])}
          onClick={() =>
            window.open(
              'https://docs.corp.kuaishou.com/d/home/<USER>',
              '_blank',
            )}
        >
          <h1>物料转换脚本</h1>
        </div>
      </div>
    </div>
  )
}

export default Guide
