import React, { useState, useEffect, useMemo } from 'react'
import { Typography } from 'antd'
import { Area } from '@ant-design/charts'

import { queryMaterialGrowth } from '@/services/material'

import styles from './style.module.less'

interface WeeklyDataPoint {
  date: string
  value: number
}

const Situation: React.FC = () => {
  const [growth, setGrowth] = useState([])
  useEffect(() => {
    queryMaterialGrowth().then((res) => {
      setGrowth(fillDataWithCumulativeValues(res.data))
    })
  }, [])

  const roundToNearestTen = (num: number): number => {
    return Math.round(num / 10) * 10
  }

  const fillDataWithCumulativeValues = (
    data: { create_date: string, count: number }[],
  ): WeeklyDataPoint[] => {
    const weeklyData: WeeklyDataPoint[] = []
    let cumulativeValue = 0

    // 将字符串日期转换为 Date 对象，便于处理
    const dataWithDates = data.map(item => ({
      ...item,
      date: new Date(item.create_date),
    }))

    // 对数据按日期进行排序
    dataWithDates.sort((a, b) => a.date.getTime() - b.date.getTime())

    // 找到每周的起始日期并计算累积值
    let currentWeekStart = new Date(dataWithDates[0].date)
    currentWeekStart.setDate(
      currentWeekStart.getDate() - currentWeekStart.getDay(),
    ) // 设定为本周的周一

    dataWithDates.forEach((item) => {
      // 如果当前数据点不在当前周范围内，则创建新的一周
      if (
        item.date.getTime()
        >= currentWeekStart.getTime() + 7 * 24 * 60 * 60 * 1000
      ) {
        weeklyData.push({
          date: currentWeekStart.toISOString().split('T')[0],
          value: roundToNearestTen(cumulativeValue),
        })
        // 更新当前周的起始日期
        currentWeekStart = new Date(item.date)
        currentWeekStart.setDate(
          currentWeekStart.getDate() - currentWeekStart.getDay(),
        )
      }

      // 累积值加上当前数据点的值
      cumulativeValue += item.count
    })

    // 添加最后一周的累积值
    weeklyData.push({
      date: currentWeekStart.toISOString().split('T')[0],
      value: roundToNearestTen(cumulativeValue),
    })

    return weeklyData
  }

  const config = useMemo(
    () => ({
      data: growth,
      xField: 'date',
      yField: 'value',
      shapeField: 'smooth',
      areaStyle: { fillOpacity: 0.6 },
      style: {
        fill: 'linear-gradient(to right, #174ae6, #7eacf7, #a7c6f1, #93ddee)',
      },
      xAxis: {
        type: 'timeCat',
        title: { text: '时间' },
        label: {
          autoHide: true, // 自动隐藏部分标签
          autoRotate: false, // 禁止自动旋转
        },
        tickInterval: 14 * 24 * 60 * 60 * 1000, // 一周的毫秒数，用于控制每周显示一次标签
      },
      yAxis: {
        title: { text: '个数' },
      },
      axis: {
        y: {
          title: '个数',
        },
        x: {
          title: '时间',
        },
      },
    }),
    [growth],
  )

  return (
    <div id="situation" className={styles['situation']}>
      <Typography.Title level={3}>接入情况</Typography.Title>
      <Area {...config} />
    </div>
  )
}

export default Situation
