import React from 'react'
import { Typography } from 'antd'

import StoreSVG from './assets/store.svg?react'
import BlocksSVG from './assets/blocks.svg?react'
import ConversionSVG from './assets/conversion.svg?react'
import ExponentSVG from './assets/exponent.svg?react'

import styles from './style.module.less'

const Features: React.FC = () => {
  return (
    <div id="features" className={styles['features']}>
      <Typography.Title level={3}>功能介绍</Typography.Title>
      <div className={styles['feature-list']}>
        <div className={styles['feature-item']}>
          <div className={styles['img']}>
            <StoreSVG width="100%" height="100%" />
          </div>

          <h1>存储</h1>
          <p>物料底层的数据模型统一，提供PaaS化的物料存储能力。</p>
        </div>
        <div className={styles['feature-item']}>
          <div className={styles['img']}>
            <BlocksSVG width="100%" height="100%" />
          </div>
          <h1>消费</h1>
          <p>提供围绕着物料领域的各类消费场景的服务。</p>
        </div>
        <div className={styles['feature-item']}>
          <div className={styles['img']}>
            <ConversionSVG width="100%" height="100%" />
          </div>
          <h1>转换</h1>
          <p>基于统一的标准协议，打破各团队物料之间流转围墙。</p>
        </div>
        <div className={styles['feature-item']}>
          <div className={styles['img']}>
            <ExponentSVG width="100%" height="100%" />
          </div>
          <h1>度量</h1>
          <p>
            制定统一的度量指标，拉齐各方标准，提升物料复用率，为”降本增效“提供物料层面的数据支撑。
          </p>
        </div>
      </div>
    </div>
  )
}

export default Features
