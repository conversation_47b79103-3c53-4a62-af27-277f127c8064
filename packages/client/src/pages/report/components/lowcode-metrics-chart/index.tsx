import React, { useState, useEffect, FC } from 'react'
import { Line } from '@mchart/pc-react'
import { observer } from 'mobx-react-lite'
import NP from 'number-precision'
import { ReportType } from '@/types'

export interface LineDataProps {
  xData: Array<string>
  yData: Record<string, Array<AnyType>>
}

const LowCodeLineChart: FC<LineDataProps> = observer(({ xData, yData }) => {
  const [lineData, setLineData] = useState([])

  useEffect(() => {
    setLineData([
      {
        name: '物料复用率',
        data: yData[ReportType.POLAR_STAR]?.map(item => NP.times(item.value, 100)) || [],
        showSymbol: true,
        tooltip: {
          valueFormatter: value => `${value}%`,
        },
      },
    ])
  }, [yData])

  return (
    <Line
      style={{
        height: '450px',
      }}
      series={lineData}
      option={{
        xAxis: { data: xData },
        // 双轴
        yAxis: [
          {
            type: 'value',
            name: '比率',
            axisLabel: { formatter: '{value}%' },
          },
        ],
      }}
    />
  )
})

export default LowCodeLineChart
