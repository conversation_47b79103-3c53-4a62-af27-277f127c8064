import React, { CSSProperties } from 'react'
import { Popover, Tooltip } from 'antd'
import { TooltipPlacement } from 'antd/es/tooltip'
import { ProfileOutlined } from '@ant-design/icons'

import styles from './index.module.less'

const overlayStyle = {
  width: '208px',
}

const overlayInnerStyle = {
  padding: '0 10px 16px',
  background: '#fff',
  borderRadius: '8px',
}

export interface CustomTooltipProps {
  /** 呈现形式 */
  type?: 'popover' | 'tooltip'
  title: string
  placement?: TooltipPlacement
  desc: React.ReactNode
  style?: CSSProperties
  className?: string
  children?: React.ReactNode
}

/**
 * 业务定制tooltip
 * 一般用于专有名词介绍
 */
const CustomTooltip: React.FC<CustomTooltipProps> = (props) => {
  const { type = 'tooltip', placement = 'bottom', title, desc, className = '', children, style } = props

  const renderChildren = children || (
    <div className={`${styles['customTooltip']} ${className}`} style={style}>
      {title}
    </div>
  )

  if (type === 'tooltip') {
    const renderTitle = (
      <div className={styles['customTooltipBox']}>
        <div className={styles['header']}>
          <ProfileOutlined className="mr-6" />
          {title}
        </div>
        <div className={styles['content']}>{desc}</div>
      </div>
    )
    return (
      <Tooltip
        zIndex={4}
        title={renderTitle}
        placement={placement}
        color="#fff"
        overlayStyle={overlayStyle}
        overlayInnerStyle={overlayInnerStyle}
      >
        {renderChildren}
      </Tooltip>
    )
  }

  const renderTitle = (
    <span>
      <ProfileOutlined />
      {title}
    </span>
  )
  return (
    <Popover content={desc} title={renderTitle} placement={placement} zIndex={4}>
      {renderChildren}
    </Popover>
  )
}

export default CustomTooltip
