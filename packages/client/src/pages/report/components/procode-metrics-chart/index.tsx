import React, { FC, useMemo } from 'react'
import { Line } from '@mchart/pc-react'
import { observer } from 'mobx-react-lite'
import NP from 'number-precision'
import { ReportType } from '@/types'

export interface LineDataProps {
  xData: Array<string>
  yData: Record<string, Array<AnyType>>
}
const ProCodeLineChart: FC<LineDataProps> = observer(({ xData, yData }) => {
  const lineData = useMemo(() => {
    return [
      {
        name: '代码复用率',
        data: yData[ReportType.POLAR_STAR]?.map(item => NP.times(item.value, 100)) || [],
        showSymbol: true,
      },
      {
        name: '项目覆盖率',
        data: yData[ReportType.PROJECT_COVERAGE_RATE]?.map(item => NP.times(item.value, 100)) || [],
        showSymbol: true,
      },
      {
        name: '页面覆盖率',
        data: yData[ReportType.PAGE_COVERAGE_RATE]?.map(item => NP.times(item.value, 100)) || [],
        showSymbol: true,
      },
      {
        name: '新增页面覆盖率',
        data: yData[ReportType.NEW_PAGE_COVERAGE_RATE]?.map(item => NP.times(item.value, 100)) || [],
        showSymbol: true,
      },
    ]
  }, [yData])

  return (
    <Line
      style={{ height: '450px' }}
      series={lineData}
      option={{
        xAxis: { data: xData },
        tooltip: {
          valueFormatter: value => `${value}%`,
        },
        // 双轴
        yAxis: [
          {
            type: 'value',
            name: '比率',
            axisLabel: { formatter: '{value}%' },
          },
        ],
      }}
    />
  )
})

export default ProCodeLineChart
