import React, { FC, useEffect, useState } from 'react'
import {
  DatePicker,
  DatePickerProps,
  Select,
  Spin,
  Tabs,
  Typography,
} from 'antd'
import NP from 'number-precision'
import cls from 'clsx'
import moment from 'dayjs'
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import {
  getPreDayRange,
  getPreWeekRange,
} from 'mybricks-material-backend/common/util/date'
import { reportModel } from '@/stores'
import { businessOptions } from '@/constants'
import { MetricsIcon, PolarStarIcon } from '../../icons'
import CustomTooltip from './components/custom-tooltip'
import ProCodeLineChart from './components/procode-metrics-chart'
import LowCodeLineChart from './components/lowcode-metrics-chart'
import { lowCodeReportMetricsList, proCodeReportMetricsList } from './config'

import styles from './index.module.less'
import { CODE_TYPE } from '@global-material-middleoffice/server-v2/shared'

export interface MetricsReportCardProps {
  title: string
  description: string
  value: string | number
  trend?: number
  className?: string
  active?: string
  loop_rate?: number
  onClick?: (val) => void
  format: (value) => number
}

moment.locale('zh-cn', {
  weekStart: 1,
})
const MetricsReportCard: FC<MetricsReportCardProps> = ({
  title,
  description,
  value,
  loop_rate,
  format,
  onClick,
  className = '',
}) => {
  return (
    <div className={styles.materialCardInfoWrapper}>
      <div
        className={cls(styles.metricsCardInfo, className)}
        onClick={() => onClick?.(title)}
      >
        <CustomTooltip
          className={styles.mb10}
          title={title}
          desc={description}
          placement="right"
        />
        <div className={cls(styles.cardValue, styles.mb10)}>
          {value ? format(value) : '--'}
        </div>
        <div>
          环比: &nbsp;
          <span className={loop_rate > 0 ? styles.riseRate : ''}>
            {loop_rate ? `${NP.times(loop_rate, 100)}%` : '--'}
            {' '}
            {loop_rate > 0
              ? (
                <ArrowUpOutlined />
              )
              : loop_rate < 0
                ? (
                  <ArrowDownOutlined />
                )
                : undefined}
          </span>
        </div>
      </div>
      <div className={styles.icon}>
        {title.includes('代码复用率')
          ? (
            <PolarStarIcon width={32} height={32} />
          )
          : (
            <MetricsIcon width={32} height={32} />
          )}
      </div>
    </div>
  )
}

const weekFormat = 'YYYY/MM/DD'
const MetricsReport: FC = () => {
  const {
    loading,
    reportData,
    lineLoading,
    xData,
    searchParams,
    codeOverview,
    updateSearchParams,
    isProCode,
  } = reportModel
  const [active, setActive] = useState(undefined)

  const preWeek = getPreWeekRange()

  // 获取这一周的起始日期
  const curWeekStartDate = moment().startOf('week')
  const disabledDate = (current) => {
    return current > curWeekStartDate.valueOf()
  }
  useEffect(() => {
    reportModel.init()
    return () => reportModel.reset()
  }, [])

  const onChange: DatePickerProps['onChange'] = (date: AnyType, dateString) => {
    if (dateString === '') {
      updateSearchParams(
        'timeRange',
        { startTime: preWeek.start, endTime: preWeek.end },
        true,
      )
      return
    }
    const startTime = moment(date).startOf('week').valueOf()
    const endTime = moment(date).endOf('week').valueOf()
    updateSearchParams('timeRange', { startTime, endTime }, true)
  }

  const onChangeDay: DatePickerProps['onChange'] = (
    date: AnyType,
    dateString,
  ) => {
    if (dateString === '') {
      const timeRange = getPreDayRange()
      updateSearchParams(
        'timeRange',
        { startTime: timeRange.start, endTime: timeRange.end },
        true,
      )
      return
    }
    updateSearchParams(
      'timeRange',
      {
        startTime: moment(date).startOf('day').valueOf(),
        endTime: moment(date).endOf('day').valueOf(),
      },
      true,
    )
  }

  const handleBuChange = (value) => {
    updateSearchParams('business', value, true)
  }

  const customWeekStartEndFormat: DatePickerProps['format'] = (
    value: AnyType,
  ) =>
    `${moment(value).startOf('week').format(weekFormat)} ~ ${moment(value)
      .endOf('week')
      .format(weekFormat)}`

  const onChangeTabKey = (key) => {
    const timeRange
      = key === CODE_TYPE.LOW_CODE ? getPreDayRange() : getPreWeekRange()
    updateSearchParams('codeType', key, false)
    updateSearchParams(
      'timeRange',
      { startTime: timeRange.start, endTime: timeRange.end },
      false,
    )
    updateSearchParams('business', '', true)
  }

  return (
    <div className={styles.metricsOverview}>
      <div className={styles.metricsOverviewContent}>
        <Tabs activeKey={searchParams.codeType} onChange={onChangeTabKey}>
          <Tabs.TabPane tab="源代码" key={CODE_TYPE.PRO_CODE}></Tabs.TabPane>
          <Tabs.TabPane tab="低代码" key={CODE_TYPE.LOW_CODE}></Tabs.TabPane>
        </Tabs>
        <div className={styles.topMetricsArea}>
          <div className={styles.dimensionArea}>
            <Select
              value={searchParams.business}
              placeholder="请选择部门"
              onChange={e => handleBuChange(e)}
              options={[
                { label: '总览', value: '' },
                ...(isProCode()
                  ? businessOptions.slice(2)
                  : businessOptions.slice(0, 2)),
              ]}
              style={{ width: '240px', marginRight: '16px' }}
            />
            {isProCode()
              ? (
                <DatePicker
                  disabledDate={disabledDate}
                  allowClear
                  onChange={onChange}
                  format={customWeekStartEndFormat}
                  picker="week"
                  style={{ width: '240px' }}
                />
              )
              : (
                <DatePicker
                  allowClear
                  disabledDate={current =>
                    (current as AnyType) >= moment().startOf('day').valueOf()}
                  onChange={onChangeDay}
                  style={{ width: '240px' }}
                />
              )}
          </div>
          <Spin spinning={loading}>
            <div className={styles.metricReportList}>
              {(isProCode()
                ? proCodeReportMetricsList
                : lowCodeReportMetricsList
              ).map((item) => {
                const overViewData
                  = codeOverview.find(oItem => oItem.type === item.field) || {}

                return (
                  <MetricsReportCard
                    onClick={val => setActive(val)}
                    active={active}
                    key={item.field}
                    {...item}
                    {...overViewData}
                  />
                )
              })}
            </div>
          </Spin>
          <div className={styles.chartWrapper}>
            <Typography.Title level={5}>详细数据</Typography.Title>
            <div>
              <Spin spinning={lineLoading}>
                {isProCode()
                  ? (
                    <ProCodeLineChart xData={xData} yData={reportData} />
                  )
                  : (
                    <LowCodeLineChart xData={xData} yData={reportData} />
                  )}
              </Spin>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default observer(MetricsReport)
