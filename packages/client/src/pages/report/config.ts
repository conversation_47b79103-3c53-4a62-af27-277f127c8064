import NP from 'number-precision'
import { ReportType } from '@/types'

export const proCodeReportMetricsList = [
  {
    title: '代码复用率',
    description: '复用了的物料的源代码行数 / （同一工程范围内使用了一次物料的源代码行数 + 工程源代码行数） 加权平均',
    field: ReportType.POLAR_STAR,
    format: value => `${NP.times(value, 100)}%`,
  },
  {
    title: '页面覆盖率',
    description: '使用了物料库的页面数 / 总页面数 ',
    field: ReportType.PAGE_COVERAGE_RATE,
    format: value => `${NP.times(value, 100)}%`,
  },
  {
    title: '新增页面覆盖率',
    description: '使用了物料库的新增页面数 / 总新增页面数 ',
    field: ReportType.NEW_PAGE_COVERAGE_RATE,
    format: value => `${NP.times(value, 100)}%`,
  },
  {
    title: '项目覆盖率',
    description: '使用了物料库的项目数 / 总项目数',
    field: ReportType.PROJECT_COVERAGE_RATE,
    format: value => `${NP.times(value, 100)}%`,
  },
]

export const lowCodeReportMetricsList = [
  {
    title: '物料复用率',
    description: '（单页面通用组件使用次数 / 单页面总的组件数）加权平均',
    value: '--',
    field: ReportType.POLAR_STAR,
    format: value => typeof value === 'number' ? `${NP.times(value, 100)}%` : value,
  },
  // {
  //  title: '成本效益',
  //  description: '人效提升',
  //  value: '--',
  //  field: ReportType.COST_EFFECTIVENESS,
  //  format: _ => _
  // }
]
