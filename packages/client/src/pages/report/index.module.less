@import '../../styles/mixin.less';

.metricsOverview {
  width: 100%;

  &Content {
    .fixed-page-margin();
    padding: 24px 0;
  }
}

.dimensionArea {
  display: flex;
  justify-content: flex-start;
}

.topMetricsArea {
  display: flex;
  flex-direction: column;
}

.riseRate {
  color: #f93920;
}

.metricReportList {
  display: flex;
  flex-direction: row;
  margin-top: 24px;
}

.materialCardInfoWrapper {
  display: flex;
  justify-content: center;
  width: 320px;
  border: 1px solid #ebedf0;
  padding: 16px 24px;
  margin-right: 40px;
  height: 130px;
  border-radius: 5px;
  align-items: center;
  box-shadow: 0 0 10px 2px #f0f2f5;
}

.materialCardInfoWrapper:hover {
  transform: scale(1.02);
  box-shadow: rgba(99, 99, 99, 0.2) 0 2px 8px 0;
}

.metricsCardInfo {
  flex-shrink: 0;
  width: 240px;
  cursor: pointer;
  position: relative;

  &:last-of-type {
    margin-right: 0;
  }

  &::before {
    content: '';
    display: block;
    width: 0;
    height: 3px;
    position: absolute;
    top: -2px;
    left: 0;
    background: #477bff;
    opacity: 0;
    transition: all 0.2s;
  }

  .cardValue {
    font-size: 32px;
    line-height: 32px;
    font-family: 'Helvetica Neue', serif;
    font-weight: 500;
  }

  .sub-title {
    font-size: 12px;
  }
}

.chartWrapper {
  margin-top: 48px;
  margin-bottom: 48px;
}

.activeCard {
  border-top: 1px solid #0075ff;

  .overview-currency,
  .cardValue {
    color: #0075ff;
  }

  &::before {
    width: 100%;
    opacity: 1;
  }
}

.count2 {
  flex-basis: 25%;
}
.count4 {
  flex-basis: 25%;
}

.metricsInfoValue {
  font-size: 32px;
  line-height: 32px;
  font-weight: 500;
}

.mb10 {
  margin-bottom: 10px;
}
