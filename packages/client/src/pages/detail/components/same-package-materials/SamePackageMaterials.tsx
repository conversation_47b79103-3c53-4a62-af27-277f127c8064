import React, { useEffect, useState } from 'react'
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons'
import { Button, Drawer, Tag } from 'antd'
import clsx from 'clsx'

import { MaterialType } from '@/constants'
import { fetchMaterialList } from '@/services/v2/material'

import styles from './style.module.less'
import { useNavigate } from 'react-router'

interface Props {
  packageName: string
  currentMaterialNamespace: string
}

const SamePackageMaterials: React.FC<Props> = (props) => {
  const { packageName, currentMaterialNamespace } = props
  const navigator = useNavigate()
  const [expanded, setExpanded] = useState(false)

  const [loading, setLoading] = useState(false)
  const [materials, setMaterials] = useState<Service.Material.MaterialDetail[]>([])
  useEffect(() => {
    setLoading(true)
    fetchMaterialList({
      namespace: packageName,
      pageSize: 200,
    }).then((res) => {
      setMaterials(res.data.data.list)
    }).finally(() => setLoading(false))
  }, [packageName])

  return (
    <>
      <Drawer
        visible={expanded}
        placement="left"
        zIndex={8}
        width={300}
        onClose={() => setExpanded(false)}
        className={styles['drawer']}
        headerStyle={{ display: 'none' }}
        destroyOnClose
      >
        <div className={styles['drawer-content']}>
          {materials.map(material => (
            <div
              key={material.id}
              title={material.namespace}
              onClick={() => {
                setExpanded(false)
                navigator(`/detail?material_id=${material.id}&business=${material.business}`)
              }}
              className={clsx({
                [styles['material-item']]: true,
                [styles['selected']]: material.namespace === currentMaterialNamespace,
              })}
            >
              <div className={styles['title']}>
                <span className={styles['name']}>{material.title}</span>
                {material.type === MaterialType.COM_LIB && <Tag color="purple">物料库</Tag>}
              </div>
              <div className={styles['namespace']}>{material.namespace}</div>
            </div>
          ))}
        </div>
      </Drawer>
      <Button
        type="text"
        icon={expanded ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
        className={styles['expanded-button']}
        style={{ left: expanded ? 300 : 0 }}
        onClick={() => setExpanded(!expanded)}
        disabled={loading || materials.length <= 1}
        loading={loading}
      >
        {`相关物料 ( ${materials.length > 0 ? materials.length - 1 : 0} )`}
      </Button>
    </>
  )
}

export default SamePackageMaterials
