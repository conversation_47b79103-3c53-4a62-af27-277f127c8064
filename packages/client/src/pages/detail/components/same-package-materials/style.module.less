.expanded-button {
  position: fixed;
  left: 0;
  top: 140px;
  z-index: 10;
  background-color: white;
  border-radius: 0 2px 2px 0 !important;
  border: 1px solid #e8e8e8;
  border-left: 0px;
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);

  &:hover, &:focus, &:active {
    background-color: white;
    border: 1px solid #e8e8e8;
    border-left: 0px;
  }
}

.drawer {
  top: 117px !important;
  bottom: 0px !important;
  width: 300px;

  :global {
    .ant-drawer-mask {
      opacity: 0 !important;
    }
  }
}

.drawer-content {
  display: flex;
  flex-direction: column;
  height: unset;
  gap: 8px;

  .material-item {
    display: flex;
    flex-direction: column;
    gap: 8px 16px;
    cursor: pointer;
    padding: 8px;
    border: 2px;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background-color: #3abcf93e;
    }

    .title {
      display: flex;
      gap: 8px;

      .name {
        font-weight: bold;
      }
    }

    .namespace {
      color: #8c8c8c;
      // 文本不换行忽略
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
