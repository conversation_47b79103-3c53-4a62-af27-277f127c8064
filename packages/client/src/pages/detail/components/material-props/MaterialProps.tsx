import React, { useEffect, useMemo, useState } from 'react'
import {
  Button,
  Card,
  Empty,
  Form,
  Input,
  message,
  Radio,
  Select,
  Space,
  Tree,
} from 'antd'
import { useForm } from 'antd/lib/form/Form'
import { createTreeMate, TreeNode } from 'treemate'
import ReactJson from 'react-json-view'
import { CopyOutlined, DownOutlined } from '@ant-design/icons'
import {
  isArray,
  isObject,
} from '@global-material-middleoffice/server-v2/shared'
import Editor from '@monaco-editor/react'

import { isCorp } from '@/utils/env'
import { clipboard } from '@/utils/BOM'
import { nextTick } from '@/utils/function'
import { userModel } from '@/stores'
import { updateMaterialInfo } from '@/services'

// import MOCK_SCHEMA from './mock.json'
import { TYPE_ARRAY } from './const'

import styles from './style.module.less'

interface Props {
  initialVersion?: string
  versions: Service.Material.Publish.NormalizePublishRecord[]
}

interface PropFormValues {
  label?: string
  type: keyof typeof TYPE_ARRAY
  description?: string
  tag: 'normal' | 'style' | 'dataSource'
  isShow: 1 | 2
  defaultValue?: string
}

const MaterialProps: React.FC<Props> = (props) => {
  const { initialVersion, versions = [] } = props

  // ======================== select version ========================
  const [selectedVersion, setSelectedVersion] = useState(initialVersion)
  useEffect(() => {
    if (initialVersion) {
      setSelectedVersion(initialVersion)
    }
    else if (versions.length) {
      setSelectedVersion(versions[0].version)
    }
  }, [initialVersion])

  // ======================== version detail ========================
  const currentVersionDetail = useMemo(() => {
    return versions.find(item => item.version === selectedVersion)
  }, [selectedVersion])
  const currentVersionPropsRawDefine = useMemo<
    GeneralMaterialSchema.Prop[]
  >(() => {
    // return MOCK_SCHEMA.props
    return currentVersionDetail?.schema?.props ?? []
  }, [currentVersionDetail])

  // 属性树
  const currentVersionPropertiesTreeData = useMemo(() => {
    const isProp = (
      prop: GeneralMaterialSchema.Prop,
    ): prop is GeneralMaterialSchema.Prop => {
      return isObject(prop) && 'identifier' in prop
    }
    return createTreeMate(currentVersionPropsRawDefine.filter(isProp), {
      getChildren: (prop: GeneralMaterialSchema.Prop) => {
        if ('members' in prop && isArray(prop.members)) {
          return prop.members.filter(isProp)
        }
        else {
          return []
        }
      },
      getIgnored: (prop: GeneralMaterialSchema.Prop) => {
        return !isProp(prop)
      },
    })
  }, [currentVersionPropsRawDefine])

  // 属性详情
  const [selectedProp, setSelectedProp]
    = useState<
      TreeNode<
        GeneralMaterialSchema.Prop,
        GeneralMaterialSchema.Prop,
        GeneralMaterialSchema.Prop
      >
    >()

  // 属性编辑
  const [form] = useForm<PropFormValues>()
  useEffect(() => {
    form.resetFields()
    nextTick(() => {
      form.setFieldsValue({
        label: selectedProp?.rawNode?.label,
        type: selectedProp?.rawNode?.type,
        description:
          selectedProp?.rawNode?.description ?? selectedProp?.rawNode?.comments,
        tag: selectedProp?.rawNode?.tag ?? 'normal',
        isShow: selectedProp?.rawNode?.isShow || 1,
        defaultValue: JSON.stringify(
          selectedProp?.rawNode?.defaultValue,
          null,
          2,
        ),
      } as unknown as PropFormValues)
    })
  }, [selectedProp])

  const [submitting, setSubmitting] = useState(false)
  const onFinish = async (values: PropFormValues) => {
    try {
      // 尝试解析 JSON
      const parsedDefaultValue = values.defaultValue
        ? JSON.parse(values.defaultValue)
        : undefined

      const { componentBundleType, packageName, componentName }
        = currentVersionDetail?.schema ?? {}

      if (selectedProp?.rawNode) {
        Object.assign(selectedProp.rawNode, {
          ...values,
          defaultValue: parsedDefaultValue,
        })
      }

      setSubmitting(true)
      const res = await updateMaterialInfo({
        param: {
          updater: isCorp() ? userModel.user.user_name : 'yingpengsha',
          namespace:
            componentBundleType === 'SLMC'
              ? `${packageName}/${componentName}`
              : packageName,
          version: selectedVersion,
          schema: {
            props: currentVersionPropertiesTreeData.treeNodes.map(
              node => node.rawNode,
            ),
          },
        },
      })

      if (res.code === 1) {
        message.success('更新成功')
      }
      else {
        throw new Error('更新失败')
      }
    }
    catch (error) {
      if (error instanceof SyntaxError) {
        message.error('默认值 JSON 格式不正确')
      }
      else {
        message.error('更新失败')
      }
    }
    finally {
      setSelectedProp(Object.assign({}, selectedProp))
      setSubmitting(false)
    }
  }

  return (
    <Space direction="vertical" size={16} style={{ width: '100%' }}>
      <Space>
        <span>版本选择：</span>
        <Select
          value={selectedVersion}
          onChange={value => setSelectedVersion(value)}
          style={{ width: 200 }}
          options={versions.map(item => ({
            label: item.version,
            value: item.version,
          }))}
        />
      </Space>
      <div className={styles['props-editor']}>
        <Card
          size="small"
          className={styles['props-selector']}
          title="属性选择"
          extra={(
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              disabled={!currentVersionPropsRawDefine}
              onClick={() =>
                clipboard(JSON.stringify(currentVersionPropsRawDefine, null, 2))}
            />
          )}
        >
          <div className={styles['scroll-container']}>
            {!currentVersionPropertiesTreeData.treeNodes.length && (
              <Empty description="该版本无属性定义" />
            )}
            <Tree
              showLine
              selectable
              defaultExpandAll
              onSelect={(_, { selected, node }) => {
                if (selected) {
                  setSelectedProp(node)
                }
                else {
                  setSelectedProp(undefined)
                }
              }}
              switcherIcon={<DownOutlined />}
              treeData={currentVersionPropertiesTreeData.treeNodes}
              titleRender={node => (
                <div>
                  <b>{node.rawNode.identifier}</b>
                  {!!node.rawNode?.comments && (
                    <span style={{ color: 'gray', marginLeft: 4 }}>
                      {`-- ${node.rawNode.comments}`}
                    </span>
                  )}
                </div>
              )}
            />
          </div>
        </Card>
        <Card
          size="small"
          className={styles['props-detail']}
          title="属性详情"
          extra={(
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              disabled={!selectedProp}
              onClick={() =>
                clipboard(JSON.stringify(selectedProp?.rawNode, null, 2))}
            />
          )}
        >
          <div className={styles['scroll-container']}>
            {!selectedProp && <Empty description="请选择属性" />}
            {selectedProp && (
              <ReactJson
                src={selectedProp?.rawNode}
                name={selectedProp?.rawNode?.identifier || 'props'}
                displayDataTypes={false}
                enableClipboard={false}
              />
            )}
          </div>
        </Card>
        <Card size="small" className={styles['props-form']} title="编辑属性">
          <div className={styles['scroll-container']}>
            {!selectedProp && <Empty description="请选择属性" />}
            <Form
              name="material-prop"
              style={{ display: selectedProp ? 'block' : 'none' }}
              form={form}
              labelAlign="left"
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
              layout="horizontal"
              onFinish={onFinish}
            >
              <Form.Item name="label" label="属性中文名称">
                <Input placeholder="请输入属性中文名" disabled={submitting} />
              </Form.Item>
              <Form.Item name="description" label="属性描述">
                <Input placeholder="请输入属性的描述" disabled={submitting} />
              </Form.Item>
              <Form.Item
                name="tag"
                label="属性分类"
                rules={[{ required: true }]}
              >
                <Radio.Group
                  optionType="button"
                  disabled={submitting}
                  options={[
                    { label: '普通属性', value: 'normal' },
                    { label: '样式', value: 'style' },
                    { label: '数据源', value: 'dataSource' },
                  ]}
                />
              </Form.Item>
              <Form.Item name="isShow" required label="是否透出当前属性">
                <Radio.Group disabled={submitting}>
                  <Radio value={1}>是</Radio>
                  <Radio value={2}>否</Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item name="type" label="属性类型" required>
                <Radio.Group
                  options={TYPE_ARRAY.map(type => ({
                    label: type,
                    value: type,
                  }))}
                  disabled={submitting}
                />
              </Form.Item>
              <Form.Item
                name="defaultValue"
                label="默认值"
                rules={[
                  {
                    validator: (_, value) => {
                      if (!value) return Promise.resolve()
                      try {
                        JSON.parse(value)
                        return Promise.resolve()
                      }
                      catch (_) {
                        return Promise.reject('请输入有效的 JSON 格式')
                      }
                    },
                  },
                ]}
              >
                <Editor
                  height="300px"
                  defaultLanguage="json"
                  className={styles['editor']}
                  theme="vs"
                  options={{
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 14,
                    lineNumbers: 'on',
                    roundedSelection: false,
                    wordWrap: 'on',
                    scrollbar: {
                      vertical: 'visible',
                      horizontal: 'visible',
                    },
                  }}
                />
              </Form.Item>
              <Form.Item label={' '} colon={false}>
                <Space>
                  <Button type="primary" htmlType="submit" loading={submitting}>
                    更新
                  </Button>
                  <Button
                    htmlType="button"
                    onClick={() => form.resetFields()}
                    disabled={submitting}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        </Card>
      </div>
    </Space>
  )
}

export default MaterialProps
