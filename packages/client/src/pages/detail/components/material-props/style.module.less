.props-editor {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  gap: 16px;

  .scroll-container {
    height: 800px;
    width: 100%;
    overflow: auto;
  }

  .props-selector {
    width: 300px;
    height: 100%;
  }

  .props-detail {
    width: 400px;
    height: 100%;
  }

  .props-form {
    flex: 1;
  }
}

.editor {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: all 0.3s;

  &:hover {
    border-color: #40a9ff;
  }

  &.focused {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}
