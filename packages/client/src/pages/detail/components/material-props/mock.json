{"packageName": "@es/kpro-data-line-chart", "componentName": "default", "componentBundleType": "SLSC", "componentChineseName": "折线图&&面积图", "description": "@es/kpro-data-line-chart", "dependencies": {"@es/kpro-tech-common-event-collector": "^1.3.0", "@es/universal-common": "^0.0.9", "safe-json-parse-and-stringify": "^0.2.0", "size-sensor": "^1.0.1"}, "devDependencies": {"@antv/g2": "4.2.8", "@babel/core": "^7.24.3", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.24.1", "@es/dumi-theme-material-antd": "0.1.1-alpha.31", "@es/image": "^1.1.2", "@es/jia-types": "latest", "@es/kpro-data-color-theme": "^0.1.4-beta.1", "@es/kpro-data-strategy-reco": "^1.0.3-beta.8", "@es/kpro-data-utils": "1.1.5-beta.2", "@es/kpro-protocol-utils": "^1.0.3", "@es/kpro-types": "^0.0.1", "@es/material-analyze": "0.1.1-beta.44", "@es/material-upload-cover": "0.1.1-beta.3", "@es/request": "^1.4.1", "@fangzhou/parse-plugin": "1.0.33", "@m-ui/react": "^2.2.1", "@mc/cli": "^0.4.26-alpha.2", "@mc/util": "^0.4.16-beta.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^12.1.3", "@types/jest": "^29.5.0", "@types/jest-image-snapshot": "^6.4.0", "@types/lodash-es": "^4.17.9", "@types/node": "^17.0.23", "@types/react": "^17.0.39", "@types/react-dom": "^17.0.11", "babel-jest": "^27.5.1", "cross-env": "^7.0.3", "dayjs": "1.11.13", "dumi": "^2.2.1", "identity-obj-proxy": "^3.0.0", "jest": "^29.5.0", "jest-css-modules": "^2.1.0", "jest-environment-jsdom": "^29.7.0", "jest-image-snapshot": "^6.4.0", "lodash-es": "^4.17.21", "react": "^17.0.2", "react-dom": "^17.0.2", "ts-jest": "^29.0.5", "typescript": "^4.5.5"}, "peerDependencies": {"@antv/g2": "^4.0", "@es/kpro-data-color-theme": "^0.1", "@es/kpro-data-strategy-reco": "^1.0", "@es/kpro-data-utils": "^1.0", "@m-ui/react": "^2.2.1"}, "gitUrl": "*************************:plateco-dev-fe/kwaishop-tech/kpro-data-line-chart.git", "props": [{"identifier": "type", "optional": false, "comments": "趋势图类型", "defaultValue": "multipleLine-singleAxis", "type": "union", "members": [{"value": "singleLine-singleAxis", "type": "literal"}, {"value": "doubleLine-doubleAxis", "type": "literal"}, {"value": "multipleLine-singleAxis", "type": "literal"}, {"value": "multipleLine-noAxis", "type": "literal"}]}, {"identifier": "sourceData", "optional": false, "comments": "图表数据源", "defaultValue": [{"category": "gmv", "label": "12:00", "time": 27666960, "value": {"value": 1164, "unit": "元"}, "valueType": "MONEY_SUFFIX", "rate": null, "desc": "实时成交金额"}, {"category": "watch", "label": "12:00", "time": 27666960, "value": 5811, "valueType": "AMOUNT", "rate": null, "desc": "实时观看人数"}, {"category": "gmv", "label": "12:01", "time": 27666961, "value": {"value": 7275, "unit": "元"}, "valueType": "MONEY_SUFFIX", "rate": null, "desc": "实时成交金额"}, {"category": "watch", "label": "12:01", "time": 27666961, "value": 3537, "valueType": "AMOUNT", "rate": null, "desc": "实时观看人数"}, {"category": "gmv", "label": "12:02", "time": 27666962, "value": {"value": 6499, "unit": "元"}, "valueType": "MONEY_SUFFIX", "rate": null, "desc": "实时成交金额"}, {"category": "watch", "label": "12:02", "time": 27666962, "value": 7985, "valueType": "AMOUNT", "rate": null, "desc": "实时观看人数"}, {"category": "gmv", "label": "12:03", "time": 27666963, "value": {"value": 9021, "unit": "元"}, "valueType": "MONEY_SUFFIX", "rate": null, "desc": "实时成交金额"}, {"category": "watch", "label": "12:03", "time": 27666963, "value": 2855, "valueType": "AMOUNT", "rate": null, "desc": "实时观看人数"}, {"category": "gmv", "label": "12:04", "time": 27666964, "value": {"value": 8827, "unit": "元"}, "valueType": "MONEY_SUFFIX", "rate": null, "desc": "实时成交金额"}, {"category": "watch", "label": "12:04", "time": 27666964, "value": 2815, "valueType": "AMOUNT", "rate": null, "desc": "实时观看人数"}, {"category": "gmv", "label": "12:05", "time": 27666965, "value": {"value": 1125, "unit": "元"}, "valueType": "MONEY_SUFFIX", "rate": null, "desc": "实时成交金额"}, {"category": "watch", "label": "12:05", "time": 27666965, "value": 3627, "valueType": "AMOUNT", "rate": null, "desc": "实时观看人数"}, {"category": "gmv", "label": "12:06", "time": 27666966, "value": {"value": 9215, "unit": "元"}, "valueType": "MONEY_SUFFIX", "rate": null, "desc": "实时成交金额"}, {"category": "watch", "label": "12:06", "time": 27666966, "value": 4992, "valueType": "AMOUNT", "rate": null, "desc": "实时观看人数"}, {"category": "gmv", "label": "12:07", "time": 27666967, "value": {"value": 1590, "unit": "元"}, "valueType": "MONEY_SUFFIX", "rate": null, "desc": "实时成交金额"}, {"category": "watch", "label": "12:07", "time": 27666967, "value": 5733, "valueType": "AMOUNT", "rate": null, "desc": "实时观看人数"}, {"category": "gmv", "label": "12:08", "time": 27666968, "value": {"value": 7283, "unit": "元"}, "valueType": "MONEY_SUFFIX", "rate": null, "desc": "实时成交金额"}, {"category": "watch", "label": "12:08", "time": 27666968, "value": 5422, "valueType": "AMOUNT", "rate": null, "desc": "实时观看人数"}, {"category": "gmv", "label": "12:09", "time": 27666969, "value": {"value": 1378, "unit": "元"}, "valueType": "MONEY_SUFFIX", "rate": null, "desc": "实时成交金额"}, {"category": "watch", "label": "12:09", "time": 27666969, "value": 6173, "valueType": "AMOUNT", "rate": null, "desc": "实时观看人数"}], "type": "array", "members": [{"type": "object", "interfaceName": "TrendChartPoint", "members": [{"identifier": "category", "optional": true, "comments": "额外的维度标签，或者作为联动key", "type": "string"}, {"identifier": "label", "optional": true, "comments": "标签", "type": "string"}, {"identifier": "value", "optional": true, "comments": "数据", "type": "union", "members": [{"type": "number"}, {"type": "null"}, {"type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "any"}]}]}, {"identifier": "valueType", "optional": true, "comments": "数据类型", "type": "string"}, {"identifier": "rate", "optional": true, "comments": "百分比， 百分位，前端添加’%‘", "type": "union", "members": [{"type": "number"}, {"type": "null"}]}, {"identifier": "desc", "optional": true, "comments": "用于tooltip上中文指标展示", "defaultValue": "单轴多线", "type": "string"}, {"identifier": "categoryLabel", "optional": true, "comments": "指标对应文案", "type": "string"}, {"identifier": "unit", "optional": true, "comments": "期望值与单位分开", "type": "string"}, {}]}]}, {"identifier": "xField", "optional": true, "comments": "x轴取数字段 默认label", "type": "string"}, {"identifier": "yField", "optional": true, "comments": "y轴取数字段 默认value", "type": "string"}, {"identifier": "categoryField", "optional": true, "comments": "类别字段，用于多条线的区分", "type": "string"}, {"identifier": "categoryLabelField", "optional": true, "comments": "指标对应文案的字段，直接在sourceData中下发,可直接生效替换，作用等同于metricLabelMap", "type": "string"}, {"identifier": "strategyData", "optional": true, "comments": "策略推荐时，数据传入", "type": "array", "members": [{"type": "any"}]}, {"identifier": "smooth", "optional": true, "comments": "false折线true曲线", "type": "boolean"}, {"identifier": "showSlider", "optional": true, "comments": "是否展示缩略轴", "type": "boolean"}, {"identifier": "sliderConfig", "optional": true, "comments": "缩略轴配置项", "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "any"}]}, {"identifier": "sliderPadding", "optional": true, "comments": "缩略轴的布局padding", "type": "array", "members": [{"type": "number"}]}, {"identifier": "showLegend", "optional": true, "comments": "是否展示图例", "defaultValue": true, "type": "boolean"}, {"identifier": "showArea", "optional": true, "comments": "是否展示为面积图", "defaultValue": true, "type": "boolean"}, {"identifier": "isShowLadder", "optional": true, "comments": "是否展示为阶梯折线图", "type": "boolean"}, {"identifier": "showPoint", "optional": true, "comments": "是否需要描点", "type": "boolean"}, {"identifier": "showAllAxis", "optional": true, "comments": "是否展示所有轴线（包括x轴）", "defaultValue": true, "type": "boolean"}, {"identifier": "colorArr", "optional": true, "comments": "配色表 [色值1, 色值2, ...]", "type": "array", "members": [{"type": "string"}]}, {"identifier": "lineStyle", "optional": true, "comments": "折线的样式配置", "type": "union", "members": [{"type": "object", "interfaceName": "IStyleCfg", "members": [{"identifier": "fill", "optional": true, "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "fillOpacity", "optional": true, "type": "number"}, {"identifier": "stroke", "optional": true, "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "strokeOpacity", "optional": true, "type": "number"}, {"identifier": "opacity", "optional": true, "type": "number"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON>", "optional": true, "comments": " 阴影模糊效果程度", "type": "number"}, {"identifier": "shadowColor", "optional": true, "comments": " 阴影颜色", "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "shadowOffsetX", "optional": true, "comments": " 阴影 x 方向偏移量", "type": "number"}, {"identifier": "shadowOffsetY", "optional": true, "comments": " 阴影 y 方向偏移量", "type": "number"}, {"identifier": "lineWidth", "optional": true, "type": "number"}, {"identifier": "lineCap", "optional": true, "comments": " 指定如何绘制每一条线段末端", "type": "union", "members": [{"value": "butt", "type": "literal"}, {"value": "round", "type": "literal"}, {"value": "square", "type": "literal"}]}, {"identifier": "lineJoin", "optional": true, "comments": " 用来设置2个长度不为0的相连部分（线段，圆弧，曲线）如何连接在一起的属性（长度为0的变形部分，其指定的末端和控制点在同一位置，会被忽略）", "type": "union", "members": [{"value": "bevel", "type": "literal"}, {"value": "round", "type": "literal"}, {"value": "miter", "type": "literal"}]}, {"identifier": "textAlign", "optional": true, "comments": " 设置文本内容的当前对齐方式", "type": "union", "members": [{"value": "start", "type": "literal"}, {"value": "center", "type": "literal"}, {"value": "end", "type": "literal"}, {"value": "left", "type": "literal"}, {"value": "right", "type": "literal"}]}, {"identifier": "textBaseline", "optional": true, "comments": " 设置在绘制文本时使用的当前文本基线", "type": "union", "members": [{"value": "top", "type": "literal"}, {"value": "hanging", "type": "literal"}, {"value": "middle", "type": "literal"}, {"value": "alphabetic", "type": "literal"}, {"value": "ideographic", "type": "literal"}, {"value": "bottom", "type": "literal"}]}, {"identifier": "fontStyle", "optional": true, "comments": " 字体样式", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "italic", "type": "literal"}, {"value": "oblique", "type": "literal"}]}, {"identifier": "fontSize", "optional": true, "comments": " 文本字体大小", "type": "number"}, {"identifier": "fontFamily", "optional": true, "comments": " 文本字体", "type": "string"}, {"identifier": "fontWeight", "optional": true, "comments": " 文本粗细", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "bold", "type": "literal"}, {"value": "bolder", "type": "literal"}, {"value": "lighter", "type": "literal"}, {"type": "number"}]}, {"identifier": "fontVariant", "optional": true, "comments": " 字体变体", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "small-caps", "type": "literal"}, {"type": "string"}]}, {"identifier": "lineHeight", "optional": true, "comments": " 文本行高", "type": "number"}]}, {"type": "array", "members": [{"type": "object", "interfaceName": "IStyleCfg", "members": [{"identifier": "fill", "optional": true, "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "fillOpacity", "optional": true, "type": "number"}, {"identifier": "stroke", "optional": true, "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "strokeOpacity", "optional": true, "type": "number"}, {"identifier": "opacity", "optional": true, "type": "number"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON>", "optional": true, "comments": " 阴影模糊效果程度", "type": "number"}, {"identifier": "shadowColor", "optional": true, "comments": " 阴影颜色", "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "shadowOffsetX", "optional": true, "comments": " 阴影 x 方向偏移量", "type": "number"}, {"identifier": "shadowOffsetY", "optional": true, "comments": " 阴影 y 方向偏移量", "type": "number"}, {"identifier": "lineWidth", "optional": true, "type": "number"}, {"identifier": "lineCap", "optional": true, "comments": " 指定如何绘制每一条线段末端", "type": "union", "members": [{"value": "butt", "type": "literal"}, {"value": "round", "type": "literal"}, {"value": "square", "type": "literal"}]}, {"identifier": "lineJoin", "optional": true, "comments": " 用来设置2个长度不为0的相连部分（线段，圆弧，曲线）如何连接在一起的属性（长度为0的变形部分，其指定的末端和控制点在同一位置，会被忽略）", "type": "union", "members": [{"value": "bevel", "type": "literal"}, {"value": "round", "type": "literal"}, {"value": "miter", "type": "literal"}]}, {"identifier": "textAlign", "optional": true, "comments": " 设置文本内容的当前对齐方式", "type": "union", "members": [{"value": "start", "type": "literal"}, {"value": "center", "type": "literal"}, {"value": "end", "type": "literal"}, {"value": "left", "type": "literal"}, {"value": "right", "type": "literal"}]}, {"identifier": "textBaseline", "optional": true, "comments": " 设置在绘制文本时使用的当前文本基线", "type": "union", "members": [{"value": "top", "type": "literal"}, {"value": "hanging", "type": "literal"}, {"value": "middle", "type": "literal"}, {"value": "alphabetic", "type": "literal"}, {"value": "ideographic", "type": "literal"}, {"value": "bottom", "type": "literal"}]}, {"identifier": "fontStyle", "optional": true, "comments": " 字体样式", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "italic", "type": "literal"}, {"value": "oblique", "type": "literal"}]}, {"identifier": "fontSize", "optional": true, "comments": " 文本字体大小", "type": "number"}, {"identifier": "fontFamily", "optional": true, "comments": " 文本字体", "type": "string"}, {"identifier": "fontWeight", "optional": true, "comments": " 文本粗细", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "bold", "type": "literal"}, {"value": "bolder", "type": "literal"}, {"value": "lighter", "type": "literal"}, {"type": "number"}]}, {"identifier": "fontVariant", "optional": true, "comments": " 字体变体", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "small-caps", "type": "literal"}, {"type": "string"}]}, {"identifier": "lineHeight", "optional": true, "comments": " 文本行高", "type": "number"}]}]}]}, {"identifier": "areaStyle", "optional": true, "type": "union", "members": [{"type": "object", "interfaceName": "IStyleCfg", "members": [{"identifier": "fill", "optional": true, "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "fillOpacity", "optional": true, "type": "number"}, {"identifier": "stroke", "optional": true, "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "strokeOpacity", "optional": true, "type": "number"}, {"identifier": "opacity", "optional": true, "type": "number"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON>", "optional": true, "comments": " 阴影模糊效果程度", "type": "number"}, {"identifier": "shadowColor", "optional": true, "comments": " 阴影颜色", "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "shadowOffsetX", "optional": true, "comments": " 阴影 x 方向偏移量", "type": "number"}, {"identifier": "shadowOffsetY", "optional": true, "comments": " 阴影 y 方向偏移量", "type": "number"}, {"identifier": "lineWidth", "optional": true, "type": "number"}, {"identifier": "lineCap", "optional": true, "comments": " 指定如何绘制每一条线段末端", "type": "union", "members": [{"value": "butt", "type": "literal"}, {"value": "round", "type": "literal"}, {"value": "square", "type": "literal"}]}, {"identifier": "lineJoin", "optional": true, "comments": " 用来设置2个长度不为0的相连部分（线段，圆弧，曲线）如何连接在一起的属性（长度为0的变形部分，其指定的末端和控制点在同一位置，会被忽略）", "type": "union", "members": [{"value": "bevel", "type": "literal"}, {"value": "round", "type": "literal"}, {"value": "miter", "type": "literal"}]}, {"identifier": "textAlign", "optional": true, "comments": " 设置文本内容的当前对齐方式", "type": "union", "members": [{"value": "start", "type": "literal"}, {"value": "center", "type": "literal"}, {"value": "end", "type": "literal"}, {"value": "left", "type": "literal"}, {"value": "right", "type": "literal"}]}, {"identifier": "textBaseline", "optional": true, "comments": " 设置在绘制文本时使用的当前文本基线", "type": "union", "members": [{"value": "top", "type": "literal"}, {"value": "hanging", "type": "literal"}, {"value": "middle", "type": "literal"}, {"value": "alphabetic", "type": "literal"}, {"value": "ideographic", "type": "literal"}, {"value": "bottom", "type": "literal"}]}, {"identifier": "fontStyle", "optional": true, "comments": " 字体样式", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "italic", "type": "literal"}, {"value": "oblique", "type": "literal"}]}, {"identifier": "fontSize", "optional": true, "comments": " 文本字体大小", "type": "number"}, {"identifier": "fontFamily", "optional": true, "comments": " 文本字体", "type": "string"}, {"identifier": "fontWeight", "optional": true, "comments": " 文本粗细", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "bold", "type": "literal"}, {"value": "bolder", "type": "literal"}, {"value": "lighter", "type": "literal"}, {"type": "number"}]}, {"identifier": "fontVariant", "optional": true, "comments": " 字体变体", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "small-caps", "type": "literal"}, {"type": "string"}]}, {"identifier": "lineHeight", "optional": true, "comments": " 文本行高", "type": "number"}]}, {"type": "array", "members": [{"type": "object", "interfaceName": "IStyleCfg", "members": [{"identifier": "fill", "optional": true, "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "fillOpacity", "optional": true, "type": "number"}, {"identifier": "stroke", "optional": true, "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "strokeOpacity", "optional": true, "type": "number"}, {"identifier": "opacity", "optional": true, "type": "number"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON>", "optional": true, "comments": " 阴影模糊效果程度", "type": "number"}, {"identifier": "shadowColor", "optional": true, "comments": " 阴影颜色", "type": "union", "members": [{"type": "string"}, {"type": "null"}]}, {"identifier": "shadowOffsetX", "optional": true, "comments": " 阴影 x 方向偏移量", "type": "number"}, {"identifier": "shadowOffsetY", "optional": true, "comments": " 阴影 y 方向偏移量", "type": "number"}, {"identifier": "lineWidth", "optional": true, "type": "number"}, {"identifier": "lineCap", "optional": true, "comments": " 指定如何绘制每一条线段末端", "type": "union", "members": [{"value": "butt", "type": "literal"}, {"value": "round", "type": "literal"}, {"value": "square", "type": "literal"}]}, {"identifier": "lineJoin", "optional": true, "comments": " 用来设置2个长度不为0的相连部分（线段，圆弧，曲线）如何连接在一起的属性（长度为0的变形部分，其指定的末端和控制点在同一位置，会被忽略）", "type": "union", "members": [{"value": "bevel", "type": "literal"}, {"value": "round", "type": "literal"}, {"value": "miter", "type": "literal"}]}, {"identifier": "textAlign", "optional": true, "comments": " 设置文本内容的当前对齐方式", "type": "union", "members": [{"value": "start", "type": "literal"}, {"value": "center", "type": "literal"}, {"value": "end", "type": "literal"}, {"value": "left", "type": "literal"}, {"value": "right", "type": "literal"}]}, {"identifier": "textBaseline", "optional": true, "comments": " 设置在绘制文本时使用的当前文本基线", "type": "union", "members": [{"value": "top", "type": "literal"}, {"value": "hanging", "type": "literal"}, {"value": "middle", "type": "literal"}, {"value": "alphabetic", "type": "literal"}, {"value": "ideographic", "type": "literal"}, {"value": "bottom", "type": "literal"}]}, {"identifier": "fontStyle", "optional": true, "comments": " 字体样式", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "italic", "type": "literal"}, {"value": "oblique", "type": "literal"}]}, {"identifier": "fontSize", "optional": true, "comments": " 文本字体大小", "type": "number"}, {"identifier": "fontFamily", "optional": true, "comments": " 文本字体", "type": "string"}, {"identifier": "fontWeight", "optional": true, "comments": " 文本粗细", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "bold", "type": "literal"}, {"value": "bolder", "type": "literal"}, {"value": "lighter", "type": "literal"}, {"type": "number"}]}, {"identifier": "fontVariant", "optional": true, "comments": " 字体变体", "type": "union", "members": [{"value": "normal", "type": "literal"}, {"value": "small-caps", "type": "literal"}, {"type": "string"}]}, {"identifier": "lineHeight", "optional": true, "comments": " 文本行高", "type": "number"}]}]}]}, {"identifier": "lineGradientColor", "optional": true, "comments": "折线，渐变色配置，优先级高于colorArr", "type": "array", "members": [{"type": "string"}]}, {"identifier": "xScaleOption", "optional": true, "comments": "x轴坐标轴配置", "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "any"}]}, {"identifier": "tooltipRender", "optional": true, "comments": "自定义悬浮提示（自定义渲染样式） -> customContent", "type": "function", "interfaceName": "TooltipRenderFunc", "members": [], "parameter": [{"identifier": "current<PERSON><PERSON><PERSON>", "optional": false, "type": "string"}, {"identifier": "currentPoints", "optional": false, "type": "union", "members": [{"type": "array", "members": [{"type": "TSTypeReference", "interfaceName": "TooltipItem", "members": [], "nodeModuleInfo": {"from": "@antv/g2/lib/interface"}}]}, {"type": "array", "members": [{"type": "object", "interfaceName": "TooltipItemPro", "members": [{"identifier": "title", "optional": true, "defaultValue": "面积图", "type": "any"}, {}]}]}]}, {"identifier": "originData", "optional": true, "type": "array", "members": [{"type": "object", "interfaceName": "TrendChartPoint", "members": [{"identifier": "category", "optional": true, "comments": "额外的维度标签，或者作为联动key", "type": "string"}, {"identifier": "label", "optional": true, "comments": "标签", "type": "string"}, {"identifier": "value", "optional": true, "comments": "数据", "type": "union", "members": [{"type": "number"}, {"type": "null"}, {"type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "any"}]}]}, {"identifier": "valueType", "optional": true, "comments": "数据类型", "type": "string"}, {"identifier": "rate", "optional": true, "comments": "百分比， 百分位，前端添加’%‘", "type": "union", "members": [{"type": "number"}, {"type": "null"}]}, {"identifier": "desc", "optional": true, "comments": "用于tooltip上中文指标展示", "defaultValue": "单轴多线", "type": "string"}, {"identifier": "categoryLabel", "optional": true, "comments": "指标对应文案", "type": "string"}, {"identifier": "unit", "optional": true, "comments": "期望值与单位分开", "type": "string"}, {}]}]}], "return": {"type": "union", "members": [{"type": "string"}, {"type": "TSTypeReference", "interfaceName": "HTMLElement", "members": []}]}}, {"identifier": "legendConfig", "optional": true, "comments": "图例相关配置项，", "defaultValue": {"position": "top-left", "offsetY": -5, "offsetX": 0, "selected": {"watch": false}}, "type": "object", "interfaceName": "LegendConfig", "members": [{"identifier": "position", "optional": true, "comments": "图例位置，top | top-left | top-right | bottom | bottom-left | bottom-right| right | right-top | right-bottom | left | left-top | right-bottom", "type": "union", "interfaceName": "LegendPositionType", "members": [{"value": "top", "type": "literal"}, {"value": "top-left", "type": "literal"}, {"value": "top-right", "type": "literal"}, {"value": "right", "type": "literal"}, {"value": "right-top", "type": "literal"}, {"value": "right-bottom", "type": "literal"}, {"value": "left", "type": "literal"}, {"value": "left-top", "type": "literal"}, {"value": "left-bottom", "type": "literal"}, {"value": "bottom", "type": "literal"}, {"value": "bottom-left", "type": "literal"}, {"value": "bottom-right", "type": "literal"}]}, {"identifier": "offsetY", "optional": true, "comments": "图例Y轴偏移量", "type": "number"}, {"identifier": "offsetX", "optional": true, "comments": "图例X轴偏移量", "type": "number"}, {"identifier": "symbol", "optional": true, "comments": "配置图例 marker 的 symbol 形状", "type": "string"}, {"identifier": "spacing", "optional": true, "comments": "图例项 marker 同后面 name 的间距", "type": "number"}, {"identifier": "style", "optional": true, "comments": "图例样式", "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "string"}]}, {"identifier": "selected", "optional": true, "comments": "图例高亮状态", "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "boolean"}]}, {"identifier": "itemName", "optional": true, "comments": "图例项 name 文本的配置。", "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "any"}]}, {}]}, {"identifier": "tooltipOffset", "optional": true, "comments": "tooltip偏移量", "type": "number"}, {"identifier": "tooltipCrosshairs", "optional": true, "comments": "配置 tooltip 的辅助线，", "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "any"}]}, {"identifier": "tooltipPosition", "optional": true, "comments": "tooltip位置", "type": "union", "members": [{"value": "left", "type": "literal"}, {"value": "right", "type": "literal"}, {"value": "bottom", "type": "literal"}, {"value": "top", "type": "literal"}]}, {"identifier": "title", "optional": true, "comments": "图表标题", "defaultValue": "面积图", "type": "string"}, {"identifier": "desc", "optional": true, "comments": "图表描述", "defaultValue": "单轴多线", "type": "string"}, {"identifier": "tooltipDataRefactor", "optional": true, "comments": "tooltip自定义数据 -> customItem", "type": "function", "interfaceName": "TooltipDataRefactorFunc", "members": [], "parameter": [{"identifier": "current<PERSON><PERSON><PERSON>", "optional": false, "type": "string"}, {"identifier": "currentPoints", "optional": false, "type": "union", "members": [{"type": "array", "members": [{"type": "TSTypeReference", "interfaceName": "TooltipItem", "members": [], "nodeModuleInfo": {"from": "@antv/g2/lib/interface"}}]}, {"type": "array", "members": [{"type": "object", "interfaceName": "TooltipItemPro", "members": [{"identifier": "title", "optional": true, "defaultValue": "面积图", "type": "any"}, {}]}]}]}, {"identifier": "originData", "optional": true, "type": "array", "members": [{"type": "object", "interfaceName": "TrendChartPoint", "members": [{"identifier": "category", "optional": true, "comments": "额外的维度标签，或者作为联动key", "type": "string"}, {"identifier": "label", "optional": true, "comments": "标签", "type": "string"}, {"identifier": "value", "optional": true, "comments": "数据", "type": "union", "members": [{"type": "number"}, {"type": "null"}, {"type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "any"}]}]}, {"identifier": "valueType", "optional": true, "comments": "数据类型", "type": "string"}, {"identifier": "rate", "optional": true, "comments": "百分比， 百分位，前端添加’%‘", "type": "union", "members": [{"type": "number"}, {"type": "null"}]}, {"identifier": "desc", "optional": true, "comments": "用于tooltip上中文指标展示", "defaultValue": "单轴多线", "type": "string"}, {"identifier": "categoryLabel", "optional": true, "comments": "指标对应文案", "type": "string"}, {"identifier": "unit", "optional": true, "comments": "期望值与单位分开", "type": "string"}, {}]}]}], "return": {"type": "union", "members": [{"type": "array", "members": [{"type": "TSTypeReference", "interfaceName": "TooltipItem", "members": [], "nodeModuleInfo": {"from": "@antv/g2/lib/interface"}}]}, {"type": "array", "members": [{"type": "object", "interfaceName": "TooltipItemPro", "members": [{"identifier": "title", "optional": true, "defaultValue": "面积图", "type": "any"}, {}]}]}]}}, {"identifier": "tooltipEnterable", "optional": true, "comments": "是否允许鼠标划入tooltip", "type": "boolean"}, {"identifier": "dom<PERSON><PERSON><PERSON>", "optional": true, "comments": "Tooltip自定义样式", "type": "TSTypeReference", "interfaceName": "TooltipDomStyles", "members": [], "nodeModuleInfo": {"from": "@antv/g2/lib/interface"}}, {"identifier": "def<PERSON><PERSON><PERSON><PERSON>", "optional": true, "comments": "y轴label数据格式化单位", "type": "string"}, {"identifier": "benchmark", "optional": true, "comments": "配合defaulUnit使用，基准格式化数值，默认是10000。", "type": "number"}, {"identifier": "renderer", "optional": true, "comments": "渲染方案用canvas或者svg", "type": "union", "members": [{"value": "canvas", "type": "literal"}, {"value": "svg", "type": "literal"}]}, {"identifier": "autoFit", "optional": true, "comments": "大小自适应父容器，默认true，", "type": "boolean"}, {"identifier": "width", "optional": true, "comments": "图表的宽度", "type": "number"}, {"identifier": "height", "optional": true, "comments": "图表的高度", "type": "number"}, {"identifier": "visible", "optional": true, "comments": "图表是否可见，默认true-显示 false-隐藏", "type": "boolean"}, {"identifier": "showTooltip", "optional": true, "comments": "是否展示Tooltip，默认true-显示 false-隐藏", "type": "boolean"}, {"identifier": "metricList", "optional": true, "comments": "过滤标准（即要显示的指标）, 默认全选", "type": "array", "members": [{"type": "string"}]}, {"identifier": "metricLabelMap", "optional": true, "comments": "图表指标文案映射", "defaultValue": {"watch": "实时观看人数", "gmv": "实时成交金额"}, "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "string"}]}, {"identifier": "formatterMap", "optional": true, "comments": "对指标进行格式化", "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "function", "interfaceName": "<PERSON><PERSON><PERSON>", "members": [], "parameter": [{"identifier": "point", "optional": false, "type": "object", "interfaceName": "TrendChartPoint", "members": [{"identifier": "category", "optional": true, "comments": "额外的维度标签，或者作为联动key", "type": "string"}, {"identifier": "label", "optional": true, "comments": "标签", "type": "string"}, {"identifier": "value", "optional": true, "comments": "数据", "type": "union", "members": [{"type": "number"}, {"type": "null"}, {"type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "any"}]}]}, {"identifier": "valueType", "optional": true, "comments": "数据类型", "type": "string"}, {"identifier": "rate", "optional": true, "comments": "百分比， 百分位，前端添加’%‘", "type": "union", "members": [{"type": "number"}, {"type": "null"}]}, {"identifier": "desc", "optional": true, "comments": "用于tooltip上中文指标展示", "defaultValue": "单轴多线", "type": "string"}, {"identifier": "categoryLabel", "optional": true, "comments": "指标对应文案", "type": "string"}, {"identifier": "unit", "optional": true, "comments": "期望值与单位分开", "type": "string"}, {}]}], "return": {"type": "object", "interfaceName": "TrendChartPoint", "members": [{"identifier": "category", "optional": true, "comments": "额外的维度标签，或者作为联动key", "type": "string"}, {"identifier": "label", "optional": true, "comments": "标签", "type": "string"}, {"identifier": "value", "optional": true, "comments": "数据", "type": "union", "members": [{"type": "number"}, {"type": "null"}, {"type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "any"}]}]}, {"identifier": "valueType", "optional": true, "comments": "数据类型", "type": "string"}, {"identifier": "rate", "optional": true, "comments": "百分比， 百分位，前端添加’%‘", "type": "union", "members": [{"type": "number"}, {"type": "null"}]}, {"identifier": "desc", "optional": true, "comments": "用于tooltip上中文指标展示", "defaultValue": "单轴多线", "type": "string"}, {"identifier": "categoryLabel", "optional": true, "comments": "指标对应文案", "type": "string"}, {"identifier": "unit", "optional": true, "comments": "期望值与单位分开", "type": "string"}, {}]}}]}, {"identifier": "customEmpty", "optional": true, "comments": "自定义空状态", "type": "TSTypeReference", "interfaceName": "JSX.Element", "members": []}, {"identifier": "store", "optional": true, "comments": "如果需要获取内部提供的信息的话，希望能传入一个ref", "type": "TSTypeReference", "interfaceName": "MutableRefObject", "members": [{"type": "any"}]}, {"identifier": "focus", "optional": true, "comments": "折线图上聚焦点生效的条件，入参是点的数据", "type": "union", "members": [{"type": "boolean"}, {"type": "function", "parameter": [{"identifier": "data", "optional": false, "type": "any"}], "return": {"type": "union", "members": [{"type": "boolean"}, {"type": "object", "interfaceName": "FocusResponse", "members": [{"identifier": "visible", "optional": false, "comments": " 是否显示", "type": "boolean"}, {"identifier": "color", "optional": true, "comments": " 显示颜色", "type": "string"}, {"identifier": "startPoint", "optional": true, "comments": " 纵向辅助线的起始点", "type": "number"}, {"identifier": "endPoint", "optional": true, "comments": " 纵向辅助线的起始点", "type": "number"}]}]}}]}, {"identifier": "focusLabel", "optional": true, "type": "string"}, {"identifier": "auxCfg", "optional": true, "comments": "辅助线配置列表", "defaultValue": [{"value": 1200, "title": "横向辅助线"}], "type": "array", "members": [{"type": "object", "interfaceName": "IAuxLineCfg", "members": [{"identifier": "value", "optional": false, "type": "number"}, {"identifier": "title", "optional": false, "defaultValue": "面积图", "type": "string"}]}]}, {"identifier": "padding", "optional": true, "comments": "设置图表的内边距", "type": "union", "members": [{"value": "auto", "type": "literal"}, {"type": "number"}, {"type": "array", "members": [{"type": "number"}]}]}, {"identifier": "appendPadding", "optional": true, "comments": "图表的内边距会在图表的 padding 的基础上加上 appendPadding", "type": "union", "members": [{"type": "number"}, {"type": "array", "members": [{"type": "number"}]}]}, {"identifier": "recoPadding", "optional": true, "comments": "策略推荐部分的内边距", "type": "union", "members": [{"type": "number"}, {"type": "array", "members": [{"type": "number"}]}]}, {"identifier": "recoConfig", "optional": true, "comments": "策略推荐部分的内边距", "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "object", "members": [{"identifier": "show", "optional": false, "type": "boolean"}, {"identifier": "label", "optional": true, "type": "string"}, {"identifier": "type", "optional": true, "defaultValue": "multipleLine-singleAxis", "type": "string"}, {"identifier": "tooltipConfig", "optional": true, "type": "object", "members": [{"identifier": "show", "optional": true, "type": "boolean"}, {"identifier": "customContent", "optional": true, "type": "function", "parameter": [{"identifier": "title", "optional": false, "defaultValue": "面积图", "type": "string"}, {"identifier": "data", "optional": false, "type": "any"}], "return": {"type": "union", "members": [{"type": "string"}, {"type": "TSTypeReference", "interfaceName": "HTMLElement", "members": []}]}}, {"identifier": "dom<PERSON><PERSON><PERSON>", "optional": true, "type": "TSTypeReference", "interfaceName": "TooltipDomStyles", "members": [], "nodeModuleInfo": {"from": "@antv/g2/lib/interface"}}]}]}]}, {"identifier": "isLineGradient", "optional": true, "comments": "折线是否渐变", "defaultValue": true, "type": "boolean"}, {"identifier": "isAreaGradient", "optional": true, "comments": "面积图是否是渐变色", "defaultValue": true, "type": "boolean"}, {"identifier": "yAxisLabel<PERSON><PERSON>atter", "optional": true, "comments": "y轴单轴 格式化对应label", "type": "function", "parameter": [{"identifier": "text", "optional": false, "type": "string"}, {"identifier": "item", "optional": false, "type": "TSTypeReference", "interfaceName": "ListItem", "members": [], "nodeModuleInfo": {"from": "@antv/g2/lib/dependents"}}, {"identifier": "index", "optional": false, "type": "number"}, {"identifier": "metricsValue", "optional": false, "type": "any"}], "return": {"type": "any"}}, {"identifier": "yAxisLabelFormatters", "optional": true, "comments": "y轴 双轴 格式化 key-对应图例，function-对应格式化方法", "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "function", "parameter": [{"identifier": "text", "optional": false, "type": "string"}, {"identifier": "item", "optional": false, "type": "TSTypeReference", "interfaceName": "ListItem", "members": [], "nodeModuleInfo": {"from": "@antv/g2/lib/dependents"}}, {"identifier": "index", "optional": false, "type": "number"}, {"identifier": "metricsItem", "optional": false, "type": "any"}], "return": {"type": "any"}}]}, {"identifier": "xAxisCfg", "optional": true, "comments": "x轴配置项", "type": "TSTypeReference", "interfaceName": "AxisCfg", "members": [], "nodeModuleInfo": {"from": "@antv/g2/lib/interface"}}, {"identifier": "yAxisCfg", "optional": true, "comments": "x轴配置项", "type": "TSTypeReference", "interfaceName": "AxisCfg", "members": [], "nodeModuleInfo": {"from": "@antv/g2/lib/interface"}}, {"identifier": "xAxisLabelCfg", "optional": true, "comments": "x轴label配置项", "type": "object", "interfaceName": "LabelCfg", "members": [{"identifier": "rotate", "optional": true, "type": "number"}, {"identifier": "offset", "optional": true, "type": "number"}, {"identifier": "spliceDate", "optional": true, "type": "boolean"}, {}]}, {"identifier": "yAxisLabelCfg", "optional": true, "comments": "y轴label配置项", "type": "object", "interfaceName": "LabelCfg", "members": [{"identifier": "rotate", "optional": true, "type": "number"}, {"identifier": "offset", "optional": true, "type": "number"}, {"identifier": "spliceDate", "optional": true, "type": "boolean"}, {}]}, {"identifier": "showAxisTitle", "optional": true, "comments": "展示坐标轴标题", "defaultValue": false, "type": "boolean"}, {"identifier": "extra", "optional": true, "comments": "额外参数,用于策略推荐部分", "type": "object", "interfaceName": "ExtraCfg", "members": [{"identifier": "isLive", "optional": true, "comments": "直播状态", "type": "boolean"}, {"identifier": "isHold", "optional": true, "comments": "手动拖拽缩略轴后，是否保持不变", "type": "boolean"}, {}]}, {"identifier": "showExplain", "optional": true, "comments": "是否展示商品讲解", "type": "boolean"}, {"identifier": "showAvgPubTime", "optional": true, "comments": "是否展示平均上车时间", "type": "boolean"}, {"identifier": "showStrategyReco", "optional": true, "comments": "是否打开策略推荐操作轴", "type": "boolean"}, {"identifier": "onStrategyRecoClick", "optional": true, "comments": "策略推荐点击事件", "type": "function", "parameter": [{"identifier": "data", "optional": false, "type": "any"}, {"identifier": "type", "optional": false, "defaultValue": "multipleLine-singleAxis", "type": "string"}, {"identifier": "isSelected", "optional": false, "type": "boolean"}], "return": {"type": "void"}}, {"identifier": "yScaleMinLimit", "optional": true, "comments": "纵轴 严格模式下的定义域最小值", "type": "number"}, {"identifier": "yScaleMaxLimit", "optional": true, "comments": "纵轴 严格模式下的定义域最大值", "type": "number"}, {"identifier": "xTickCount", "optional": true, "comments": "x轴 tick 个数。对应展示label的个数", "type": "number"}, {"identifier": "yTickCount", "optional": true, "comments": "y轴 tick 个数。", "type": "number"}, {"identifier": "markData", "optional": true, "comments": "辅助框，框选一段图区", "defaultValue": [{"start": "12:01", "end": "12:03"}, {"start": "12:05", "end": "12:06"}], "type": "array", "members": [{"type": "object", "interfaceName": "IMarkDataProps", "members": [{"identifier": "start", "optional": true, "type": "any"}, {"identifier": "end", "optional": true, "type": "any"}, {"identifier": "startPosition", "optional": true, "type": "string"}, {"identifier": "endPosition", "optional": true, "type": "string"}, {"identifier": "color", "optional": true, "type": "string"}]}]}, {"identifier": "markDataFill", "optional": true, "comments": "辅助框颜色", "defaultValue": ["#B7B7B7", "#A7A7A7"], "type": "array", "members": [{"type": "string"}]}, {"identifier": "markLabelOptions", "optional": true, "comments": "文本标记配置项", "defaultValue": [{"startPosition": "12:02", "endPosition": 8300, "content": "116大促活动期"}, {"startPosition": "12:01", "content": "双十二活动，年货节", "color": "red"}], "type": "array", "members": [{"type": "object", "interfaceName": "IMarkLabelOptionsProps", "members": [{"identifier": "startPosition", "optional": false, "comments": "起始位置 可以填写数据源中的原始数据或者填写关键字：'min'、'max'、'median'、'start'、'end' 分别代表数据的最大值、最小值、中间值以及坐标系区间的起始和结束", "type": "union", "members": [{"type": "string"}, {"type": "number"}]}, {"identifier": "endPosition", "optional": true, "comments": "结束位置", "type": "union", "members": [{"type": "string"}, {"type": "number"}]}, {"identifier": "content", "optional": false, "comments": "文本标记内容", "type": "string"}, {"identifier": "color", "optional": true, "comments": "文本颜色", "type": "string"}, {}]}]}, {"identifier": "dataMarker", "optional": true, "comments": "数据点标记", "type": "array", "members": [{"type": "object", "interfaceName": "DataMarkerOptionsProps", "members": [{"identifier": "startPosition", "optional": false, "comments": "起始位置 可以填写数据源中的原始数据或者填写关键字：'min'、'max'、'median'、'start'、'end' 分别代表数据的最大值、最小值、中间值以及坐标系区间的起始和结束", "type": "union", "members": [{"type": "string"}, {"type": "number"}]}, {"identifier": "endPosition", "optional": true, "comments": "结束位置", "type": "union", "members": [{"type": "string"}, {"type": "number"}]}, {}]}]}, {"identifier": "lineMarker", "optional": true, "comments": "竖线标记", "type": "array", "members": [{"type": "object", "interfaceName": "LineMarkerOptionsProps", "members": [{"identifier": "startPosition", "optional": false, "type": "string"}, {}]}]}, {"identifier": "htmlMarker", "optional": true, "comments": "HTML标记", "type": "array", "members": [{"type": "object", "interfaceName": "HtmlMarkerOptionsProps", "members": [{"identifier": "startPosition", "optional": false, "type": "string"}, {}]}]}, {"identifier": "onAnnotationRegionClickHandle", "optional": true, "comments": "区块标记，点击事件", "type": "function", "parameter": [{"identifier": "data", "optional": false, "type": "any"}], "return": {"type": "void"}}, {"identifier": "onAnnotationHtmlClickHandle", "optional": true, "comments": "自定义标签，点击事件", "type": "function", "parameter": [{"identifier": "data", "optional": false, "type": "any"}], "return": {"type": "void"}}, {"identifier": "onPlotClickHandle", "optional": true, "comments": "图上任何一点，点击事件", "type": "function", "parameter": [{"identifier": "data", "optional": false, "type": "any"}], "return": {"type": "void"}}, {"identifier": "scoreOptions", "optional": true, "comments": "目标线设置", "type": "object", "interfaceName": "IScoreOptions", "members": [{"identifier": "scoreData", "optional": false, "comments": "目标设定值", "type": "union", "members": [{"type": "number"}, {"type": "string"}]}, {"identifier": "color", "optional": true, "comments": "目标线颜色，默认蓝色", "type": "string"}, {"identifier": "prefix", "optional": true, "comments": "目标数值前缀", "type": "string"}, {"identifier": "suffix", "optional": true, "comments": "目标数值后缀", "type": "string"}]}, {"identifier": "combinedTag", "optional": true, "comments": "组合标记", "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "array", "members": [{"type": "object", "interfaceName": "CombinedTagItem", "members": [{"identifier": "startTime", "optional": false, "comments": "起始点时间戳", "type": "number"}, {"identifier": "startLabel", "optional": false, "comments": "起始点标签", "type": "string"}, {"identifier": "endTime", "optional": true, "comments": "结束点时间戳", "type": "number"}, {"identifier": "endLabel", "optional": true, "comments": "结束点标签", "type": "string"}, {"identifier": "content", "optional": false, "comments": "区域标签内容", "type": "string"}, {"identifier": "color", "optional": true, "comments": "区域色值", "type": "string"}, {}]}]}]}, {"identifier": "combinedTagConfig", "optional": true, "type": "TSTypeReference", "interfaceName": "Record", "members": [{"type": "string"}, {"type": "any"}]}, {"identifier": "combinedTagHtml", "optional": true, "comments": "组合标记-标签自定义", "type": "function", "parameter": [{"identifier": "data", "optional": false, "type": "any"}, {"identifier": "id", "optional": false, "type": "string"}], "return": {"type": "union", "members": [{"type": "void"}, {"type": "string"}, {"type": "TSTypeReference", "interfaceName": "HTMLElement", "members": []}]}}, {"identifier": "combinedActiveTagLabel", "optional": true, "comments": "选中态的label标签", "type": "string"}, {"identifier": "combinedActiveTagColor", "optional": true, "comments": "选中态颜色色值", "type": "string"}, {"identifier": "slider<PERSON><PERSON><PERSON>", "optional": true, "comments": "缩略轴初始选择数据源的范围", "type": "number"}, {"identifier": "onRegisterResetSelectedState", "optional": true, "comments": "注册重置选中状态方法", "type": "function", "parameter": [{"identifier": "reset", "optional": false, "type": "TSTypeReference", "interfaceName": "Function", "members": []}], "return": {"type": "void"}}], "publishTime": 1733148566141, "extraInfo": {"es": {}}, "libraryName": "KproDataLineChart", "assetDownloadType": "cdn", "author": "lishengnan05", "version": "2.0.16-beta.15", "tags": {"business": "kael", "platform": ["pc"], "meta": {"businessField": ["data"], "customize": ["图表组件"], "framework": ["react"], "granularity": ["base", "business"], "scene": ["chart"], "materielType": ["kpro"]}, "category": ["common"]}, "thumbnailUrl": "https://f2.eckwai.com/kos/nlav12333/fangzhou/material-middleoffice/script/es/<EMAIL>", "assetDownloadUrl": "https://w2.eckwai.com/kos/nlav12333/unpkg/@es/kpro-data-line-chart/2.0.16-beta.15/index.min.js", "instructionUrl": "https://cdnfile.corp.kuaishou.com/kc/files/a/kwaishop-web-doc/@es/kpro-data-line-chart/2.0.16-beta.15/components/index.html?page=%2Fcomponents", "updateTime": 1733148690474, "extraConfig": {"es": {"umdUrl": "https://w2.eckwai.com/kos/nlav12333/unpkg/@es/kpro-data-line-chart/2.0.16-beta.15/index.min.js", "defaultUmdUrl": "https://w2.eckwai.com/kos/nlav12333/unpkg/@es/kpro-data-line-chart/2.0.16-beta.15/index.default.min.js", "sceneContent": {}}, "externalizedAssets": {"url": "https://w2.eckwai.com/kos/nlav12333/unpkg/@es/kpro-data-line-chart/2.0.16-beta.15/index.min.js", "externals": [{"packageName": "@antv/g2", "version": "4.2.8", "libraryName": "G2", "url": "https://w1.beckwai.com/kos/nlav12333/web-assets/lib/antv/g2/4.2.10/g2.min.js"}, {"packageName": "@es/kpro-data-utils", "version": "1.1.5-beta.2", "libraryName": "KproDataUtils", "url": "https://w2.eckwai.com/kos/nlav12333/unpkg/@es/kpro-data-utils/1.1.5-beta.2/index.min.js"}, {"packageName": "@m-ui/react", "version": "2.2.2", "libraryName": "@m-ui/react", "url": "https://unpkg.corp.kuaishou.com/@m-ui/react@2.2.2/dist/@m-ui/react.min.js"}, {"packageName": "dayjs", "version": "1.11.13", "libraryName": "dayjs", "url": "https://w1.eckwai.com/kos/nlav12333/web-assets/lib/dayjs/1.11.10/dayjs.min.js"}, {"packageName": "lodash-es", "version": "4.17.21", "libraryName": "_", "url": "https://w1.eckwai.com/kos/nlav12333/web-assets/lib/lodash/4.17.21/lodash.min.js"}, {"packageName": "react", "version": "17.0.2", "libraryName": "React", "url": "https://f2.eckwai.com/kos/nlav12333/web-assets/lib/react/17.0.2/react.production.min.js"}, {"packageName": "@es/kpro-data-color-theme", "version": "0.1.4-beta.1", "libraryName": "KproDataColorTheme", "url": "https://w2.eckwai.com/kos/nlav12333/unpkg/@es/kpro-data-color-theme/0.1.4-beta.1/index.min.js"}, {"packageName": "@es/kpro-data-strategy-reco", "version": "1.0.3-beta.8", "libraryName": "KproDataStrategyReco", "url": "https://w2.eckwai.com/kos/nlav12333/unpkg/@es/kpro-data-strategy-reco/1.0.3-beta.8/index.min.js"}]}}}