import { FC, useEffect, useState } from 'react'
import { Alert, Radio, Space, Table } from 'antd'
import { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

import { MaterialReferItem } from '@/types'
import { BusinessMap, INIT_PAGE_INDEX, INIT_PAGE_SIZE } from '@/constants'

import <PERSON><PERSON>serLink from '@/components/kim-user-link'
import { useBusinessFromURLQuery } from '@/hooks/useBusinessFromURLQuery'
import {
  ALL_LOW_CODE_BUSINESS,
  ALL_SOURCE_CODE_BUSINESS,
  CODE_TYPE,
  LOW_CODE_BUSINESS,
  SOURCE_CODE_BUSINESS,
} from '@global-material-middleoffice/server-v2/shared'
import { fetchMaterialReferList } from '@/services/v2/material-refer'

const columns: ColumnsType<MaterialReferItem> = [
  {
    title: '预览链接',
    dataIndex: 'ref_page',
    key: 'ref_page',
    render: (refPage) => {
      return (
        <div style={{ width: '100%', display: 'flex' }}>
          <div
            style={{
              flex: 1,
              width: 0,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            <a href={refPage} target="_blank" rel="noreferrer">
              {refPage}
            </a>
          </div>
        </div>
      )
    },
  },
  {
    title: '引用次数',
    dataIndex: 'refer_count',
    key: 'refer_count',
    width: 90,
  },
  {
    title: '引用来源',
    dataIndex: 'ref_business',
    key: 'ref_business',
    width: 90,
    render: val => BusinessMap[val],
  },
  {
    title: '使用版本',
    dataIndex: 'version',
    key: 'version',
    width: 120,
    render: version => (version ? version : '-'),
  },
  {
    title: '更新时间',
    dataIndex: 'create_time',
    key: 'create_time',
    width: 180,
    render: (time) => {
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
  },
  {
    title: '更新人',
    dataIndex: 'create_name',
    key: 'creator_name',
    width: 100,
    render: (_, { creator_name, creator_id }) => {
      return (
        <KimUserLink
          displayName={creator_name}
          userName={creator_id}
          showUserName
        />
      )
    },
  },
]

type MaterialReferProps = {
  materialId: number
}

const MaterialRefer: FC<MaterialReferProps> = (props) => {
  const { materialId } = props
  const [refers, setRefers] = useState<MaterialReferItem[]>([])
  const [loading, setLoading] = useState(false)
  const [pageIndex, setPageIndex] = useState(INIT_PAGE_INDEX)
  const [total, setTotal] = useState(0)
  const [business] = useBusinessFromURLQuery()
  const [sourceType, setSourceType] = useState<'namespace' | 'package'>(
    'namespace',
  )

  useEffect(() => {
    setLoading(true)
    fetchMaterialReferList({
      id: materialId,
      pageSize: INIT_PAGE_SIZE,
      pageNum: pageIndex,
      source_type: sourceType,
      code_type: ALL_LOW_CODE_BUSINESS.includes(
        business as unknown as LOW_CODE_BUSINESS,
      )
        ? CODE_TYPE.LOW_CODE
        : CODE_TYPE.PRO_CODE,
    })
      .then(async (res) => {
        const { list, total }
          = res.data.data as Service.PaginationResult<MaterialReferItem>
        const transformedReferList: MaterialReferItem[] = await Promise.all(
          list.map(async (item) => {
            const match = item.ref_page.match(/\?repo_project_id=([^&]+)&file_path=([^&]+)/)
            const repoProjectId = match ? match[1] : null
            const filePath = match ? match[2] : null
            if (repoProjectId && filePath) {
              return {
                ...item,
                ref_page: `https://git.corp.kuaishou.com/search?search=${filePath}&project_id=${repoProjectId}`,
              }
            }
            else {
              return item
            }
          }),
        )
        setRefers(transformedReferList)
        setTotal(total)
      })
      .finally(() => setLoading(false))
  }, [pageIndex, sourceType])

  return (
    <Space direction="vertical" style={{ width: '100%' }} size={16}>
      {ALL_SOURCE_CODE_BUSINESS.includes(
        business as unknown as SOURCE_CODE_BUSINESS,
      ) && (
        <>
          <Radio.Group
            value={sourceType}
            disabled={loading}
            onChange={(e) => {
              setSourceType(e.target.value)
            }}
            optionType="button"
            options={[
              {
                label: '被页面引用情况',
                value: 'namespace',
              },
              {
                label: 'NPM 包引用记录',
                value: 'package',
              },
            ]}
          />
          {sourceType === 'namespace' && (
            <Alert
              type="info"
              message="被页面引用情况仅包含 H5/PC 等项目的引用情况"
            />
          )}
        </>
      )}
      <Table
        rowKey="id"
        columns={columns}
        dataSource={refers}
        loading={loading}
        pagination={{
          size: 'small',
          current: pageIndex,
          total: total,
          pageSize: INIT_PAGE_SIZE,
          showSizeChanger: false,
          onChange: (page) => {
            setPageIndex(page)
          },
        }}
      />
    </Space>
  )
}

export default MaterialRefer
