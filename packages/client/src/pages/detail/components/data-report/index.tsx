import React, { FC, useEffect, useState } from 'react'
import { Line } from '@mchart/pc-react'
import dayjs from 'dayjs'
import { message } from 'antd'
import NP from 'number-precision'
import { queryMaterialReuseRateUsingGET } from '@/services'
import { MetricItem } from '../metrics-item'

interface DataReportProps {
  materialId: number
  business: Business
}

const lowCodeMetric = {
  title: '物料复用率',
  description: '使用了该组件的页面数 / 所有页面数',
  value: '33.33%',
}
const DataReport: FC<DataReportProps> = (props) => {
  const { materialId, business } = props
  const [reuseData, setReuseData] = useState<Module.Report.Report[]>([])

  useEffect(() => {
    queryMaterialReuseRateUsingGET({ id: materialId, business })
      .then((res) => {
        setReuseData(res as unknown as Module.Report.Report[])
      })
      .catch(() => {
        message.error('复用率指标获取失败')
      })
  }, [materialId])

  return (
    <div style={{ padding: '16px 0' }}>
      <MetricItem fieldInfo={lowCodeMetric} />
      <Line
        style={{ marginTop: 16 }}
        name="复用率"
        series={[
          {
            name: '复用率',
            data: reuseData?.map(item => NP.times(item.rate, 100)) || [],
            showSymbol: true,
          },
        ]}
        option={{
          xAxis: {
            data: reuseData?.map(item => dayjs(item.create_time).format('MM/DD')) || [],
          },
          tooltip: {
            valueFormatter: value => `${value}%`,
          },
          yAxis: [
            {
              type: 'value',
              name: '复用率',
              min: 0,
              max: 100,
              axisLabel: { formatter: '{value}%' },
            },
          ],
        }}
      />
    </div>
  )
}

export default DataReport
