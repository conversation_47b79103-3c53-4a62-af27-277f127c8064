import ItemizeList from '@/components/itemize-list'
import MaterialCard from '@/components/material-card.new'
import { MaterialType } from '@/constants'
import { fetchMaterialList } from '@/services/v2/material'
import { Spin } from 'antd'
import React, { useEffect, useState } from 'react'

interface Props {
  libraryDetail?: Service.Material.MaterialDetail
}

const MaterialList: React.FC<Props> = (props) => {
  const { libraryDetail } = props
  if (libraryDetail?.type !== MaterialType.COM_LIB) return null

  const [loading, setLoading] = useState(false)
  const [materialList, setMaterialList] = useState<Service.Material.MaterialDetail[]>([])
  useEffect(() => {
    if (libraryDetail && libraryDetail.type === MaterialType.COM_LIB) {
      setLoading(true)
      fetchMaterialList({
        namespace: libraryDetail.namespace,
        type: MaterialType.COMPONENT,
        pageSize: 1000,
        tags: {
          business: libraryDetail.business,
        },
      })
        .then((res) => {
          setMaterialList(res.data.data.list)
        })
        .finally(() => setLoading(false))
    }
  }, [libraryDetail])

  return (
    <Spin spinning={loading}>
      <ItemizeList
        data={materialList}
        renderItem={item => (
          <MaterialCard key={item.id} material={item} />
        )}
      />
    </Spin>
  )
}

export default MaterialList
