import {
  FC,
  useMemo,
  useState,
  useRef,
  useEffect,
  ReactNode,
} from 'react'
import axios from 'axios'
import { Result } from 'antd'
import { SmileOutlined } from '@ant-design/icons'
import MarkdownPreview from '@uiw/react-markdown-preview'

import AutoFullParentLoading from '@/components/auto-full-parent-loading'

import styles from './index.module.less'

export interface MaterialDocRenderProps {
  loading?: boolean
  docUrl?: string
}

const MaterialDoc: FC<MaterialDocRenderProps> = (props) => {
  const { docUrl } = props
  const [assetLoading, setAssetLoading] = useState(false)
  const loading = useMemo(() => props.loading || assetLoading, [props.loading, assetLoading])
  const isValidURL = useMemo(() =>
    docUrl?.startsWith('http')
    && !docUrl.includes('components.corp'),
  [docUrl])

  // ========================  markdown ========================
  const isMarkdownDoc = useMemo(() => {
    return isValidURL && docUrl?.endsWith('.md')
  }, [isValidURL, docUrl])
  const [markdownContent, setMarkdownContent] = useState<string | undefined>(undefined)
  useEffect(() => {
    if (isMarkdownDoc) {
      setAssetLoading(true)
      axios
        .get(docUrl)
        .then((res) => {
          setMarkdownContent(res.data)
        })
        .catch((e) => {
          setError({
            message: '加载失败',
            stack: `渲染 Markdown(${docUrl})失败: ${e.toString()}`,
          })
        })
        .finally(() => {
          setAssetLoading(false)
        })
    }
  }, [docUrl])

  // ======================== error catch ========================
  const [error, setError] = useState<{
    message: string
    stack: ReactNode
  }>()

  const iframeRef = useRef<HTMLIFrameElement>(null)

  const handleOnload = () => {
    const iframe = iframeRef.current
    try {
      const height = iframe.contentWindow.document.body.scrollHeight + 'px'
      if (height) {
        iframe.height = height
      }
    }
    catch (e) {
      console.log('获取iframe高度失败', e)
    }
  }

  return (
    <div className={styles['material-doc']}>
      {/* loading */}
      <AutoFullParentLoading loading={loading} />
      {/* empty */}
      {!loading && !isValidURL
      && (
        <Result
          icon={<SmileOutlined />}
          title="物料文档正在建设中"
        />
      )}
      {/* error */}
      {!loading && error && (
        <Result
          status="error"
          title={error.message}
          subTitle={error.stack}
        />
      )}
      {/* markdown */}
      {isMarkdownDoc && !loading && !error && markdownContent
        ? (
          <MarkdownPreview
            source={markdownContent}
            wrapperElement={{ 'data-color-mode': 'light' }}
          />
        )
        : null}
      {/* iframe */}
      {!isMarkdownDoc && !loading && !error && isValidURL
        ? (
          <iframe
            src={docUrl}
            ref={iframeRef}
            style={{
              height: '100vh',
              width: '100%',
              minHeight: 'calc(100vh - 372px)',
              border: '1px solid #174ae6',
              overflowY: 'scroll',
            }}
            onLoad={handleOnload}
            onError={(e) => {
              setError({
                message: '加载失败',
                stack: `渲染 ${docUrl} 失败: ${e.toString()}`,
              })
            }}
          />
        )
        : null}
    </div>
  )
}

export default MaterialDoc
