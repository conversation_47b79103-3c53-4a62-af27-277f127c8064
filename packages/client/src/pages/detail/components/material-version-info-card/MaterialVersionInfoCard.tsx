import { FC, useEffect, useMemo, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Descriptions, Divider, Row, Space, Tag, Typography } from 'antd'
import {
  BuildFilled,
  CalendarOutlined,
  DatabaseOutlined,
  GitlabFilled,
  UserOutlined,
} from '@ant-design/icons'
import gitURLParse from 'git-url-parse'
import {
  ALL_LOW_CODE_BUSINESS,
  BUSINESS_KEY_MAP,
  BUSINESS_NAME,
  LOW_CODE_BUSINESS,
} from '@global-material-middleoffice/server-v2/shared'

import KimUserLink from '@/components/kim-user-link'
import CopiedTextBox from '@/components/copied-text-box'
import { cdnCut, formatTime } from '@/utils'
import { fetchTransformedTags } from '@/services/v2/material'

import styles from './index.module.less'
import MaterialCover from '@/components/material-cover'

const { Paragraph } = Typography

export interface MaterialVersionInfoProps {
  namespace?: string
  materialVersionDetail?: Service.Material.Publish.NormalizePublishRecord
  loading?: boolean // TODO:
}

const MaterialVersionInfo: FC<MaterialVersionInfoProps> = ({ namespace, materialVersionDetail }) => {
  // ======================== 封面 ========================
  const coverUrl = useMemo(
    () => {
      if (materialVersionDetail?.preview_img) {
        return cdnCut({ url: materialVersionDetail.preview_img, width: 900, height: 660 })
      }
    },
    [materialVersionDetail],
  )

  // ======================== git url ========================
  const gitURL = useMemo(() => {
    try {
      return gitURLParse(materialVersionDetail?.schema?.gitUrl).toString('https')
    }
    catch (error) {
      console.error(error)
      return ''
    }
  }, [materialVersionDetail?.schema?.gitUrl])

  // ======================== tags ========================
  const [transformedTags, setTransformedTags] = useState<Service.Material.MaterialDetail['tags']>({
    domain: [],
    platform: [],
    business: [],
    category: [],
    meta: [],
  })
  useEffect(() => {
    if (materialVersionDetail?.schema?.tags) {
      fetchTransformedTags(materialVersionDetail.schema.tags).then((res) => {
        setTransformedTags(res.data.data)
      })
    }
  }, [materialVersionDetail?.schema?.tags])

  const content = (
    <Card className={styles['material-info-content-wrapper']}>
      <Row gutter={24}>
        <Col span={18}>
          <Space size={16} direction="vertical">
            <div className={styles['material-header']}>
              <div className={styles['material-title']}>
                <span className={styles['name']}>
                  {materialVersionDetail?.schema?.componentChineseName ?? '-'}
                </span>
                <span className={styles['version']}>
                  {materialVersionDetail?.schema?.version ?? '-'}
                </span>

              </div>
              <Space>
                <Button
                  icon={<GitlabFilled style={{ color: '#E24329' }} />}
                  onClick={() => window.open(gitURL, '__blank')}
                >
                  查看源码
                </Button>
                {ALL_LOW_CODE_BUSINESS.includes(materialVersionDetail?.schema?.tags.business as LOW_CODE_BUSINESS)
                && materialVersionDetail?.schema?.componentBundleType !== 'SL'
                && (
                  <Button
                    icon={<BuildFilled style={{ color: '#174ae6' }} />}
                    onClick={() => window.open(`/preview?id=${materialVersionDetail.material_id}&version=${materialVersionDetail?.version}`, '__blank')}
                  >
                    去低码工程试用
                  </Button>
                )}
              </Space>
            </div>
            <CopiedTextBox
              label="命名空间"
              value={namespace ?? '-'}
            />
            <Divider type="horizontal" style={{ margin: 0 }} />
            <Descriptions layout="vertical" size="small">
              <Descriptions.Item label={<b>运行端</b>}>
                {transformedTags.platform.map((tag) => {
                  return (
                    <Tag color="processing" key={tag.id}>
                      {tag.label}
                    </Tag>
                  )
                })}
              </Descriptions.Item>
              <Descriptions.Item label={<b>类型</b>}>
                {transformedTags.category.map((tag) => {
                  return (
                    <Tag color="processing" key={tag.id}>
                      {tag.label}
                    </Tag>
                  )
                })}
                {transformedTags.category.length === 0 && <span>未分类</span>}
              </Descriptions.Item>
              {transformedTags.meta.map((meta) => {
                return (
                  <Descriptions.Item key={meta.id} label={<b>{meta.label}</b>}>
                    {meta.children?.map((tag) => {
                      return (
                        <Tag color="processing" key={tag.id}>
                          {tag.label}
                        </Tag>
                      )
                    })}
                  </Descriptions.Item>
                )
              })}
            </Descriptions>
            {materialVersionDetail?.schema?.description
              ? (
                <>
                  <Divider type="horizontal" style={{ margin: 0 }} />
                  <Typography.Title level={5} style={{ margin: 0 }}>
                    详细介绍
                  </Typography.Title>
                  <Paragraph
                    title={`${materialVersionDetail.schema.description}`}
                    ellipsis={{
                      rows: 3,
                      expandable: true,
                    }}
                  >
                    {materialVersionDetail.schema.description}
                  </Paragraph>
                </>
              )
              : null}
            <Divider type="horizontal" style={{ margin: 0 }} />
            <Descriptions size="small">
              <Descriptions.Item
                label={(
                  <Space>
                    <UserOutlined />
                    创建者
                  </Space>
                )}
              >
                <KimUserLink
                  displayName={materialVersionDetail?.creator_name}
                  userName={materialVersionDetail?.schema?.author}
                  showUserName
                />
              </Descriptions.Item>
              <Descriptions.Item
                label={(
                  <Space>
                    <CalendarOutlined />
                    创建时间
                  </Space>
                )}
              >
                {formatTime(materialVersionDetail?.schema?.publishTime)}
              </Descriptions.Item>
              <Descriptions.Item
                label={(
                  <Space>
                    <DatabaseOutlined />
                    生产方
                  </Space>
                )}
              >
                {BUSINESS_NAME[BUSINESS_KEY_MAP[materialVersionDetail?.schema?.tags.business]] ?? '-'}
              </Descriptions.Item>
            </Descriptions>
          </Space>
        </Col>
        <Col span={6}>
          <MaterialCover className={styles['cover']} url={coverUrl} />
        </Col>
      </Row>
    </Card>
  )
  if (materialVersionDetail?.schema?.componentBundleType !== 'SL') {
    return content
  }
  else {
    return (
      <Badge.Ribbon text="物料库" color="purple" placement="start">
        {content}
      </Badge.Ribbon>
    )
  }
}

export default MaterialVersionInfo
