.material-info-content-wrapper {
  display: flex;
  gap: 24px;

  .cover {
    flex-shrink: 0;
    border-radius: 2px;
    width: 100%;
    height: 220px;
  }

  .material-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .material-title {
    display: flex;
    gap: 8px;
    align-items: center;

    .name {
      font-size: 24px;
      font-weight: 600;
    }

    .version {
      color: #333840;
      background-color: #f0f0f0;
      font-weight: 500;
      font-size: 16px;
      padding: 2px 8px;
      border-radius: 2px;
      display: flex;
      align-items: center;
    }

    .namespace {
      color: #333840;
      background-color: #f0f0f0;
      font-weight: 500;
      font-size: 16px;
      padding: 2px 8px;
      border-radius: 4px;
      display: flex;
      align-items: center;
    }
  }

  .base-block-wrapper {
    display: flex;
    flex-direction: column;

    .base-block-title {
      color: #1f1f1f;
      font-size: 16px;
    }

    .base-line-wrapper {
      display: grid;
      grid-template-columns: repeat(3, 33.33%);
    }
  }
}
