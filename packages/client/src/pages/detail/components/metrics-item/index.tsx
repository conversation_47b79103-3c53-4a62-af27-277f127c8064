import React, { ReactNode } from 'react'
import cls from 'clsx'

import styles from './index.module.less'

type MetricItemProps = {
  fieldInfo: { title: string, description: string, value: AnyType, icon?: ReactNode }
  children?: ReactNode
}
export const MetricItem: React.FC<MetricItemProps> = ({ fieldInfo, children }) => {
  const { title, description } = fieldInfo
  return (
    <div className={cls(styles.metricDetailItem)}>
      <div className={styles.mainContent}>
        <div className={styles.metricDetailItemTitle}>
          {title}
        </div>
        <div className={cls(styles.metricDetailItemDesc, 'ml-8')}>{description || '-'}</div>
      </div>
      <div>{children}</div>
    </div>
  )
}
