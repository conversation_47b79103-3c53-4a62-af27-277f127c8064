.metricDetailItem {
  display: flex;
  font-weight: 400;
  font-size: 14px;
  flex-direction: column;
  // border: 1px solid rgb(230, 233, 237);
  // box-shadow: 2px -3px 5px rgba(29, 35, 41, 0.06);
  margin-bottom: 16px;
  // border-radius: 5px;
  width: 600px;

  .mainContent {
    padding: 4px 8px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-left: 3px solid var(--theme-color);
  }
  &Title {
    color: #666;
    margin: 0 12px;
    flex-shrink: 0;
    font-weight: 400;
    font-size: 16px;
    padding-right: 12px;
    border-right: 1px solid rgb(215, 209, 209);
    // white-space: wrap;
    // flex: 90px 0 0;
    // flex-wrap: wrap;
  }

  &Value {
    flex: 0 0 100px;
    color: #434343;
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;

  }

  .detailTitleInfo {
    overflow: hidden;
    -webkit-box-orient: vertical;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box !important;
    -webkit-box-orient: vertical;
  }
  &Desc {
    color: #8c8c8c;
    flex: 1;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}