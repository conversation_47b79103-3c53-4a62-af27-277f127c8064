import React, { useMemo } from 'react'
import { LOW_CODE_BUSINESS } from '@global-material-middleoffice/server-v2/shared'

import { getFangZhouPreviewUrl, getKaelPreviewUrl } from '@/utils'

import styles from './style.module.less'

interface Props {
  materialId: number
  version: string
  business: LOW_CODE_BUSINESS
}

const ComponentPreview: React.FC<Props> = (props) => {
  const { materialId, version, business } = props

  const iframeSRC = useMemo(() => {
    return business === LOW_CODE_BUSINESS.KAEL
      ? getKaelPreviewUrl(materialId, version)
      : getFangZhouPreviewUrl(materialId, version)
  }, [materialId, version, business])

  return (
    <iframe
      width="100%"
      height={800}
      className={styles['preview-iframe']}
      src={iframeSRC}
    />
  )
}

export default ComponentPreview
