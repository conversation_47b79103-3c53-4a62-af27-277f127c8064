import dayjs from 'dayjs'
import semver from 'semver'
import { FC, useEffect, useMemo, useState } from 'react'
import { Card, Input, Modal, Radio, Space, Spin, Table } from 'antd'
import { MATERIAL_VERSION_TYPE } from '@global-material-middleoffice/server-v2/shared'

import { fetchMaterialDetail } from '@/services/v2/material'
import KimUserLink from '@/components/kim-user-link'

import MaterialDoc from '../doc-content'
import MaterialVersionInfo from '../material-version-info-card'

import styles from './index.module.less'

const MATERIAL_VERSION_TYPE_NAME_MAP = {
  [MATERIAL_VERSION_TYPE.RELEASE]: '正式版本',
  [MATERIAL_VERSION_TYPE.PRERELEASE]: '测试版本',
}

export interface VersionManageProps {
  material?: Service.Material.MaterialDetail
}

const VersionManage: FC<VersionManageProps> = (props) => {
  const { material } = props
  const historyVersions = material?.historyVersions || []
  const versionsMap = useMemo(() => {
    const release: Service.Material.Publish.NormalizePublishRecord[] = []
    const prerelease: Service.Material.Publish.NormalizePublishRecord[] = []
    historyVersions.forEach((item) => {
      if (semver.valid(item.version) && !semver.prerelease(item.version)) {
        release.push(item)
      }
      else {
        prerelease.push(item)
      }
    })
    return {
      [MATERIAL_VERSION_TYPE.RELEASE]: release,
      [MATERIAL_VERSION_TYPE.PRERELEASE]: prerelease,
    }
  }, [historyVersions])
  // ======================== version list ========================
  const [materialVersionType, setMaterialVersionType] = useState(MATERIAL_VERSION_TYPE.RELEASE)
  useEffect(() => versionsMap[MATERIAL_VERSION_TYPE.RELEASE].length
    ? setMaterialVersionType(MATERIAL_VERSION_TYPE.RELEASE)
    : setMaterialVersionType(MATERIAL_VERSION_TYPE.PRERELEASE)
  , [versionsMap])
  const [keyword, setKeyword] = useState('')
  const onTabChange = (key) => {
    if (key !== materialVersionType) {
      setMaterialVersionType(key)
      setKeyword('')
    }
  }
  const matchedVersions = useMemo(() => {
    return versionsMap[materialVersionType].filter((item) => {
      return !keyword || item.version.includes(keyword)
    })
  }, [historyVersions, materialVersionType, keyword])

  // ======================== version detail ========================
  const [detailLoading, setDetailLoading] = useState(false)
  const [versionDetailModalVisible, setVersionDetailModalVisible] = useState(false)
  const [versionDetail, setVersionDetail] = useState<Service.Material.Publish.NormalizePublishRecord>()
  const handleClose = () => {
    setVersionDetailModalVisible(false)
    setVersionDetail(undefined)
  }
  const onViewVersionDetail = (version: string) => {
    setVersionDetailModalVisible(true)
    setDetailLoading(true)
    fetchMaterialDetail({
      id: material.id,
      version,
    })
      .then((res) => {
        setVersionDetail(res.data.data.currentVersion)
      })
      .finally(() => {
        setDetailLoading(false)
      })
  }

  return (
    <div>
      <div className={styles.filter}>
        <Radio.Group
          optionType="button"
          buttonStyle="solid"
          value={materialVersionType}
          onChange={e => onTabChange(e.target.value)}
          options={Object.entries(MATERIAL_VERSION_TYPE_NAME_MAP).map(([key, value]) => ({
            label: `${value}（${versionsMap[key].length}）`,
            value: key,
          }))}
        />
        <Input
          className={styles.input}
          type="text"
          value={keyword}
          placeholder="可输入版本号搜索"
          onChange={e => setKeyword(e.target.value)}
        />
      </div>

      <Table
        rowKey="material_id"
        columns={[
          {
            title: '物料名称',
            key: 'title',
            render: () => material?.title,
          },
          {
            title: '版本号',
            dataIndex: 'version',
            key: 'version',
          },
          {
            title: '发布人',
            dataIndex: 'creator',
            key: 'creator',
            render: (creator, { creator_name }) => {
              return (
                <KimUserLink
                  displayName={creator_name}
                  userName={creator}
                  showUserName
                />
              )
            },
          },
          {
            title: '发布时间',
            dataIndex: 'create_time',
            key: 'create_time',
            render: (time) => {
              return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-'
            },
          },
          {
            title: '操作',
            dataIndex: 'version',
            key: 'version',
            render: (version) => {
              return <a onClick={() => onViewVersionDetail(version)}>查看详情</a>
            },
          },
        ]}
        dataSource={matchedVersions}
        pagination={false}
      />
      <Modal
        title="版本详情"
        width={1200}
        bodyStyle={{ minHeight: '600px', overflowY: 'scroll' }}
        visible={versionDetailModalVisible}
        onOk={() => handleClose()}
        onCancel={() => handleClose()}
        footer={null}
      >
        <Spin spinning={detailLoading}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <MaterialVersionInfo namespace={material?.namespace} materialVersionDetail={versionDetail} />
            <Card title="文档">
              <MaterialDoc docUrl={versionDetail?.schema?.instructionUrl} />
            </Card>
          </Space>
        </Spin>
      </Modal>
    </div>
  )
}

export default VersionManage
