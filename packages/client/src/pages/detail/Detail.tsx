import { FC, useEffect, useMemo, useState } from 'react'
import { Card, Result } from 'antd'
import { observer } from 'mobx-react-lite'
import { Business, MaterialType } from '@/constants'
import useUrlState from '@ahooksjs/use-url-state'
import { LOW_CODE_BUSINESS } from '@global-material-middleoffice/server-v2/shared'

import { fetchMaterialDetail } from '@/services/v2/material'

import BackwardNav from '../../layouts/backward-nav'
import MaterialVersionCard from './components/material-version-info-card'
import MaterialList from './components/material-list'
import MaterialDoc from './components/doc-content'
import ComponentPreview from './components/component-preview'
import VersionManage from './components/versions-manage'
import DataReport from './components/data-report'
import MaterialRefer from './components/material-refer'
import MaterialsProps from './components/material-props'

import styles from './style.module.less'
import SamePackageMaterials from './components/same-package-materials'

const MaterialComLibDetail: FC = () => {
  const [urlState] = useUrlState({ material_id: 0, namespace: 0 }, { navigateMode: 'replace' })
  const { material_id, namespace } = urlState

  // ======================== material info ========================
  const [loading, setLoading] = useState(false)
  const [materialDetail, setMaterialDetail] = useState<Service.Material.MaterialDetail>()
  useEffect(() => {
    if (material_id ?? namespace) {
      setLoading(true)

      const params: Service.Material.DetailQueries = Object.assign({},
        material_id ? { id: +material_id } : { namespace: decodeURIComponent(namespace) },
        { options: { withVersions: true } })

      fetchMaterialDetail(params)
        .then((res) => {
          setMaterialDetail(res.data.data)
        })
        .finally(() => setLoading(false))
    }
  }, [material_id, namespace])

  if (!material_id && !namespace) {
    return <Result title="缺少物料 id 或 namespace" />
  }

  // ======================== 组件列表 ========================
  const SubComponentsTab = useMemo(() => {
    const show = materialDetail && materialDetail.type === MaterialType.COM_LIB
    return {
      key: 'component-list',
      tab: '组件列表',
      show,
      content: <MaterialList libraryDetail={materialDetail} />,
    }
  }, [materialDetail])

  // ======================== 使用文档 ========================
  const MaterialDocTab = useMemo(() => {
    const show: boolean = materialDetail?.type !== MaterialType.COM_LIB
    return {
      key: 'readme',
      tab: '使用文档',
      show,
      content: show && (
        <MaterialDoc
          loading={loading}
          docUrl={materialDetail?.currentVersion.readme}
        />
      ),
    }
  }, [materialDetail, loading])

  // ======================== 组件预览 ========================
  const MaterialPreviewTab = useMemo(() => {
    const show: boolean = materialDetail
      && materialDetail.type === MaterialType.COMPONENT
      // 低代码业务组件展示预览
      && Object.values(LOW_CODE_BUSINESS).includes(materialDetail.business as LOW_CODE_BUSINESS)
      // 逻辑组件不展示预览
      && materialDetail.tags.category.find(tag => tag.key === 'logic') === undefined
    return {
      key: 'preview',
      tab: '组件预览',
      show,
      content: show && (
        <ComponentPreview
          materialId={materialDetail.id}
          version={materialDetail.currentVersion.version}
          business={materialDetail.business}
        />
      ),
    }
  }, [materialDetail])

  // ======================== 版本管理 ========================
  const MaterialVersionsTab = useMemo(() => {
    return {
      key: 'version-manage',
      tab: '版本列表',
      show: true,
      content: (
        <VersionManage material={materialDetail} />
      ),
    }
  }, [materialDetail])

  // ======================== 数据指标 ========================
  const MaterialMetricsTab = useMemo(() => {
    const show: boolean = [Business.KAEL, Business.FANG_ZHOU].includes(materialDetail?.business)
    return {
      key: 'metrics',
      tab: '数据指标',
      show,
      content: show && (
        <DataReport materialId={materialDetail.id} business={materialDetail.business} />
      ),
    }
  }, [materialDetail])

  // ======================== 引用 ========================
  const MaterialReferTab = useMemo(() => {
    const show = materialDetail && materialDetail.type !== MaterialType.COM_LIB
    return {
      key: 'refer',
      tab: '引用记录',
      show: show,
      content: show && (
        <MaterialRefer materialId={materialDetail.id} />
      ),
    }
  }, [materialDetail])

  // ======================== 属性协议 ========================
  const MaterialPropsSchema = useMemo(() => {
    const show = materialDetail && materialDetail.type !== MaterialType.COM_LIB
    return {
      key: 'material_schema',
      tab: '属性协议',
      show: show,
      content: show && (
        <MaterialsProps
          initialVersion={materialDetail?.version}
          versions={materialDetail?.historyVersions}
        />
      ),
    }
  }, [materialDetail])

  // ======================== tabs ========================
  const tabs = useMemo(() => {
    return [
      SubComponentsTab,
      MaterialDocTab,
      MaterialPreviewTab,
      MaterialVersionsTab,
      MaterialReferTab,
      MaterialMetricsTab,
      MaterialPropsSchema,
    ].filter(tab => tab.show)
  }, [SubComponentsTab, MaterialDocTab, MaterialPreviewTab, MaterialVersionsTab, MaterialMetricsTab])
  const [currentTab, setCurrentTab] = useState<string>(tabs[0].key)
  useEffect(() => setCurrentTab(tabs[0].key), [tabs])

  return (
    <div className={styles['material-detail']}>
      <BackwardNav />
      <div className={styles['material-detail-content']}>
        <MaterialVersionCard
          loading={loading}
          namespace={materialDetail?.namespace}
          materialVersionDetail={materialDetail?.currentVersion}
        />
        <Card
          tabList={tabs}
          activeTabKey={currentTab}
          onTabChange={key => setCurrentTab(key)}
        >
          {tabs.map(tab => (
            <div key={tab.key} style={{ display: tab.key === currentTab ? 'block' : 'none' }}>
              {tab?.content}
            </div>
          ))}
        </Card>
      </div>

      {!!materialDetail
      && materialDetail.type !== MaterialType.COM_LIB
      && (
        <SamePackageMaterials
          currentMaterialNamespace={materialDetail.namespace}
          packageName={materialDetail?.currentVersion.schema?.packageName}
        />
      )}
    </div>
  )
}

export default observer(MaterialComLibDetail)
