import { JsonObject } from 'type-fest'
import { isArray, isObject, isObjectLike, isString } from 'lodash-es'
import { createContext, useContext, useEffect, useState } from 'react'
import { createTreeMate, TreeMate } from 'treemate'

import {
  queryComponentMetaListByBusinessUsingGET,
  viewAllMaterialTagUsingGET,
} from '@/services'
import { safeParse } from '@/utils'
import { Tag } from '@/types'

import { getLingZhuDomainDirectory } from '@/services/v2/lingzhu'
import { useBusinessFromURLQuery } from '@/hooks/useBusinessFromURLQuery'
import { STATIST_TREE_NODE } from '@/components/domain-selector/DomainSelector'

export interface MaterialListContextType {
  tagOptions: { value: string, label: string }[]
  domainDirectory: TreeMate<Service.Forward.LingZhu.DomainDirectory>
  metaOptions: (Module.Meta.Meta & {
    items: Module.Meta.MetaValue[]
    condition?: {
      field: string
      value: string[]
    }
  })[]
}

const MaterialListContext = createContext<(MaterialListContextType | undefined)>(undefined)
export const useMaterialListContext = () => useContext<MaterialListContextType | undefined>(MaterialListContext)

const MaterialListContextProvider: React.FC = ({ children }) => {
  const [business] = useBusinessFromURLQuery()

  // ======================== 物料分类选项 ========================
  const [tagOptions, setTagOptions] = useState<
    MaterialListContextType['tagOptions']
  >([])
  useEffect(() => {
    viewAllMaterialTagUsingGET({
      pageNum: 1,
      pageSize: 800,
    }).then((res) => {
      const dataSource = res.dataSource as Tag[]
      const nextTagOptions = dataSource
        .sort((a, b) => a.order - b.order)
        .map(item => ({
          value: item.id.toString(),
          label: item.title,
        }))
      setTagOptions(nextTagOptions)
    })
  }, [business])

  // ======================== 元信息选项 ========================
  const [metaOptions, setMetaOptions] = useState<
    MaterialListContextType['metaOptions']
  >([])
  useEffect(() => {
    queryComponentMetaListByBusinessUsingGET({ business }).then((res) => {
      const nextMetaOptions = (res as (Module.Meta.Meta & { items: Module.Meta.MetaValue[] })[]).map((item) => {
        const rawMetaObj = safeParse(item.meta) as JsonObject
        let condition: {
          field: string
          value: string[]
        }

        if (
          isObjectLike(rawMetaObj)
          && 'condition' in rawMetaObj
          && isObject(rawMetaObj.condition)
          && 'meta' in rawMetaObj.condition
          && isString(rawMetaObj.condition.meta)
          && 'meta_value' in rawMetaObj.condition
          && isArray(rawMetaObj.condition.meta_value)
          && rawMetaObj.condition.meta_value.length
          && rawMetaObj.condition.meta_value.every(isString)
        ) {
          condition = {
            field: rawMetaObj.condition.meta,
            value: rawMetaObj.condition.meta_value,
          }
        }

        return {
          ...item,
          condition,
          items: item.items.sort((a, b) => a.order - b.order),
        }
      })
      setMetaOptions(nextMetaOptions)
    })
  }, [business])

  // ======================== 领域目录信息获取 ========================
  const [domainDirectory, setDomainDirectory] = useState<
    TreeMate<Service.Forward.LingZhu.DomainDirectory>
  >(createTreeMate([STATIST_TREE_NODE]))
  useEffect(() => {
    getLingZhuDomainDirectory()
      .then((res) => {
        setDomainDirectory(
          createTreeMate([STATIST_TREE_NODE, ...res.data.data.children], {
            getKey: node => node.value,
          }),
        )
      })
  }, [])

  return (
    <MaterialListContext.Provider value={{ tagOptions, metaOptions, domainDirectory }}>
      {children}
    </MaterialListContext.Provider>
  )
}

export const withMaterialListContextProvider = (Component: React.FC) => {
  return function ComponentWithProvider() {
    return (
      <MaterialListContextProvider>
        <Component />
      </MaterialListContextProvider>
    )
  }
}
