import React, { useEffect, useState } from 'react'
import { Pagination } from 'antd'
import { useUpdateEffect } from 'ahooks'
import LibrariesFilterForm, {
  LibrariesFilterFormValues,
} from './LibrariesFilterForm'

import { Material } from '@/types'
import { MaterialType } from '@/constants'
import { viewAllExternalMaterialListUsingGET } from '@/services'

import { useBusinessFromURLQuery } from '@/hooks/useBusinessFromURLQuery'
import useDomainFromURLQuery from '@/hooks/useDomainFromURLQuery'

import ItemizeList from '@/components/itemize-list'
import MaterialCard from '@/components/material-card'

import styles from '../style.module.less'

const Libraries: React.FC = () => {
  // ======================== fetch params ========================
  const [business] = useBusinessFromURLQuery()
  const [domain, setDomain] = useDomainFromURLQuery()
  const [librariesSearchParams, setLibrariesSearchParams] = useState<LibrariesFilterFormValues>()
  const [paginationParams, setPaginationParams] = useState({
    pageNum: 1,
    pageSize: 20,
  })

  // 物料列表更新 domain 后，物料库列表页也要刷新
  useUpdateEffect(() => {
    const nextParams = {
      ...librariesSearchParams,
      domain,
    }
    const nextPaginationParams = {
      ...paginationParams,
      pageNum: 1,
    }
    setLibrariesSearchParams(nextParams)
    setInitialValues(nextParams)
    setPaginationParams(nextPaginationParams)
    fetchLibraries(nextParams, nextPaginationParams)
  }, [domain])

  // ======================== fetcher ========================
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [dataSource, setDataSource] = useState<Material[]>([])
  const fetchLibraries = async (
    librariesSearchParams: LibrariesFilterFormValues,
    paginationParams: { pageNum: number, pageSize: number },
  ) => {
    setLoading(true)
    window.scrollTo(0, 0)
    try {
      const { dataSource, total } = await viewAllExternalMaterialListUsingGET({
        ...librariesSearchParams,
        ...paginationParams,
        business,
        type: MaterialType.COM_LIB,
      })
      setDataSource(dataSource as Material[])
      setTotal(total)
    }
    finally {
      setLoading(false)
    }
  }

  // ======================== 初始化 ========================
  const [initialValues, setInitialValues] = useState<LibrariesFilterFormValues>()
  useEffect(() => {
    if (business) {
      const initialParams = {
        domain: useDomainFromURLQuery.getDomainSync(business),
      }
      const initialPaginationParams = {
        ...paginationParams,
        pageNum: 1,
      }
      setInitialValues(initialParams)
      setLibrariesSearchParams(initialParams)
      setPaginationParams(initialPaginationParams)
      fetchLibraries(
        initialParams,
        initialPaginationParams,
      )
    }
  }, [business])

  return (
    <div className={styles['material-list-content-layout']}>
      <LibrariesFilterForm
        loading={loading}
        initialValues={initialValues}
        onSubmit={(nextParams) => {
          setDomain(nextParams.domain)
          setLibrariesSearchParams(nextParams)
          setPaginationParams({ ...paginationParams, pageNum: 1 })
          fetchLibraries(nextParams, { ...paginationParams, pageNum: 1 })
        }}
      />
      <ItemizeList
        loading={loading}
        data={dataSource}
        itemKey="id"
        renderItem={item => (
          <MaterialCard type="component-lib" material={item} />
        )}
      />

      <Pagination
        disabled={loading}
        onChange={(page, pageSize) => {
          setPaginationParams({ pageNum: page, pageSize })
          fetchLibraries(librariesSearchParams, { pageNum: page, pageSize })
        }}
        pageSize={paginationParams.pageSize}
        current={paginationParams.pageNum}
        total={total}
        showSizeChanger
        showQuickJumper={true}
        showTotal={total => `共计 ${total} 个物料`}
        hideOnSinglePage={false}
        style={{ alignSelf: 'flex-end', marginBottom: '20px' }}
      />
    </div>
  )
}

export default Libraries
