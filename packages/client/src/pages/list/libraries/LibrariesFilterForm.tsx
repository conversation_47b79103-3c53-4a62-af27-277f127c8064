import { Form } from 'antd'
import React, { useEffect } from 'react'

import TagSelector from '@/components/tag-selector'
import { nextTick } from '@/utils/function'
import { Business, PlatformOptions } from '@/constants'
import { useBusinessFromURLQuery } from '@/hooks/useBusinessFromURLQuery'

import DomainSelector from '@/components/domain-selector'
import { useMaterialListContext } from '../MaterialListContext'
import KeywordSearcher from '../components/keyword-searcher'

import styles from '../style.module.less'

export interface LibrariesFilterFormValues {
  keyword?: string
  platform?: string
  domain?: number
  [key: `meta.${string}`]: string
}

interface Props {
  loading?: boolean
  initialValues?: LibrariesFilterFormValues
  onSubmit?: (value: LibrariesFilterFormValues) => void
}

const LibrariesFilterForm: React.FC<Props> = (props) => {
  const [formInstance] = Form.useForm<LibrariesFilterFormValues>()

  const [business] = useBusinessFromURLQuery()

  useEffect(() => {
    formInstance.resetFields()
  }, [props.initialValues])

  const { metaOptions } = useMaterialListContext()

  const onSubmit = () => {
    formInstance.submit()
    props.onSubmit?.(formInstance.getFieldsValue())
  }

  return (
    <div className={styles['filter-form']}>
      <Form
        layout="horizontal"
        labelAlign="left"
        form={formInstance}
        initialValues={props.initialValues}
        onFieldsChange={(changedFields) => {
          if (changedFields.some(item => item.name[0] !== 'keyword')) {
            onSubmit()
          }
        }}
      >
        <Form.Item name="keyword">
          <KeywordSearcher
            disabled={props.loading}
            loading={props.loading}
            onSearch={onSubmit}
          />
        </Form.Item>
        <div className={styles['selectors']}>
          <Form.Item name="domain" label="业务目录" hidden={business !== Business.KAEL}>
            <DomainSelector disabled={props.loading} />
          </Form.Item>
          <Form.Item name="platform" label="运行端">
            <TagSelector
              disabled={props.loading}
              options={PlatformOptions}
              emptyOption
            />
          </Form.Item>
          {/* 需要封装 */}
          {metaOptions.map((metaOption) => {
            return (
              <Form.Item
                noStyle
                key={metaOption.id}
                dependencies={
                  metaOption.condition
                    ? [`meta.${metaOption.condition.field}`]
                    : []
                }
              >
                {({ getFieldValue, setFieldsValue }) => {
                  const shouldHide
                    = !!metaOption.condition
                    && !metaOption.condition.value.includes(
                      getFieldValue(`meta.${metaOption.condition.field}`),
                    )
                  if (getFieldValue(`meta.${metaOption.value}`) && shouldHide) {
                    nextTick(() =>
                      setFieldsValue({
                        [`meta.${metaOption.value}`]: undefined,
                      }),
                    )
                  }
                  return (
                    <Form.Item
                      hidden={shouldHide}
                      name={`meta.${metaOption.value}`}
                      label={metaOption.title}
                    >
                      <TagSelector
                        emptyOption
                        disabled={props.loading}
                        options={metaOption.items.map(item => ({
                          label: item.title,
                          value: item.value,
                        }))}
                      />
                    </Form.Item>
                  )
                }}
              </Form.Item>
            )
          })}
        </div>
      </Form>
    </div>
  )
}

export default LibrariesFilterForm
