import React, { useEffect, useState } from 'react'
import { Pagination } from 'antd'
import { useUpdateEffect } from 'ahooks'

import MaterialsFilterForm, {
  MaterialsFilterFormValues,
} from './MaterialsFilterForm'

import { MaterialType } from '@/constants'
import { viewAllExternalMaterialListUsingGET } from '@/services'
import { useBusinessFromURLQuery } from '@/hooks/useBusinessFromURLQuery'
import useDomainFromURLQuery from '@/hooks/useDomainFromURLQuery'
import { getMaterialIntegralType } from '@/utils'
import { getLocalStorageItem } from '@/utils/localstorage'
import { Material, SortingType } from '@/types'

import ItemizeList from '@/components/itemize-list'
import MaterialCard from '@/components/material-card'

import styles from '../style.module.less'

const Materials: React.FC = () => {
  // ======================== fetch params ========================
  const [business] = useBusinessFromURLQuery()
  const [domain, setDomain] = useDomainFromURLQuery()
  const [materialsSearchParams, setMaterialsSearchParams] = useState<MaterialsFilterFormValues>()
  const [paginationParams, setPaginationParams] = useState({
    pageNum: 1,
    pageSize: 20,
  })

  // 物料库列表更新 domain 后，物料列表页也要刷新
  useUpdateEffect(() => {
    const nextParams = {
      ...materialsSearchParams,
      domain,
    }
    const nextPaginationParams = {
      ...paginationParams,
      pageNum: 1,
    }
    setMaterialsSearchParams(nextParams)
    setInitialValues(nextParams)
    setPaginationParams(nextPaginationParams)
    fetchMaterials(nextParams, nextPaginationParams)
  }, [domain])

  // ======================== fetcher ========================
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [dataSource, setDataSource] = useState<Material[]>([])
  const fetchMaterials = async (
    materialsSearchParams: MaterialsFilterFormValues,
    paginationParams: { pageNum: number, pageSize: number },
  ) => {
    setLoading(true)
    window.scrollTo(0, 0)
    try {
      const { dataSource, total } = await viewAllExternalMaterialListUsingGET({
        ...materialsSearchParams,
        ...paginationParams,
        business,
        type: MaterialType.COMPONENT,
        integralType: getMaterialIntegralType(business),
      })
      setDataSource(dataSource as Material[])
      setTotal(total)
    }
    finally {
      setLoading(false)
    }
  }

  // ======================== 初始化 ========================
  const [initialValues, setInitialValues] = useState<MaterialsFilterFormValues>()
  useEffect(() => {
    if (business) {
      const initialParams = {
        sortingType: getLocalStorageItem('material_list_sorting_type')
          || SortingType.INTEGRAL,
        domain: useDomainFromURLQuery.getDomainSync(business),
      }
      const initialPaginationParams = {
        ...paginationParams,
        pageNum: 1,
      }
      setInitialValues(initialParams)
      setMaterialsSearchParams(initialParams)
      setPaginationParams(initialPaginationParams)
      fetchMaterials(
        initialParams,
        initialPaginationParams,
      )
    }
  }, [business])

  return (
    <div className={styles['material-list-content-layout']}>
      <MaterialsFilterForm
        loading={loading}
        initialValues={initialValues}
        onSubmit={(nextParams) => {
          setDomain(nextParams.domain)
          setMaterialsSearchParams(nextParams)
          setPaginationParams({ ...paginationParams, pageNum: 1 })
          fetchMaterials(nextParams, { ...paginationParams, pageNum: 1 })
        }}
      />

      <ItemizeList
        loading={loading}
        data={dataSource}
        itemKey="id"
        renderItem={item => <MaterialCard type="component" material={item} />}
      />

      <Pagination
        disabled={loading}
        onChange={(page, pageSize) => {
          setPaginationParams({ pageNum: page, pageSize })
          fetchMaterials(materialsSearchParams, { pageNum: page, pageSize })
        }}
        pageSize={paginationParams.pageSize}
        current={paginationParams.pageNum}
        total={total}
        showSizeChanger
        showQuickJumper={true}
        showTotal={total => `共计 ${total} 个物料`}
        hideOnSinglePage={false}
        style={{ alignSelf: 'flex-end', marginBottom: '20px' }}
      />
    </div>
  )
}

export default Materials
