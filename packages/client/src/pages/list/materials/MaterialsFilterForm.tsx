import { Form, Space } from 'antd'
import React, { useEffect } from 'react'

import TagSelector from '@/components/tag-selector'
import { nextTick } from '@/utils/function'
import { Business, PlatformOptions } from '@/constants'

import { useMaterialListContext } from '../MaterialListContext'
import KeywordSearcher from '../components/keyword-searcher'
import Sortord from '../components/sortord'

import styles from '../style.module.less'
import DomainSelector from '@/components/domain-selector'
import { useBusinessFromURLQuery } from '@/hooks/useBusinessFromURLQuery'

export interface MaterialsFilterFormValues {
  keyword?: string
  domain?: number
  sortingType?: string
  platform?: string
  tagId?: string
  [key: `meta.${string}`]: string
}

interface Props {
  loading?: boolean
  initialValues?: MaterialsFilterFormValues
  onSubmit?: (value: MaterialsFilterFormValues) => void
}

const MaterialsFilterForm: React.FC<Props> = (props) => {
  const [business] = useBusinessFromURLQuery()
  const [formInstance] = Form.useForm<MaterialsFilterFormValues>()

  const { metaOptions, tagOptions } = useMaterialListContext()

  useEffect(() => {
    formInstance.resetFields()
  }, [props.initialValues])

  const onSubmit = () => {
    formInstance.submit()
    const formValues = formInstance.getFieldsValue()
    props.onSubmit?.({
      ...formValues,
      keyword: formValues.keyword?.trim(),
    })
  }

  return (
    <div className={styles['filter-form']}>
      <Form
        layout="horizontal"
        labelAlign="left"
        form={formInstance}
        initialValues={props.initialValues}
        onFieldsChange={(changedFields) => {
          if (changedFields.some(item => item.name[0] !== 'keyword')) {
            onSubmit()
          }
        }}
      >
        <Space direction="horizontal" size="large">
          <Form.Item name="keyword">
            <KeywordSearcher
              onSearch={onSubmit}
              disabled={props.loading}
              loading={props.loading}
            />
          </Form.Item>
          <Form.Item name="sortingType">
            <Sortord disabled={props.loading} loading={props.loading} />
          </Form.Item>
        </Space>
        <div className={styles['selectors']}>
          <Form.Item name="domain" label="业务目录" hidden={business !== Business.KAEL}>
            <DomainSelector disabled={props.loading} />
          </Form.Item>
          <Form.Item name="platform" label="运行端">
            <TagSelector
              disabled={props.loading}
              options={PlatformOptions}
              emptyOption
            />
          </Form.Item>
          <Form.Item name="tagId" label="分类">
            <TagSelector
              disabled={props.loading}
              options={tagOptions}
              emptyOption
            />
          </Form.Item>
          {/* 需要封装 */}
          {metaOptions.map((metaOption) => {
            return (
              <Form.Item
                noStyle
                key={metaOption.id}
                dependencies={
                  metaOption.condition
                    ? [`meta.${metaOption.condition.field}`]
                    : []
                }
              >
                {({ getFieldValue, setFieldsValue }) => {
                  const shouldHide
                    = !!metaOption.condition
                    && !metaOption.condition.value.includes(
                      getFieldValue(`meta.${metaOption.condition.field}`),
                    )
                  if (getFieldValue(`meta.${metaOption.value}`) && shouldHide) {
                    nextTick(() =>
                      setFieldsValue({
                        [`meta.${metaOption.value}`]: undefined,
                      }),
                    )
                  }
                  return (
                    <Form.Item
                      hidden={shouldHide}
                      name={`meta.${metaOption.value}`}
                      label={metaOption.title}
                    >
                      <TagSelector
                        emptyOption
                        disabled={props.loading}
                        options={metaOption.items.map(item => ({
                          label: item.title,
                          value: item.value,
                        }))}
                      />
                    </Form.Item>
                  )
                }}
              </Form.Item>
            )
          })}
        </div>
      </Form>
    </div>
  )
}

export default MaterialsFilterForm
