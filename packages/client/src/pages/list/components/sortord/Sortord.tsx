import React from 'react'
import { observer } from 'mobx-react-lite'
import { Select, SelectProps } from 'antd'

import { IntegralType, SortingType } from '@/types'
import { getMaterialIntegralType } from '@/utils'
import { getLocalStorageItem, setLocalStorageItem } from '@/utils/localstorage'
import { useBusinessFromURLQuery } from '@/hooks/useBusinessFromURLQuery'

interface Props extends SelectProps {}

const STORAGE_KEY = 'material_list_sorting_type'
const storageInitialValue = getLocalStorageItem(STORAGE_KEY)

const Sortord: React.FC<Props> = (props) => {
  const [business] = useBusinessFromURLQuery()
  const sortingTypeOptions = [
    {
      label:
        getMaterialIntegralType(business) === IntegralType.REFER_COUNT
          ? '按引用次数排序'
          : '按组件质量排序',
      value: SortingType.INTEGRAL,
    },
    {
      label: '按更新时间排序',
      value: SortingType.UPDATE_TIME,
    },
  ]

  return (
    <Select
      bordered={false}
      style={{ width: 140 }}
      options={sortingTypeOptions}
      defaultValue={
        sortingTypeOptions.find(item => item.value === storageInitialValue)
          ? storageInitialValue
          : SortingType.INTEGRAL
      }
      onChange={(value, option) => {
        setLocalStorageItem(STORAGE_KEY, value)
        props.onChange?.(value, option)
      }}
    />
  )
}

export default observer(Sortord)
