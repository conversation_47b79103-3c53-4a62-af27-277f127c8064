.contentBlock {
  display: grid;
  grid-gap: 25px;
  @media (min-width: 0) and (max-width: 899px) {
    grid-template-columns: repeat(2, minmax(200px, 1fr));
  }

  @media (min-width: 900px) and (max-width: 1299px) {
    grid-template-columns: repeat(3, minmax(250px, 1fr));
  }

  @media (min-width: 1300px) and (max-width: 2399px) {
    grid-template-columns: repeat(4, minmax(250px, 1fr));
  }

  @media (min-width: 2400px) {
    grid-template-columns: repeat(5, minmax(300px, 1fr));
  }

  &.isSelector {
    border: 1px solid var(--border-color);
  }

  :global {
    .ant-spin-dot-item {
      background-color: var(--theme-color);
    }
  }
}

.empty {
  flex: 1;
}

.emptyContentBlock {
  display: block;
}

.spin {
  flex: 1;
  margin-top: 160px;
}

.noMore {
  width: 100%;
  height: 54px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 400;
}

.loading {
  width: 100%;
  margin-top: 20px;
}
