import { FC, useCallback, useEffect, useMemo } from 'react'
import { Empty, Pagination, Spin } from 'antd'
import { PaginationConfig } from 'antd/lib/pagination'
import { observer } from 'mobx-react-lite'
import cx from 'clsx'

import { INIT_PAGE_INDEX, INIT_PAGE_SIZE } from '@/constants'
import { DisplayMode, Material } from '@/types'

import MaterialItem from '@/components/material-card'

import styles from './index.module.less'

let container: HTMLDivElement = null

export interface MaterialListProps {
  displayMode?: DisplayMode
  type: 'component-lib' | 'component'
  onPageChange?: (page, pageSize) => void
  onShowSizeChange?: (current, page) => void
  fetchMoreList?: () => Promise<void>
  data: Material[]
  loading?: boolean
  total: number
  pageIndex: number
  pageSize?: number
  emptyText?: string
  pagination?: PaginationConfig
}

const MaterialList: FC<MaterialListProps> = ({
  displayMode = 'scroll-list',
  type = 'component-lib',
  onPageChange,
  onShowSizeChange,
  fetchMoreList,
  loading,
  total,
  pageIndex = INIT_PAGE_INDEX,
  pageSize = INIT_PAGE_SIZE,
  data,
  emptyText = '暂无组件',
  pagination,
}) => {
  const onScroll = useCallback(() => {
    if (
      loading
      || total === 0
      || (total && pageIndex * pageSize >= total)
      || displayMode === 'pagination'
    ) {
      return
    }

    const scrollTop = container.scrollTop || 0
    const clientHeight = container.clientHeight
    const scrollHeight = container.scrollHeight

    if (scrollTop + clientHeight >= scrollHeight - 500) {
      if (fetchMoreList) {
        fetchMoreList?.().catch(console.log)
      }
    }
  }, [loading, total, pageIndex, pageSize, displayMode])

  useEffect(() => {
    container = document.querySelector('#material-layout')
  }, [])

  useEffect(() => {
    const debouncedOnScroll = window._.debounce(onScroll, 50)
    container?.addEventListener('scroll', debouncedOnScroll, false)
    return () => {
      container?.removeEventListener('scroll', debouncedOnScroll, false)
    }
  }, [onScroll])

  const empty = useMemo(() => {
    if (total === 0 && !loading) {
      return (
        <Empty
          className={styles.empty}
          imageStyle={{ height: '152px' }}
          description={emptyText}
        />
      )
    }

    return null
  }, [total, loading, emptyText])

  const spin = useMemo(() => {
    return (!total || total < 0) && loading
      ? (
        <Spin className={styles.spin} />
      )
      : null
  }, [total, loading])

  const handlePageChange = (page, pageSize) => {
    onPageChange?.(page, pageSize)
  }
  const handleShowSizeChange = (current, pageSize) => {
    onShowSizeChange?.(current, pageSize)
  }

  return (
    <Spin spinning={displayMode === 'pagination' && loading}>
      <div
        className={cx(
          styles.contentBlock,
          empty ? styles.emptyContentBlock : '',
        )}
      >
        {empty}
        {data?.map((item, index) => (
          <MaterialItem
            key={`${item.namespace}-${index}`}
            type={type}
            material={item}
          />
        ))}
        {displayMode === 'scroll-list' && spin}
      </div>
      {displayMode === 'pagination' && total > 0
        ? (
          <Pagination
            showSizeChanger
            onShowSizeChange={handleShowSizeChange}
            defaultCurrent={1}
            pageSize={pagination?.pageSize || pageSize}
            onChange={handlePageChange}
            showQuickJumper={true}
            current={pagination?.current || pageIndex}
            total={total}
            hideOnSinglePage={false}
            style={{
              float: 'right',
              padding: '0 24px 16px 0',
              marginTop: '16px',
            }}
          />
        )
        : null}
    </Spin>
  )
}

export default observer(MaterialList)
