import React from 'react'
import clsx from 'clsx'
import { Input } from 'antd'
import { SearchProps } from 'antd/lib/input'

import styles from './style.module.less'

interface Props extends SearchProps {}

const KeywordSearcher: React.FC<Props> = (props) => {
  const { style, className, ...otherProps } = props
  return (
    <Input.Search
      style={{ width: '360px', ...style }}
      className={clsx(styles['search'], className)}
      allowClear
      enterButton
      size="middle"
      placeholder="请输入关键字"
      {...otherProps}
    />
  )
}

export default KeywordSearcher
