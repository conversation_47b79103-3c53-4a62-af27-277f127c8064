import React from 'react'
import { Tabs } from 'antd'
import useUrlState from '@ahooksjs/use-url-state'

import Materials from './materials'
import Libraries from './libraries'

import { withMaterialListContextProvider } from './MaterialListContext'

import styles from './style.module.less'

const { TabPane } = Tabs

const MaterialList: React.FC = () => {
  const [urlState, setUrlState] = useUrlState(
    {
      ['material-type']: 'materials',
    },
    {
      navigateMode: 'replace',
    },
  )

  return (
    <Tabs
      activeKey={urlState['material-type']}
      onChange={value => setUrlState({ ['material-type']: value })}
      className={styles['primary-category-tabs']}
    >
      <TabPane tab="物料库" key="libraries">
        <Libraries />
      </TabPane>
      <TabPane tab="物料" key="materials">
        <Materials />
      </TabPane>
    </Tabs>
  )
}

export default withMaterialListContextProvider(MaterialList)
