@import "../../styles/mixin.less";

.primary-category-tabs {
  :global {
    .ant-tabs-nav {
      margin-bottom: 0px;
      position: sticky;
      top: 72px;
      z-index: 1000;
      background-color: white;
    }

    .ant-tabs-nav::before {
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      width: 100vw;
    }

    .ant-tabs-nav-wrap,
    .ant-tabs-content-holder {
      .fixed-page-margin();
    }

    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: var(--theme-color);
    }
    .ant-tabs-ink-bar {
      background-color: var(--theme-color);
    }
  }
}

.material-list-page {
  width: 100%;
  height: 100%;
}

.filter-form {
  :global {
    .ant-form-item-label {
      font-weight: bold;
      min-width: 90px;
    }
  }

  .selectors {
    display: flex;
    flex-direction: column;
    gap: 10px;

    :global {
      .ant-form-item {
        margin-bottom: 0 !important;
      }
    }
  }
}

.material-list-content-layout {
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding: 20px 0;
}

.material-list-container {
  // min-width: 1440px;
  margin-top: 24px;
  background: rgb(255, 255, 255);
  border-radius: 5px;
  flex: 1;

  .operateArea {
    display: flex;
    justify-content: flex-end;
    padding: 0 24px;
  }
}

.iconRadio {
  display: flex;
  align-items: center;
}
