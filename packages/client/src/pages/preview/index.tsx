import React, { useRef, useState } from 'react'
import { Alert, Spin, Tabs } from 'antd'
import { getFangZhouPreviewUrl, getKaelPreviewUrl, getQueryString } from '@/utils'
import { Business } from '@/constants'

import styles from './index.module.less'

export default function PreviewPage() {
  const [loading, setLoading] = useState(true)
  const [activeKey, setActiveKey] = useState(Business.FANG_ZHOU)
  const hasActiveTabs = useRef([Business.FANG_ZHOU])
  const materialId = getQueryString('id')
  const version = getQueryString('version')
  const onChangeTab = (tab) => {
    setActiveKey(tab)
    if (!hasActiveTabs.current.includes(tab)) {
      hasActiveTabs.current.push(tab)
      setLoading(true)
    }
  }
  return (
    <div className={styles.previewContainer}>
      <Alert type="info" message="预览物料转到低代码场景下的使用情况" className={styles.alert} />
      <Tabs activeKey={activeKey} onChange={onChangeTab}>
        <Tabs.TabPane key={Business.FANG_ZHOU} tab="方舟">
          <Spin spinning={loading}>
            <iframe
              width="100%"
              height={800}
              className={styles.iframe}
              src={getFangZhouPreviewUrl(materialId, version)}
              onLoad={() => setLoading(false)}
            />
          </Spin>
        </Tabs.TabPane>
        <Tabs.TabPane key={Business.KAEL} tab="灵筑">
          <Spin spinning={loading}>
            <iframe
              width="100%"
              height={800}
              className={styles.iframe}
              src={getKaelPreviewUrl(materialId, version)}
              onLoad={() => setLoading(false)}
            />
          </Spin>
        </Tabs.TabPane>
      </Tabs>
    </div>
  )
}
