import { Select, Space, Typography } from 'antd'
import { useEffect, useState } from 'react'
import MarkdownPreview from '@uiw/react-markdown-preview'
import BackwardNav from '@/layouts/backward-nav'

import hljs from 'highlight.js'
import 'highlight.js/styles/default.css'

import styles from './index.module.less'

import schema20241114 from './schema.2024.11.14.md?raw'
import schema20241118 from './schema.2024.11.28.md?raw'
import schema20241216 from './schema.2024.12.16.md?raw'
import schema20250527 from './schema.2025.5.27.md?raw'

const SCHEMA_MAP = [
  {
    version: '2024.11.14',
    schema: schema20241114,
  },
  {
    version: '2024.11.28',
    schema: schema20241118,
  },
  {
    version: '2024.12.16',
    schema: schema20241216,
  },
  {
    version: '2025.5.27',
    schema: schema20250527,
  },
].sort((a, b) => new Date(b.version).getTime() - new Date(a.version).getTime())

export default function ComponentSchema() {
  // ======================== version ========================
  const [schemaVersion, setSchemaVersion] = useState(SCHEMA_MAP[0].version)

  useEffect(() => {
    hljs.highlightAll()
  }, [])

  return (
    <>
      <BackwardNav />
      <div className={styles['schema-container']}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Space direction="horizontal" size="large">
            <Typography.Title level={2} style={{ margin: 0 }}>物料中台通用协议</Typography.Title>
            <Select
              options={SCHEMA_MAP.map(item => ({
                value: item.version,
                label: item.version,
              }))}
              value={schemaVersion}
              onChange={setSchemaVersion}
              style={{ width: 200 }}
            />
          </Space>
          <MarkdownPreview
            source={SCHEMA_MAP.find(item => item.version === schemaVersion)?.schema}
            wrapperElement={{ 'data-color-mode': 'light' }}
          />
        </Space>
      </div>
    </>
  )
}
