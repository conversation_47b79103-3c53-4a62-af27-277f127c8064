## 目标
基于通用物料协议，让组件自由地在不同的业务中流通起来

## 协议详情

### 字段详情
<table id="props">
  <thead>
    <tr>
      <th colspan="2">字段</th>
      <th>字段描述</th>
      <th colspan="2">字段类型</th>
      <th colspan="2">是否必填<br />✅必选❌非必填</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td colspan="2">packageName</td>
      <td>物料库/组件库名称（同 package.json）</td>
      <td colspan="2">
        <code>string</code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">libraryName</td>
      <td>物料库/组件库挂载在全局（globalThis、window、global）的 key</td>
      <td colspan="2">
        <code> string </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">componentName</td>
      <td>物料名称/变量名（物料导入时的变量名、从 libraryName 取值时的 key）</td>
      <td colspan="2">
        <code>string</code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">componentChineseName</td>
      <td>物料中文名称</td>
      <td colspan="2">
        <code> string </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">componentBundleType</td>
      <td>物料库打包类型</td>
      <td colspan="2">
        <code>"SLMC" | "SL" // 物料、物料库（<s>"SLSC"</s> 单包单组件已不推荐使用）</code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">version</td>
      <td>物料版本</td>
      <td colspan="2">
        <code> string </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">description</td>
      <td>物料描述。遵循 <a href="https://semver.org/" target="__blank">semver</a>规范</td>
      <td colspan="2">
        <code> string </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">instructionUrl</td>
      <td>物料使用说明地址</td>
      <td colspan="2">
        <code> string </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">assetDownloadType</td>
      <td>物料资产存储类型</td>
      <td colspan="2">
        <code> "cdn" | "npm" </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">assetDownloadUrl</td>
      <td>物料资产下载地址</td>
      <td colspan="2">
        <code> string </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">assetPeerDownloadUrl</td>
      <td>物料资产主入口资源依赖的相关资源</td>
      <td colspan="2">
        <code> string[] </code>
      </td>
      <td colspan="2">❌</td>
    </tr>
    <tr>
      <td colspan="2">author</td>
      <td>物料作者</td>
      <td colspan="2">
        <code> string </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">repository&nbsp;<span class="new-tag">NEW</span></td>
      <td>物料仓库地址（git 地址、平台地址等）</td>
      <td colspan="2">
        <code> 
        string | {
          type: string;
          url: string;
          branch?: string;
          directory?: string;
        }
        </code>
      </td>
      <td colspan="2">❌</td>
    </tr>
    <tr>
      <td colspan="2"><s>gitUrl</s></td>
      <td>物料 Git 仓库（推荐使用 repository 字段）</td>
      <td colspan="2">
        <code> string </code>
      </td>
      <td colspan="2">❌</td>
    </tr>
    <tr>
      <td colspan="2">thumbnailUrl</td>
      <td>物料缩略图/列表</td>
      <td colspan="2">
        <code> string | string[] </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">publishTime</td>
      <td>发布时间</td>
      <td colspan="2">
        <code> string </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td colspan="2">updateTime</td>
      <td>更新时间</td>
      <td colspan="2">
        <code> string </code>
      </td>
      <td colspan="2">✅</td>
    </tr>
    <tr>
      <td rowspan="5">tags</td>
      <td>domain</td>
      <td rowspan="5">
        <code> object </code>
      </td>
      <td>所属业务目录</td>
      <td>
        <code>
          number[] // 业务域节点路径，如 [1, 2, 3] 表示业务域树上的节点路径为 1 -> 2 -> 3，默认为 [0]，表示全部。
        </code>
      </td>
      <td>❌</td>
      <td rowspan="5">✅</td>
    </tr>
    <tr>
      <td>platform<br /></td>
      <td>适用平台</td>
      <td>
        <code> "pc" | "h5" | "rn" | "tk" | ("pc" | "h5" | "rn" | "tk")[] </code>
      </td>
      <td>✅</td>
    </tr>
    <tr>
      <td>business</td>
      <td>业务场景</td>
      <td>
        <code>
          "es" | "biz" | "locallife" | "locallifeClient" | "fangzhou" | "kael" //
          电商、商业化、本地前端、本地客户端、方舟、灵筑
        </code>
      </td>
      <td>✅</td>
    </tr>
    <tr>
      <td>category<br />类型：string | string[]</td>
      <td>组件类型</td>
      <td>
        <code>
          "common" | "navigation" | "layout" | "dataInput" | "dataDisplay" | "feedback" | "others" | "chart" | "logic"
          // 通用、导航、布局、数据录入、数据展示、反馈、其他类型、图表、逻辑
        </code>
      </td>
      <td>✅</td>
    </tr>
    <tr>
      <td>meta</td>
      <td>可前往管理后台定制业务域专属标签</td>
      <td>
        <code> Record<'元信息键', Array<'扩展值'>> </code>
      </td>
      <td>❌</td>
    </tr>
    <tr>
      <td rowspan="6">props</td>
      <td>identifier</td>
      <td>props 字段名</td>
      <td rowspan="6">
        <code> object[] </code>
      </td>
      <td>
        <code> string </code>
      </td>
      <td>✅</td>
      <td rowspan="6">❌</td>
    </tr>
    <tr>
      <td>optional</td>
      <td>是否必须</td>
      <td>
        <code> boolean </code>
      </td>
      <td>✅</td>
    </tr>
    <tr>
      <td>description</td>
      <td>props 描述</td>
      <td><code> string </code></td>
      <td>❌</td>
    </tr>
    <tr>
      <td>defaultValue</td>
      <td>props 默认值</td>
      <td><code> JsonValue </code></td>
      <td>❌</td>
    </tr>
    <tr>
      <td>type</td>
      <td>props 值类型</td>
      <td>
        （遵循 <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html">typescript 规范</a>）
        string、number、boolean、null、void、function、array、object、TSTypeReference、enum 等等
      </td>
      <td>✅</td>
    </tr>
    <tr>
      <td>[typeExtendsKey]</td>
      <td>type 附属字段</td>
      <td>
        （根据前面 type 的值动态变化）members、paramter、return、isEnum、value、interfaceName、typeName、extends、object等
      </td>
      <td>❓</td>
    </tr>
    <tr>
      <td colspan="2">dependencies</td>
      <td>同 package.json</td>
      <td colspan="2">
        <code> object </code>
      </td>
      <td colspan="2">❌</td>
    </tr>
    <tr>
      <td colspan="2">devDependencies</td>
      <td>同 package.json</td>
      <td colspan="2">
        <code> object </code>
      </td>
      <td colspan="2">❌</td>
    </tr>
    <tr>
      <td colspan="2">peerDependencies</td>
      <td>同 package.json</td>
      <td colspan="2">
        <code> object </code>
      </td>
      <td colspan="2">❌</td>
    </tr>
    <tr>
      <td colspan="2">components</td>
      <td>组件库内部组件描述（组件库类型需要）</td>
      <td colspan="2">
        <code>
          ({ namespace: string; version: string } | { title: string; children: { namespace: string; version: string }[]
          })[]</code
        >
      </td>
      <td colspan="2">❌</td>
    </tr>
    <tr>
      <td colspan="2">extraConfig</td>
      <td>
        <span>扩展字段，额外的自定义配置。</span><br/>
        <span>可以由生产方和消费方自行约定，物料中台仅做存储。</span><br/>
        <b><span style="color: red;">⚠️ </span>该对象内的配置应当均为可选内容，如果是消费方必须的内容，请及时联系平台进行协商设计。</b>
      </td>
      <td colspan="2">
        <code>
          { [key in 'es' | 'biz' | 'locallife' | 'locallifeClient' | 'fangzhou' | 'kael']?: JsonValue, [key: string]?:
          JsonValue }
        </code>
      </td>
      <td colspan="2">❌</td>
    </tr>
    <tr>
      <td colspan="2">extraConfig.externalizedAssets</td>
      <td>
        外部化依赖版本后的物料资源声明。具体可查看
        <a href="https://docs.corp.kuaishou.com/k/home/<USER>/fcABozdLP99NxFX24_eNrVSZb" target="__blank">《【RFC】物料外部化（external）依赖方案设计》</a>
      </td>
      <td colspan="2">
        <details>
          <summary>类型详情</summary>
          <pre>
            <code class="language-typescript">
interface ExternalizedAssets {
  /**
  * 已经进行了外部化的资源
  */
  url: string;
  /**
  * 外部化资源的配置
  */
  externals: {
    /**
    * 包名
    */
    packageName: string;
    /**
    * 版本
    */
    version: string;
    /**
    * 外部化资源的全局名称
    */
    libraryName: string;
    /**
    * 外部化资源的 URL
    */
    url: string | string[];
  }[]
}
            </code>
          </pre>
        </details>
      </td>
      <td colspan="2">❌</td>
    </tr>
  </tbody>
</table>

<style>
#props details {
  margin: 0;
}
#props details code {
  padding: 0;
}
.new-tag {
  display: inline-block;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 600;
  line-height: 14px;
  color: #fff;
  background-color: #52c41a;
  border-radius: 2px;
}
</style>

### 案例演示

#### 单包单组件场景

> 简介：假设有一个包为 `@fangzhou/goods-card`，这个 npm 包中只有**一个**组件，组件构建时挂载到了 window 的 `goodsCard` 上。

因此该组件的配置如下：

```jsonc
{
  // npm 包名称 必填
  "packageName": "@fangzhou/goods-card",
  // 挂载在 window 上的 key
  "componentName": "goodsCard",
  "libraryName": "goodsCard",
  // 标识单包单组件
  "componentBundleType": "SLSC",
  "componentChineseName": "商品卡片",
  "tags": {
  	"platform": "pc",
    "business": "fangzhou",
    "category": "dataDisplay"
  },
  "description": "这是一个商品卡片组件",
  "instructionUrl": "https://docs/",
  "gitUrl": "https://git/",
  "assetDownloadType": "cdn",
  "assetDownloadUrl": "https://cdn",
  "author": "xxx",
  "thumbnailUrl": "https://cdn",
  "version": "0.1.0",
  "publishTime": "2021-09-10 20:30:33",
  "updateTime": "2021-10-10 20: 59: 59"
}
```

#### 单包多组件场景

> 简介：假设有一个包为 `@fangzhou/pc-common`，这个npm包中只有**多个**组件，其中有一个组件叫做商品卡片，该卡片组件构建时挂载到了 window 下的 `pcCommon` 下的 `goodsCard` 上。

因此该组件的配置如下：

```jsonc
{
  // npm包名称 必填
  "packageName": "@fangzhou/pc-common",
  // 组件库挂载在window上的key
  "libraryName": "pcCommon",
  // 其中一个商品卡片组件，在组件库内的挂载的key
  "componentName": "goodsCard",
  // 标识单包多组件
  "componentBundleType": "SLMC",
  "componentChineseName": "商品卡片",
  "tags": {
  	"platform": "pc",
    "business": "fangzhou",
    "category": "dataDisplay"
  },
  "description": "这是一个商品卡片组件",
  "instructionUrl": "https://docs/",
  "gitUrl": "https://git/",
  "assetDownloadType": "cdn",
  "assetDownloadUrl": "https://cdn",
  "author": "xxx",
  "thumbnailUrl": "https://cdn",
  "version": "0.1.0",
  "publishTime": "2021-09-10 20:30:33",
  "updateTime": "2021-10-10 20: 59: 59",
}
```

#### 单包多组件场景(tree shaking场景)

> 简介：假设有一个包为 `@fangzhou/pc-common`，这个npm包中只有`多个`组件，其中有一个组件叫做商品卡片，该卡片组件构建时挂载到了 window下的`pcCommon`下的`goodsCard`上，引用时不希望引入大包，引入单个组件即可

因此该组件的配置如下：

```jsonc
{
  // npm包名称 必填
  "packageName": "@fangzhou/pc-common",
  // 组件库挂载在window上的key
  "libraryName": "pcCommon",
  // 其中一个商品卡片组件，在组件库内的挂载的key
  "componentName": "goodsCard",
  // 标识单包多组件
  "componentBundleType": "SLMC",
  "componentChineseName": "商品卡片",
  "tags": {
  	"platform": "pc",
    "business": "fangzhou",
    "category": "dataDisplay"
  },
  "description": "这是一个商品卡片组件",
  "instructionUrl": "https://docs/",
  "gitUrl": "https://git/",
  "assetDownloadType": "cdn",
  "assetDownloadUrl": "https://cdn",
  "author": "xxx",
  "thumbnailUrl": "https://cdn",
  "version": "0.1.0",
  "publishTime": "2021-09-10 20:30:33",
  "updateTime": "2021-10-10 20: 59: 59",
  // 处理tree shaking场景
  "extraConfig": {
    "fangzhou": {
      "importSubPath": "@fangzhou/pc-common/dist/es/goods-card"
    }
  }
}
```

#### 复杂props举例

> 简介：假设有一个包为 `@fangzhou/goods-card`，这个 npm 包中只有**一个**组件，组件构建时挂载到了 window 的 `goodsCard` 上，它有很多属性。

```jsonc
{
  // npm包名称 必填
  "packageName": "@fangzhou/goods-card",
  // 挂载在window上的key
  "componentName": "goodsCard",
  "libraryName": "goodsCard",
  // 标识单包单组件
  "componentBundleType": "SLSC",
  "componentChineseName": "商品卡片",
  "tags": {
    "platform": "pc",
    "business": "fangzhou",
    "category": "dataDisplay"
  },
  "description": "这是一个商品卡片组件",
  "instructionUrl": "https://docs/",
  "gitUrl": "https://git/",
  "assetDownloadType": "cdn",
  "assetDownloadUrl": "https://cdn",
  "author": "xxx",
  "thumbnailUrl": "https://cdn",
  "version": "0.1.0",
  "publishTime": "2021-09-10 20:30:33",
  "updateTime": "2021-10-10 20: 59: 59",
  "props": [
    {
      "identifier": "title",
      "type": "string",
      "optional": true,
      "description": "商品选择卡片的标题",
      "defaultValue": "默认标题"
    },
    {
      "identifier": "dataSource",
      "optional": true,
      "description": "表格列展示数据",
      "defaultValue": "",
      "type": "array",
      "members": [
        {
          "type": "TSTypeReference",
          "members": [
            {
              "identifier": "value",
              "type": "string"
            },
            {
              "identifier": "name",
              "type": "string"
            }
          ],
          "interfaceName": "IValue"
        }
      ]
    },
    {
      "identifier": "children",
      "type": "TSTypeReference",
      "interfaceName": "ReactNode",
      "optional": true,
      "description": "主要内容区插槽"
    },
    {
      "identifier": "onChange",
      "type": "function",
      "optional": true,
      "description": "change事件"
    }
  ]
}

```

#### 物料协议中携带扩展的元信息

> 简介：假设有一个物料为 `@fangzhou/pc-common`，想要定义一些自定义元信息，方便在物料筛选时通过元信息进行过滤，可以进行如下 schema 配置。

<div style="font-weight: bold; color: red;">
⚠️ meta信息必须是“元信息管理”中已配置的信息
</div>
<br/>

```jsonc
{
  // npm包名称 必填
  "packageName": "@fangzhou/pc-common",
  // 标识组件库
  "componentBundleType": "SL",
  // 组件库名
  "componentChineseName": "PC 通用组件库",
  "description": "这是一个PC 通用组件库",
  // 可选
  "gitUrl": "https://git/",
  // 可选
  "assetDownloadType": "cdn",
  // 可选
  "assetDownloadUrl": "https://cdn",
  "author": "xxx",
  // 组件库缩略图
  "thumbnailUrl": "https://cdn",
  "version": "0.1.0",
  "publishTime": "2021-09-10 20:30:33",
  "updateTime": "2021-10-10 20: 59: 59",
  "tags": {
    "platform": "pc",
    "business": "fangzhou",
    "category": "dataDisplay",
    "meta": {
      "scene1": ["fangzhou", "legao"],
      "framework": ["React"]
    }
  },
  "components": [
    {
      "namespace": "@fangzhou/pc-common-component-1",
      "version": "0.1.0"
    },
    {
      "title": "分类",
      "children": [
        {
          "namespace": "@fangzhou/pc-common-component-2",
          "version": "0.1.1"
        }
      ]
    }
  ]
}
```

#### 组件库协议示例

> 简介：假设有一个组件库为 `@fangzhou/pc-common`，内部有许多组件，组件库资源构建时挂载到了 window 的 `PCCommon` 上。

```jsonc
{
  // npm包名称 必填
  "packageName": "@fangzhou/pc-common",
  // 标识组件库
  "componentBundleType": "SL",
  // 组件库名
  "componentChineseName": "PC 通用组件库",
  "description": "这是一个PC 通用组件库",
  // 可选
  "gitUrl": "https://git/",
  // 可选
  "assetDownloadType": "cdn",
  // 可选
  "assetDownloadUrl": "https://cdn",
  "author": "xxx",
  // 组件库缩略图
  "thumbnailUrl": "https://cdn",
  "version": "0.1.0",
  "publishTime": "2021-09-10 20:30:33",
  "updateTime": "2021-10-10 20: 59: 59",
  "components": [
    {
      "namespace": "@fangzhou/pc-common-component-1",
      "version": "0.1.0"
    },
    {
      "title": "分类",
      "children": [
        {
          "namespace": "@fangzhou/pc-common-component-2",
          "version": "0.1.1"
        }
      ]
    }
  ]
}
```
