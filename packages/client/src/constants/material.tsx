/** 物料类型，组件库/组件 */
import { Tag } from 'antd'
import { PLATFORMS } from '@global-material-middleoffice/server-v2/shared'

export enum MaterialType {
  /** 组件库 */
  COM_LIB = 'com_lib',
  /** 组件 */
  COMPONENT = 'component',
  PICTURE = 'picture',
  TEMPLATE = 'template',
  THEME = 'theme',
  /** 埋点 */
  SPM = 'spm',
}

export enum VersionType {
  /** 历史版本 */
  HISTORY = 0,
  /** 分支版本 */
  BRANCH = 1,
}

export const MaterialTypeMap = {
  [MaterialType.COMPONENT]: '组件',
  [MaterialType.COM_LIB]: '组件库',
}

export const MaterialTypeOptions = [
  { label: '组件', value: MaterialType.COMPONENT },
  { label: '组件库', value: MaterialType.COM_LIB },
]

export const PlatformOptions = [
  { label: 'PC', value: PLATFORMS.PC },
  { label: 'H5', value: PLATFORMS.H5 },
  { label: 'RN', value: PLATFORMS.RN },
  { label: 'TK', value: PLATFORMS.TK },
]

/** 标签、场景生效状态 */
export enum EffectStatus {
  /** 删除 */
  DELETE = -1,
  /** 禁用 */
  DISABLED = 0,
  /** 生效中 */
  EFFECT = 1,
}

export const EffectStatusMap = {
  [EffectStatus.DELETE]: '已删除',
  [EffectStatus.DISABLED]: '禁用',
  [EffectStatus.EFFECT]: '生效中',
}

export const EFFECT_STATUS_TAG = {
  [EffectStatus.DELETE]: <Tag color="red">已删除</Tag>,
  [EffectStatus.DISABLED]: <Tag color="gray">禁用</Tag>,
  [EffectStatus.EFFECT]: <Tag color="green">生效中</Tag>,
}

export const EffectStatusOptions = [
  {
    label: '生效中',
    value: EffectStatus.EFFECT,
  },
  {
    label: '禁用',
    value: EffectStatus.DISABLED,
  },
  {
    label: '已删除',
    value: EffectStatus.DELETE,
  },
]

export enum Business {
  /** 方舟 */
  FANG_ZHOU = 'fangzhou',
  /** 本地生活 */
  LOCAL_LIFE = 'locallife',
  /** 本地生活客户端 */
  LOCAL_LIFE_CLIENT = 'locallifeClient',
  /** 电商前端 */
  ES = 'es',
  /** 海外电商 */
  KWAI_ES = 'kwaies',
  /** 商业化 */
  BIZ = 'biz',
  /** 灵筑 */
  KAEL = 'kael',
}

export const BusinessMap = {
  [Business.FANG_ZHOU]: '方舟',
  [Business.ES]: '电商前端',
  [Business.KWAI_ES]: '海外电商前端',
  [Business.BIZ]: '商业化',
  [Business.KAEL]: '灵筑',
  [Business.LOCAL_LIFE]: '本地生活',
  [Business.LOCAL_LIFE_CLIENT]: '本地生活客户端',
}

export const businessOptions = [
  {
    label: '方舟',
    value: Business.FANG_ZHOU,
  },
  {
    label: '灵筑',
    value: Business.KAEL,
  },
  {
    label: '电商前端',
    value: Business.ES,
  },
  {
    label: '海外电商前端',
    value: Business.KWAI_ES,
  },
  {
    label: '商业化前端',
    value: Business.BIZ,
  },
  {
    label: '本地生活前端',
    value: Business.LOCAL_LIFE,
  },
  {
    label: '本地生活客户端',
    value: Business.LOCAL_LIFE_CLIENT,
  },
]

export enum ConvertStatus {
  /** 失败 */
  FAILED = -1,
  /** 转换中 */
  LOADING = 0,
  /** 成功 */
  SUCCESS = 1,
  /** 已发布 */
  PUBLISHED = 2,
}

export const ConvertStatusMap = {
  [ConvertStatus.FAILED]: '失败',
  [ConvertStatus.LOADING]: '转换中',
  [ConvertStatus.SUCCESS]: '转换成功，未发布',
  [ConvertStatus.PUBLISHED]: '已发布',
}

export const ConvertColorMap = {
  [ConvertStatus.FAILED]: '#ff4d4f',
  [ConvertStatus.LOADING]: '#174ae6',
  [ConvertStatus.SUCCESS]: '#faad14',
  [ConvertStatus.PUBLISHED]: '#52c41a',
}

export const INIT_PAGE_SIZE = 20
export const INIT_PAGE_INDEX = 1
export const defaultComLibIcon = 'https://f2.eckwai.com/udata/pkg/eshop/chrome-plugin-upload/2022-08-08/1659944542878.fa06412c751185cf.png'
export const defaultComponentIcon = 'https://f2.eckwai.com/udata/pkg/eshop/chrome-plugin-upload/2022-08-08/1659945545554.479c68e4609f462f.png'
