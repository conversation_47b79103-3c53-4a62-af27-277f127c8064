import { makeAutoObservable } from 'mobx'
import dayjs from 'dayjs'
import { getPreWeekRange } from 'mybricks-material-backend/common/util/date'
import { queryMaterialReportDataUsingGET, queryMaterialReportOverviewUsingGET } from '@/services'
import { CODE_TYPE } from '@global-material-middleoffice/server-v2/shared'

const preDate = getPreWeekRange()
const defaultSearchParams = {
  business: '',
  codeType: CODE_TYPE.PRO_CODE,
  timeRange: {
    startTime: preDate.start,
    endTime: preDate.end,
  },
}

class ReportModel {
  loading = false
  searchParams = Object.assign({}, defaultSearchParams)
  reportData = {}
  codeOverview = []
  xData: []
  lineLoading = false

  constructor() {
    makeAutoObservable(this)
  }

  isProCode = () => {
    return this.searchParams.codeType === CODE_TYPE.PRO_CODE
  }

  reset = () => {
    this.reportData = {}
    this.codeOverview = []
    this.searchParams = defaultSearchParams
    this.searchParams = Object.assign({}, defaultSearchParams)
  }

  init = async () => {
    await this.resetAndGetReportOverviewData(true)
  }

  queryReportLineData = async () => {
    this.lineLoading = true
    const reportData = await queryMaterialReportDataUsingGET({
      business: this.searchParams.business,
      code_type: this.searchParams.codeType,
    })
    Object.keys(reportData ?? {}).forEach((key) => {
      reportData[key] = reportData[key]?.reverse()
    })
    this.reportData = reportData

    try {
      this.xData = reportData[Object.keys(reportData)[0]]?.map((item) => {
        if (this.isProCode()) {
          return (dayjs(item.start_time).format('MM/DD') + '-' + dayjs(item.end_time).format('MM/DD'))
        }

        return dayjs(item.start_time).format('MM/DD')
      }) ?? []
      this.lineLoading = false
    }
    catch (error) {
      console.log('error', error)
      this.lineLoading = false
    }
  }

  resetAndGetReportOverviewData = async (flag?: boolean) => {
    this.loading = true
    try {
      const { startTime, endTime } = this.searchParams.timeRange
      if (flag) {
        await this.queryReportLineData()
      }
      this.codeOverview = await queryMaterialReportOverviewUsingGET({
        business: this.searchParams.business,
        code_type: this.searchParams.codeType,
        start_time: startTime,
        end_time: endTime,
      })
    }
    finally {
      this.loading = false
    }
  }

  search = () => {
    this.resetAndGetReportOverviewData()
  }

  updateSearchParams = async (fieldName: string, value: AnyType, search = true) => {
    if (value !== this.searchParams[fieldName] || search) {
      this.searchParams[fieldName] = value
      if (search) {
        this.resetAndGetReportOverviewData(fieldName === 'business')
      }
    }
  }
}

export const reportModel = new ReportModel()
