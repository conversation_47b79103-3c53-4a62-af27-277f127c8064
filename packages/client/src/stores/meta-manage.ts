import { makeAutoObservable } from 'mobx'
import { Meta } from '@/types'
import {
  Business,
  createMetaUsingPOST,
  deleteMetaUsingPOST,
  updateMetaStatusUsingPOST,
  updateMetaUsingPOST,
  viewAllMetaManageListUsingGET,
} from '@/services'
import { userModel } from '@/stores/user'
import { EffectStatus } from '@/constants'

export const PageSize = 10
class MetaManageModel {
  meta = {
    pagination: {
      total: 0,
      current: 1,
      pageSize: PageSize,
    },
    loading: false,
    searchParams: {
      title: '',
      status: undefined,
    },
    dataSource: [] as Meta[],
  }

  constructor() {
    makeAutoObservable(this)
  }

  reset = () => {
    this.meta = {
      pagination: {
        total: 0,
        current: 1,
        pageSize: PageSize,
      },
      loading: false,
      searchParams: {
        title: '',
        status: undefined,
      },
      dataSource: [] as Meta[],
    }
  }

  getMetas = async () => {
    this.meta.dataSource = []
    this.meta.pagination.total = 0
    this.meta.loading = true
    const { dataSource = [], pageNum, pageSize, total } = await viewAllMetaManageListUsingGET({
      title: this.meta.searchParams.title,
      status: this.meta.searchParams.status,
      pageNum: this.meta.pagination.current,
      pageSize: this.meta.pagination.pageSize,
    })
    if (this.meta.pagination.current !== Number(pageNum)) {
      this.meta.loading = false
      return
    }

    this.meta.pagination.total = total
    this.meta.loading = false
    this.meta.dataSource = dataSource as Meta[]
    this.meta.pagination = {
      total: total,
      current: pageNum,
      pageSize: pageSize,
    }
  }

  createMeta = async (meta: { title: string, value: string, business: Business, meta: string }) => {
    const data = await createMetaUsingPOST({
      title: meta.title,
      value: meta.value,
      business: meta.business,
      meta: meta.meta,
      creator_id: userModel.user?.id,
    })

    if (data.code !== 1) {
      throw Error(data.message)
    }
  }

  updateMeta = async (meta: { id: number, value: string, title: string }) => {
    const data = await updateMetaUsingPOST(meta)

    if (data.code !== 1) {
      throw Error(data.message)
    }
  }

  deleteMeta = async (id: number) => {
    const data = await deleteMetaUsingPOST(id)

    if (data.code !== 1) {
      throw Error(data.message)
    }
  }

  updateMetaStatus = async (meta: { id: number, status: EffectStatus }) => {
    const data = await updateMetaStatusUsingPOST(meta)

    if (data.code !== 1) {
      throw Error(data.message)
    }
  }
}

export const metaManageModel = new MetaManageModel()
