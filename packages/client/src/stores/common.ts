import { makeAutoObservable } from 'mobx'
import { getUrlParam } from '@/utils'
import { AnyType } from '@/types'

class Common {
  isSelector = !!getUrlParam('isSelector')
  hideScene = !!getUrlParam('hideScene')
  hideCategory = !!getUrlParam('hideCategory')
  hideTag = !!getUrlParam('hideTag')
  previewUrl = ''

  defaultSelectedComponent: Array<{ materialId: number, namespace: string }> = []
  defaultSelectedComLib: AnyType[] = []
  selectedMaterials: Array<{ materialId: number, namespace: string, version: string }> = []

  onMessage = (event) => {
    const { materialModalRefer, defaultSelected, action, selectedMaterials } = event.data

    if (materialModalRefer) {
      if (action === 'default') {
        try {
          const parsedDefaultSelected = JSON.parse(defaultSelected)
          this.defaultSelectedComponent = parsedDefaultSelected.components ?? []
          this.defaultSelectedComLib = parsedDefaultSelected.comLibs ?? []
        }
        catch {
          this.defaultSelectedComponent = []
          this.defaultSelectedComLib = []
        }
      }
      else if (action === 'select') {
        try {
          this.selectedMaterials = JSON.parse(selectedMaterials)
        }
        catch {}
      }
    }
  }

  init = async () => {
    if (this.isSelector) {
      window.parent.postMessage({ materialType: 'init' }, '*')
      window.addEventListener('message', this.onMessage)
    }
  }

  constructor() {
    makeAutoObservable(this)
  }
}

export const commonModel = new Common()
