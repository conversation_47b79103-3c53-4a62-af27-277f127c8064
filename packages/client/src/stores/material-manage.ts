import { makeAutoObservable } from 'mobx'
import { message } from 'antd'
import type { TablePaginationConfig } from 'antd/es/table'
import type { FilterValue } from 'antd/es/table/interface'
import { getUrlParam, parseMaterial } from '@/utils'
import { Material, Scene, Tag } from '@/types'
import { INIT_PAGE_INDEX, INIT_PAGE_SIZE, MaterialType } from '@/constants'
import { userModel } from './user'
import {
  createMaterialInfoUsingPOST,
  createMaterialLibInfoUsingPOST,
  createMaterialTagUsingPOST,
  deleteMaterialInfoUsingPOST,
  updateMaterialInfoUsingPOST,
  updateMaterialTagsByIdUsingPOST,
  updateMaterialTagUsingPOST,
  updateMaterialVersionInfoUsingPOST,
  viewAllMaterialManageListUsingGET,
  viewAllMaterialTagUsingGET,
  queryVersionsByMaterialIdsUsingPOST,
  enableMaterialInfoUsingPOST,
} from '@/services'

export const okCallback = (msg: string) => message.success(msg)
export const errCallback = (err: AnyType) => message.error(err)

interface TableParams {
  pagination?: TablePaginationConfig
  sortField?: string
  sortOrder?: string
  filters?: Record<string, FilterValue>
}

export type CommonModalType = 'scene' | 'tag' | 'material'
export const MaterialPageSize = 10

export const MaterialSelectPageSize = 15
const isCommonMaterial = location.pathname.endsWith('/material')
class MaterialManageModel {
  sceneList: Scene[] = []
  tagList: Tag[] = []
  type: MaterialType | null = (isCommonMaterial && getUrlParam('type')) ? (getUrlParam('type') as MaterialType) : null

  keyword = ''
  pageIndex = INIT_PAGE_INDEX
  readonly pageSize: number = MaterialPageSize
  materialList: Material[] = []
  materialSelectTableData: Material[] = []
  materialSelectTableLoading = false
  materialSelectTableTotal = 0
  materialSelectTableParams: TableParams = {
    pagination: {
      current: 1,
      pageSize: MaterialSelectPageSize,
    },
  }

  materialTableData: Material[] = []
  materialTableParams: TableParams = {
    pagination: {
      current: 1,
      pageSize: MaterialPageSize,
    },
  }

  materialTableTotal = 0
  materialTableLoading = false
  total = 0
  loading = false

  materialSceneLoading = false
  materialTagLoading = false
  sceneSearchParams = {}
  tagSearchParams = {}
  materialSearchParams = {}

  mapMaterialIdToVersions = {}

  /** 有标题就可见 */
  commonModalTitle = undefined
  commonModalType: CommonModalType | undefined = undefined
  commonModalSubmitLoading = false
  commonModalFormInitValues: Record<string, AnyType> = {}
  commonModalCurrentRecord: Record<string, AnyType> = {}

  constructor() {
    makeAutoObservable(this)
  }

  reset = () => {
    this.materialSearchParams = {}
    this.materialTableTotal = 0
    this.materialTableData = []
    this.keyword = ''
    this.materialTableParams = {
      pagination: {
        current: 1,
        pageSize: INIT_PAGE_SIZE,
      },
    }
  }

  init = async () => {
    await this.getCategoryList()
    await this.resetAndGetMaterialTableList()
  }

  refreshMaterialTableList = async () => {
    this.materialTableData = []
    this.materialTableTotal = 0
    this.materialTableLoading = true
    const keyword = (this.materialSearchParams as Record<string, string>).keyword
    const { dataSource = [], pageNum, pageSize, total } = await viewAllMaterialManageListUsingGET({
      ...this.materialSearchParams,
      keyword: keyword?.trim() ?? undefined,
      pageNum: this.materialTableParams.pagination.current,
      pageSize: this.materialTableParams.pagination.pageSize,
    })
    if (this.materialTableParams.pagination.current !== Number(pageNum)) {
      this.materialTableLoading = false
      return
    }

    this.materialTableTotal = total
    this.materialTableLoading = false
    this.materialTableData = dataSource.map(parseMaterial)
    this.materialTableParams.pagination = {
      total: total,
      current: pageNum,
      pageSize: pageSize,
    }
  }

  resetAndGetMaterialTableList = async (params?: Record<string, AnyType>) => {
    if (params) {
      this.materialSearchParams = params
    }
    this.materialTableParams = {
      pagination: {
        current: 1,
        pageSize: this.materialTableParams.pagination.pageSize,
      },
    }
    this.materialTableData = []
    this.materialTableTotal = 0
    this.materialTableLoading = true
    const keyword = (this.materialSearchParams as Record<string, string>).keyword
    const { dataSource = [], pageNum, pageSize, total } = await viewAllMaterialManageListUsingGET({
      ...this.materialSearchParams,
      keyword: keyword?.trim() ?? undefined,
      pageNum: this.materialTableParams.pagination.current,
      pageSize: this.materialTableParams.pagination.pageSize,
    })
    if (this.materialTableParams.pagination.current !== Number(pageNum)) {
      this.materialTableLoading = false
      return
    }

    this.materialTableTotal = total
    this.materialTableLoading = false
    this.materialTableData = dataSource.map(parseMaterial)
    this.materialTableParams.pagination = {
      total: total,
      current: pageNum,
      pageSize: pageSize,
    }
  }

  deleteMaterial = async (id) => {
    deleteMaterialInfoUsingPOST({ id }).then((res) => {
      if (res?.code === 1) {
        okCallback('删除成功')
        this.refreshMaterialTableList()
      }
    })
  }

  enableMaterial = async (id, userId) => {
    enableMaterialInfoUsingPOST({ id, updater_id: userId }).then((res) => {
      if (res?.code === 1) {
        okCallback('启用成功')
        this.refreshMaterialTableList()
      }
    })
  }

  updateCategoryStatus = async (params) => {
    try {
      const res: AnyType = await updateMaterialTagUsingPOST({ param: params })
      okCallback('更新分类成功')
      if (res.code === 1) {
        this.getCategoryList()
      }
      return true
    }
    catch (error: AnyType) {
      errCallback(error?.message || '操作失败')
      return false
    }
  }

  submitMaterialCategoryById = async (params: Record<string, AnyType>) => {
    try {
      const res: AnyType = await updateMaterialTagsByIdUsingPOST({ param: { ...params, creator_id: userModel.user?.id } as AnyType })
      if (res.code === 1) {
        okCallback('更新物料分类成功')
        this.refreshMaterialTableList()
      }
      else {
        throw Error(res.message ?? '更新物料分类失败，请重试')
      }
      return true
    }
    catch (error: AnyType) {
      errCallback(error?.message || '操作失败')
      return false
    }
  }

  getCategoryList = async (params?: Record<string, AnyType>) => {
    if (params) {
      this.tagSearchParams = params
    }
    try {
      this.materialTagLoading = true
      const { dataSource } = await viewAllMaterialTagUsingGET({
        ...this.tagSearchParams,
        pageNum: 1,
        pageSize: 200,
      })
      this.tagList = dataSource as Tag[]
    }
    catch (_) {
      this.tagList = []
    }
    finally {
      this.materialTagLoading = false
    }
  }

  getMaterialTableData = async () => {
    this.materialTableLoading = true
    try {
      const keyword = (this.materialSearchParams as Record<string, string>).keyword
      const { dataSource = [], pageNum, total } = await viewAllMaterialManageListUsingGET({
        type: this.type,
        ...this.materialSearchParams,
        keyword: keyword?.trim() ?? undefined,
        pageNum: this.materialTableParams.pagination.current,
        pageSize: this.materialTableParams.pagination.pageSize,
      })
      if (this.materialTableParams.pagination.current !== pageNum) {
        return
      }
      this.materialTableTotal = total
      this.materialTableData = dataSource.map(parseMaterial)
    }
    finally {
      this.materialTableLoading = false
    }
  }

  getMaterialSelectTableData = async (business) => {
    this.materialSelectTableLoading = true
    try {
      const { dataSource = [], pageNum, total, pageSize } = await viewAllMaterialManageListUsingGET({
        type: MaterialType.COMPONENT,
        business,
        pageNum: this.materialSelectTableParams.pagination.current,
        pageSize: this.materialSelectTableParams.pagination.pageSize,
      })
      if (this.materialSelectTableParams.pagination.current !== pageNum) {
        return
      }
      this.materialSelectTableTotal = total
      this.materialSelectTableData = dataSource.map(parseMaterial)
      this.materialSelectTableParams.pagination = {
        total: total,
        current: pageNum,
        pageSize: pageSize,
      }
    }
    finally {
      this.materialSelectTableLoading = false
    }
  }

  updateSearchParams = async (fieldName: string, value: AnyType, search = true) => {
    if (value !== this[fieldName]) {
      this[fieldName] = value
      if (search) {
        this.resetAndGetMaterialTableList()
      }
    }
  }

  updateTableParams = async (fieldName: string, value: AnyType, search = true) => {
    if (value !== this.materialTableParams[fieldName]) {
      this.materialTableParams[fieldName] = value
      if (search) {
        this.getMaterialTableData()
      }
    }
  }

  updateMaterialSelectTableParams = async (value: AnyType, search = true) => {
    if (value !== this.materialSelectTableParams) {
      this.materialSelectTableParams = value

      if (search) {
        this.getMaterialSelectTableData(value.business)
      }
    }
  }

  submitEditMaterial = async (material, submitCallback) => {
    try {
      await updateMaterialInfoUsingPOST({ param: { ...material, updater_id: userModel.user?.id } })
      okCallback('更新物料成功')
      submitCallback(true)
      return true
    }
    catch (error: AnyType) {
      errCallback(error?.message || '操作失败')
      submitCallback(false)
      return false
    }
  }

  createMaterial = async (material) => {
    const res = await createMaterialInfoUsingPOST({ ...material, creator_id: userModel.user?.id })

    if (res.code !== 1) {
      throw Error(res.message)
    }
  }

  createMaterialLibMaterial = async (material) => {
    const res = await createMaterialLibInfoUsingPOST({ ...material, creator_id: userModel.user?.id })

    if (res.code !== 1) {
      throw Error(res.message)
    }
  }

  resetCommonModal = () => {
    this.commonModalType = undefined
    this.commonModalTitle = undefined
    this.commonModalFormInitValues = undefined
  }

  submitCategory = async (tagParams, type: 'create' | 'edit') => {
    let submitPromise: Promise<Record<string, AnyType>>
    if (type === 'create') {
      submitPromise = createMaterialTagUsingPOST({ param: { ...tagParams, creator_id: userModel.user?.id } })
    }
    else {
      submitPromise = updateMaterialTagUsingPOST({ param: { ...this.commonModalCurrentRecord, ...tagParams } })
    }
    try {
      const res = await submitPromise
      if (res.code === 1) {
        okCallback(`${type === 'create' ? '创建' : '更新'}标签成功`)
        this.resetCommonModal()
        this.getCategoryList()
      }
      return true
    }
    catch (error: AnyType) {
      errCallback(error?.message || '操作失败')
      return false
    }
  }

  updateMaterialVersionStatus = async ({ id, status }, submitCallback: (flag) => void) => {
    try {
      const res: AnyType = await updateMaterialVersionInfoUsingPOST({ param: { id, status } })
      if (res.code === 1) {
        submitCallback?.(true)
      }
    }
    catch (_) {
      submitCallback?.(false)
    }
  }

  closeCommonEditModal = () => {
    this.commonModalTitle = undefined
    this.commonModalType = undefined
  }

  openCommonEditModal = (title: string, editType: CommonModalType, initValues = {}, currentRecord = undefined) => {
    this.commonModalTitle = title
    this.commonModalType = editType
    this.commonModalFormInitValues = initValues
    this.commonModalCurrentRecord = currentRecord
  }

  getMaterialsVersions = async (materialIds: number []) => {
    // 将之前获取过物料版本的物料id过滤
    const originMaterialIds = Object.keys(this.mapMaterialIdToVersions)
    const requiredReqMaterials = materialIds.filter(mId => !originMaterialIds.includes(String(mId)))
    if (requiredReqMaterials.length) {
      try {
        const res = await queryVersionsByMaterialIdsUsingPOST({ materialIds: requiredReqMaterials })
        Object.keys(res).forEach((materialId) => {
          this.mapMaterialIdToVersions[materialId] = res[materialId]
        })
      }
      catch (_) {
        return this.mapMaterialIdToVersions
      }
    }
    return this.mapMaterialIdToVersions
  }
}

export const materialManageModel = new MaterialManageModel()
