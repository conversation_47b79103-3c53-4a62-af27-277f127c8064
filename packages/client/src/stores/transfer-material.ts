import { makeAutoObservable } from 'mobx'
import { Convert, TableParams } from '@/types'
import { INIT_PAGE_INDEX, INIT_PAGE_SIZE } from '@/constants'
import { viewAllConvertListUsingGET } from '@/services'

const transferPageSize = 10
// TODO:请求&属性名称
class TransferModel {
  pageIndex = INIT_PAGE_INDEX
  readonly pageSize: number = INIT_PAGE_SIZE
  transferList: Convert[] = []
  total = 0
  loading = false
  transferTableParams: TableParams = {
    pagination: {
      current: 1,
      pageSize: transferPageSize,
    },
  }

  searchParams = {}

  constructor() {
    makeAutoObservable(this)
  }

  reset = () => {
    this.pageIndex = INIT_PAGE_INDEX
    this.total = 0
    this.transferList = []
    this.searchParams = {}
    this.transferTableParams = {
      pagination: {
        current: 1,
        pageSize: transferPageSize,
      },
    }
  }

  init = async (params?: Record<string, AnyType>) => {
    await this.resetAndGetTransferList(params)
  }

  resetAndGetTransferList = async (params?: Record<string, AnyType>) => {
    if (params) {
      this.searchParams = params
    }
    this.pageIndex = 1
    this.transferList = []
    this.total = 0
    this.loading = true
    this.transferTableParams = {
      pagination: {
        current: 1,
        pageSize: this.transferTableParams.pagination.pageSize,
      },
    }
    try {
      const { dataSource = [], pageNum, total, pageSize } = await viewAllConvertListUsingGET({
        ...this.searchParams,
        pageNum: this.transferTableParams.pagination.current,
        pageSize: this.transferTableParams.pagination.pageSize,
      })
      if (this.transferTableParams.pagination.current !== pageNum) {
        return
      }

      this.total = total
      this.transferList = dataSource as Convert[]
      this.transferTableParams.pagination = {
        total: total,
        current: pageNum,
        pageSize: pageSize,
      }
    }
    finally {
      this.loading = false
    }
  }

  search = () => {
    this.resetAndGetTransferList()
  }

  getTransferTableData = async () => {
    this.loading = true
    // this.transferTableParams.pagination.current = this.transferTableParams.pagination.current + 1;
    const { dataSource = [], pageNum, total, pageSize } = await viewAllConvertListUsingGET({
      ...this.searchParams,
      pageNum: this.transferTableParams.pagination.current,
      pageSize: this.transferTableParams.pagination.pageSize,
    })
    if (this.transferTableParams.pagination.current !== Number(pageNum)) {
      this.loading = false
      return
    }
    this.loading = false
    this.total = total
    this.transferList = dataSource as Convert[]
    this.transferTableParams.pagination = {
      total: total,
      current: pageNum,
      pageSize: pageSize,
    }
  }

  updateSearchParams = async (fieldName: string, value: AnyType, search = true) => {
    if (value !== this[fieldName]) {
      this[fieldName] = value
      if (search) {
        this.resetAndGetTransferList()
      }
    }
  }

  updateTableParams = async (fieldName: string, value: AnyType, search = true) => {
    if (value !== this.transferTableParams[fieldName]) {
      this.transferTableParams[fieldName] = value
      if (search) {
        this.getTransferTableData()
      }
    }
  }
}

export const transferModel = new TransferModel()
