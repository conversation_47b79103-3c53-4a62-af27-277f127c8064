import { makeAutoObservable } from 'mobx'
import { Meta } from '@/types'
import {
  createMetaValueUsingPOST,
  updateMetaValueStatusUsingPOST,
  updateMetaValueUsingPOST,
  viewAllMetaValueManageListUsingGET,
} from '@/services'
import { userModel } from '@/stores/user'
import { EffectStatus } from '@/constants'

export const PageSize = 1000
class MetaValueManageModel {
  metaValue = {
    pagination: {
      total: 0,
      current: 1,
      pageSize: PageSize,
    },
    loading: false,
    searchParams: {
      title: '',
      status: undefined,
    },
    dataSource: [] as Meta[],
  }

  metaId: number

  constructor() {
    makeAutoObservable(this)
  }

  reset = () => {
    this.metaValue = {
      pagination: {
        total: 0,
        current: 1,
        pageSize: PageSize,
      },
      loading: false,
      searchParams: {
        title: '',
        status: undefined,
      },
      dataSource: [] as Meta[],
    }
  }

  getMetaValues = async () => {
    this.metaValue.dataSource = []
    this.metaValue.pagination.total = 0
    this.metaValue.loading = true

    const { dataSource = [], pageNum, pageSize, total } = await viewAllMetaValueManageListUsingGET({
      title: this.metaValue.searchParams.title,
      status: this.metaValue.searchParams.status,
      meta_id: this.metaId,
      pageNum: this.metaValue.pagination.current,
      pageSize: this.metaValue.pagination.pageSize,
    })
    if (this.metaValue.pagination.current !== Number(pageNum)) {
      this.metaValue.loading = false
      return
    }

    this.metaValue.loading = false
    this.metaValue.dataSource = dataSource as Meta[]
    this.metaValue.pagination = {
      total,
      current: pageNum,
      pageSize: pageSize,
    }
  }

  createMetaValue = async (meta: { title: string, value: string, order: number }) => {
    const data = await createMetaValueUsingPOST({
      title: meta.title,
      value: meta.value,
      order: meta.order,
      meta_id: this.metaId,
      creator_id: userModel.user?.id,
    })

    if (data.code !== 1) {
      throw Error(data.message)
    }
  }

  updateMetaValue = async (meta: { id: number, value: string, title: string, order: number }) => {
    const data = await updateMetaValueUsingPOST(meta)

    if (data.code !== 1) {
      throw Error(data.message)
    }
  }

  updateMetaValueStatus = async (meta: { id: number, status: EffectStatus }) => {
    const data = await updateMetaValueStatusUsingPOST(meta)

    if (data.code !== 1) {
      throw Error(data.message)
    }
  }
}

export const metaValueManageModel = new MetaValueManageModel()
