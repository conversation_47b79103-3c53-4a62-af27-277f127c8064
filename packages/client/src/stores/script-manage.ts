import { makeAutoObservable } from 'mobx'
import { message } from 'antd'
import { AnyType, ScriptItem, TableParams } from '@/types'
import { ConvertStatus, INIT_PAGE_INDEX, INIT_PAGE_SIZE } from '@/constants'
import {
  createScriptUsingPOST,
  deleteScriptUsingPOST,
  queryConvertStatusUsingGET,
  updateScriptInfoUsingPOST,
  viewAllScriptListUsingGET,
} from '@/services'
import { userModel } from './user'

export const okCallback = (msg: string) => message.success(msg)

export const errCallback = (err: AnyType) => message.error(err)

export type RunningStatus = 'success' | 'failed' | 'running' | 'not-started'
class ScriptModel {
  keyword = ''
  pageIndex = INIT_PAGE_INDEX
  readonly pageSize: number = INIT_PAGE_SIZE
  scriptList: ScriptItem[] = []
  total = 0
  loading = false
  /** 分页展示，数据和分页参数 */
  // materialTableData: Material[] = [];
  scriptTableParams: TableParams = {
    pagination: {
      current: 1,
      pageSize: 20,
    },
  }

  scriptRunningStatus: RunningStatus = 'not-started'
  scriptModalLoading = false
  transfer_id = undefined
  scriptSearchParams = {}
  constructor() {
    makeAutoObservable(this)
  }

  reset = () => {
    this.pageIndex = INIT_PAGE_INDEX
    this.total = 0
    this.scriptList = []
    this.keyword = ''
    this.scriptSearchParams = {}

    this.scriptTableParams = {
      pagination: {
        current: 1,
        pageSize: INIT_PAGE_SIZE,
      },
    }
  }

  init = async () => {
    await this.resetAndGetScriptList()
  }

  resetAndGetScriptList = async () => {
    this.pageIndex = 1
    this.scriptList = []
    this.total = 0
    this.loading = true
    this.scriptTableParams = {
      pagination: {
        current: 1,
        pageSize: this.scriptTableParams.pagination.pageSize,
      },
    }
    try {
      const { dataSource = [], pageNum, total } = await viewAllScriptListUsingGET({
        ...this.scriptSearchParams,
        pageNum: this.scriptTableParams.pagination.current,
        pageSize: this.scriptTableParams.pagination.pageSize,
      })
      if (this.scriptTableParams.pagination.current === pageNum) {
        this.total = total
        this.scriptList = dataSource as ScriptItem[]
      }
    }
    finally {
      this.loading = false
    }
  }

  search = () => {
    this.resetAndGetScriptList()
  }

  deleteScript = async (id) => {
    deleteScriptUsingPOST({ id })
      .then((res: AnyType) => {
        if (res.code === 1) {
          okCallback('删除成功')
          this.getScriptTableData()
        }
        else {
          errCallback(res.message || '删除失败')
        }
      })
  }

  getScriptTableData = async (params?: Record<string, AnyType>) => {
    this.loading = true
    if (params) {
      this.scriptSearchParams = params
    }
    const { dataSource = [], pageNum, total } = await viewAllScriptListUsingGET({
      ...this.scriptSearchParams,
      pageSize: 10,
      pageNum: 1,
    })
    if (this.scriptTableParams.pagination.current !== pageNum) {
      this.loading = false
      return
    }
    this.loading = false
    this.total = total
    this.scriptList = dataSource as ScriptItem[]
  }

  updateSearchParams = async (fieldName: string, value: AnyType, search = true) => {
    if (value !== this[fieldName]) {
      this[fieldName] = value
      if (search) {
        this.resetAndGetScriptList()
      }
    }
  }

  updateTableParams = async (fieldName: string, value: AnyType, search = true) => {
    if (value !== this.scriptTableParams[fieldName]) {
      this.scriptTableParams[fieldName] = value
      if (search) {
        this.getScriptTableData()
      }
    }
  }

  setScriptModalLoading = (status: boolean) => {
    this.scriptModalLoading = status
  }

  queryTransferStatus = async ({ transfer_id }) => {
    try {
      const result = await queryConvertStatusUsingGET({ id: transfer_id })
      if (result.result === ConvertStatus.SUCCESS) {
        return true
      }
      else if (result.result === ConvertStatus.FAILED) {
        message.error(result.text)
        return false
      }
      return undefined
    }
    catch (error) {
      console.error(error)
    }
  }

  submitScript = async (script, type: 'create' | 'edit', submitCallback?: (flag) => void) => {
    let submitPromise: Promise<Record<string, AnyType>>
    if (type === 'create') {
      submitPromise = createScriptUsingPOST({ param: { ...script, creator_id: userModel.user?.id } })
    }
    else {
      submitPromise = updateScriptInfoUsingPOST({ param: { ...script } })
    }
    try {
      const res = await submitPromise
      if (res.code === -1) {
        errCallback(res.message)
        return false
      }
      if (res.code === 1) {
        submitCallback(true)
        okCallback(`${type === 'create' ? '创建脚本' : '更新脚本'}成功`)
        this.resetAndGetScriptList()
      }
      return true
    }
    catch (error: AnyType) {
      errCallback(error?.message || '操作失败')
      submitCallback(false)
      return false
    }
  }
}

export const scriptModel = new ScriptModel()
