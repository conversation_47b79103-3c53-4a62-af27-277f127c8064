import { UserInfo } from '@/types/user'
import { removeCookie, setCookie } from '@/utils'
import { COOKIE_LOGIN_USER } from '@/constants'
import { makeAutoObservable } from 'mobx'

class User {
  user: UserInfo = null

  constructor() {
    makeAutoObservable(this)
  }

  setUser(user: UserInfo) {
    this.user = user
  }

  login(user: UserInfo) {
    this.user = user

    setCookie(COOKIE_LOGIN_USER, JSON.stringify(user))
  }

  logout() {
    this.user = null
    removeCookie(COOKIE_LOGIN_USER)
  }
}

export const userModel: User = new User()
