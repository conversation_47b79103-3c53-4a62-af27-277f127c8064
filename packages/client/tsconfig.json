{
  "compilerOptions": {
    "declaration": true,
    "jsx": "react-jsx",
    "module": "esnext",
    "target": "ESNext",
    "moduleResolution": "Node",
    "lib": ["dom", "es6", "dom.iterable", "esnext"],
    "types": ["mybricks-material-backend"],
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowJs": true,
    "sourceMap": true,
    "strict": false,
    "checkJs": false,
    "baseUrl": "./",
    "paths": {
      "@/*": ["./src/*"],
      "@styles/*": ["./src/styles/*"],
    },
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
  },
  "include": [
    "src",
    "./node_modules/@global-material-middleoffice/server-v2/src/**/**.d.ts"
  ],
}
