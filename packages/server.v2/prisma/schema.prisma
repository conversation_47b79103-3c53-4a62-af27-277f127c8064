generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["relationJoins"]
}

datasource db {
  provider     = "mysql"
  relationMode = "prisma"
  url          = "mysql://gifshow_15328_v1_rw:<EMAIL>:15328/gifshow"
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model analysis_code_lines {
  id              Int    @id
  scan_id         Int
  business        String @db.VarChar(32)
  repo_project_id Int
  code_lines      Int?
  create_time     Int?
  status          Int    @default(1)

  @@index([scan_id, business], map: "idx_scanid_bu")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model analysis_material_meta {
  id                 Int     @id
  scan_id            Int
  business           String  @db.VarChar(32)
  project_id         Int
  code_lines         Int     @default(0)
  package            String? @db.VarChar(64)
  file_path          String? @db.VarChar(1024)
  type               String? @db.VarChar(16)
  material_name      String? @db.VarChar(64)
  variable_name      String? @db.VarChar(64)
  third_usage        String? @db.MediumText
  filter_third_usage String  @db.MediumText
  create_time        Int?
  status             Int     @default(1)

  @@index([scan_id, business], map: "idx_scanid_bu")
}

model analysis_project_collection {
  id          Int    @id
  tag         Json   @db.Json
  name        String @default("") @db.VarChar(64)
  projects    Json   @db.Json
  materials   Json   @db.Json
  business    String @db.VarChar(32)
  start_time  Int?
  end_time    Int?
  payload     Json   @db.Json
  viewers     Json   @db.Json
  maintainers Json   @db.Json
  updater     Int?
  update_time Int
  creator     Int?
  create_time Int
  status      Int    @default(1)

  @@map("analysis_project_collection")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model analysis_new_code_lines {
  id                Int     @id
  scan_id           Int
  business          String  @db.VarChar(32)
  repo_project_id   Int
  start_commit_hash String? @db.VarChar(64)
  end_commit_hash   String? @db.VarChar(64)
  new_code_lines    Int?
  create_time       Int?
  status            Int     @default(1)

  @@index([scan_id, business], map: "idx_scanid_bu")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model analysis_new_material_usage {
  id              Int     @id
  start_scan_id   Int
  end_scan_id     Int
  business        String  @db.VarChar(32)
  repo_project_id Int
  package         String? @db.VarChar(64)
  route_path      String? @db.VarChar(256)
  file_path       String? @db.VarChar(1024)
  material_info   String? @db.MediumText
  create_time     Int?
  status          Int     @default(1)

  @@index([end_scan_id, business], map: "idx_scanid_bu")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model analysis_raw_data {
  id              Int     @id
  scan_id         Int
  business        String  @db.VarChar(32)
  repo_project_id Int
  content         String? @db.MediumText
  type            String? @db.VarChar(32)
  create_time     Int?
  status          Int     @default(1)

  @@index([scan_id, business], map: "idx_scanid_bu")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model analysis_repo_meta {
  id          Int     @id
  project_id  Int
  clone_url   String  @db.VarChar(512)
  name        String  @db.VarChar(64)
  business    String? @db.VarChar(32)
  create_time Int?
  status      Int     @default(1)

  @@index([project_id], map: "idx_project_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model analysis_result {
  id          Int     @id
  scan_id     Int
  business    String  @db.VarChar(32)
  type        String? @db.VarChar(64)
  value       Float?  @db.Float
  content     String? @db.MediumText
  create_time Int?
  status      Int     @default(1)

  @@index([scan_id, business], map: "idx_scanid_bu")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model analysis_route_file_usage {
  id                   Int     @id
  scan_id              Int
  business             String  @db.VarChar(32)
  repo_project_id      Int
  package              String? @db.VarChar(64)
  route_path           String? @db.VarChar(256)
  file_path            String? @db.VarChar(1024)
  file_create_time     Int?
  material_info        String? @db.MediumText
  filter_material_info String? @db.MediumText
  create_time          Int?
  status               Int     @default(1)

  @@index([scan_id, business], map: "idx_scanid_bu")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model analysis_scan_info {
  id          Int     @id
  scan_range  String  @db.VarChar(1024)
  type        String? @db.VarChar(32)
  start_time  Int?
  end_time    Int?
  display_txt String? @db.VarChar(128)
  create_time Int?
  status      Int     @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model business_config {
  id          Int     @id
  business    String  @db.VarChar(50)
  meta        String? @db.MediumText
  updater_id  Int?
  update_time Int?
  status      Int     @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model business_project {
  id          Int     @id
  business    String  @db.VarChar(50)
  title       String  @db.VarChar(100)
  git_url     String? @default("") @db.VarChar(256)
  content     String? @db.MediumText
  start_time  Int
  end_time    Int
  type        String? @db.VarChar(50)
  update_time Int?
  status      Int     @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model business_report {
  id           Int    @id
  business     String @db.VarChar(50)
  type         String @db.VarChar(50)
  value        Float  @default(0) @db.Float
  code_type    String @db.VarChar(50)
  relation_key Int?
  start_time   Int
  end_time     Int
  update_time  Int?
  status       Int    @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model lowcode_build_report {
  id          Int    @id
  page_id     Int
  type        String @db.VarChar(50)
  value       Float  @default(0) @db.Float
  update_time Int?
  status      Int    @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model lowcode_page_info {
  id          Int     @id
  business    String  @db.VarChar(50)
  url         String  @db.VarChar(256)
  version     String? @default("") @db.VarChar(50)
  schema      String? @db.MediumText
  content     String? @db.MediumText
  payload     Json?
  create_time Int?
  update_time Int?
  status      Int     @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material {
  id          Int     @id
  namespace   String  @default("") @db.VarChar(255)
  version     String  @default("1.0.0") @db.VarChar(50)
  title       String  @default("") @db.VarChar(100)
  description String? @default("") @db.VarChar(256)
  type        String  @default("") @db.VarChar(100)
  business    String  @default("") @db.VarChar(50)
  domain      String? @default("0") @db.VarChar(255)
  git_url     String? @default("") @db.VarChar(256)
  platform    String? @default("") @db.VarChar(50)
  creator_id  Int?
  create_time Int?
  updater_id  Int?
  update_time Int?
  meta        String? @db.MediumText
  status      Int     @default(1)

  @@index([creator_id], map: "idx_creator_info")
  @@index([namespace], map: "idx_namespace")
  @@index([type], map: "idx_type")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_convert {
  id          Int     @id
  material_id Int
  version     String  @default("1.0.0") @db.VarChar(50)
  script      String? @db.MediumText
  bundle      String? @db.MediumText
  result      Int?    @default(0)
  reason      String? @default("") @db.VarChar(256)
  creator_id  Int?
  create_time Int?
  status      Int     @default(1)
  business    String  @db.VarChar(50)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_convert_script {
  id          Int     @id
  business    String  @db.VarChar(50)
  script      String? @db.MediumText
  creator_id  Int?
  create_time Int?
  status      Int     @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_meta {
  id          Int     @id
  title       String  @db.VarChar(50)
  business    String  @db.VarChar(50)
  creator_id  Int?
  create_time Int?
  status      Int     @default(1)
  value       String  @db.VarChar(50)
  meta        String? @db.MediumText
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_meta_value {
  id          Int    @id
  title       String @db.VarChar(50)
  value       String @db.VarChar(50)
  meta_id     Int
  order       Int?   @default(0)
  creator_id  Int?
  create_time Int?
  status      Int    @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_meta_value_relation {
  id            Int  @id
  meta_value_id Int
  material_id   Int
  creator_id    Int?
  create_time   Int?
  status        Int  @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_pub {
  id          Int     @id
  material_id Int
  version     String  @default("1.0.0") @db.VarChar(50)
  preview_img String  @db.MediumText
  content     String? @db.MediumText
  schema      String? @db.MediumText
  creator_id  Int?
  create_time Int?
  status      Int     @default(1)
  readme      String? @db.MediumText

  @@unique([material_id, version])
  @@index([version], map: "idx_version")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_refer {
  id               Int     @id
  material_id      Int
  version          String  @default("1.0.0") @db.VarChar(50)
  ref_page         String? @default("") @db.VarChar(256)
  ref_page_version String? @default("") @db.VarChar(32)
  ref_business     String? @default("") @db.VarChar(50)
  type             String  @default("refer") @db.VarChar(100)
  refer_count      Int?
  creator_id       Int?
  create_time      Int?
  status           Int     @default(1)
  code_type        String  @db.VarChar(50)
  namespace        String  @default("") @db.VarChar(255)

  @@index([material_id], map: "idx_material_id")
  @@index([ref_page], map: "idx_ref_page")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_scene {
  id          Int    @id
  title       String @db.VarChar(50)
  order       Int?   @default(0)
  creator_id  Int?
  create_time Int?
  status      Int    @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_scene_relation {
  id          Int  @id
  material_id Int
  scene_id    Int
  creator_id  Int?
  create_time Int?
  status      Int  @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_tag {
  id          Int     @id
  title       String  @db.VarChar(50)
  order       Int?    @default(0)
  creator_id  Int?
  create_time Int?
  status      Int     @default(1)
  value       String? @db.VarChar(50)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model material_tag_relation {
  id          Int  @id
  material_id Int
  tag_id      Int
  creator_id  Int?
  create_time Int?
  status      Int  @default(1)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user {
  id          Int    @id
  user_id     String @default("") @db.VarChar(50)
  name        String @default("") @db.VarChar(100)
  user_name   String @db.VarChar(100)
  avatar      String @default("") @db.VarChar(255)
  department  String @default("") @db.VarChar(100)
  email       String @unique(map: "uniq_idx_email") @db.VarChar(100)
  create_time Int?
  status      Int    @default(1)

  @@index([user_id], map: "idx_user_id")
  @@index([user_name, name], map: "idx_user_info")
}

model shared {
  id          Int    @id @db.UnsignedMediumInt
  key         String @db.VarChar(100)
  value       Json?
  payload     Json?
  create_time Int?
  status      Int    @default(1)
}

/// 物料积分（源码物料为成熟度，低代码物料为引用次数）
model material_integral {
  id            Int    @id @default(autoincrement())
  material_id   Int
  integral_type String @db.VarChar(32)
  integral      Int
  create_time   Int
  update_time   Int
  status        Int    @default(1)

  @@map("material_integral")
}
