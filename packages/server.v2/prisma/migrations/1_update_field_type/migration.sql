-- AlterTable
ALTER TABLE `analysis_code_lines` DROP PRIMARY KEY,
    <PERSON><PERSON><PERSON>Y `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `scan_id` INTEGER NOT NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    <PERSON><PERSON><PERSON><PERSON> `create_time` INTEGER NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `analysis_material_meta` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `scan_id` INTEGER NOT NULL,
    <PERSON><PERSON><PERSON>Y `project_id` INTEGER NOT NULL,
    M<PERSON>IFY `code_lines` INTEGER NOT NULL DEFAULT 0,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    M<PERSON><PERSON>Y `create_time` INTEGER NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `analysis_new_code_lines` DROP PRIMARY KEY,
    <PERSON><PERSON><PERSON>Y `id` INTEGER NOT NULL AUTO_INCREMENT,
    <PERSON><PERSON><PERSON>Y `scan_id` INTEGER NOT NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    MODIFY `create_time` INTEGER NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `analysis_new_material_usage` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `start_scan_id` INTEGER NOT NULL,
    MODIFY `end_scan_id` INTEGER NOT NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    MODIFY `create_time` INTEGER NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `analysis_raw_data` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `scan_id` INTEGER NOT NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    MODIFY `create_time` INTEGER NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `analysis_repo_meta` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    MODIFY `create_time` INTEGER NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `analysis_result` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `scan_id` INTEGER NOT NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    MODIFY `create_time` INTEGER NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `analysis_route_file_usage` DROP PRIMARY KEY,
    DROP COLUMN `filter_and_merged_material_info`,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `scan_id` INTEGER NOT NULL,
    MODIFY `file_create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    MODIFY `create_time` INTEGER NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `analysis_scan_info` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `start_time` INTEGER NULL,
    MODIFY `end_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    MODIFY `create_time` INTEGER NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `business_config` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `updater_id` INTEGER NULL,
    MODIFY `update_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `business_project` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `start_time` INTEGER NOT NULL,
    MODIFY `end_time` INTEGER NOT NULL,
    MODIFY `update_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `business_report` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `relation_key` INTEGER NULL,
    MODIFY `start_time` INTEGER NOT NULL,
    MODIFY `end_time` INTEGER NOT NULL,
    MODIFY `update_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `lowcode_build_report` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `page_id` INTEGER NOT NULL,
    MODIFY `update_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `lowcode_page_info` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `update_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `updater_id` INTEGER NULL,
    MODIFY `update_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_convert` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `material_id` INTEGER NOT NULL,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_convert_script` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_meta` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_meta_value` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `meta_id` INTEGER NOT NULL,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_meta_value_relation` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `meta_value_id` INTEGER NOT NULL,
    MODIFY `material_id` INTEGER NOT NULL,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_pub` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `material_id` INTEGER NOT NULL,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_refer` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `material_id` INTEGER NOT NULL,
    MODIFY `refer_count` INTEGER NULL,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_scene` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_scene_relation` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `material_id` INTEGER NOT NULL,
    MODIFY `scene_id` INTEGER NOT NULL,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_tag` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `material_tag_relation` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `material_id` INTEGER NOT NULL,
    MODIFY `tag_id` INTEGER NOT NULL,
    MODIFY `creator_id` INTEGER NULL,
    MODIFY `create_time` INTEGER NULL,
    MODIFY `status` INTEGER NOT NULL DEFAULT 1,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `shared` DROP PRIMARY KEY,
    MODIFY `id` MEDIUMINT UNSIGNED NOT NULL AUTO_INCREMENT,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `user` DROP PRIMARY KEY,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    MODIFY `create_time` INTEGER NULL,
    ADD PRIMARY KEY (`id`);

-- CreateIndex
CREATE UNIQUE INDEX `material_pub_material_id_version_key` ON `material_pub`(`material_id`, `version`);

