-- CreateTable
CREATE TABLE `analysis_code_lines` (
    `id` BIGINT NOT NULL,
    `scan_id` VARCHAR(32) NOT NULL,
    `business` VARCHAR(32) NOT NULL,
    `repo_project_id` INTEGER NOT NULL,
    `code_lines` INTEGER NULL,
    `status` INTEGER NULL,
    `create_time` BIGINT NULL,

    INDEX `idx_scanid_bu`(`scan_id`, `business`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `analysis_material_meta` (
    `id` BIGINT NOT NULL,
    `scan_id` VARCHAR(32) NOT NULL,
    `business` VARCHAR(32) NOT NULL,
    `project_id` VARCHAR(512) NULL,
    `package` VARCHAR(64) NULL,
    `code_lines` INTEGER NULL,
    `file_path` VARCHAR(1024) NULL,
    `status` INTEGER NULL,
    `create_time` BIGINT NULL,
    `type` VARCHAR(16) NULL,
    `material_name` VARCHAR(64) NULL,
    `variable_name` VARCHAR(64) NULL,
    `third_usage` MEDIUMTEXT NULL,
    `filter_third_usage` MEDIUMTEXT NOT NULL,

    INDEX `idx_scanid_bu`(`scan_id`, `business`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `analysis_new_code_lines` (
    `id` BIGINT NOT NULL,
    `scan_id` VARCHAR(32) NOT NULL,
    `business` VARCHAR(32) NOT NULL,
    `repo_project_id` INTEGER NOT NULL,
    `new_code_lines` INTEGER NULL,
    `start_commit_hash` VARCHAR(64) NULL,
    `end_commit_hash` VARCHAR(64) NULL,
    `status` INTEGER NULL,
    `create_time` BIGINT NULL,

    INDEX `idx_scanid_bu`(`scan_id`, `business`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `analysis_new_material_usage` (
    `id` BIGINT NOT NULL,
    `start_scan_id` VARCHAR(32) NOT NULL,
    `end_scan_id` VARCHAR(32) NOT NULL,
    `business` VARCHAR(32) NOT NULL,
    `repo_project_id` INTEGER NOT NULL,
    `package` VARCHAR(64) NULL,
    `route_path` VARCHAR(256) NULL,
    `file_path` VARCHAR(1024) NULL,
    `material_info` MEDIUMTEXT NULL,
    `status` INTEGER NULL,
    `create_time` BIGINT NULL,

    INDEX `idx_scanid_bu`(`end_scan_id`, `business`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `analysis_raw_data` (
    `id` BIGINT NOT NULL,
    `scan_id` VARCHAR(32) NOT NULL,
    `business` VARCHAR(32) NOT NULL,
    `repo_project_id` INTEGER NOT NULL,
    `content` MEDIUMTEXT NULL,
    `type` VARCHAR(32) NULL,
    `status` INTEGER NULL,
    `create_time` BIGINT NULL,

    INDEX `idx_scanid_bu`(`scan_id`, `business`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `analysis_repo_meta` (
    `id` BIGINT NOT NULL,
    `clone_url` VARCHAR(512) NOT NULL,
    `project_id` INTEGER NOT NULL,
    `name` VARCHAR(64) NOT NULL,
    `status` INTEGER NULL,
    `create_time` BIGINT NULL,
    `business` VARCHAR(32) NULL,

    INDEX `idx_project_id`(`project_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `analysis_result` (
    `id` BIGINT NOT NULL,
    `scan_id` VARCHAR(32) NOT NULL,
    `business` VARCHAR(32) NOT NULL,
    `type` VARCHAR(64) NULL,
    `value` FLOAT NULL,
    `content` MEDIUMTEXT NULL,
    `status` INTEGER NULL,
    `create_time` BIGINT NULL,

    INDEX `idx_scanid_bu`(`scan_id`, `business`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `analysis_route_file_usage` (
    `id` BIGINT NOT NULL,
    `scan_id` VARCHAR(32) NOT NULL,
    `business` VARCHAR(32) NOT NULL,
    `repo_project_id` INTEGER NOT NULL,
    `package` VARCHAR(64) NULL,
    `route_path` VARCHAR(256) NULL,
    `file_path` VARCHAR(1024) NULL,
    `file_create_time` BIGINT NULL,
    `material_info` MEDIUMTEXT NULL,
    `status` INTEGER NULL,
    `create_time` BIGINT NULL,
    `filter_material_info` MEDIUMTEXT NULL,
    `filter_and_merged_material_info` MEDIUMTEXT NULL,

    INDEX `idx_scanid_bu`(`scan_id`, `business`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `analysis_scan_info` (
    `id` BIGINT NOT NULL,
    `scan_range` VARCHAR(1024) NOT NULL,
    `type` VARCHAR(32) NULL,
    `start_time` BIGINT NULL,
    `end_time` BIGINT NULL,
    `status` INTEGER NULL,
    `create_time` BIGINT NULL,
    `display_txt` VARCHAR(128) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `business_config` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `business` VARCHAR(50) NOT NULL,
    `meta` MEDIUMTEXT NULL,
    `updater_id` BIGINT UNSIGNED NULL,
    `update_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `business_project` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `business` VARCHAR(50) NOT NULL,
    `title` VARCHAR(100) NOT NULL,
    `git_url` VARCHAR(256) NULL DEFAULT '',
    `content` MEDIUMTEXT NULL,
    `start_time` BIGINT NOT NULL,
    `end_time` BIGINT NOT NULL,
    `update_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,
    `type` VARCHAR(50) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `business_report` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `business` VARCHAR(50) NOT NULL,
    `type` VARCHAR(50) NOT NULL,
    `value` FLOAT NOT NULL DEFAULT 0,
    `code_type` VARCHAR(50) NOT NULL,
    `relation_key` BIGINT UNSIGNED NULL,
    `start_time` BIGINT NOT NULL,
    `end_time` BIGINT NOT NULL,
    `update_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `lowcode_build_report` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `page_id` BIGINT UNSIGNED NOT NULL,
    `type` VARCHAR(50) NOT NULL,
    `value` FLOAT NOT NULL DEFAULT 0,
    `update_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `lowcode_page_info` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `business` VARCHAR(50) NOT NULL,
    `url` VARCHAR(256) NOT NULL,
    `version` VARCHAR(50) NULL DEFAULT '',
    `schema` MEDIUMTEXT NULL,
    `content` MEDIUMTEXT NULL,
    `update_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `namespace` VARCHAR(255) NOT NULL DEFAULT '',
    `version` VARCHAR(50) NOT NULL DEFAULT '1.0.0',
    `title` VARCHAR(100) NOT NULL DEFAULT '',
    `description` VARCHAR(256) NULL DEFAULT '',
    `type` VARCHAR(100) NOT NULL DEFAULT '',
    `business` VARCHAR(50) NOT NULL DEFAULT '',
    `git_url` VARCHAR(256) NULL DEFAULT '',
    `platform` VARCHAR(50) NULL DEFAULT '',
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `updater_id` BIGINT UNSIGNED NULL,
    `update_time` BIGINT NOT NULL,
    `meta` MEDIUMTEXT NULL,
    `status` INTEGER NULL DEFAULT 1,

    INDEX `idx_creator_info`(`creator_id`),
    INDEX `idx_namespace`(`namespace`),
    INDEX `idx_type`(`type`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_convert` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `material_id` BIGINT UNSIGNED NOT NULL,
    `version` VARCHAR(50) NOT NULL DEFAULT '1.0.0',
    `script` MEDIUMTEXT NULL,
    `bundle` MEDIUMTEXT NULL,
    `result` INTEGER NULL DEFAULT 0,
    `reason` VARCHAR(256) NULL DEFAULT '',
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,
    `business` VARCHAR(50) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_convert_script` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `business` VARCHAR(50) NOT NULL,
    `script` MEDIUMTEXT NULL,
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_meta` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(50) NOT NULL,
    `business` VARCHAR(50) NOT NULL,
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,
    `value` VARCHAR(50) NOT NULL,
    `meta` MEDIUMTEXT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_meta_value` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(50) NOT NULL,
    `value` VARCHAR(50) NOT NULL,
    `meta_id` BIGINT NOT NULL,
    `order` INTEGER NULL DEFAULT 0,
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_meta_value_relation` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `meta_value_id` BIGINT NOT NULL,
    `material_id` BIGINT NOT NULL,
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_pub` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `material_id` BIGINT NOT NULL,
    `version` VARCHAR(50) NOT NULL DEFAULT '1.0.0',
    `preview_img` MEDIUMTEXT NOT NULL,
    `content` MEDIUMTEXT NULL,
    `schema` MEDIUMTEXT NULL,
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,
    `readme` MEDIUMTEXT NULL,

    INDEX `idx_version`(`version`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_refer` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `material_id` BIGINT UNSIGNED NOT NULL,
    `version` VARCHAR(50) NOT NULL DEFAULT '1.0.0',
    `ref_page` VARCHAR(256) NULL DEFAULT '',
    `ref_business` VARCHAR(50) NULL DEFAULT '',
    `type` VARCHAR(100) NOT NULL DEFAULT 'refer',
    `refer_count` BIGINT NULL,
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,
    `code_type` VARCHAR(50) NOT NULL,
    `namespace` VARCHAR(255) NOT NULL DEFAULT '',

    INDEX `idx_material_id`(`material_id`),
    INDEX `idx_ref_page`(`ref_page`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_scene` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(50) NOT NULL,
    `order` INTEGER NULL DEFAULT 0,
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_scene_relation` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `material_id` BIGINT NOT NULL,
    `scene_id` BIGINT NOT NULL,
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_tag` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(50) NOT NULL,
    `order` INTEGER NULL DEFAULT 0,
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,
    `value` VARCHAR(50) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `material_tag_relation` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `material_id` BIGINT NOT NULL,
    `tag_id` BIGINT NOT NULL,
    `creator_id` BIGINT UNSIGNED NULL,
    `create_time` BIGINT NOT NULL,
    `status` INTEGER NULL DEFAULT 1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` VARCHAR(50) NOT NULL DEFAULT '',
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `user_name` VARCHAR(100) NOT NULL,
    `avatar` VARCHAR(255) NOT NULL DEFAULT '',
    `department` VARCHAR(100) NOT NULL DEFAULT '',
    `email` VARCHAR(100) NOT NULL,
    `status` INTEGER NOT NULL DEFAULT 1,
    `create_time` BIGINT NOT NULL,

    UNIQUE INDEX `uniq_idx_email`(`email`),
    INDEX `idx_user_id`(`user_id`),
    INDEX `idx_user_info`(`user_name`, `name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

