{"name": "@global-material-middleoffice/server-v2", "private": true, "exports": {"./shared": {"types": "./src/shared.ts", "import": "./src/shared.ts", "require": "./src/shared.ts"}}, "scripts": {"dev": "pnpm run start:dev", "build": "nest build", "start": "nest start", "start:dev": "cross-env NODE_ENV=development nest start --debug --watch", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "node --max-old-space-size=8192 dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "db:pull": "prisma db pull", "db:generate": "prisma generate", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "postinstall": "pnpm run db:generate"}, "dependencies": {"@gitbeaker/rest": "^40.4.0", "@infra-node/kconf": "^1.1.17", "@kael/complexity": "^1.0.40", "@kael/schema-utils": "^1.0.40", "@ks-material-middleoffice/measure-sdk": "0.8.0-beta.4", "@nestjs/common": "^10.4.4", "@nestjs/core": "^10.4.4", "@nestjs/platform-express": "^10.4.4", "@nestjs/schedule": "^4.1.1", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.4.2", "@prisma/client": "^5.20.0", "axios": "^0.28.1", "body-parser": "^1.20.3", "chalk": "^4.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cron": "^3.1.7", "dayjs": "^1.11.13", "form-data": "^4.0.0", "fs-extra": "^11.2.0", "gen-uniqueid": "^0.0.2", "git-url-parse": "^14.1.0", "lodash": "^4.17.21", "nest-winston": "^1.9.7", "number-precision": "^1.6.0", "p-limit": "^3.1.0", "proper-lockfile": "^4.1.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "semver": "^7.6.2", "simple-git": "^3.27.0", "winston": "^3.15.0", "winston-daily-rotate-file": "^5.0.0", "winston-transport": "^4.8.0", "workerpool": "^9.1.3", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.4.4", "@stylistic/eslint-plugin": "^2.10.1", "@swc/cli": "^0.4.0", "@swc/core": "^1.7.26", "@types/body-parser": "^1.19.5", "@types/cookie-parser": "^1.4.7", "@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/git-url-parse": "^9.0.3", "@types/jest": "^29.5.13", "@types/lodash": "^4.17.10", "@types/node": "^20.16.11", "@types/proper-lockfile": "^4.1.4", "@types/semver": "^7.5.8", "@types/supertest": "^6.0.2", "cross-env": "^7.0.3", "eslint": "^9.14.0", "globals": "^15.11.0", "jest": "^29.7.0", "nodemon": "^3.1.7", "pm2": "^5.4.2", "prisma": "^5.20.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "10.9.1", "tsconfig-paths": "^4.2.0", "type-fest": "^4.26.1", "typescript": "^5.6.3", "typescript-eslint": "^8.11.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}