{"compilerOptions": {"strict": true, "module": "commonjs", "declarationMap": false, "declaration": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2018", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "esModuleInterop": true, "allowJs": true, "paths": {"@/*": ["./src/*"]}}}