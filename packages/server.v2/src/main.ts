import { NestFactory } from '@nestjs/core'
import { ValidationPipe } from '@nestjs/common'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import {
  closeSync,
  existsSync,
  openSync,
  readFileSync,
  writeFileSync,
} from 'fs'
import { WinstonModule } from 'nest-winston'
import cookieParser from 'cookie-parser'
import { JsonObject } from 'type-fest'
import compression from 'compression'
import bodyParser from 'body-parser'
import { join } from 'path'
import { lock } from 'proper-lockfile'

import { AppModule } from './app.module'

import { BadRequestExceptionFilter } from './filters/bad-request-exception/bad-request-exception.filter'
import { HttpExceptionFilter } from './filters/http-exception/http-exception.filter'
import { AssertionErrorFilter } from './filters/assertion-error/assertion-error.filter'
import { PrismaErrorFilter } from './filters/prisma-errors/prisma-errors.filter'
import { NormalErrorFilter } from './filters/normal-error/normal-error.filter'
import { StandardJSONResponseWrapperInterceptor } from './interceptors/transform/transform.interceptor'

import { loggerInstance } from './tools/winston'
import { PrismaClientInstance } from './tools/prisma'
import { CURRENT_BUSINESS } from './constants/envs'
import { PROJECT_ROOT } from './constants/path'

export let CURRENT_SERVICE_PORT: number
export const isMainService = (): boolean =>
  CURRENT_SERVICE_PORT === +process.env?.AUTO_PORT0

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create(AppModule, {
    logger: WinstonModule.createLogger({
      instance: loggerInstance,
    }),
  })

  app.use(cookieParser())
  app.use(compression())
  app.use(bodyParser.json({ limit: '50mb' }))
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }))

  // ======================== pipe ========================
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  )

  // ======================== filters ========================
  app.useGlobalFilters(
    new NormalErrorFilter(),
    new PrismaErrorFilter(),
    new AssertionErrorFilter(),
    new HttpExceptionFilter(),
    new BadRequestExceptionFilter(),
  )

  // ======================== interceptors ========================
  app.useGlobalInterceptors(new StandardJSONResponseWrapperInterceptor())

  // ======================== cors ========================
  app.enableCors({
    origin: true,
    allowedHeaders:
      'X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Observe',
    methods: 'GET,PUT,POST,DELETE,UPDATE,PATCH,OPTIONS',
    credentials: true,
  })

  // ======================== global prefix ========================
  app.setGlobalPrefix('api/v2')

  // ======================== swagger docs ========================
  const config = new DocumentBuilder()
    .setTitle('v2 APIs')
    .setDescription('v2 APIs')
    .setVersion('2.0')
    .build()

  const document = SwaggerModule.createDocument(app, config)
  SwaggerModule.setup('/api/v2/docs', app, document)

  let port
    = +process.env?.[process.env.port_name]
      || +process.env?.AUTO_PORT0 + 1
      || 3000

  let tik = 0
  while (tik < 10) {
    try {
      await app.listen(port)
      break
    }
    catch (error) {
      // @ts-expect-error 约定 error.code 为 EADDRINUSE 时端口被占用
      if (error.code === 'EADDRINUSE') {
        loggerInstance.warn(
          `Port ${port} is can not use. Trying the next port...`,
        )
      }
      else {
        loggerInstance.error(error)
        throw error
      }
      tik += 1
      port += 1
    }
  }
  if (tik >= 10) {
    throw new Error('Ten ports are already in use.')
  }

  CURRENT_SERVICE_PORT = port

  console.log(`Application is running on: ${await app.getUrl()}`)

  // ======================== 记录服务运行的端口 ========================
  const recordedFilePath = join(PROJECT_ROOT, './ports.json')
  let prePortsMapper: JsonObject = {}
  // 检查文件是否存在，如果不存在则创建一个空文件
  if (!existsSync(recordedFilePath)) {
    const fd = openSync(recordedFilePath, 'w') // 创建空文件
    closeSync(fd) // 关闭文件描述符
  }
  try {
    const release = await lock(recordedFilePath, {
      retries: {
        retries: 10,
        factor: 1.5,
        minTimeout: 100,
        maxTimeout: 5000,
      },
    })
    if (existsSync(recordedFilePath)) {
      prePortsMapper = JSON.parse(
        readFileSync(recordedFilePath, 'utf8') || '{}',
      )
    }
    prePortsMapper[CURRENT_BUSINESS] = port
    writeFileSync(
      recordedFilePath,
      JSON.stringify(prePortsMapper, null, 2),
      'utf8',
    )
    await release()
  }
  catch (error) {
    loggerInstance.error('文件操作失败或锁定失败', error)
  }
}

bootstrap().catch(async (error) => {
  console.error('Error during bootstrap:', error)
  await PrismaClientInstance.$disconnect()
  process.exit(1)
})

process.on('SIGINT', async () => {
  await PrismaClientInstance.$disconnect()
  process.exit(0)
})
process.on('SIGTERM', async () => {
  await PrismaClientInstance.$disconnect()
  process.exit(0)
})
