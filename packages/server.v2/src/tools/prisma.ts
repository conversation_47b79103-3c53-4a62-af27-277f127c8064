import { IS_CORP } from '@/constants/envs'
import { PrismaClient } from '@prisma/client'
import * as GenUniqueId from 'gen-uniqueid'

import { loggerInstance } from './winston'
import { decodeFromBase64 } from './base64'
import { logStringify } from './json'
import { JsonObject } from 'type-fest'

const DATABASE_CONFIG = IS_CORP
  ? {
    dbType: 'mysql',
    database: 'gifshow',
    host: decode<PERSON><PERSON><PERSON><PERSON><PERSON>('Y2x1c3RlcjAxLnByb3h5c3FsLmtzcWwuaW50ZXJuYWw='),
    user: decode<PERSON><PERSON><PERSON><PERSON><PERSON>('Z2lmc2hvd18xMzAwMF92MV9ydw=='),
    password: decode<PERSON><PERSON><PERSON>ase64(
      'S3YxOXZMckhoOGNRMFNvZDZtWUdwSUZDdDNKd2t1S3o=',
    ),
    port: decodeF<PERSON><PERSON>ase64('NjAzMg=='),
  }
  : {
    dbType: 'mysql',
    database: 'gifshow',
    host: decode<PERSON><PERSON><PERSON><PERSON><PERSON>(
      'cHVibGljLXhtLWQtY2RzLXN0YWdpbmctbm9kZTI4LmlkY2hiMWF6MS5oYjEua3dhaWRjLmNvbQ',
    ),
    user: decodeFromBase64('Z2lmc2hvd18xNTMyOF92MV9ydw==`'),
    password: decodeFromBase64(
      'S3YxclUxaW1kcGdHZVpTQWhUTUxRenNWeWpheDV2WW8=',
    ),
    port: decodeFromBase64('MTUzMjg='),
  }

export const PrismaClientInstance = new PrismaClient({
  datasourceUrl: `${DATABASE_CONFIG.dbType}://${DATABASE_CONFIG.user}:${DATABASE_CONFIG.password}@${DATABASE_CONFIG.host}:${DATABASE_CONFIG.port}/${DATABASE_CONFIG.database}`,
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'event',
      level: 'info',
    },
    {
      emit: 'event',
      level: 'warn',
    },
    {
      emit: 'event',
      level: 'error',
    },
  ],
})

// PrismaClientInstance.$on('query', (e) => {
//   loggerInstance.info(logStringify(e as unknown as JsonObject))
// })

PrismaClientInstance.$on('info', (e) => {
  loggerInstance.info(logStringify(e as unknown as JsonObject))
})

PrismaClientInstance.$on('warn', (e) => {
  loggerInstance.warn(logStringify(e as unknown as JsonObject))
})

PrismaClientInstance.$on('error', (e) => {
  loggerInstance.error(logStringify(e as unknown as JsonObject))
})

const SNOW_FLAKE = new GenUniqueId.SnowFlake({
  workerId: process.env.WorkerId == undefined ? 1 : process.env.WorkerId,
})

export function genPrimaryIndex(): number {
  return SNOW_FLAKE.NextId()
}
