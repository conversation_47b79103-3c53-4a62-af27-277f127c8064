import {
  LibraryMaterialUsage,
  MaterialUsage,
  UsedLibraryMaterial,
} from '@ks-material-middleoffice/measure-sdk'

export function mergeLibraryMaterialUsageWithoutClone(
  A: LibraryMaterialUsage,
  B: LibraryMaterialUsage,
): LibraryMaterialUsage {
  for (const [packageName, materialUsage] of Object.entries(B)) {
    if (A[packageName]) {
      A[packageName] = mergeMaterialUsage(A[packageName], materialUsage)
    }
    else {
      A[packageName] = Object.assign({}, materialUsage)
    }
  }
  return A
}

export function mergeMaterialUsage(
  A: MaterialUsage,
  B: MaterialUsage,
): LibraryMaterialUsage[keyof LibraryMaterialUsage] {
  const result = Object.assign({}, A)
  for (const [materialName, materialUsage] of Object.entries(B)) {
    if (result[materialName]) {
      result[materialName] += materialUsage
    }
    else {
      result[materialName] = materialUsage
    }
  }
  return result
}

export function eraseLibraryMaterialUsageWithoutClone(
  A: LibraryMaterialUsage,
  B: LibraryMaterialUsage,
): LibraryMaterialUsage {
  for (const [packageName, materialUsage] of Object.entries(B)) {
    if (A[packageName]) {
      A[packageName] = eraseMaterialUsage(A[packageName], materialUsage)
    }
  }
  return A
}

export function eraseMaterialUsage(
  A: MaterialUsage,
  B: MaterialUsage,
): LibraryMaterialUsage[keyof LibraryMaterialUsage] {
  const result = Object.assign({}, A)
  for (const [materialName, materialUsage] of Object.entries(B)) {
    if (result[materialName]) {
      result[materialName] -= materialUsage
      if (result[materialName] < 0) {
        result[materialName] = 0
      }
    }
  }
  return result
}

export function multiplyLibraryMaterialUsage(
  source: LibraryMaterialUsage,
  number: number,
): LibraryMaterialUsage {
  const result = Object.assign({}, source)
  for (const [packageName, materialUsage] of Object.entries(result)) {
    result[packageName] = multiplyMaterialUsage(materialUsage, number)
  }
  return result
}

export function multiplyMaterialUsage(
  source: MaterialUsage,
  number: number,
): MaterialUsage {
  const result = Object.assign({}, source)
  for (const [materialName, materialUsage] of Object.entries(result)) {
    result[materialName] = materialUsage * number
  }
  return result
}

export function ignoreLibraryMaterialUsageWithoutClone(
  source: LibraryMaterialUsage,
  libraryMaterial: UsedLibraryMaterial,
): LibraryMaterialUsage {
  for (const [packageName, materials] of Object.entries(libraryMaterial)) {
    for (const material of materials) {
      if (source[packageName] && source[packageName][material]) {
        delete source[packageName][material]
      }
    }
  }
  return source
}
