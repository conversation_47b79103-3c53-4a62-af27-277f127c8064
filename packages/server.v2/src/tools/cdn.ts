import axios from 'axios'
import { join } from 'path'
import assert from 'assert'
import FormData from 'form-data'
import { JsonObject } from 'type-fest'

import { IS_DEV, IS_PROD } from '@/constants/envs'
import { decodeFromBase64 } from './base64'

export interface CDNParams {
  dir: string
  filename: string
  content: string | Buffer
  customCDNRequestParams?: JsonObject
}

export const DEFAULT_CDN_CONFIG = IS_PROD
  ? {
    origin: decodeFromBase64('aHR0cDovL2tjZG4uaW50ZXJuYWw='),
    pid: decodeFromBase64('a3dhaXNob3Atd2ViLWRldg=='),
    token: decodeFromBase64(
      'MTIzMzNfYzNmY2NlNzA3ZmIxZTIxOGZlY2EyNTEwY2ZmOWQyYzU=',
    ),
  }
  : {
    origin: decodeFromBase64('aHR0cHM6Ly9rY2RuLmNvcnAua3VhaXNob3UuY29t'),
    pid: decodeFromBase64('a3dhaXNob3Atd2ViLWRldg=='),
    token: decodeFromBase64(
      'MTIzMzNfYzNmY2NlNzA3ZmIxZTIxOGZlY2EyNTEwY2ZmOWQyYzU=',
    ),
  }

export function uploadToCDN(params: CDNParams): Promise<string> | never {
  assert(params.dir, 'dir is required')

  const CDNRequestParams = Object.assign(
    Object.create(null),
    DEFAULT_CDN_CONFIG,
    params.customCDNRequestParams,
  )

  const formData = new FormData()
  formData.append('pid', CDNRequestParams.pid)
  formData.append(
    'dir',
    CDNRequestParams?.dir
    || join('fangzhou', 'material-platform-metrics-service', params.dir),
  )
  formData.append('allowRewrite', CDNRequestParams?.allowRewrite || 'true')
  formData.append('allowHash', CDNRequestParams?.allowHash || 'false')
  formData.append(
    'files[]',
    Buffer.isBuffer(params.content)
      ? params.content
      : Buffer.from(params.content || '', 'utf-8'),
    {
      filename: encodeURI(params.filename),
    },
  )

  const url = `${CDNRequestParams.origin}/api/kcdn/v1/service/npmUpload/multiple?token=${CDNRequestParams.token}`

  return axios
    .post(url, formData, {
      headers: formData.getHeaders(),
      timeout: 60 * 1000,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    })
    .then((e) => {
      try {
        if (
          e.status === 200
          && e.data
          && e.data.result === 1
          && e.data.data.success
        ) {
          const rawUrl = e.data.data.fileResults?.[0]?.cdnUrl

          return `https://f2.eckwai.com/${new URL(rawUrl).pathname}`
        }
        else {
          console.log(JSON.stringify(e.data.data, null, 2))
          return Promise.reject('上传失败')
        }
      }
      catch (error) {
        if (error instanceof Error) {
          return Promise.reject('上传失败，错误原因是' + error.message)
        }
      }
    })
}

export async function fetchFromCDN<T>(originURL: string): Promise<T> {
  if (IS_DEV) {
    return axios.get(originURL).then(e => e.data)
  }
  else {
    const CDN_path = originURL.split('//kos/').pop()
    if (!CDN_path) {
      throw new Error('Invalid CDN path')
    }
    const internalCDN_URL = `http://cdn-peload.internal/kos/${CDN_path}`
    return axios
      .get(internalCDN_URL, {
        headers: {
          kwaiform: 'kcdn-kwaishop-web-dev',
        },
      })
      .then(e => e.data)
  }
}

/** 转换 CDN 链接为内网 CDN 资源链接  */
export const convertCDNUrl2IdcUrl = (url: string): string => {
  return url.replace(
    /^https?:\/\/[^\\/]+/,
    IS_PROD ? 'http://cdn-peload.internal' : 'https://cdnfile.corp.kuaishou.com',
  )
}
