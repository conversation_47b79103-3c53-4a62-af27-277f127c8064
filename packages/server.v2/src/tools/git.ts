import thirdGitUrlParse, { GitUrl } from 'git-url-parse'

export const isValidGitUrl = (git: string): boolean => {
  try {
    thirdGitUrlParse(git)
    return true
  }
  catch (_) {
    return false
  }
}

export const git2httpLink = (git: string): string => {
  const parsed = thirdGitUrlParse(git)
  return `https://git.${parsed.source}/${parsed.full_name}`
}

export const getNamespaceFromGitUrl = (git: string): string => {
  const parsed = thirdGitUrlParse(git)
  return parsed.full_name
}

export const reasonableGitUrlParse = (url: string): GitUrl => {
  try {
    return thirdGitUrlParse(url.trim())
  }
  catch (_) {
    throw new Error(`Invalid git url: ${url}`)
  }
}

export const isSameGitUrl = (git1: string, git2: string): boolean => {
  try {
    return (
      thirdGitUrlParse(git1).full_name === thirdGitUrlParse(git2).full_name
    )
  }
  catch {
    return false
  }
}
