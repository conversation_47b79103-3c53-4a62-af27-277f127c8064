import { join } from 'path'
import { LoggerOptions, transports } from 'winston'

import { CURRENT_BUSINESS, IS_PROD } from '@/constants/envs'
import { PROJECT_ROOT } from '@/constants/path'

const BaseTransports: LoggerOptions['transports'] = [
  new transports.Console({
    level: 'silly',
  }),
]

const ProdTransports: LoggerOptions['transports'] = IS_PROD
  ? [
    new transports.File({
      filename: join(PROJECT_ROOT, 'logs', CURRENT_BUSINESS, 'error.log'),
      level: 'error',
    }),
    new transports.File({
      filename: join(PROJECT_ROOT, 'logs', CURRENT_BUSINESS, 'combined.log'),
      level: 'info',
    }),
  ]
  : []

export const combinedTransports: LoggerOptions['transports']
  = BaseTransports.concat(ProdTransports)
