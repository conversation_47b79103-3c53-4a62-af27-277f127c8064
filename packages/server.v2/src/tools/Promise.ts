import pLimit from 'p-limit'
import { loggerInstance } from './winston'

export interface PromiseAllFulfilledResult<T> {
  result: T[]
  errors: Error[]
}
export async function PromiseAllFulfilled<T = void>(
  promises: Array<Promise<T>>,
): Promise<PromiseAllFulfilledResult<T>> {
  const errors = []
  const result = await Promise.allSettled(
    promises.map(async (promise) => {
      promise.catch((error) => {
        loggerInstance.error(error)
        errors.push(error)
      })
      return promise
    }),
  ).then(results =>
    results
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value),
  )
  return { result, errors }
}

export async function limitInvokePromiseAllFulfilled<T>(
  promises: Array<() => Promise<T>>,
  limit: number = 20,
): Promise<PromiseAllFulfilledResult<T>> {
  const limiter = pLimit(limit)

  return PromiseAllFulfilled(promises.map(promise => limiter(promise)))
}
