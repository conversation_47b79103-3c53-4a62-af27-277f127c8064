import { isArray } from 'lodash'

export function flattenComponentField(
  components: GeneralMaterialSchema.Schema['components'],
): { namespace: string, version: string }[] {
  const result: { namespace: string, version: string }[] = []

  if (isArray(components)) {
    components.forEach((item) => {
      if ('children' in item) {
        if (isArray(item.children)) {
          result.push(...flattenComponentField(item.children ?? []))
        }
      }
      else {
        result.push(item)
      }
    })
  }

  return result
}
