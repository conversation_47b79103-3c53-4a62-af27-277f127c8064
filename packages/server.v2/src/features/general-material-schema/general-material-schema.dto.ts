import { BUSINESS, now } from '@/shared'
import { Transform, Type } from 'class-transformer'
import {
  IsString,
  IsEnum,
  IsNumber,
  IsArray,
  IsOptional,
  ValidateNested,
  IsObject,
  IsNotEmpty,
} from 'class-validator'
import type { JsonObject } from 'type-fest'
import {
  ASSET_DOWNLOAD_TYPE,
  BUNDLE_TYPES,
  CATEGORY,
  PLATFORMS,
} from './constants'
import assert from 'assert'
import { isArray, isNumber, isObject, isString } from '../../tools/type'
import dayjs from 'dayjs'

class Repository {
  @IsEnum(['git'])
  @IsNotEmpty()
  type: 'git'

  @IsString()
  @IsNotEmpty()
  url: string

  @IsString()
  @IsOptional()
  branch?: string

  @IsString()
  @IsOptional()
  directory?: string
}

class Tags implements GeneralMaterialSchema.Tags {
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  @Transform(({ value }) => {
    if (Array.isArray(value) && value.length > 0) {
      return value.map(Number)
    }
    else if (typeof value === 'string') {
      return [Number(value)]
    }
    else {
      return [0]
    }
  })
  domain: number[]

  @IsEnum(PLATFORMS, { each: true })
  @IsNotEmpty()
  platform: PLATFORMS | PLATFORMS[]

  @IsEnum(BUSINESS)
  @IsNotEmpty()
  business: BUSINESS

  @IsEnum(CATEGORY, { each: true })
  @IsOptional()
  @Transform(({ value }) => {
    if (Array.isArray(value) && value.length > 0) {
      return value
    }
    else if (typeof value === 'string') {
      return [value]
    }
    else {
      return [CATEGORY.OTHER]
    }
  })
  category: CATEGORY | CATEGORY[]

  @IsObject()
  @IsOptional()
  meta?: Record<string, Array<string>>
}

export class GeneralMaterialSchemaDTO implements GeneralMaterialSchema.Schema {
  @IsString()
  @IsNotEmpty()
  packageName: string

  @IsString()
  @IsNotEmpty()
  libraryName: string

  @IsString()
  @IsNotEmpty()
  componentName: string

  @IsString()
  @IsNotEmpty()
  componentChineseName: string

  @IsEnum(BUNDLE_TYPES)
  @IsNotEmpty()
  componentBundleType: string

  @IsString()
  @IsNotEmpty()
  @Type(() => String)
  version: string

  @IsString()
  @IsNotEmpty()
  @Type(() => String)
  description: string

  @IsString()
  @IsOptional()
  instructionUrl?: string

  @IsEnum(['cdn', 'npm'])
  @IsNotEmpty()
  assetDownloadType: ASSET_DOWNLOAD_TYPE

  @IsString()
  @IsNotEmpty()
  assetDownloadUrl: string

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  assetPeerDownloadUrl?: string[]

  @IsString()
  @IsNotEmpty()
  @Type(() => String)
  author: string

  @ValidateNested()
  @Type(() => Repository)
  @IsOptional()
  repository?: Repository | string

  @IsString()
  @IsOptional()
  gitUrl?: string

  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => value ?? '')
  thumbnailUrl: string | string[]

  @IsString()
  @IsOptional()
  @Transform(({ value }) => dayjs(value ?? now()).format('YYYY-MM-DD HH:mm:ss'))
  publishTime: string

  @IsString()
  @IsOptional()
  @Transform(({ value }) => dayjs(value ?? now()).format('YYYY-MM-DD HH:mm:ss'))
  updateTime: string

  @ValidateNested()
  @IsNotEmpty()
  @Type(() => Tags)
  tags: Tags

  @IsArray()
  @IsOptional()
  props?: GeneralMaterialSchema.Prop[]

  @IsObject()
  @IsOptional()
  dependencies?: Record<string, string>

  @IsObject()
  @IsOptional()
  devDependencies?: Record<string, string>

  @IsObject()
  @IsOptional()
  peerDependencies?: Record<string, string>

  @IsArray()
  @IsOptional()
  components?: (
    | { namespace: string, version: string }
    | { title: string, children: { namespace: string, version: string }[] }
  )[]

  @IsObject()
  @IsOptional()
  extraConfig?: JsonObject
}

export class GeneralMaterialLibrarySchemaDTO
implements GeneralMaterialSchema.LibrarySchema {
  @IsString()
  @IsNotEmpty()
  packageName: string

  @IsString()
  @IsOptional()
  libraryName?: string

  @IsString()
  @IsOptional()
  componentName?: string

  @IsString()
  @IsNotEmpty()
  componentChineseName: string

  @IsEnum(BUNDLE_TYPES)
  @IsNotEmpty()
  componentBundleType: string

  @IsString()
  @IsNotEmpty()
  @Type(() => String)
  version: string

  @IsString()
  @IsNotEmpty()
  @Type(() => String)
  description: string

  @IsString()
  @IsOptional()
  instructionUrl?: string

  @IsEnum(['cdn', 'npm'])
  @IsNotEmpty()
  assetDownloadType: ASSET_DOWNLOAD_TYPE

  @IsString()
  @IsNotEmpty()
  assetDownloadUrl: string

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  assetPeerDownloadUrl?: string[]

  @IsString()
  @IsNotEmpty()
  @Type(() => String)
  author: string

  @ValidateNested()
  @Type(() => Repository)
  @IsOptional()
  repository?: Repository | string

  @IsString()
  @IsOptional()
  gitUrl?: string

  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => value ?? '')
  thumbnailUrl: string | string[]

  @IsString()
  @IsOptional()
  @Transform(({ value }) => dayjs(value ?? now()).format('YYYY-MM-DD HH:mm:ss'))
  publishTime: string

  @IsString()
  @IsOptional()
  @Transform(({ value }) => dayjs(value ?? now()).format('YYYY-MM-DD HH:mm:ss'))
  updateTime: string

  @ValidateNested()
  @IsNotEmpty()
  @Type(() => Tags)
  tags: Tags

  @IsArray()
  @IsOptional()
  props?: GeneralMaterialSchema.Prop[]

  @IsObject()
  @IsOptional()
  dependencies?: Record<string, string>

  @IsObject()
  @IsOptional()
  devDependencies?: Record<string, string>

  @IsObject()
  @IsOptional()
  peerDependencies?: Record<string, string>

  @IsArray()
  @IsOptional()
  components?: (
    | { namespace: string, version: string }
    | { title: string, children: { namespace: string, version: string }[] }
  )[]

  @IsObject()
  @IsOptional()
  extraConfig?: JsonObject
}

export function isGeneralMaterialSchema(
  schema: unknown,
): schema is GeneralMaterialSchema.Schema {
  try {
    validateGeneralMaterialSchema(schema)
    return true
  }
  catch (_) {
    return false
  }
}

export function validateGeneralMaterialSchema(
  schema: unknown,
): asserts schema is GeneralMaterialSchema.Schema {
  assert(isObject(schema))

  // ======================== packageName ========================
  assert('packageName' in schema)
  assert(isString(schema.packageName))

  // ======================== componentName ========================
  assert('componentName' in schema)
  assert(isString(schema.componentName))

  // ======================== componentBundleType ========================
  assert('componentBundleType' in schema)
  assert(isString(schema.componentBundleType))
  assert(Object.keys(BUNDLE_TYPES).includes(schema.componentBundleType))

  // ======================== libraryName ========================
  assert('libraryName' in schema)
  assert(isString(schema.libraryName))
  assert(schema.libraryName.length > 0)

  // ======================== componentChineseName ========================
  assert('componentChineseName' in schema)
  assert(isString(schema.componentChineseName))
  assert(schema.componentChineseName.length > 0)

  // ======================== author ========================
  assert('author' in schema)
  assert(isString(schema.author))
  assert(schema.author.length > 0)

  // ======================== assetDownloadType ========================
  assert('assetDownloadType' in schema)
  assert(isString(schema.assetDownloadType))
  assert(['cdn', 'npm'].includes(schema.assetDownloadType))

  // ======================== assetDownloadUrl ========================
  assert('assetDownloadUrl' in schema)
  assert(isString(schema.assetDownloadUrl))

  // ======================== thumbnailUrl ========================
  assert('thumbnailUrl' in schema)
  assert(isString(schema.thumbnailUrl))

  // ======================== description ========================
  assert('description' in schema)
  assert(isString(schema.description))

  // ======================== instructionUrl ========================
  assert('instructionUrl' in schema)
  assert(isString(schema.instructionUrl))

  // ======================== tags ========================
  assert('tags' in schema)
  assert(isObject(schema.tags))

  // ======================== tags.domain ========================
  if ('domain' in schema.tags) {
    assert(isArray(schema.tags.domain))
    assert(schema.tags.domain.every(item => isNumber(item)))
  }

  // ======================== version ========================
  assert('version' in schema)
  assert(isString(schema.version))
}
