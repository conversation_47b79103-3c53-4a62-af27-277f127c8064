export enum ASSET_DOWNLOAD_TYPE {
  CDN = 'cdn',
  NPM = 'npm',
}

export enum BUNDLE_TYPES {
  SLSC = 'SLSC',
  SLMC = 'SLMC',
  SL = 'SL',
}

export enum PLATFORMS {
  PC = 'pc',
  H5 = 'h5',
  RN = 'rn',
  TK = 'tk',
}

export enum CATEGORY {
  COMMON = 'common',
  NAVIGATION = 'navigation',
  LAYOUT = 'layout',
  DATA_INPUT = 'dataInput',
  DATA_DISPLAY = 'dataDisplay',
  FEEDBACK = 'feedback',
  OTHER = 'other',
  CHART = 'chart',
  LOGIC = 'logic',
}

export enum PROP_TYPES {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  NULL = 'null',
  VOID = 'void',
  FUNCTION = 'function',
  ARRAY = 'array',
  OBJECT = 'object',
  TSTYPE_REFERENCE = 'TSTypeReference',
  ENUM = 'enum',
}
