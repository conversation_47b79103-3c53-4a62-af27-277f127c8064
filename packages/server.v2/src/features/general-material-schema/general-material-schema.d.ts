declare namespace GeneralMaterialSchema {
  type PackageJSON = import('type-fest').PackageJson

  export interface Schema {
    /**
     * 物料库/组件库名称（同 package.json）
     */
    packageName: string
    /**
     * 物料库/组件库挂载在全局（globalThis、window、global）的 key
     */
    libraryName: string
    /**
     * 物料名称/变量名（物料导入时的变量名、从 libraryName 取值时的 key）
     */
    componentName: string
    /**
     * 物料中文名称（用于展示）
     */
    componentChineseName: string
    /**
     * 物料库打包类型。"SLMC" | "SL" // 物料、物料库（"SLSC" 单包单组件已不推荐使用）
     */
    componentBundleType: string
    /**
     * 物料版本，遵循 [semver](https://semver.org/) 规范。
     */
    version: string
    /**
     * 物料描述
     */
    description: string
    /**
     * 物料使用说明文档地址
     */
    instructionUrl?: string
    /**
     * 物料资产存储类型
     */
    assetDownloadType: import('./constants').ASSET_DOWNLOAD_TYPE
    /**
     * 物料资产下载地址
     */
    assetDownloadUrl: string
    /**
     * 物料资产主入口资源依赖的相关资源
     */
    assetPeerDownloadUrl?: string[]
    /**
     * 物料作者。使用邮箱前缀
     */
    author: string
    /**
     * 物料仓库地址。git 地址、平台地址等（repository 与 gitUrl 字段两者必填一项）
     */
    repository?:
      | string
      | {
        type: 'git'
        url: string
        branch?: string
        directory?: string
      }
    /**
     * @deprecated
     * 物料 Git 仓库（推荐使用 repository 字段,repository 与 gitUrl 字段两者必填一项）
     */
    gitUrl?: string
    /**
     * 物料预览图/列表
     */
    thumbnailUrl: string | string[]
    /**
     * 物料发布时间
     */
    publishTime: string
    /**
     * 物料最后更新时间
     */
    updateTime: string
    /**
     * 物料标签
     */
    tags: Tags
    /**
     * 物料属性
     */
    props?: Array<Prop>
    /**
     * 物料依赖
     */
    dependencies?: PackageJSON['dependencies']
    /**
     * 物料开发依赖
     */
    devDependencies?: PackageJSON['devDependencies']
    /**
     * 物料 peer 依赖
     */
    peerDependencies?: PackageJSON['peerDependencies']
    /**
     * 组件库内部组件描述（组件库类型需要）
     */
    components?: (
      | { namespace: string, version: string }
      | { title: string, children: { namespace: string, version: string }[] }
    )[]
    /**
     * 物料额外配置
     */
    extraConfig?: Base.JSONObject
  }

  export type LibrarySchema = import('type-fest').SetOptional<
    Schema,
    'libraryName' | 'componentName'
  >

  export interface Tags {
    /**
     * 所属业务目录。业务域节点路径，如 [1, 2, 3] 表示业务域树上的节点路径为 1 -> 2 -> 3，默认为 [0]，表示全部。
     */
    domain?: number[]
    /**
     * 适用平台
     */
    platform: Platform | Platform[]
    /**
     * 业务场景
     */
    business: Business
    /**
     * 物料分类
     */
    category: string | string[]
    /**
     * 自定义标签
     */
    meta?: Record<string, Array<string>>
  }

  export type Platform = import('./constants').PLATFORMS
  export type Business = import('@/constants/business').BUSINESS
  // common(通用)、navigation(导航)、layout(布局)、dataInput(数据录入)、dataDisplay(数据展示)、feedback(反馈)、others(其他类型)、chart(图表)、logic(逻辑)
  export type Category = import('./constants').CATEGORY

  interface Prop extends Base.JSONObject {
    identifier: string
    optional: boolean
    description: string
    defaultValue: Base.JSONValue
    // props值类型（遵循ts规范）: string、number、boolean、null、void、function、array、object、TSTypeReference、enum等等
    type: import('./constants').PROP_TYPES
    [key: string]: Base.JSONValue
  }

  export { Prop }
}
