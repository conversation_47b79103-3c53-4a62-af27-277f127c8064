import { <PERSON><PERSON><PERSON>, Logger, MiddlewareConsumer } from '@nestjs/common'
import { ScheduleModule } from '@nestjs/schedule'

import { AppController } from './app.controller'
import { AppService } from './app.service'

import { LoggingMiddleware } from './middlewares/logging/logging.middleware'
import { ScanInfoModule as AnalysisScanInfoModule } from './modules/analysis/scan-info/scan-info.module'
import { RawDataModule as AnalysisRawDataModule } from './modules/analysis/raw-data/raw-data.module'
import { RepoMetaModule as AnalysisRepoMetaModule } from './modules/analysis/repo-meta/repo-meta.module'
import { ComputeModule } from './modules/compute/compute.module'
import { MaterialUsageModule as AnalysisMaterialUsageModule } from './modules/analysis/material-usage/material-usage.module'
import { NewCodeLinesModule as AnalysisNewCodeLinesModule } from './modules/analysis/new-code-lines/new-code-lines.module'
import { CodeLinesModule as AnalysisCodeLinesModule } from './modules/analysis/code-lines/code-lines.module'
import { NewMaterialUsageModule as AnalysisNewMaterialUsageModule } from './modules/analysis/new-material-usage/new-material-usage.module'
import { BusinessConfigModule as AnalysisBusinessConfigModule } from './modules/analysis/business-config/business-config.module'
import { MaterialModule } from './modules/material/material.module'
import { MaterialPublishModule } from './modules/material/material-publish/material-publish.module'
import { ResultModule as AnalysisResultModule } from './modules/analysis/result/result.module'
import { MaterialMetaModule as AnalysisMaterialMetaModule } from './modules/analysis/material-meta/material-meta.module'
import { GitlabModule } from './modules/gitlab/gitlab.module'
import { KconfModule } from './modules/kconf/kconf.module'
import { SharedModule } from './modules/shared/shared.module'
import { AnalysisModule } from './modules/analysis/analysis.module'
import { ForwardModule } from './modules/forward/forward.module'
import { LowCodePageInfoModule } from './modules/low-code/page-info/page-info.module'
import { ReportModule } from './modules/low-code/report/report.module'
import { UserModule } from './modules/user/user.module'
import { MaterialIntegralModule } from './modules/material/material-integral/material-integral.module'
import { OpenModule } from './modules/open/open.module'
import { MaterialTagModule } from './modules/material/material-tag/material-tag.module'
import { MaterialMetaModule } from './modules/material/material-meta/material-meta.module'

@Module({
  imports: [
    ScheduleModule.forRoot(),
    // ServeStaticModule.forRoot({
    //   rootPath: join(__dirname, '..', 'client'),
    // }),
    AnalysisScanInfoModule,
    AnalysisRawDataModule,
    AnalysisRepoMetaModule,
    ComputeModule,
    AnalysisMaterialUsageModule,
    AnalysisNewCodeLinesModule,
    AnalysisCodeLinesModule,
    AnalysisNewMaterialUsageModule,
    AnalysisBusinessConfigModule,
    MaterialModule,
    MaterialPublishModule,
    AnalysisResultModule,
    AnalysisMaterialMetaModule,
    GitlabModule,
    KconfModule,
    SharedModule,
    AnalysisModule,
    ForwardModule,
    LowCodePageInfoModule,
    ReportModule,
    UserModule,
    MaterialIntegralModule,
    OpenModule,
    MaterialTagModule,
    MaterialMetaModule,
  ],
  controllers: [AppController],
  providers: [AppService, Logger],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer): void {
    consumer.apply(LoggingMiddleware).forRoutes('*')
  }
}
