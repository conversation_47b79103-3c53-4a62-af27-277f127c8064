export const IS_DEV = process.env.NODE_ENV === 'development'
export const IS_PROD = !IS_DEV
export const IS_STAGING = process.env.KWS_SERVICE_STAGE === 'STAGING'
export const IS_CORP = process.env.KWS_SERVICE_STAGE === 'PROD'
export const IS_ON_SERVER = IS_STAGING || IS_CORP

export const CURRENT_BUSINESS = process.env.business || 'v2'
export const BLOB_STORE_DOMAIN = IS_PROD ? 'http://bs3-hb1.internal' : 'https://bs3-hb1.corp.kuaishou.com'
