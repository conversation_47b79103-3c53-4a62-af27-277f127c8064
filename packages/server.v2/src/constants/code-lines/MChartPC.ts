export const MChartPCCodeLines = [
  {
    materialName: 'echarts',
    codeLines: 0,
  },
  {
    materialName: 'version',
    codeLines: 1,
  },
  {
    materialName: 'Bullet<PERSON>hart',
    codeLines: 264,
  },
  {
    materialName: 'MixChart',
    codeLines: 41,
  },
  {
    materialName: 'MChart',
    codeLines: 54,
  },
  {
    materialName: 'Scatter',
    codeLines: 136,
  },
  {
    materialName: 'Radar',
    codeLines: 147,
  },
  {
    materialName: 'Line',
    codeLines: 218,
  },
  {
    materialName: 'Bar',
    codeLines: 197,
  },
  {
    materialName: 'Pie',
    codeLines: 239,
  },
  {
    materialName: 'Map',
    codeLines: 537,
  },
  {
    materialName: 'Funnel',
    codeLines: 1192,
  },
  {
    materialName: 'WordCloud',
    codeLines: 1923,
  },
  {
    materialName: 'Series',
    codeLines: 177,
  },
  {
    materialName: 'renderAdaptors',
    codeLines: 50,
  },
  {
    materialName: 'registerRenderAdaptor',
    codeLines: 50,
  },
  {
    materialName: 'ConfigProvider',
    codeLines: 46,
  },
  {
    materialName: 'globalPlugins',
    codeLines: 0,
  },
  {
    materialName: 'globalHooksPlugins',
    codeLines: 0,
  },
  {
    materialName: 'registerGlobalPlugins',
    codeLines: 10,
  },
  {
    materialName: 'registerGlobalHooksPlugins',
    codeLines: 10,
  },
  {
    materialName: 'setDebug',
    codeLines: 50,
  },
  {
    materialName: 'defineMChartPlugin',
    codeLines: 30,
  },
  {
    materialName: 'defineHooksPlugin',
    codeLines: 30,
  },
  {
    materialName: 'defineHooksPlugin',
    codeLines: 30,
  },
  {
    materialName: 'defineHooksPlugin',
    codeLines: 30,
  },
  {
    materialName: 'useTooltip',
    codeLines: 229,
  },
  {
    materialName: 'DatasetClass',
    codeLines: 105,
  },
  {
    materialName: 'useDataset',
    codeLines: 105,
  },
]
