export const MUICodeLines = [
  {
    materialName: 'Affix',
    codeLines: 676,
  },
  {
    materialName: 'Anchor',
    codeLines: 1162,
  },
  {
    materialName: 'AutoComplete',
    codeLines: 791,
  },
  {
    materialName: 'Alert',
    codeLines: 1032,
  },
  {
    materialName: 'Avatar',
    codeLines: 945,
  },
  {
    materialName: 'BackTop',
    codeLines: 337,
  },
  {
    materialName: 'Badge',
    codeLines: 1549,
  },
  {
    materialName: 'Breadcrumb',
    codeLines: 991,
  },
  {
    materialName: 'Button',
    codeLines: 2243,
  },
  {
    materialName: 'Calendar',
    codeLines: 1503,
  },
  {
    materialName: 'Card',
    codeLines: 1112,
  },
  {
    materialName: 'Collapse',
    codeLines: 908,
  },
  {
    materialName: 'Carousel',
    codeLines: 728,
  },
  {
    materialName: 'Cascader',
    codeLines: 1889,
  },
  {
    materialName: 'Checkbox',
    codeLines: 1333,
  },
  {
    materialName: 'Col',
    codeLines: 345,
  },
  {
    materialName: 'Comment',
    codeLines: 551,
  },
  {
    materialName: 'ConfigProvider',
    codeLines: 3070,
  },
  {
    materialName: 'DatePicker',
    codeLines: 4067,
  },
  {
    materialName: 'Descriptions',
    codeLines: 1366,
  },
  {
    materialName: 'Divider',
    codeLines: 382,
  },
  {
    materialName: 'Dropdown',
    codeLines: 1449,
  },
  {
    materialName: 'Drawer',
    codeLines: 1894,
  },
  {
    materialName: 'Empty',
    codeLines: 589,
  },
  {
    materialName: 'Form',
    codeLines: 7458,
  },
  {
    materialName: 'Grid',
    codeLines: 1472,
  },
  {
    materialName: 'Input',
    codeLines: 3887,
  },
  {
    materialName: 'Image',
    codeLines: 600,
  },
  {
    materialName: 'InputNumber',
    codeLines: 892,
  },
  {
    materialName: 'Layout',
    codeLines: 1826,
  },
  {
    materialName: 'List',
    codeLines: 1858,
  },
  {
    materialName: 'message',
    codeLines: 1296,
  },
  {
    materialName: 'Menu',
    codeLines: 3033,
  },
  {
    materialName: 'Mentions',
    codeLines: 771,
  },
  {
    materialName: 'Modal',
    codeLines: 3323,
  },
  {
    materialName: 'Statistic',
    codeLines: 667,
  },
  {
    materialName: 'notification',
    codeLines: 1535,
  },
  {
    materialName: 'PageHeader',
    codeLines: 1032,
  },
  {
    materialName: 'Pagination',
    codeLines: 927,
  },
  {
    materialName: 'Popconfirm',
    codeLines: 797,
  },
  {
    materialName: 'Popover',
    codeLines: 710,
  },
  {
    materialName: 'Progress',
    codeLines: 1343,
  },
  {
    materialName: 'Radio',
    codeLines: 1334,
  },
  {
    materialName: 'Rate',
    codeLines: 515,
  },
  {
    materialName: 'Result',
    codeLines: 802,
  },
  {
    materialName: 'Row',
    codeLines: 5,
  },
  {
    materialName: 'Select',
    codeLines: 2801,
  },
  {
    materialName: 'Skeleton',
    codeLines: 1147,
  },
  {
    materialName: 'Slider',
    codeLines: 1086,
  },
  {
    materialName: 'Space',
    codeLines: 745,
  },
  {
    materialName: 'Spin',
    codeLines: 654,
  },
  {
    materialName: 'Steps',
    codeLines: 1795,
  },
  {
    materialName: 'Switch',
    codeLines: 491,
  },
  {
    materialName: 'Table',
    codeLines: 13163,
  },
  {
    materialName: 'Transfer',
    codeLines: 3037,
  },
  {
    materialName: 'Tree',
    codeLines: 2821,
  },
  {
    materialName: 'TreeSelect',
    codeLines: 818,
  },
  {
    materialName: 'Tabs',
    codeLines: 1795,
  },
  {
    materialName: 'Tag',
    codeLines: 1179,
  },
  {
    materialName: 'TimePicker',
    codeLines: 877,
  },
  {
    materialName: 'Timeline',
    codeLines: 863,
  },
  {
    materialName: 'Tooltip',
    codeLines: 1193,
  },
  {
    materialName: 'Typography',
    codeLines: 3306,
  },
  {
    materialName: 'Upload',
    codeLines: 5301,
  },
  {
    materialName: 'version',
    codeLines: 3,
  },
] as const
