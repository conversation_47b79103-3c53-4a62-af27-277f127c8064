export const MUIMobileCodeLines = [
  {
    materialName: 'ConfigProvider',
    codeLines: 53,
  },
  {
    materialName: 'ActionSheet',
    codeLines: 312,
  },
  {
    materialName: 'AutoCenter',
    codeLines: 28,
  },
  {
    materialName: 'Avatar',
    codeLines: 142,
  },
  {
    materialName: 'Badge',
    codeLines: 139,
  },
  {
    materialName: 'Button',
    codeLines: 338,
  },
  {
    materialName: 'Calendar',
    codeLines: 532,
  },
  {
    materialName: 'CapsuleTabs',
    codeLines: 231,
  },
  {
    materialName: 'Card',
    codeLines: 145,
  },
  {
    materialName: 'CascadePicker',
    codeLines: 102,
  },
  {
    materialName: 'CascadePickerView',
    codeLines: 16,
  },
  {
    materialName: 'Cascader',
    codeLines: 284,
  },
  {
    materialName: 'CascaderView',
    codeLines: 324,
  },
  {
    materialName: 'CheckList',
    codeLines: 204,
  },
  {
    materialName: 'Checkbox',
    codeLines: 514,
  },
  {
    materialName: 'Collapse',
    codeLines: 286,
  },
  {
    materialName: 'DatePicker',
    codeLines: 583,
  },
  {
    materialName: 'DatePickerView',
    codeLines: 79,
  },
  {
    materialName: 'Dialog',
    codeLines: 724,
  },
  {
    materialName: 'Divider',
    codeLines: 91,
  },
  {
    materialName: 'Dropdown',
    codeLines: 346,
  },
  {
    materialName: 'Ellipsis',
    codeLines: 258,
  },
  {
    materialName: 'Empty',
    codeLines: 130,
  },
  {
    materialName: 'ErrorBlock',
    codeLines: 154,
  },
  {
    materialName: 'FloatingBubble',
    codeLines: 205,
  },
  {
    materialName: 'FloatingPanel',
    codeLines: 269,
  },
  {
    materialName: 'Form',
    codeLines: 1331,
  },
  {
    materialName: 'Grid',
    codeLines: 236,
  },
  {
    materialName: 'Image',
    codeLines: 444,
  },
  {
    materialName: 'ImageUploader',
    codeLines: 478,
  },
  {
    materialName: 'ImageViewer',
    codeLines: 609,
  },
  {
    materialName: 'IndexBar',
    codeLines: 364,
  },
  {
    materialName: 'InfiniteScroll',
    codeLines: 171,
  },
  {
    materialName: 'Input',
    codeLines: 395,
  },
  {
    materialName: 'JumboTabs',
    codeLines: 357,
  },
  {
    materialName: 'List',
    codeLines: 349,
  },
  {
    materialName: 'Loading',
    codeLines: 87,
  },
  {
    materialName: 'Mask',
    codeLines: 187,
  },
  {
    materialName: 'Modal',
    codeLines: 589,
  },
  {
    materialName: 'NavBar',
    codeLines: 273,
  },
  {
    materialName: 'NoticeBar',
    codeLines: 275,
  },
  {
    materialName: 'NumberKeyboard',
    codeLines: 395,
  },
  {
    materialName: 'PageIndicator',
    codeLines: 160,
  },
  {
    materialName: 'PasscodeInput',
    codeLines: 375,
  },
  {
    materialName: 'Picker',
    codeLines: 555,
  },
  {
    materialName: 'PickerView',
    codeLines: 615,
  },
  {
    materialName: 'Popover',
    codeLines: 728,
  },
  {
    materialName: 'Popup',
    codeLines: 371,
  },
  {
    materialName: 'ProgressBar',
    codeLines: 77,
  },
  {
    materialName: 'ProgressCircle',
    codeLines: 122,
  },
  {
    materialName: 'PullToRefresh',
    codeLines: 245,
  },
  {
    materialName: 'Radio',
    codeLines: 273,
  },
  {
    materialName: 'Rate',
    codeLines: 137,
  },
  {
    materialName: 'Result',
    codeLines: 218,
  },
  {
    materialName: 'SafeArea',
    codeLines: 46,
  },
  {
    materialName: 'ScrollMask',
    codeLines: 102,
  },
  {
    materialName: 'SearchBar',
    codeLines: 354,
  },
  {
    materialName: 'Selector',
    codeLines: 412,
  },
  {
    materialName: 'SideBar',
    codeLines: 264,
  },
  {
    materialName: 'Skeleton',
    codeLines: 150,
  },
  {
    materialName: 'Slider',
    codeLines: 488,
  },
  {
    materialName: 'Space',
    codeLines: 166,
  },
  {
    materialName: 'SpinLoading',
    codeLines: 89,
  },
  {
    materialName: 'Stepper',
    codeLines: 330,
  },
  {
    materialName: 'Steps',
    codeLines: 288,
  },
  {
    materialName: 'SwipeAction',
    codeLines: 338,
  },
  {
    materialName: 'Swiper',
    codeLines: 536,
  },
  {
    materialName: 'Switch',
    codeLines: 193,
  },
  {
    materialName: 'TabBar',
    codeLines: 237,
  },
  {
    materialName: 'Tabs',
    codeLines: 613,
  },
  {
    materialName: 'Tag',
    codeLines: 106,
  },
  {
    materialName: 'TextArea',
    codeLines: 397,
  },
  {
    materialName: 'Toast',
    codeLines: 586,
  },
  {
    materialName: 'TreeSelect',
    codeLines: 550,
  },
  {
    materialName: 'VirtualInput',
    codeLines: 301,
  },
  {
    materialName: 'WaterMark',
    codeLines: 183,
  },
  {
    materialName: 'SegmentedControl',
    codeLines: 182,
  },
  {
    materialName: 'Guide',
    codeLines: 518,
  },
  {
    materialName: 'setDefaultConfig',
    codeLines: 260,
  },
] as const
