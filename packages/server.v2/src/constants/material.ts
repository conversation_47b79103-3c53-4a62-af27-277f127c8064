/** 物料类型，组件库/组件 */
export enum MATERIAL_TYPE {
  /** 组件库 */
  COM_LIB = 'com_lib',
  /** 组件 */
  COMPONENT = 'component',
}

export const ALL_MATERIAL_TYPE = [
  MATERIAL_TYPE.COM_LIB,
  MATERIAL_TYPE.COMPONENT,
] as const

export const PLATFORM = {
  PC: 'pc',
  H5: 'h5',
  RN: 'rn',
  TK: 'tk',
}

export const ALL_PLATFORM = [
  PLATFORM.PC,
  PLATFORM.H5,
  PLATFORM.RN,
  PLATFORM.TK,
] as const

export const PLATFORM_NAMES = {
  [PLATFORM.PC]: 'PC',
  [PLATFORM.H5]: 'H5',
  [PLATFORM.RN]: 'RN',
  [PLATFORM.TK]: 'TK',
}

export enum MATERIAL_VERSION_TYPE {
  RELEASE = 'release',
  PRERELEASE = 'prerelease',
}
