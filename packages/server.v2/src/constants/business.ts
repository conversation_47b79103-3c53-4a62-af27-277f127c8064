export const isSourceCodeBusiness = (
  business: unknown,
): business is SOURCE_CODE_BUSINESS => {
  return ALL_SOURCE_CODE_BUSINESS.includes(business as SOURCE_CODE_BUSINESS)
}

export enum SOURCE_CODE_BUSINESS {
  BIZ = 'biz',
  ES = 'es',
  KWAI_ES = 'kwaies',
  LOCAL_LIFE = 'locallife',
  LOCAL_LIFE_CLIENT = 'locallifeClient',
}

export const ALL_SOURCE_CODE_BUSINESS: SOURCE_CODE_BUSINESS[] = [
  SOURCE_CODE_BUSINESS.BIZ,
  SOURCE_CODE_BUSINESS.ES,
  SOURCE_CODE_BUSINESS.KWAI_ES,
  SOURCE_CODE_BUSINESS.LOCAL_LIFE,
  SOURCE_CODE_BUSINESS.LOCAL_LIFE_CLIENT,
]

export type SOURCE_CODE_BUSINESS_TYPES =
  | keyof typeof SOURCE_CODE_BUSINESS
  | (string & {})

export enum SOURCE_CODE_BUSINESS_NAME {
  BIZ = '商业化前端',
  ES = '电商前端',
  KWAI_ES = '海外电商前端',
  LOCAL_LIFE = '本地生活前端',
  LOCAL_LIFE_CLIENT = '本地生活客户端',
}

export const SOURCE_CODE_BUSINESS_NAME_MAP = {
  [SOURCE_CODE_BUSINESS.BIZ]: SOURCE_CODE_BUSINESS_NAME.BIZ,
  [SOURCE_CODE_BUSINESS.ES]: SOURCE_CODE_BUSINESS_NAME.ES,
  [SOURCE_CODE_BUSINESS.KWAI_ES]: SOURCE_CODE_BUSINESS_NAME.KWAI_ES,
  [SOURCE_CODE_BUSINESS.LOCAL_LIFE]: SOURCE_CODE_BUSINESS_NAME.LOCAL_LIFE,
  [SOURCE_CODE_BUSINESS.LOCAL_LIFE_CLIENT]:
    SOURCE_CODE_BUSINESS_NAME.LOCAL_LIFE_CLIENT,
} as const

export enum LOW_CODE_BUSINESS {
  KAEL = 'kael',
  FANGZHOU = 'fangzhou',
}

export const isLowCodeBusiness = (
  business: unknown,
): business is LOW_CODE_BUSINESS => {
  return ALL_LOW_CODE_BUSINESS.includes(business as LOW_CODE_BUSINESS)
}

export const ALL_LOW_CODE_BUSINESS = [
  LOW_CODE_BUSINESS.FANGZHOU,
  LOW_CODE_BUSINESS.KAEL,
] as const

export enum LOW_CODE_BUSINESS_NAME {
  KAEL = '灵筑',
  FANGZHOU = '方舟',
}

export enum BUSINESS {
  BIZ = SOURCE_CODE_BUSINESS.BIZ,
  ES = SOURCE_CODE_BUSINESS.ES,
  KWAI_ES = SOURCE_CODE_BUSINESS.KWAI_ES,
  LOCAL_LIFE = SOURCE_CODE_BUSINESS.LOCAL_LIFE,
  LOCAL_LIFE_CLIENT = SOURCE_CODE_BUSINESS.LOCAL_LIFE_CLIENT,
  KAEL = LOW_CODE_BUSINESS.KAEL,
  FANGZHOU = LOW_CODE_BUSINESS.FANGZHOU,
}

export const ALL_BUSINESS = [
  BUSINESS.BIZ,
  BUSINESS.ES,
  BUSINESS.KWAI_ES,
  BUSINESS.LOCAL_LIFE,
  BUSINESS.LOCAL_LIFE_CLIENT,
  BUSINESS.KAEL,
  BUSINESS.FANGZHOU,
] as const

export enum BUSINESS_NAME {
  BIZ = SOURCE_CODE_BUSINESS_NAME.BIZ,
  ES = SOURCE_CODE_BUSINESS_NAME.ES,
  KWAI_ES = SOURCE_CODE_BUSINESS_NAME.KWAI_ES,
  LOCAL_LIFE = SOURCE_CODE_BUSINESS_NAME.LOCAL_LIFE,
  LOCAL_LIFE_CLIENT = SOURCE_CODE_BUSINESS_NAME.LOCAL_LIFE_CLIENT,
  KAEL = LOW_CODE_BUSINESS_NAME.KAEL,
  FANGZHOU = LOW_CODE_BUSINESS_NAME.FANGZHOU,
}

export enum BUSINESS_KEY_MAP {
  biz = 'BIZ',
  es = 'ES',
  kwaies = 'KWAI_ES',
  locallife = 'LOCAL_LIFE',
  locallifeClient = 'LOCAL_LIFE_CLIENT',
  kael = 'KAEL',
  fangzhou = 'FANGZHOU',
}

export function isBusiness(business: unknown): business is BUSINESS {
  return isLowCodeBusiness(business) || isSourceCodeBusiness(business)
}
