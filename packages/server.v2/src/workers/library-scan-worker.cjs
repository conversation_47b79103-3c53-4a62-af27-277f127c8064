/* eslint-disable @typescript-eslint/no-var-requires */
// const {
//   LibraryModeBootstrap,
// } = require('@ks-material-middleoffice/measure-sdk');
const workerpool = require('workerpool');
const bootstrap =
  require('@ks-material-middleoffice/measure-sdk').LibraryModeBootstrap;

workerpool.worker({
  bootstrap: (params) => {
    return bootstrap({
      ...params,
    }).catch((e) => {
      throw {
        name: e.name,
        message: e.message,
        stack: e.stack,
      };
    });
  },
});
