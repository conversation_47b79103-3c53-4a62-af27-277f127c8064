/* eslint-disable @typescript-eslint/no-var-requires */
const workerpool = require('workerpool');
const bootstrap =
  require('@ks-material-middleoffice/measure-sdk').AppModeBootstrap;

workerpool.worker({
  bootstrap: (params) => {
    let pkgFilterFunction;
    // let fileFilterFunction;
    try {
      pkgFilterFunction = params.eval(unescape(params.pkg_filter));
      // fileFilterFunction = params.eval(unescape(params.file_filter));
    } catch {}
    return bootstrap({
      ...params,
      packageFilter: (pkg) => {
        try {
          if ('blackPkgs' in params && params.blackPkgs.includes(pkg.name)) {
            return false;
          }
          const isVue = pkg.dependencies.some((dep) => dep === 'vue');
          if (isVue) return false;

          if (pkgFilterFunction) {
            return pkgFilterFunction(pkg.dependencies);
          }
        } catch (e) {
          return true;
        }

        return true;
      },
      // fileFilter: (file) => {
      //   if (fileFilterFunction) {
      //     try {
      //       return fileFilterFunction(file);
      //     } catch (e) {
      //       return true;
      //     }
      //   }
      // },
    }).catch((e) => {
      throw {
        name: e.name,
        message: e.message,
        stack: e.stack,
      };
    });
  },
});
