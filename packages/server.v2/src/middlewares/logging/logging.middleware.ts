import chalk from 'chalk'
import { Request, Response, NextFunction } from 'express'

import { Injectable, NestMiddleware } from '@nestjs/common'
import { loggerInstance } from '@/tools/winston'

const statusCodeColor = (code: number): string => {
  if (code >= 200 && code < 300) {
    return chalk.green(code) // Green color for success status codes
  }
  else if (code >= 300 && code < 400) {
    return chalk.cyan(code) // Cyan color for redirection status codes
  }
  else if (code >= 400 && code < 500) {
    return chalk.yellow(code) // Yellow color for client error status codes
  }
  else if (code >= 500) {
    return chalk.red(code) // Red color for server error status codes
  }
  else {
    return chalk.reset(code) // Reset color for other status codes
  }
}

@Injectable()
export class LoggingMiddleware implements NestMiddleware {
  private logger = loggerInstance

  use(req: Request, res: Response, next: NextFunction): void {
    const { method, originalUrl } = req
    const start = Date.now()

    res.on('finish', () => {
      const { statusCode } = res
      const responseTime = Date.now() - start

      this.logger.http({
        message: `${method} ${originalUrl} ${statusCodeColor(statusCode)} - ${responseTime}ms`,
        method,
        url: originalUrl,
        statusCode,
        responseTime,
      })
    })

    next()
  }
}
