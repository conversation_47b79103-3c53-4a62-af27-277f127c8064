import { loggerInstance } from '@/tools/winston'
import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpStatus,
} from '@nestjs/common'
import { BaseExceptionFilter } from '@nestjs/core'
import { Prisma } from '@prisma/client'

@Catch(
  Prisma.PrismaClientKnownRequestError,
  Prisma.PrismaClientValidationError,
  Prisma.PrismaClientRustPanicError,
  Prisma.PrismaClientUnknownRequestError,
)
export class PrismaErrorFilter
  extends BaseExceptionFilter
  implements ExceptionFilter {
  private readonly logger = loggerInstance

  catch(
    exception:
      | Prisma.PrismaClientKnownRequestError
      | Prisma.PrismaClientValidationError
      | Prisma.PrismaClientRustPanicError
      | Prisma.PrismaClientUnknownRequestError,
    host: ArgumentsHost,
  ): void {
    const ctx = host.switchToHttp()
    const response = ctx.getResponse()

    let status = HttpStatus.INTERNAL_SERVER_ERROR
    let message = 'An unexpected error occurred.'

    this.logger.error('[PrismaError]', exception)

    if (exception instanceof Prisma.PrismaClientValidationError) {
      status = HttpStatus.BAD_REQUEST
      message = exception.message
    }
    else if (exception instanceof Prisma.PrismaClientKnownRequestError) {
      message = exception.message
      switch (exception.code) {
        case 'P2000':
          status = HttpStatus.BAD_REQUEST
          break
        case 'P2002':
          status = HttpStatus.CONFLICT
          break
        case 'P2003':
          status = HttpStatus.BAD_REQUEST
          break
        case 'P2014':
          status = HttpStatus.CONFLICT
          break
        case 'P2025':
          status = HttpStatus.NOT_FOUND
          break
        default:
          status = HttpStatus.BAD_REQUEST
          break
      }
    }
    else if (exception instanceof Prisma.PrismaClientRustPanicError) {
      status = HttpStatus.INTERNAL_SERVER_ERROR
      message = 'A critical error occurred in the database engine.'
    }
    else if (exception instanceof Prisma.PrismaClientUnknownRequestError) {
      status = HttpStatus.INTERNAL_SERVER_ERROR
      message = 'An unknown database error occurred.'
    }

    const errorResponse: Service.StandardJSONResponseWrapper = {
      code: 0,
      data: null,
      message: message,
    }

    response.status(status).json(errorResponse)
  }
}
