import { ApiTags } from '@nestjs/swagger'
import { Controller, Get, Query } from '@nestjs/common'
import { GitlabService } from './gitlab.service'

@ApiTags('gitlab')
@Controller('gitlab')
export class GitlabController {
  constructor(private readonly gitlabService: GitlabService) {}

  @Get('commits')
  async getCommits(@Query('projectId') projectId: string): Promise<unknown> {
    return this.gitlabService.getProjectCommits(projectId)
  }

  @Get('last-commit-time')
  async getLastCommitTime(@Query('projectId') projectId: string): Promise<number> {
    return this.gitlabService.getProjectLastCommitTime(projectId)
  }

  @Get('project-id/by-namespace')
  async getProjectId(@Query('namespace') namespace: string): Promise<number> {
    return this.gitlabService.findProjectIdByCloneURL(namespace)
  }

  @Get('project-id/by-clone-url')
  async getProjectIdByCloneURL(@Query('cloneURL') cloneURL: string): Promise<number> {
    return this.gitlabService.findProjectIdByCloneURL(cloneURL)
  }

  @Get('clone-url')
  async getCloneURL(@Query('projectId') projectId: string): Promise<string> {
    return this.gitlabService.findCloneURLByProjectId(projectId)
  }
}
