import axios from 'axios'
import dayjs from 'dayjs'
import { Injectable } from '@nestjs/common'

import { decodeFromBase64 } from '@/tools/base64'
import { logStringify } from '@/tools/json'
import { reasonableGitUrlParse } from '@/tools/git'

@Injectable()
export class GitlabService {
  private readonly gitlabRequest = axios.create({
    baseURL: 'https://git.corp.kuaishou.com/api/v4',
    params: {
      access_token: decodeFromBase64('OHpRTndiN2tWZGQ3MWlNVHo4Sm8='),
    },
  })

  async getProjectCommits(projectId: string | number): Promise<unknown> {
    try {
      const response = await this.gitlabRequest.get(
        `/projects/${projectId}/repository/commits`,
      )
      return response.data
    }
    catch (error) {
      if (error instanceof Error) {
        throw new Error(
          `Fail to get project commits from Gitlab API : ${
            error.message
            + logStringify({
              projectId,
            })
          }`,
        )
      }
    }
  }

  async getProjectLastCommitTime(
    projectId: string | number,
    options?: {
      since?: number
      until?: number
    },
  ): Promise<number> {
    try {
      const response = await this.gitlabRequest.get(
        `/projects/${projectId}/repository/commits`,
        {
          params: {
            since: options?.since
              ? dayjs(options.since).toISOString()
              : undefined,
            until: options?.until
              ? dayjs(options.until).toISOString()
              : undefined,
          },
        },
      )
      return response.data.length
        ? dayjs(response.data[0].created_at).valueOf()
        : undefined
    }
    catch (error) {
      if (error instanceof Error) {
        throw new Error(
          `Fail to get project last commit time from Gitlab API : ${
            error.message
            + logStringify({
              projectId,
            })
          }`,
        )
      }
    }
  }

  async findProjectIdByCloneURL(cloneURL: string): Promise<number> | never {
    try {
      const namespace = reasonableGitUrlParse(cloneURL).full_name
      const encodeNamespace = encodeURIComponent(namespace)
      const response = await this.gitlabRequest.get(
        `/projects/${encodeNamespace}`,
      )
      return response.data.id
    }
    catch (error) {
      if (error instanceof Error) {
        throw new Error(
          `Fail to find project id for clone_url from Gitlab API : ${
            error.message
            + logStringify({
              cloneURL,
            })
          }`,
        )
      }
    }
  }

  async findCloneURLByProjectId(
    projectId: string | number,
  ): Promise<string> | never {
    try {
      const response = await this.gitlabRequest.get(`/projects/${projectId}`)
      return response.data.http_url_to_repo
    }
    catch (error) {
      if (error instanceof Error) {
        throw new Error(
          `Fail to find clone url for project id : ${
            error.message
            + logStringify({
              projectId,
            })
          }`,
        )
      }
    }
  }
}
