import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { analysis_new_material_usage, Prisma } from '@prisma/client'
import { LibraryMaterialUsage } from '@ks-material-middleoffice/measure-sdk'

import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import {
  limitInvokePromiseAllFulfilled,
  PromiseAllFulfilled,
  PromiseAllFulfilledResult,
} from '@/tools/Promise'
import { RECORD_STATUS } from '@/constants/status'
import { ALL_SOURCE_CODE_BUSINESS, SOURCE_CODE_BUSINESS, now } from '@/shared'
import { mergeLibraryMaterialUsageWithoutClone } from '@/tools/material-usage'
import { loggerInstance } from '@/tools/winston'
import { logStringify } from '@/tools/json'
import { chunkArray } from '@/tools/array'
import { RepoMetaService } from '../repo-meta/repo-meta.service'

@Injectable()
export class NewMaterialUsageService {
  private readonly prisma = PrismaClientInstance
  private readonly logger = loggerInstance

  constructor(private readonly repoMetaService: RepoMetaService) {}

  async serialize(
    record: analysis_new_material_usage,
  ): Promise<Service.Analysis.NewMaterialUsage.SerializeAnalysisNewMaterialUsage> {
    try {
      return Object.freeze({
        ...record,
        material_info: record.material_info
          ? (JSON.parse(record.material_info) as LibraryMaterialUsage)
          : null,
      })
    }
    catch (error) {
      if (error instanceof Error) {
        throw new Error(
          `Failed to serialize new material usage: ${logStringify({ id: record.id, error: error.message })}`,
        )
      }
    }
  }

  async create(
    param: Service.Analysis.NewMaterialUsage.CreateParams,
  ): Promise<Service.Analysis.NewMaterialUsage.NewMaterialUsageRecord> {
    return this.prisma.analysis_new_material_usage.create({
      data: {
        ...param,
        id: genPrimaryIndex(),
        create_time: now(),
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async batchCreate(
    params: Service.Analysis.NewMaterialUsage.CreateParams[],
  ): Promise<PromiseAllFulfilledResult<Prisma.BatchPayload>> {
    const create_time = now()
    const chunkedParamsList = chunkArray(params, 20)
    return limitInvokePromiseAllFulfilled(
      chunkedParamsList.map(
        chunkedParams => async () =>
          this.prisma.analysis_new_material_usage.createMany({
            data: chunkedParams.map(param => ({
              ...param,
              id: genPrimaryIndex(),
              create_time,
              status: RECORD_STATUS.EFFECT,
            })),
          }),
      ),
    )
  }

  async batchDelete(params: {
    scanId: number
    businesses: SOURCE_CODE_BUSINESS[]
  }): Promise<Prisma.BatchPayload> {
    return this.prisma.analysis_new_material_usage.updateMany({
      where: {
        end_scan_id: params.scanId,
        business: {
          in: params.businesses,
        },
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async findProjectNewMaterialUsageGroupByFilePath(
    params: Service.Analysis.NewMaterialUsage.FindProjectNewMaterialUsageParams,
  ): Promise<
      PromiseAllFulfilledResult<Service.Analysis.NewMaterialUsage.SerializeAnalysisNewMaterialUsage>
    > {
    assert(params.scanId, '扫描 ID 不能为空')
    assert(params.repoProjectId, '项目 ID 不能为空')

    const records = await this.prisma.analysis_new_material_usage.findMany({
      where: {
        repo_project_id: params.repoProjectId,
        end_scan_id: params.scanId,
        status: RECORD_STATUS.EFFECT,
      },
      distinct: ['file_path'],
    })

    return PromiseAllFulfilled(records.map(record => this.serialize(record)))
  }

  async findAllNewMaterialUsageByScanGroupByBusiness(
    scanId: number,
    filter?: {
      businesses?: SOURCE_CODE_BUSINESS[]
      projectIds?: number[]
      excludeRepoProjectIds?: number[]
    },
  ): Promise<
      Service.Analysis.NewMaterialUsage.BusinessNewMaterialUsageReport[]
    > {
    assert(scanId, '扫描 ID 不能为空')
    const { businesses = ALL_SOURCE_CODE_BUSINESS, projectIds = [] } = filter ?? {}

    const optionalConditions: Prisma.analysis_new_material_usageFindManyArgs['where']['AND'] = []
    if (projectIds.length) {
      optionalConditions.push({
        repo_project_id: {
          in: projectIds,
        },
      })
    }
    if (filter?.excludeRepoProjectIds?.length) {
      optionalConditions.push({
        repo_project_id: {
          notIn: filter.excludeRepoProjectIds,
        },
      })
    }

    const records = await this.prisma.analysis_new_material_usage.findMany({
      where: {
        end_scan_id: scanId,
        business: {
          in: businesses,
        },
        AND: optionalConditions,
        status: RECORD_STATUS.EFFECT,
      },
      distinct: ['repo_project_id', 'file_path'],
    })

    const result: Service.Analysis.MaterialUsage.BusinessMaterialUsageReport[]
      = businesses.map(business => ({
        business,
        usage: {},
      }))

    const { errors } = await PromiseAllFulfilled(
      records.map(async (record) => {
        const { business, material_info } = await this.serialize(record)
        const businessReport = result.find(
          item => item.business === business,
        )
        assert(businessReport, `未知的业务类型: ${business}`)
        businessReport.usage = mergeLibraryMaterialUsageWithoutClone(
          businessReport.usage,
          material_info,
        )
      }),
    )

    errors.forEach((error) => {
      this.logger.warn(
        `Failed to find all material usage by scan: ${error.message}`,
      )
    })

    return result
  }

  async findAllNewMaterialUsageByScanGroupByProject(
    scanId: number,
    filter?: {
      projectIds?: number[]
      business?: SOURCE_CODE_BUSINESS
      excludeRepoProjectIds?: number[]
    },
  ): Promise<
      {
        projectId: number
        projectName: string
        materialUsage: LibraryMaterialUsage
      }[]
    > {
    assert(scanId, '扫描 ID 不能为空')

    const optionalConditions: Prisma.analysis_new_material_usageFindManyArgs['where']['AND'] = []
    if (filter?.projectIds?.length) {
      optionalConditions.push({
        repo_project_id: {
          in: filter.projectIds,
        },
      })
    }
    if (filter?.excludeRepoProjectIds?.length) {
      optionalConditions.push({
        repo_project_id: {
          notIn: filter.excludeRepoProjectIds,
        },
      })
    }

    const records = await this.prisma.analysis_new_material_usage.findMany({
      where: {
        end_scan_id: scanId,
        business: filter.business,
        AND: optionalConditions,
        status: RECORD_STATUS.EFFECT,
      },
      distinct: ['repo_project_id', 'file_path'],
    })

    const result: {
      projectId: number
      projectName: string
      materialUsage: LibraryMaterialUsage
    }[] = []

    const { errors } = await PromiseAllFulfilled(
      records.map(async (record) => {
        const { repo_project_id, material_info } = await this.serialize(record)
        const projectIdx = result.findIndex(
          item => item.projectId === repo_project_id,
        )
        if (projectIdx === -1) {
          const repoInfo
            = await this.repoMetaService.findOneByProjectId(repo_project_id)
          result.push({
            projectId: repo_project_id,
            projectName: repoInfo?.name,
            materialUsage: material_info,
          })
        }
        else {
          result[projectIdx].materialUsage
            = mergeLibraryMaterialUsageWithoutClone(
              result[projectIdx].materialUsage,
              material_info,
            )
        }
      }),
    )

    errors.forEach((error) => {
      this.logger.warn(
        `Failed to find all material usage by scan: ${error.message}`,
      )
    })

    return result
  }

  static clearUselessMaterialInfoWithoutClone(
    libraryMaterialUsage: LibraryMaterialUsage,
  ): LibraryMaterialUsage {
    for (const [library, materialUsage] of Object.entries(
      libraryMaterialUsage,
    )) {
      for (const [materialName, count] of Object.entries(materialUsage)) {
        if (count <= 0) {
          delete libraryMaterialUsage[library][materialName]
        }
      }
      if (Object.keys(libraryMaterialUsage[library]).length === 0) {
        delete libraryMaterialUsage[library]
      }
    }
    return libraryMaterialUsage
  }
}
