import { Controller, Get, Query } from '@nestjs/common'
import { NewMaterialUsageService } from './new-material-usage.service'
import { FindProjectNewMaterialUsageDto } from './dto/find-new-project-material-usage.dto'
import { ApiTags } from '@nestjs/swagger'
import { PromiseAllFulfilledResult } from '@/tools/Promise'

@ApiTags('analysis/new-material-usage')
@Controller('analysis/new-material-usage')
export class NewMaterialUsageController {
  constructor(
    private readonly newMaterialUsageService: NewMaterialUsageService,
  ) {}

  @Get('/project/files')
  findProjectMaterialUsageByFiles(
    @Query()
    query: FindProjectNewMaterialUsageDto,
  ): Promise<PromiseAllFulfilledResult<Service.Analysis.NewMaterialUsage.SerializeAnalysisNewMaterialUsage>> {
    return this.newMaterialUsageService.findProjectNewMaterialUsageGroupByFilePath(
      query,
    )
  }
}
