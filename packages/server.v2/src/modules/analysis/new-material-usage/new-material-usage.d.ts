declare namespace Service.Analysis.NewMaterialUsage {
  export type NewMaterialUsageRecord =
    import('@prisma/client').analysis_new_material_usage

  export type SerializeAnalysisNewMaterialUsage =
    import('type-fest').OverrideProperties<
      import('@prisma/client').analysis_new_material_usage,
      {
        material_info: import('@ks-material-middleoffice/measure-sdk').LibraryMaterialUsage
      }
    >

  export type CreateParams = Required<
    Omit<
      import('@prisma/client').analysis_new_material_usage,
      'id' | 'status' | 'create_time'
    >
  >

  export interface FindProjectNewMaterialUsageParams {
    scanId: number
    repoProjectId: number
  }

  export type BusinessNewMaterialUsageReport =
    Service.Analysis.MaterialUsage.BusinessMaterialUsageReport
}
