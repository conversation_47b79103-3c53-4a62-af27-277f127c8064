import { Module } from '@nestjs/common'
import { NewMaterialUsageController } from './new-material-usage.controller'
import { NewMaterialUsageService } from './new-material-usage.service'
import { RepoMetaModule } from '../repo-meta/repo-meta.module'

@Module({
  imports: [RepoMetaModule],
  controllers: [NewMaterialUsageController],
  providers: [NewMaterialUsageService],
  exports: [NewMaterialUsageService],
})
export class NewMaterialUsageModule {}
