import type { JsonValue } from 'type-fest'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsIn, IsNotEmpty, IsNumber, IsString } from 'class-validator'

import { SOURCE_CODE_BUSINESS, SOURCE_CODE_BUSINESS_TYPES } from '@/shared'

export class CreateResultParamsDTO
implements Service.Analysis.Result.CreateResultParams {
  @ApiProperty({ description: '扫描ID' })
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  scanId: number

  @ApiProperty({ description: '业务线' })
  @IsNotEmpty()
  @IsIn([
    SOURCE_CODE_BUSINESS.BIZ,
    SOURCE_CODE_BUSINESS.ES,
    SOURCE_CODE_BUSINESS.LOCAL_LIFE,
    SOURCE_CODE_BUSINESS.LOCAL_LIFE_CLIENT,
  ])
  business: SOURCE_CODE_BUSINESS_TYPES

  @ApiProperty({ description: '分析结果类型' })
  @IsNotEmpty()
  @IsString()
  type: string

  @ApiProperty({ description: '分析结果值' })
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  value: number

  @ApiProperty({ description: '分析结果内容' })
  content?: Record<string, JsonValue>
}
