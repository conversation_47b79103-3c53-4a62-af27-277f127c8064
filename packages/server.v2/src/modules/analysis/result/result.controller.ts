import type { JsonObject } from 'type-fest'
import { ApiTags } from '@nestjs/swagger'
import { Body, Controller, Get, Post } from '@nestjs/common'

import { INDICATOR_TYPE, MATERIAL_POINTS_BASE_INDEX_TYPE } from '@/shared'

import { CreateResultParamsDTO } from './dto/create-result-params.dto'
import { ResultService } from './result.service'
import { ScanInfoService } from '../scan-info/scan-info.service'

@ApiTags('analysis/result')
@Controller('analysis/result')
export class ResultController {
  constructor(
    private readonly resultService: ResultService,
    private readonly scanInfoService: ScanInfoService,
  ) {}

  @Post('create')
  async createResult(@Body() params: CreateResultParamsDTO): Promise<Service.Analysis.Result.ResultRecord> {
    return await this.resultService.createResult(params)
  }

  @Get('new-code-coverage')
  async findNewCodeCoverage(): Promise<(Service.Analysis.ScanInfo.SerializeAnalysisScanInfo & {
    indicators: {
      business: string
      value: number
      content: JsonObject
    }[]
  })[]> {
    const scanIds = (
      await this.scanInfoService.findAllByTypeOrderByStartTime(
        MATERIAL_POINTS_BASE_INDEX_TYPE,
      )
    ).list.map(scan => scan.id)
    return await this.resultService.findList({
      scanIds: scanIds,
      indicatorType: INDICATOR_TYPE.NEW_CODE_COVERAGE,
    })
  }
}
