import assert from 'assert'
import { JsonObject } from 'type-fest'
import { Prisma } from '@prisma/client'
import { Injectable } from '@nestjs/common'

import { now } from '@/shared'
import { PrismaClientInstance } from '@/tools/prisma'
import { genPrimaryIndex } from '@/tools/prisma'
import { RECORD_STATUS } from '@/constants/status'

import { ScanInfoService } from '../scan-info/scan-info.service'

@Injectable()
export class ResultService {
  private readonly prisma = PrismaClientInstance
  constructor(private readonly scanInfoService: ScanInfoService) {}

  async createResult(params: Service.Analysis.Result.CreateResultParams): Promise<Service.Analysis.Result.ResultRecord> {
    await this.deleteOne({
      scanId: params.scanId,
      type: params.type,
      business: params.business,
    })
    return await this.prisma.analysis_result.create({
      data: {
        id: genPrimaryIndex(),
        scan_id: params.scanId,
        business: params.business,
        type: params.type,
        value: params.value,
        content: JSON.stringify(params.content) ?? '{}',
        status: RECORD_STATUS.EFFECT,
        create_time: now(),
      },
    })
  }

  async deleteOne(params: { scanId: number, type: string, business: string }): Promise<Prisma.BatchPayload> {
    return await this.prisma.analysis_result.updateMany({
      where: {
        scan_id: params.scanId,
        type: params.type,
        business: params.business,
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async findList(params: Service.Analysis.Result.FindListParams = {}): Promise<(Service.Analysis.ScanInfo.SerializeAnalysisScanInfo & {
    indicators: {
      business: string
      value: number
      content: JsonObject
    }[]
  })[]> {
    assert(params.scanIds, 'must provide scanIds')
    const scanIds = Array.isArray(params.scanIds)
      ? params.scanIds
      : [params.scanIds]

    const { list: scanInfos }
      = await this.scanInfoService.findListByIds(scanIds)
    const records = await this.prisma.analysis_result.findMany({
      where: {
        scan_id: {
          in: scanIds,
        },
        type: params.indicatorType,
        status: RECORD_STATUS.EFFECT,
      },
    })

    return scanInfos
      .map((scanInfo) => {
        const indicators = records.filter(
          record => record.scan_id === scanInfo.id,
        )
        return {
          ...scanInfo,
          indicators: indicators.map(indicator => ({
            business: indicator.business,
            value: indicator.value ?? 0,
            content: JSON.parse(indicator.content),
          })),
        }
      })
      .sort((a, b) => a.start_time - b.start_time)
  }
}
