import dayjs from 'dayjs'
import assert from 'assert'
import { resolve } from 'path'
import workerpool from 'workerpool'
import { OverrideProperties } from 'type-fest'
import { existsSync, mkdirSync, rmSync } from 'fs'
import { execSync } from 'child_process'
import { Injectable } from '@nestjs/common'
import { stringify } from 'git-url-parse'
import {
  APPModeBootstrapArguments,
  APPModeReport,
  LibraryModeBootstrapArguments,
  LibraryModeReport,
} from '@ks-material-middleoffice/measure-sdk'

import { omitNullableValues } from '@/tools/object'
import { isValidGitUrl, reasonableGitUrlParse } from '@/tools/git'
import { loggerInstance } from '@/tools/winston'
import {
  CLOUD_STORAGE_ROOT,
  PROJECT_ROOT,
  SERVICE_ROOT,
} from '@/constants/path'
import { ALL_REPO_TYPE, DateParam, REPO_TYPE } from '@/shared'
import { logStringify } from '@/tools/json'
import { IS_ON_SERVER } from '@/constants/envs'

@Injectable()
export class ScanService {
  private readonly logger = loggerInstance
  static SCAN_PROJECTS_DIR = IS_ON_SERVER
    ? resolve(CLOUD_STORAGE_ROOT, 'projects')
    : resolve(PROJECT_ROOT, 'projects')

  async scanProject<T extends REPO_TYPE>(
    type: T,
    gitURL: string,
    startDate: DateParam,
    endDate: DateParam,
    partialOptions?: {
      branch?: string
      entry?: string[] | string
      blackList?: string[]
      pkgFilter?: string
      fileFilter?: string
      blackPkgs?: string[]
    },
  ): Promise<T extends REPO_TYPE.MATERIAL ? LibraryModeReport : APPModeReport> {
    assert(type, 'repo type is required')
    assert(ALL_REPO_TYPE.includes(type), 'repo type is invalid')
    assert(gitURL, 'gitURL is required')
    assert(isValidGitUrl(gitURL), 'gitURL is invalid')
    assert(startDate, 'start time is required')
    assert(endDate, 'end time is required')

    await this.cloneProject(gitURL)
    const namespace = reasonableGitUrlParse(gitURL).full_name
    let rawData
    if (type === REPO_TYPE.MATERIAL) {
      rawData = await this.execSDKToScanProject(type, {
        cwd: resolve(ScanService.SCAN_PROJECTS_DIR, namespace),
        entries: partialOptions?.entry,
        sinceDate: dayjs(startDate).format('YYYY-MM-DD'),
        endDate: dayjs(endDate).format('YYYY-MM-DD'),
        packageFilter: partialOptions?.pkgFilter,
        blacklist: partialOptions?.blackList,
        branch: partialOptions?.branch,
        blackPkgs: [],
      } as OverrideProperties<
        LibraryModeBootstrapArguments,
        { packageFilter?: string, fileFilter?: string }
      > & {
        blackPkgs?: string[]
      })
    }
    else {
      rawData = await this.execSDKToScanProject(type, {
        cwd: resolve(ScanService.SCAN_PROJECTS_DIR, namespace),
        query: partialOptions?.entry,
        sinceDate: dayjs(startDate).format('YYYY-MM-DD'),
        endDate: dayjs(endDate).format('YYYY-MM-DD'),
        packageFilter: partialOptions?.pkgFilter,
        blacklist: partialOptions?.blackList,
        branch: partialOptions?.branch,
        blackPkgs: partialOptions?.blackPkgs,
      } as OverrideProperties<
        APPModeBootstrapArguments,
        { packageFilter?: string, fileFilter?: string }
      > & {
        blackPkgs?: string[]
      })
    }

    return rawData
  }

  static GIT_TOKEN
    = 'oauth:' + Buffer.from('VVhtUFMxZXhVVU43N3VqbVR5UDE=', 'base64').toString()

  async cloneProject(gitURL: string): Promise<void> {
    assert(gitURL, 'gitURL is required')
    assert(isValidGitUrl(gitURL), 'gitURL is invalid')
    const httpGitURL = reasonableGitUrlParse(gitURL).toString('https')
    const parsedObject = reasonableGitUrlParse(httpGitURL)

    const namespace = parsedObject.full_name
    const projectPath = resolve(ScanService.SCAN_PROJECTS_DIR, namespace)
    const addedAuthURL = stringify({
      ...parsedObject,
      token: ScanService.GIT_TOKEN,
    })
    if (existsSync(projectPath)) {
      const log = execSync('git fetch origin', {
        cwd: projectPath,
        stdio: 'inherit',
      })
      rmSync(resolve(projectPath, './.git/index.lock'), { force: true })
      this.logger.info(`git fetch origin: ${log}`)
    }
    else {
      const dir = resolve(projectPath, '..')
      mkdirSync(dir, { recursive: true })
      const log = execSync(`git clone ${addedAuthURL}`, { cwd: dir })
      this.logger.info(`git clone ${addedAuthURL}: ${log}`)
    }
  }

  static LIBRARY_WORKER_SCRIPT_PATH = resolve(
    SERVICE_ROOT,
    './src/workers/library-scan-worker.cjs',
  )

  static APP_WORKER_SCRIPT_PATH = resolve(
    SERVICE_ROOT,
    './src/workers/app-scan-worker.cjs',
  )

  async execSDKToScanProject<T extends REPO_TYPE>(
    type: T,
    params: (T extends REPO_TYPE.MATERIAL
      ? OverrideProperties<
        LibraryModeBootstrapArguments,
        { packageFilter?: string, fileFilter?: string }
      >
      : OverrideProperties<
        APPModeBootstrapArguments,
        { packageFilter?: string, fileFilter?: string }
      >) & {
        blackPkgs?: string[]
      },
  ): Promise<T extends REPO_TYPE.MATERIAL ? LibraryModeReport : APPModeReport> {
    const pool = workerpool.pool(
      type === REPO_TYPE.MATERIAL
        ? ScanService.LIBRARY_WORKER_SCRIPT_PATH
        : ScanService.APP_WORKER_SCRIPT_PATH,
      {
        workerType: 'process',
      },
    )

    this.logger.info(`scan project: ${logStringify({ type, params })}`)

    try {
      const result = await pool.exec('bootstrap', [
        omitNullableValues({ ...params }),
      ])
      pool.terminate()
      return Object.assign(result, {
        cliParams: params,
      })
    }
    catch (err) {
      pool.terminate()
      throw new Error(
        `Scan project encountered an error: ${logStringify({
          type,
          params,
        })}\n${err}`,
      )
    }
  }
}
