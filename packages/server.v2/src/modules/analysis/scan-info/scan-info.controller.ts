import { Prisma } from '@prisma/client'
import { ApiTags } from '@nestjs/swagger'
import { Controller, Get, Param, Post, Query } from '@nestjs/common'

import {
  SOURCE_CODE_BUSINESS,
  DateParam,
  getWeekRange,
  MATERIAL_POINTS_BASE_INDEX_TYPE,
} from '@/shared'

import { ScanInfoService } from './scan-info.service'

@ApiTags('analysis/scan-info')
@Controller('analysis/scan-info')
export class ScanInfoController {
  constructor(private readonly scanInfoService: ScanInfoService) {}

  @Get('detail')
  async getScanInfoById(@Query('id') id: string): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo | null> {
    return this.scanInfoService.findOneById(+id)
  }

  @Get('type/:type')
  async getScanInfosByTypeOrderByTime(@Param('type') type: string): Promise<{
    list: Service.Analysis.ScanInfo.SerializeAnalysisScanInfo[]
    total: number
    errors: Error[]
  }> {
    return this.scanInfoService.findAllByTypeOrderByStartTime(type)
  }

  @Post('create-or-update')
  async createOrUpdateScanInfo(params: {
    type?: string
    time?: DateParam
    businesses: SOURCE_CODE_BUSINESS[]
  }): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo> {
    const {
      time,
      type = 'test',
      businesses = [
        SOURCE_CODE_BUSINESS.BIZ,
        SOURCE_CODE_BUSINESS.ES,
        SOURCE_CODE_BUSINESS.LOCAL_LIFE,
        SOURCE_CODE_BUSINESS.LOCAL_LIFE_CLIENT,
      ],
    } = params ?? {}
    const { start, end } = getWeekRange(time)
    return this.scanInfoService.createOrUpdate({
      type,
      startTime: start,
      endTime: end,
      businesses,
    })
  }

  @Get('last-one')
  async getLastScanInfo(@Query('scanId') scanId: string): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo | null> {
    return this.scanInfoService.findLastOne(+scanId)
  }

  @Get('range')
  async getScanInfosByTimeRange(
    @Query()
    queries: {
      startTime?: number
      endTime?: number
      type?: string
    },
  ): Promise<{
      list: Service.Analysis.ScanInfo.SerializeAnalysisScanInfo[]
      total: number
      errors: Error[]
    }> {
    const {
      startTime,
      endTime,
      type = MATERIAL_POINTS_BASE_INDEX_TYPE,
    } = queries
    return this.scanInfoService.findByTimeRange({
      startTime: +startTime,
      endTime: +endTime,
      type,
    })
  }

  @Post('clear-useless-scan-info')
  async clearUselessScanInfo(): Promise<Prisma.BatchPayload> {
    return this.scanInfoService.clearUselessScanInfo()
  }
}
