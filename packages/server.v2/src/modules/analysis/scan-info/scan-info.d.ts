declare namespace Service.Analysis.ScanInfo {
  export type ScanInfoRecord = import('@prisma/client').analysis_scan_info
  export type SerializeAnalysisScanInfo =
    import('type-fest').OverrideProperties<
      ScanInfoRecord,
      { scan_range: string[] }
    >

  export interface CreateParams {
    type: string
    startTime: number
    endTime: number
    businesses: import('@/constants/business').SOURCE_CODE_BUSINESS[]
  }
}
