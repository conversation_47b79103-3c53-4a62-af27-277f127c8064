import dayjs from 'dayjs'
import { Injectable } from '@nestjs/common'
import { analysis_scan_info, Prisma } from '@prisma/client'

import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { PromiseAllFulfilled } from '@/tools/Promise'
import { RECORD_STATUS } from '@/constants/status'
import { MATERIAL_POINTS_BASE_INDEX_TYPE } from '@/shared'
import { logStringify } from '@/tools/json'
import { isArray } from '@/tools/type'

@Injectable()
export class ScanInfoService {
  private readonly prisma = PrismaClientInstance

  private async serialize(
    scanInfo: analysis_scan_info,
  ): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo> {
    try {
      return Object.freeze({
        ...scanInfo,
        scan_range: JSON.parse(scanInfo.scan_range || '[]') as string[],
      })
    }
    catch (error) {
      throw new Error(
        `Failed to serialize scan info: ${logStringify({ rawDataId: scanInfo.id, error: error.toString() })}`,
      )
    }
  }

  async create(params: Service.Analysis.ScanInfo.CreateParams): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo> {
    const { type, startTime, endTime, businesses } = params
    const record = await this.prisma.analysis_scan_info.create({
      data: {
        id: genPrimaryIndex(),
        type,
        start_time: startTime,
        end_time: endTime,
        scan_range: JSON.stringify(businesses),
        create_time: Date.now(),
        display_txt: `${dayjs(startTime).format('YYYY-MM-DD')} ~ ${dayjs(endTime).format('YYYY-MM-DD')}`,
      },
    })

    return this.serialize(record)
  }

  async update(id: number, data: Partial<analysis_scan_info>): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo> {
    const record = await this.prisma.analysis_scan_info.update({
      where: { id },
      data,
    })

    return this.serialize(record)
  }

  async findOneById(
    id: number,
  ): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo | null> {
    const record = await this.prisma.analysis_scan_info.findUniqueOrThrow({
      where: { id, status: RECORD_STATUS.EFFECT },
    })
    return this.serialize(record)
  }

  async findLastOne(scanId: number): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo | null> {
    const current = await this.findOneById(scanId)
    const record = await this.prisma.analysis_scan_info.findFirst({
      where: {
        type: current.type,
        start_time: { lt: current.start_time },
        status: RECORD_STATUS.EFFECT,
      },
      orderBy: { start_time: 'desc' },
    })
    return record ? this.serialize(record) : null
  }

  async findOneByStartTimeAndType(params: {
    startTime: number
    type: string
  }): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo> {
    const record = await this.prisma.analysis_scan_info.findFirstOrThrow({
      where: {
        start_time: params.startTime,
        type: params.type,
        status: RECORD_STATUS.EFFECT,
      },
    })
    return this.serialize(record)
  }

  async findAllByTypeOrderByStartTime(type: string): Promise<{
    list: Service.Analysis.ScanInfo.SerializeAnalysisScanInfo[]
    total: number
    errors: Error[]
  }> {
    const records = await this.prisma.analysis_scan_info.findMany({
      where: { type, status: RECORD_STATUS.EFFECT },
      orderBy: { start_time: 'desc' },
    })

    const { result, errors } = await PromiseAllFulfilled(
      records.map(this.serialize),
    )

    return {
      list: result,
      total: result.length,
      errors,
    }
  }

  async findOneByTypeAndTime(params: {
    type: string
    startTime: number
    endTime: number
  }): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo> {
    const record = await this.prisma.analysis_scan_info.findFirstOrThrow({
      where: {
        type: params.type,
        start_time: params.startTime,
        end_time: params.endTime,
        status: RECORD_STATUS.EFFECT,
      },
    })

    return this.serialize(record)
  }

  async findNewestScanInfoByType(
    type: string = MATERIAL_POINTS_BASE_INDEX_TYPE,
  ): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo | null> {
    const record = await this.prisma.analysis_scan_info.findFirst({
      where: {
        type,
        status: RECORD_STATUS.EFFECT,
      },
      orderBy: { start_time: 'desc' },
    })

    return record ? this.serialize(record) : null
  }

  async createOrUpdate(params: Service.Analysis.ScanInfo.CreateParams): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo> {
    const { type, startTime, endTime, businesses } = params
    const record = await this.findOneByTypeAndTime({
      type,
      startTime,
      endTime,
    }).catch(() => null)

    if (record) {
      const mergedBusinesses = [
        ...new Set([...record.scan_range, ...businesses]),
      ]
      return this.update(record.id, {
        scan_range: JSON.stringify(mergedBusinesses),
      })
    }

    return this.create({
      type,
      startTime,
      endTime,
      businesses,
    })
  }

  async findListByIds(ids: number[]): Promise<{
    list: Service.Analysis.ScanInfo.SerializeAnalysisScanInfo[]
    total: number
    errors: Error[]
  }> {
    const records = await this.prisma.analysis_scan_info.findMany({
      where: {
        id: {
          in: ids,
        },
        status: RECORD_STATUS.EFFECT,
      },
    })

    const { result, errors } = await PromiseAllFulfilled(
      records.map(this.serialize),
    )

    return {
      list: result,
      total: result.length,
      errors,
    }
  }

  async findMaterialPointsBaseScanList(): Promise<{
    list: Service.Analysis.ScanInfo.SerializeAnalysisScanInfo[]
    total: number
    errors: Error[]
  }> {
    return this.findAllByTypeOrderByStartTime(MATERIAL_POINTS_BASE_INDEX_TYPE)
  }

  // 获取指定时间范围内的扫描信息，会将开始时间前的第一个扫描信息和结束时间后的最后一个扫描信息都返回
  async findByTimeRange({
    startTime,
    endTime,
    type,
  }: {
    startTime?: number
    endTime?: number
    type?: string
  }): Promise<{
      list: Service.Analysis.ScanInfo.SerializeAnalysisScanInfo[]
      total: number
      errors: Error[]
    }> {
    const whereClause: Prisma.analysis_scan_infoWhereInput = {
      AND: [],
      status: RECORD_STATUS.EFFECT,
      type,
    }

    if (startTime && isArray(whereClause.AND)) {
      whereClause.AND.push({ start_time: { gte: startTime } })
    }

    if (endTime && isArray(whereClause.AND)) {
      whereClause.AND.push({ end_time: { lte: endTime } })
    }

    const records = await this.prisma.analysis_scan_info.findMany({
      where: whereClause,
      orderBy: { start_time: 'asc' },
    })

    const { result, errors } = await PromiseAllFulfilled(
      records.map(this.serialize),
    )

    return {
      list: result,
      total: result.length,
      errors,
    }
  }

  async clearUselessScanInfo(): Promise<Prisma.BatchPayload> {
    const { list } = await this.findMaterialPointsBaseScanList()
    const needClearRecords = list.filter((item) => {
      return (
        item.scan_range.length < 4
        || !item.start_time
        || !item.end_time
        || item.display_txt.startsWith('Invalid Date')
      )
    })
    return this.prisma.analysis_scan_info.updateMany({
      where: {
        id: {
          in: needClearRecords.map(item => item.id),
        },
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }
}
