import { ApiTags } from '@nestjs/swagger'
import { Controller, Get, Post, Query } from '@nestjs/common'

import { RepoMetaService } from './repo-meta.service'

@ApiTags('analysis/repo-meta')
@Controller('analysis/repo-meta')
export class RepoMetaController {
  constructor(private readonly repoMetaService: RepoMetaService) {}

  @Get()
  async findOne(@Query() params: Service.Analysis.RepoMeta.FindOneParams): Promise<Service.Analysis.RepoMeta.RepoMetaRecord | null> {
    return this.repoMetaService.findOne({
      id: params?.id ? +params.id : undefined,
      project_id: params?.project_id ? +params.project_id : undefined,
    })
  }

  @Get('list')
  async findAll(
    @Query() params: Service.Analysis.RepoMeta.ListParams,
  ): Promise<Service.Analysis.RepoMeta.FindAllResult> {
    return this.repoMetaService.findAll(params)
  }

  @Post('refresh-clone-url')
  async refreshCloneURL(): Promise<{
    refreshedRecords: number[]
    errors: Error[]
  }> {
    return this.repoMetaService.refreshRepoURL2HTTP()
  }
}
