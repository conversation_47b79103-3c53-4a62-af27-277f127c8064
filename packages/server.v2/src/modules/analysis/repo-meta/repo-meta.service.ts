import assert from 'assert'
import { Injectable } from '@nestjs/common'

import { SOURCE_CODE_BUSINESS, now } from '@/shared'
import { RECORD_STATUS } from '@/constants/status'
import { PromiseAllFulfilled } from '@/tools/Promise'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'

import { GitlabService } from '@/modules/gitlab/gitlab.service'
import { reasonableGitUrlParse } from '@/tools/git'
import { Prisma } from '@prisma/client'

@Injectable()
export class RepoMetaService {
  private readonly prisma = PrismaClientInstance
  constructor(private readonly gitlabService: GitlabService) {}

  static judgeRepoBusiness(cloneURL: string): SOURCE_CODE_BUSINESS | 'UNKNOWN' {
    const { full_name } = reasonableGitUrlParse(cloneURL)
    if (full_name.startsWith('locallife/client'))
      return SOURCE_CODE_BUSINESS.LOCAL_LIFE_CLIENT
    if (full_name.startsWith('plateco-dev-fe')) return SOURCE_CODE_BUSINESS.ES
    if (full_name.startsWith('locallife/lfe') || full_name.startsWith('lfe'))
      return SOURCE_CODE_BUSINESS.LOCAL_LIFE
    if (full_name.startsWith('ks-ad')) return SOURCE_CODE_BUSINESS.BIZ

    return 'UNKNOWN'
  }

  async refreshRepoURL2HTTP(): Promise<{
    refreshedRecords: number[]
    errors: Error[]
  }> {
    const records = await this.prisma.analysis_repo_meta.findMany()
    const refreshedRecords: number[] = []
    const { errors } = await PromiseAllFulfilled(
      records.map(async (record) => {
        const { id } = record
        let { clone_url } = record
        clone_url = clone_url.trim()
        const parseToHTTPs = reasonableGitUrlParse(clone_url).toString('https')
        if (parseToHTTPs !== clone_url) {
          refreshedRecords.push(id)
          await this.prisma.analysis_repo_meta.update({
            where: { id },
            data: { clone_url: parseToHTTPs },
          })
        }
      }),
    )
    return { refreshedRecords, errors }
  }

  async findOne(params: Partial<Service.Analysis.RepoMeta.RepoMetaRecord>): Promise<Service.Analysis.RepoMeta.RepoMetaRecord | null> {
    const record = await this.prisma.analysis_repo_meta.findFirst({
      where: { status: RECORD_STATUS.EFFECT, ...params },
    })
    return record
  }

  async findOneByProjectId(projectId: number): Promise<Service.Analysis.RepoMeta.RepoMetaRecord> {
    const record = await this.prisma.analysis_repo_meta.findFirstOrThrow({
      where: { project_id: projectId, status: RECORD_STATUS.EFFECT },
    })
    return record
  }

  async findListByProjectIds(projectIds: number[]): Promise<Service.Analysis.RepoMeta.RepoMetaRecord[]> {
    const records = await this.prisma.analysis_repo_meta.findMany({
      where: { project_id: { in: projectIds }, status: RECORD_STATUS.EFFECT },
    })
    return records
  }

  async findOneFromHistoryByCloneURL(cloneURL: string): Promise<Service.Analysis.RepoMeta.RepoMetaRecord | null> {
    const record = await this.prisma.analysis_repo_meta.findFirst({
      where: { clone_url: cloneURL },
    })

    return record
  }

  async findOrCreateRepoMeta(params: Service.Analysis.RepoMeta.CreateParams): Promise<Service.Analysis.RepoMeta.RepoMetaRecord> {
    // ======================== 入参处理 ========================
    assert(params.clone_url, 'clone_url is required')
    const {
      clone_url,
      business = RepoMetaService.judgeRepoBusiness(clone_url),
    } = params
    const httpsCloneURL = reasonableGitUrlParse(clone_url).toString('https')
    if (business === 'UNKNOWN')
      throw new Error(`Unknown business for clone_url: ${httpsCloneURL}`)

    // ======================== 查找 project id ========================
    let oldProjectId: number
    const oldRecord = await this.findOneFromHistoryByCloneURL(httpsCloneURL)
    if (oldRecord) {
      oldProjectId = oldRecord.project_id
    }
    let gitlabReturnedId: number

    try {
      gitlabReturnedId
        = await this.gitlabService.findProjectIdByCloneURL(httpsCloneURL)
    }
    catch (_) {
      gitlabReturnedId = undefined
    }

    if (!gitlabReturnedId && !oldProjectId) {
      throw new Error(
        `Failed to find project id for clone_url: ${httpsCloneURL}`,
      )
    }

    // ======================== 是否已有 ========================
    if (gitlabReturnedId && !oldRecord) {
      await this.deleteRepoMetaByProjectId(gitlabReturnedId)
      return this.prisma.analysis_repo_meta.create({
        data: {
          id: genPrimaryIndex(),
          project_id: gitlabReturnedId,
          business,
          clone_url: httpsCloneURL,
          name: reasonableGitUrlParse(clone_url).name,
          create_time: now(),
          status: RECORD_STATUS.EFFECT,
        },
      })
    }

    return oldRecord
  }

  async deleteRepoMetaByProjectId(projectId: number): Promise<Prisma.BatchPayload> {
    return this.prisma.analysis_repo_meta.updateMany({
      where: { project_id: projectId, status: RECORD_STATUS.EFFECT },
      data: { status: RECORD_STATUS.DELETE },
    })
  }

  async findRepoInfoWithProjectIds(projectIds: number[]): Promise<Service.Analysis.RepoMeta.RepoMetaRecord[]> {
    return this.prisma.analysis_repo_meta.findMany({
      where: { project_id: { in: projectIds }, status: RECORD_STATUS.EFFECT },
    })
  }

  async findAll(params: Service.Analysis.RepoMeta.ListParams): Promise<Service.Analysis.RepoMeta.FindAllResult> {
    const { query, page = 1, pageSize = 10 } = params
    const where: Prisma.analysis_repo_metaWhereInput = {
      status: RECORD_STATUS.EFFECT,
    }

    if (query) {
      where.OR = [
        { name: { contains: query } },
        { clone_url: { contains: query } },
      ]
    }

    const totalCount = await this.prisma.analysis_repo_meta.count({ where })
    const totalPages = Math.ceil(totalCount / pageSize)

    const records = await this.prisma.analysis_repo_meta.findMany({
      where,
      orderBy: { create_time: 'desc' },
      skip: (page - 1) * pageSize,
      take: +pageSize,
    })

    return {
      data: records,
      pagination: {
        currentPage: page,
        pageSize,
        totalCount,
        totalPages,
      },
    }
  }
}
