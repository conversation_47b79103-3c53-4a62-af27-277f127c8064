declare namespace Service.Analysis.RepoMeta {
  export type RepoMetaRecord = import('@prisma/client').analysis_repo_meta

  export type CreateParams = Required<Pick<RepoMetaRecord, 'clone_url'>> &
    Partial<Pick<RepoMetaRecord, 'business'>>

  export type FindOneParams = Partial<
    Pick<
      RepoMetaRecord,
      'id' | 'project_id' | 'clone_url' | 'name' | 'business'
    >
  >

  export interface ListParams extends Service.__OldPaginationParams {
    query?: string
  }

  export interface FindAllResult {
    data: RepoMetaRecord[]
    pagination: Service.__OldPaginationInfo
  }

  export type FindOneResult = RepoMetaRecord

  export interface RefreshCloneURLResult {
    refreshedRecords: number[]
    errors: Error[]
  }
}
