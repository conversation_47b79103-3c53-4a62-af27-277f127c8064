declare namespace Service.Analysis {
  export interface FullProcessAnalysisParams {
    time?: import('@/tools/date').DateParam
    tag?: string
    businesses?: import('@/constants/business').SOURCE_CODE_BUSINESS[]
    repoTypes?: import('@/constants/repo-type').REPO_TYPE[]
  }

  export interface ScanOneProjectParams<
    T extends import('@/constants/repo-type').REPO_TYPE,
  > {
    type: T
    gitURL: string
    startDate: import('@/tools/date').DateParam
    endDate: import('@/tools/date').DateParam
    partialOptions?: {
      branch?: string
      entry?: string[] | string
      blackList?: string[]
      pkgFilter?: string
    }
  }
}
