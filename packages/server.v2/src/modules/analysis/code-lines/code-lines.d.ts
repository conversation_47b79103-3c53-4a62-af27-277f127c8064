declare namespace Service.Analysis.CodeLines {
  export type CodeLinesRecord = import('@prisma/client').analysis_code_lines

  export type CreateParams = Required<
    Omit<
      import('@prisma/client').analysis_code_lines,
      'id' | 'status' | 'create_time'
    >
  >

  export interface FindOneCodeLinesRecordParams {
    scanId: number
    repoProjectId: number
  }

  export interface FindListCodeLinesRecordParams {
    scanId?: number
    repoProjectId?: number
  }
}
