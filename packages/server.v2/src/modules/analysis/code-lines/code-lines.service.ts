import assert from 'assert'
import { Injectable } from '@nestjs/common'

import { RECORD_STATUS } from '@/constants/status'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { SOURCE_CODE_BUSINESS, now } from '@/shared'
import { chunkArray } from '@/tools/array'
import { limitInvokePromiseAllFulfilled, PromiseAllFulfilledResult } from '@/tools/Promise'
import { Prisma } from '@prisma/client'

@Injectable()
export class CodeLinesService {
  private readonly prisma = PrismaClientInstance

  async create(params: Service.Analysis.CodeLines.CreateParams): Promise<Service.Analysis.CodeLines.CodeLinesRecord> {
    return this.prisma.analysis_code_lines.create({
      data: {
        ...params,
        id: genPrimaryIndex(),
        create_time: now(),
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async batchCreate(params: Service.Analysis.CodeLines.CreateParams[]): Promise<PromiseAllFulfilledResult<Prisma.BatchPayload>> {
    const create_time = now()
    const chunkedParamsList = chunkArray(params, 20)
    return limitInvokePromiseAllFulfilled(
      chunkedParamsList.map(
        chunkedParams => async () =>
          this.prisma.analysis_code_lines.createMany({
            data: chunkedParams.map(param => ({
              ...param,
              id: genPrimaryIndex(),
              create_time,
              status: RECORD_STATUS.EFFECT,
            })),
          }),
      ),
    )
  }

  async batchDelete(params: { scanId: number, businesses: SOURCE_CODE_BUSINESS[] }): Promise<Prisma.BatchPayload> {
    return this.prisma.analysis_code_lines.updateMany({
      where: {
        scan_id: params.scanId,
        business: {
          in: params.businesses,
        },
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async findOne(
    params: Service.Analysis.CodeLines.FindOneCodeLinesRecordParams,
  ): Promise<Service.Analysis.CodeLines.CodeLinesRecord> | never {
    assert(params.scanId, '扫描ID不能为空')
    assert(params.repoProjectId, '仓库项目ID不能为空')
    const record = await this.prisma.analysis_code_lines.findFirstOrThrow({
      where: {
        scan_id: params.scanId,
        repo_project_id: params.repoProjectId,
        status: RECORD_STATUS.EFFECT,
      },
    })
    return record
  }

  async findList(
    params: Service.Analysis.CodeLines.FindListCodeLinesRecordParams,
  ): Promise<Service.Analysis.CodeLines.CodeLinesRecord[]> {
    const records = this.prisma.analysis_code_lines.findMany({
      where: {
        scan_id: params.scanId,
        repo_project_id: params.repoProjectId,
        status: RECORD_STATUS.EFFECT,
      },
    })
    return records
  }
}
