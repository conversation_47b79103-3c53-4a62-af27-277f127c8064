import { ApiTags } from '@nestjs/swagger'
import { Controller, Get, Query } from '@nestjs/common'

import { CodeLinesService } from './code-lines.service'
import { FindOneCodeLinesRecordDTO } from './dto/find-one-code-lines.dto'
import { FindListCodeLinesRecordDTO } from './dto/find-list-code-lies.dto'

@ApiTags('analysis/code-lines')
@Controller('code-lines')
export class CodeLinesController {
  constructor(private readonly codeLinesService: CodeLinesService) {}

  @Get()
  async findOne(@Query() query: FindOneCodeLinesRecordDTO): Promise<Service.Analysis.CodeLines.CodeLinesRecord | never> {
    return await this.codeLinesService.findOne(query)
  }

  @Get('list')
  async findList(@Query() query: FindListCodeLinesRecordDTO): Promise<Service.Analysis.CodeLines.CodeLinesRecord[]> {
    return await this.codeLinesService.findList(query)
  }
}
