import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsInt } from 'class-validator'

export class FindListCodeLinesRecordDTO
implements Service.Analysis.CodeLines.FindListCodeLinesRecordParams {
  @ApiProperty({ description: '扫描ID' })
  @IsInt()
  @Type(() => Number)
  scanId: number

  @ApiProperty({ description: '仓库项目ID' })
  @IsInt()
  @Type(() => Number)
  repoProjectId: number
}
