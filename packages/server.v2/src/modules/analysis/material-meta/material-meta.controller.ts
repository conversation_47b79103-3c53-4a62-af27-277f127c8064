import { Body, Controller, Post } from '@nestjs/common'
import { MaterialMetaService } from './material-meta.service'
import { SOURCE_CODE_BUSINESS } from '@/shared'

@Controller('analysis/material-meta')
export class MaterialMetaController {
  constructor(private readonly materialMetaService: MaterialMetaService) {}

  @Post('delete-records-in-batches')
  async deleteRecordsInBatches(
    @Body() body: { scanId: number, business: SOURCE_CODE_BUSINESS },
  ): Promise<number> {
    return this.materialMetaService.updateRecordsInBatches({
      scanId: body.scanId,
      businesses: [body.business],
    })
  }

  @Post('manually-calibrate-some-data')
  async manuallyCalibrateSomeData(): Promise<void> {
    return await this.materialMetaService.manuallyCalibrateSomeData()
  }
}
