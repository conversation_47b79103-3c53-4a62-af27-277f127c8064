import { Prisma } from '@prisma/client'
import { Injectable } from '@nestjs/common'
import { LibraryMaterialUsage } from '@ks-material-middleoffice/measure-sdk'

import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { loggerInstance } from '@/tools/winston'
import {
  limitInvokePromiseAllFulfilled,
  PromiseAllFulfilledResult,
  PromiseAllFulfilled,
} from '@/tools/Promise'
import { RECORD_STATUS } from '@/constants/status'
import { MUICodeLines } from '@/constants/code-lines/MUI'
import { MUIMobileCodeLines } from '@/constants/code-lines/MUI-mobile'
import { MChartPCCodeLines } from '@/constants/code-lines/MChartPC'
import {
  mergeLibraryMaterialUsageWithoutClone,
  multiplyLibraryMaterialUsage,
} from '@/tools/material-usage'
import { logStringify, safeStringifyMaybeNull } from '@/tools/json'
import { ALL_SOURCE_CODE_BUSINESS, SOURCE_CODE_BUSINESS, now } from '@/shared'
import { chunkArray } from '@/tools/array'
import { omit } from '@/tools/object'

@Injectable()
export class MaterialMetaService {
  private readonly prisma = PrismaClientInstance
  private readonly logger = loggerInstance

  async serialize(
    materialMeta: Service.Analysis.MaterialMeta.MaterialMetaRecord,
  ): Promise<Service.Analysis.MaterialMeta.SerializedMaterialMetaRecord> {
    try {
      return Object.freeze({
        ...materialMeta,
        third_usage: materialMeta.third_usage
          ? (JSON.parse(materialMeta.third_usage) as LibraryMaterialUsage)
          : null,
        filter_third_usage: materialMeta.filter_third_usage
          ? (JSON.parse(
            materialMeta.filter_third_usage,
          ) as LibraryMaterialUsage)
          : null,
      })
    }
    catch (error) {
      throw new Error(
        `Failed to serialize material meta record: ${error + logStringify(materialMeta)}`,
      )
    }
  }

  async create(
    param: Service.Analysis.MaterialMeta.CreateParams,
  ): Promise<Service.Analysis.MaterialMeta.MaterialMetaRecord> {
    return this.prisma.analysis_material_meta.create({
      data: {
        ...param,
        id: genPrimaryIndex(),
        type: 'detail',
        create_time: now(),
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async batchCreate(
    params: Service.Analysis.MaterialMeta.CreateParams[],
  ): Promise<PromiseAllFulfilledResult<Prisma.BatchPayload>> {
    const create_time = now()
    const chunkedParamsList = chunkArray(params, 20)
    return await limitInvokePromiseAllFulfilled(
      chunkedParamsList.map(
        param => async (): Promise<Prisma.BatchPayload> =>
          this.prisma.analysis_material_meta.createMany({
            data: param.map(item => ({
              ...item,
              id: genPrimaryIndex(),
              type: 'detail',
              create_time,
              status: RECORD_STATUS.EFFECT,
            })),
          }),
      ),
    )
  }

  async batchDelete(params: {
    scanId: number
    businesses: SOURCE_CODE_BUSINESS[]
  }): Promise<Prisma.BatchPayload> {
    return this.prisma.analysis_material_meta.updateMany({
      where: {
        scan_id: params.scanId,
        business: {
          in: params.businesses,
        },
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async updateRecordsInBatches(params: {
    scanId: number
    businesses: SOURCE_CODE_BUSINESS[]
  }): Promise<number> {
    const batchSize = 1000 // 设置每批处理的记录数
    let updatedCount = 0

    while (true) {
      // 分批查询需要更新的记录
      const records = await this.prisma.analysis_material_meta.findMany({
        where: {
          scan_id: params.scanId,
          business: {
            in: params.businesses,
          },
          status: RECORD_STATUS.EFFECT,
        },
        take: batchSize,
      })

      // 如果没有记录，结束循环
      if (records.length === 0) {
        break
      }

      // 更新这一批记录的状态
      const updateResult = await this.prisma.analysis_material_meta.updateMany({
        where: {
          id: { in: records.map(record => record.id) }, // 使用 id 过滤
        },
        data: {
          status: RECORD_STATUS.DELETE,
        },
      })

      // 累计已更新的记录数
      updatedCount += updateResult.count
    }

    return updatedCount // 返回更新的记录总数
  }

  async findAllByScanId(
    scanId,
    businesses: SOURCE_CODE_BUSINESS[] = ALL_SOURCE_CODE_BUSINESS,
  ): Promise<
      PromiseAllFulfilledResult<Service.Analysis.MaterialMeta.SerializedMaterialMetaRecord>
    > {
    const records = await this.prisma.analysis_material_meta.findMany({
      where: {
        scan_id: scanId,
        business: {
          in: businesses,
        },
        status: RECORD_STATUS.EFFECT,
      },
    })

    return PromiseAllFulfilled(records.map(this.serialize))
  }

  async findAllMaterialInfoByScanId(
    scanId: number,
  ): Promise<Service.Analysis.MaterialMeta.MaterialPackageReport[]> {
    const records = await this.prisma.analysis_material_meta.findMany({
      where: {
        scan_id: scanId,
        status: RECORD_STATUS.EFFECT,
      },
    })

    const result: Service.Analysis.MaterialMeta.MaterialPackageReport[] = []
    const { errors } = await PromiseAllFulfilled(
      records.map(async (record) => {
        const {
          package: packageName = '',
          material_name,
          business,
          code_lines,
          filter_third_usage,
        } = await this.serialize(record)
        const packageReport
          = result.find(item => item.packageName === packageName)
          || (result.push({
            scanId,
            business,
            packageName,
            materials: [],
          })
          && result[result.length - 1])
        const isInserted = !!packageReport.materials.find(
          material => material.materialName === material_name,
        )
        if (isInserted) return
        packageReport.materials.push({
          scanId,
          business,
          packageMaterialName: packageName,
          materialName: material_name,
          codeLines: code_lines,
          third_usage: filter_third_usage,
        })
      }),
    )

    errors.forEach((error) => {
      this.logger.warn(
        `Failed to find all material info by scan: ${error.message}`,
      )
    })

    return result
  }

  async syncMaterialMateToExportAllFromAnotherPackage(
    scanId: number,
    businesses: SOURCE_CODE_BUSINESS[] = ALL_SOURCE_CODE_BUSINESS,
  ): Promise<Service.Analysis.MaterialMeta.CreateParams[]> {
    const { result: currentScanAllMaterialMeta } = await this.findAllByScanId(
      scanId,
      businesses,
    )
    const exportedThirdPartyMaterialMeta = currentScanAllMaterialMeta.filter(
      item => item.material_name === '__ALL_FROM_THIRD_PARTY__',
    )
    const needAddMaterialMeta: Service.Analysis.MaterialMeta.CreateParams[]
      = []
    for (let i = 0; i < exportedThirdPartyMaterialMeta.length; i++) {
      const { file_path: targetMaterialPackage }
        = exportedThirdPartyMaterialMeta[i]
      const targetThirdPartyMaterials = currentScanAllMaterialMeta.filter(
        item =>
          item.package === targetMaterialPackage
          && item.material_name !== '__ALL_FROM_THIRD_PARTY__',
      )
      for (let j = 0; j < targetThirdPartyMaterials.length; j++) {
        const item = targetThirdPartyMaterials[j]
        needAddMaterialMeta.push({
          ...omit(item, ['id', 'create_time', 'status']),
          project_id: exportedThirdPartyMaterialMeta[i].project_id,
          type: exportedThirdPartyMaterialMeta[i].type,
          package: targetMaterialPackage,
          third_usage: safeStringifyMaybeNull(item.third_usage),
          filter_third_usage: safeStringifyMaybeNull(item.filter_third_usage),
        })
      }
    }
    await this.batchCreate(needAddMaterialMeta)
    return needAddMaterialMeta
  }

  async mergeThirdUsage(
    sourceMaterialUsage: LibraryMaterialUsage,
    thirdUsageOrScanId?:
      | Service.Analysis.MaterialMeta.MaterialPackageReport[]
      | number,
    specifyingMultiples?: number,
  ): Promise<LibraryMaterialUsage> {
    const thirdUsage = Array.isArray(thirdUsageOrScanId)
      ? thirdUsageOrScanId
      : await this.findAllMaterialInfoByScanId(thirdUsageOrScanId)

    let needMergedThirdUsage: LibraryMaterialUsage = {}

    const walkedMaterial: string[] = []

    for (const [library] of Object.entries(sourceMaterialUsage)) {
      const thirdUsageLibrary = thirdUsage.find(
        item => item.packageName === library,
      )
      if (!thirdUsageLibrary) continue
      for (const [material, useCount] of Object.entries(
        sourceMaterialUsage[library],
      )) {
        if (walkedMaterial.includes(`${library}/${material}`)) continue
        walkedMaterial.push(`${library}/${material}`)
        const thirdUsageMaterial = thirdUsageLibrary.materials.find(
          item => item.materialName === material,
        )
        if (!thirdUsageMaterial) continue
        needMergedThirdUsage = mergeLibraryMaterialUsageWithoutClone(
          needMergedThirdUsage,
          multiplyLibraryMaterialUsage(
            thirdUsageMaterial.third_usage,
            specifyingMultiples ?? useCount,
          ),
        )
      }
    }

    return mergeLibraryMaterialUsageWithoutClone(
      sourceMaterialUsage,
      needMergedThirdUsage,
    )
  }

  async computeLibraryMaterialCodeLines(
    sourceMaterialUsage: LibraryMaterialUsage,
    thirdUsageOrScanId?:
      | Service.Analysis.MaterialMeta.MaterialPackageReport[]
      | number,
  ): Promise<LibraryMaterialUsage> {
    const sourceMaterialUsageCopy: LibraryMaterialUsage = JSON.parse(
      JSON.stringify(sourceMaterialUsage),
    )
    const thirdUsage = Array.isArray(thirdUsageOrScanId)
      ? thirdUsageOrScanId
      : await this.findAllMaterialInfoByScanId(thirdUsageOrScanId)

    const walkedMaterial: string[] = []

    for (const [library] of Object.entries(sourceMaterialUsageCopy)) {
      const thirdUsageLibrary = thirdUsage.find(
        item => item.packageName === library,
      )
      if (!thirdUsageLibrary) {
        for (const [material] of Object.entries(
          sourceMaterialUsageCopy[library],
        )) {
          sourceMaterialUsageCopy[library][material] = 0
        }
        continue
      }
      for (const [material, useCount] of Object.entries(
        sourceMaterialUsageCopy[library],
      )) {
        if (walkedMaterial.includes(`${library}/${material}`)) continue
        walkedMaterial.push(`${library}/${material}`)
        const thirdUsageMaterial = thirdUsageLibrary.materials.find(
          item => item.materialName === material,
        )
        if (!thirdUsageMaterial) {
          sourceMaterialUsageCopy[library][material] = 0
          continue
        }
        sourceMaterialUsageCopy[library][material]
          = useCount * thirdUsageMaterial.codeLines
      }
    }

    return sourceMaterialUsageCopy
  }

  async manuallyCalibrateSomeData(): Promise<void> {
    await this.overrideMUIMaterialMeta()
    await this.overrideMUIMobileMaterialMeta()
    await this.overrideMChartPCReactMaterialMeta()
  }

  // ======================== 写死所有 @mchart/pc-react 的行数统计 ========================
  async overrideMChartPCReactMaterialMeta(): Promise<void> {
    const MChartPCRecords = await this.prisma.analysis_material_meta.findMany({
      where: {
        package: '@mchart/pc-react',
        status: RECORD_STATUS.EFFECT,
      },
    })

    for (const record of MChartPCRecords) {
      const { material_name } = record
      const codeLines = MChartPCCodeLines.find(
        item => item.materialName === material_name,
      )?.codeLines ?? 0
      if (record.code_lines !== codeLines || record.project_id !== 56657) {
        await this.prisma.analysis_material_meta.update({
          where: { id: record.id },
          data: {
            code_lines: codeLines,
            project_id: 25750,
          },
        })
      }
    }
  }

  // ======================== 写死所有 @ad/mui-mobile 的行数统计 ========================
  async overrideMUIMobileMaterialMeta(): Promise<void> {
    const MUIMobileRecords = await this.prisma.analysis_material_meta.findMany({
      where: {
        package: '@ad/mui-mobile',
        status: RECORD_STATUS.EFFECT,
      },
    })

    for (const record of MUIMobileRecords) {
      const { material_name } = record
      const codeLines = MUIMobileCodeLines.find(
        item => item.materialName === material_name,
      )?.codeLines ?? 0
      if (record.code_lines !== codeLines || record.project_id !== 56657) {
        await this.prisma.analysis_material_meta.update({
          where: { id: record.id },
          data: {
            code_lines: codeLines,
            project_id: 56657,
          },
        })
      }
    }
  }

  // ======================== 写死所有 @m-ui/react 的行数统计 ========================
  async overrideMUIMaterialMeta(): Promise<void> {
    const MUIRecords = await this.prisma.analysis_material_meta.findMany({
      where: {
        package: '@m-ui/react',
        status: RECORD_STATUS.EFFECT,
      },
    })

    for (const record of MUIRecords) {
      const { material_name } = record
      const codeLines = MUICodeLines.find(
        item => item.materialName === material_name,
      )?.codeLines ?? 0
      if (record.code_lines !== codeLines || record.project_id !== 28031) {
        await this.prisma.analysis_material_meta.update({
          where: { id: record.id },
          data: {
            code_lines: codeLines,
            project_id: 28031,
          },
        })
      }
    }

    await this.injectOldMUIMaterialMeta()
  }

  // ======================== 手动写入 `m-ui` 库的的物料信息，hack 它们的物料第三方引用 `@m-ui/react`的同名物料，这样使用次数就能叠加到 `@m-ui/react` 上了 ========================
  async injectOldMUIMaterialMeta(): Promise<void> {
    const MUIRecords = await this.prisma.analysis_material_meta.findMany({
      where: {
        package: '@m-ui/react',
        status: RECORD_STATUS.EFFECT,
      },
    })
    await this.prisma.analysis_material_meta.updateMany({
      where: {
        package: 'm-ui',
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
    const newOldMUIRecords = MUIRecords.map(record => ({
      ...record,
      package: 'm-ui',
      code_lines: 0,
      third_usage: JSON.stringify({
        '@m-ui/react': {
          [record.material_name]: 1,
        },
      }),
      filter_third_usage: JSON.stringify({
        '@m-ui/react': {
          [record.material_name]: 1,
        },
      }),
    }))
    await this.batchCreate(newOldMUIRecords)
  }
}
