declare namespace Service.Analysis.MaterialMeta {
  export type MaterialMetaRecord =
    import('@prisma/client').analysis_material_meta

  export type SerializedMaterialMetaRecord =
    import('type-fest').OverrideProperties<
      MaterialMetaRecord,
      {
        third_usage:
          | import('@ks-material-middleoffice/measure-sdk').LibraryMaterialUsage
          | null
        filter_third_usage:
          | import('@ks-material-middleoffice/measure-sdk').LibraryMaterialUsage
          | null
      }
    >

  export type CreateParams = Required<
    Omit<
      import('@prisma/client').analysis_material_meta,
      'id' | 'status' | 'create_time'
    >
  >

  export interface MaterialPackageReport {
    scanId: number
    business: import('@/constants/business').Business
    packageName: string
    materials: MaterialReport[]
  }

  export interface MaterialReport {
    scanId: number
    business: import('@/constants/business').Business
    packageMaterialName: string
    materialName: string
    codeLines: number
    third_usage: import('@ks-material-middleoffice/measure-sdk').LibraryMaterialUsage
  }
}
