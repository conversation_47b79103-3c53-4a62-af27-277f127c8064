import { Controller, Post, Body, Get, Query } from '@nestjs/common'
import { ProjectCollectionService } from './project-collection.service'
import { CollectionComputeService } from './collection-compute.service'

@Controller('analysis/project-collection')
export class ProjectCollectionController {
  constructor(
    private readonly projectCollectionService: ProjectCollectionService,
    private readonly collectionComputeService: CollectionComputeService,
  ) {}

  @Post('create')
  async create(
    @Body() createDto: Service.Analysis.ProjectCollection.CreateParams,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection> {
    return this.projectCollectionService.create(createDto)
  }

  @Post('list')
  async findAll(
    @Body() listParams: Service.Analysis.ProjectCollection.ListParams,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection[]> {
    return this.projectCollectionService.findList(listParams)
  }

  @Get('detail')
  async findOne(@Query('id') id: string): Promise<Service.Analysis.ProjectCollection.ProjectCollection | null> {
    return this.projectCollectionService.findOne(+id)
  }

  @Post('update')
  async update(
    @Body() updateDto: Service.Analysis.ProjectCollection.UpdateParams,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection> {
    return this.projectCollectionService.update(updateDto.id, updateDto)
  }

  @Post('remove')
  async delete(@Body('id') id: number): Promise<Service.Analysis.ProjectCollection.ProjectCollection> {
    return this.projectCollectionService.delete(id)
  }

  @Post('add-project')
  async addProject(
    @Body() addProjectDto: Service.Analysis.ProjectCollection.AddProjectParams,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection> {
    return this.projectCollectionService.addProject(addProjectDto)
  }

  @Post('remove-project')
  async removeProject(
    @Body()
    removeProjectDto: Service.Analysis.ProjectCollection.RemoveProjectParams,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection> {
    return this.projectCollectionService.removeProject(removeProjectDto)
  }

  @Get('compute/new-code-coverage')
  async computeNewCodeCoverage(@Query('id') id: string): Promise<
    Service.Analysis.ProjectCollection.CollectionNewCodeCoverageItem[]
  > {
    return this.collectionComputeService.computeCollectionNewCodeCoverage(+id)
  }
}
