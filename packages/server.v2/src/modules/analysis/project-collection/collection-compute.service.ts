import dayjs from 'dayjs'
import assert from 'assert'
import { isEmpty } from 'lodash'
import type { JsonValue } from 'type-fest'
import { Injectable } from '@nestjs/common'
import { UsedLibraryMaterial } from '@ks-material-middleoffice/measure-sdk'

import { PromiseAllFulfilled } from '@/tools/Promise'
import {
  SOURCE_CODE_BUSINESS,
  isObject,
  isSourceCodeBusiness,
  isString,
} from '@/shared'
import { PageComputeCacheService } from '@/modules/shared/page-compute-cache.service'
import {
  eraseLibraryMaterialUsageWithoutClone,
  ignoreLibraryMaterialUsageWithoutClone,
} from '@/tools/material-usage'

import { ProjectCollectionService } from './project-collection.service'
import { MaterialUsageService } from '../material-usage/material-usage.service'
import { NewMaterialUsageService } from '../new-material-usage/new-material-usage.service'
import { MaterialMetaService } from '../material-meta/material-meta.service'
import { NewCodeLinesService } from '../new-code-lines/new-code-lines.service'

export const DEFAULT_MATERIAL_CODE_LINES_LIMIT = 20 * 10000

export const EXCLUDE_REPO_PROJECT_IDS = [
  22365, // https://git.corp.kuaishou.com/ks-ad/ad-fe/eco/kuaishou-frontend-ad-social.git
  // 44136, // https://git.corp.kuaishou.com/ks-ad/ad-fe/bi/ad-martech-cdp2.git
]

@Injectable()
export class CollectionComputeService {
  constructor(
    private readonly projectCollectionService: ProjectCollectionService,
    private readonly materialUsageService: MaterialUsageService,
    private readonly newMaterialUsageService: NewMaterialUsageService,
    private readonly materialMetaService: MaterialMetaService,
    private readonly newCodeLinesService: NewCodeLinesService,
    private readonly pageComputeCacheService: PageComputeCacheService,
  ) {}

  async computeCollectionNewCodeCoverage(
    collectionId: number,
  ): Promise<
      Service.Analysis.ProjectCollection.CollectionNewCodeCoverageItem[]
    > {
    const collectionDetail
      = await this.projectCollectionService.findOne(collectionId)

    const cache = await this.pageComputeCacheService.getCache(
      collectionId,
      Math.max(dayjs().startOf('day').valueOf(), collectionDetail.update_time),
    )
    if (cache) {
      return cache.value as unknown as Service.Analysis.ProjectCollection.CollectionNewCodeCoverageItem[]
    }

    const {
      business,
      projects: userTargetProjects,
      materials: userTargetMaterials,
      payload,
    } = collectionDetail

    const excludeRepoProjectIds
      = userTargetProjects.length
      && userTargetProjects.every(item =>
        EXCLUDE_REPO_PROJECT_IDS.includes(+item.id),
      )
        ? []
        : EXCLUDE_REPO_PROJECT_IDS

    let ignoredLibraries: string[] = []
    if (
      isObject(payload)
      && 'ignoredLibraries' in payload
      && Array.isArray(payload.ignoredLibraries)
      && payload.ignoredLibraries.every(isString)
    ) {
      ignoredLibraries = payload.ignoredLibraries
    }

    assert(isSourceCodeBusiness(business), 'business is invalid')
    assert(
      ProjectCollectionService.isIdNameArray(userTargetProjects),
      'projects is invalid',
    )
    assert(
      ProjectCollectionService.isIdNameArray(userTargetMaterials),
      'materials is invalid',
    )
    const scanInfos = (
      await this.projectCollectionService.findCollectionScanIds(collectionId)
    ).sort((a, b) => a.start_time - b.start_time)

    if (scanInfos.length === 0) {
      return []
    }

    // 找到在指定范围内之前已经超出阈值的物料
    const firstScanInfo = scanInfos[0]
    const exceededCodeLinesLibraryMaterial
      = await this.computeExceededLibraryMaterials(firstScanInfo.id, business)

    const scansNewCodeCoverage = await PromiseAllFulfilled(
      scanInfos.map(
        async (
          currentScan,
        ): Promise<Service.Analysis.ProjectCollection.CollectionNewCodeCoverageItem> => {
          const allMaterialMeta
            = await this.materialMetaService.findAllMaterialInfoByScanId(
              currentScan.id,
            )
          // 本次扫描的库物料使用情况
          const currentScanNewLibraryMaterialUsage = (
            await this.newMaterialUsageService.findAllNewMaterialUsageByScanGroupByBusiness(
              currentScan.id,
              {
                businesses: [business],
                projectIds: isEmpty(userTargetProjects)
                  ? undefined
                  : userTargetProjects.map(item => +item.id),
                excludeRepoProjectIds,
              },
            )
          ).find(item => item.business === business)?.usage
          assert(
            currentScanNewLibraryMaterialUsage,
            'current scan library material usage not found',
          )
          // 本次扫描的库物料使用情况，包含间接引用
          const currentScanNewLibraryMaterialUsageWithThirdUsage
            = await this.materialMetaService.mergeThirdUsage(
              currentScanNewLibraryMaterialUsage,
              allMaterialMeta,
            )
          // 忽略已经超出阈值的库物料
          const ignoredExceededCodeLinesLibraryMaterialUsage
            = ignoreLibraryMaterialUsageWithoutClone(
              currentScanNewLibraryMaterialUsageWithThirdUsage,
              exceededCodeLinesLibraryMaterial,
            )
          // 计算有效的新增的库物料的代码行数
          const currentScanNewLibraryMaterialCodeLines
            = await this.materialMetaService.computeLibraryMaterialCodeLines(
              ignoredExceededCodeLinesLibraryMaterialUsage,
              allMaterialMeta,
            )

          const currentScanNewLibraryMaterialReport = Object.fromEntries(
            Object.entries(currentScanNewLibraryMaterialCodeLines).map(
              ([library, materialCodeLines]) => {
                if (ignoredLibraries.includes(library)) {
                  return [library, {}]
                }
                return [
                  library,
                  Object.fromEntries(
                    Object.entries(materialCodeLines).map(
                      ([material, codeLines]) => {
                        return [
                          material,
                          {
                            totalCodeLines: codeLines,
                            totalCount:
                              ignoredExceededCodeLinesLibraryMaterialUsage?.[
                                library
                              ]?.[material] ?? 0,
                          },
                        ]
                      },
                    ),
                  ),
                ]
              },
            ),
          )
          // 本次新增物料代码行
          let totalNewMaterialCodeLines = 0
          for (const [library, materialCodeLines] of Object.entries(
            currentScanNewLibraryMaterialCodeLines,
          )) {
            if (ignoredLibraries.includes(library)) {
              continue
            }
            for (const [material, codeLines] of Object.entries(
              materialCodeLines,
            )) {
              if (
                !isEmpty(userTargetMaterials)
                && userTargetMaterials.every(
                  item => item.name !== `${library}/${material}`,
                )
              ) {
                continue
              }
              totalNewMaterialCodeLines += codeLines
            }
          }
          // 计算本次扫描内，新增的业务项目代码行数
          const currentScanProfessionProjectNewCodeLines = (
            await this.newCodeLinesService.findBusinessProfessionProjectNewCodeLines(
              {
                scanId: currentScan.id,
                business,
                excludeRepoProjectIds,
              },
            )
          ).filter((item) => {
            if (excludeRepoProjectIds.includes(item.repo_project_id)) {
              return false
            }
            if (!isEmpty(userTargetProjects)) {
              return userTargetProjects.some(
                project => project.id === item.repo_project_id,
              )
            }
            return true
          })

          const projectNewMaterialUsage
            = await this.newMaterialUsageService.findAllNewMaterialUsageByScanGroupByProject(
              currentScan.id,
              {
                projectIds: isEmpty(userTargetProjects)
                  ? undefined
                  : userTargetProjects.map(item => +item.id),
                business,
                excludeRepoProjectIds,
              },
            )
          const { result: projectNewLibraryMaterialCodeLiens }
            = await PromiseAllFulfilled(
              projectNewMaterialUsage.map(async (item) => {
                const materialUsage = ignoreLibraryMaterialUsageWithoutClone(
                  await this.materialMetaService.computeLibraryMaterialCodeLines(
                    await this.materialMetaService.mergeThirdUsage(
                      item.materialUsage,
                      allMaterialMeta,
                    ),
                    allMaterialMeta,
                  ),
                  exceededCodeLinesLibraryMaterial,
                )
                ignoredLibraries.forEach((library) => {
                  if (library in materialUsage) {
                    delete materialUsage[library]
                  }
                })
                return {
                  projectId: item.projectId,
                  projectName: item.projectName,
                  materialUsage: materialUsage,
                }
              }),
            )

          return {
            scan: currentScan,
            // 本次扫描内，新增的专业项目代码行数
            totalNewCodeLines: currentScanProfessionProjectNewCodeLines.reduce(
              (acc, curr) => acc + curr.new_code_lines,
              0,
            ),
            // 本次扫描内，新增的库物料的代码行数
            totalNewMaterialCodeLines,
            // 本次扫描内，新增的业务项目代码行数明细
            professionProjectNewCodeLines:
              currentScanProfessionProjectNewCodeLines,
            // 本次扫描内，新增的库物料的代码行数明细
            newLibraryMaterialUsage: currentScanNewLibraryMaterialReport,
            // 本次扫描内，项目为单位新增的库物料的代码行数明细
            projectNewLibraryMaterialCodeLiens,
            // 本次扫描内，被排除的物料
            exceededCodeLinesLibraryMaterial,
          }
        },
      ),
    )

    await this.pageComputeCacheService.setCache(
      collectionId,
      scansNewCodeCoverage.result as unknown as JsonValue,
    )

    return scansNewCodeCoverage.result
  }

  async computeExceededLibraryMaterials(
    scanId: number,
    business: SOURCE_CODE_BUSINESS,
    options?: {
      materialCodeLinesLimit?: number
      multipleOfIndirectReference?: number
    },
  ): Promise<UsedLibraryMaterial> {
    const {
      materialCodeLinesLimit = DEFAULT_MATERIAL_CODE_LINES_LIMIT,
      multipleOfIndirectReference,
    } = options || {}
    const currentBusinessMaterialUsage = (
      await this.materialUsageService.findAllMaterialUsageByScanGroupByBusiness(
        scanId,
        [business],
      )
    ).find(item => item.business === business)?.usage
    assert(
      currentBusinessMaterialUsage,
      'current library material usage not found',
    )
    const currentNewLibraryMaterialUsage = (
      await this.newMaterialUsageService.findAllNewMaterialUsageByScanGroupByBusiness(
        scanId,
        {
          businesses: [business],
        },
      )
    ).find(item => item.business === business)?.usage
    assert(
      currentNewLibraryMaterialUsage,
      'current new library material usage not found',
    )
    const beforeCurrentScanLibraryMaterialUsage
      = eraseLibraryMaterialUsageWithoutClone(
        JSON.parse(JSON.stringify(currentBusinessMaterialUsage)),
        currentNewLibraryMaterialUsage,
      )
    const allMaterialMeta
      = await this.materialMetaService.findAllMaterialInfoByScanId(scanId)

    const beforeCurrentScanLibraryMaterialUsageWithThirdUsage
      = await this.materialMetaService.mergeThirdUsage(
        beforeCurrentScanLibraryMaterialUsage,
        allMaterialMeta,
        multipleOfIndirectReference,
      )
    const beforeCurrentScanLibraryMaterialCodeLinesWithThirdUsage
      = await this.materialMetaService.computeLibraryMaterialCodeLines(
        beforeCurrentScanLibraryMaterialUsageWithThirdUsage,
        allMaterialMeta,
      )

    // 找出超出 materialCodeLinesLimit 的物料有哪些
    const exceededCodeLinesLibraryMaterial: UsedLibraryMaterial = {}
    for (const [library, materialCodeLines] of Object.entries(
      beforeCurrentScanLibraryMaterialCodeLinesWithThirdUsage,
    )) {
      if (!(library in exceededCodeLinesLibraryMaterial)) {
        exceededCodeLinesLibraryMaterial[library] = []
      }
      for (const [material, codeLines] of Object.entries(materialCodeLines)) {
        if (codeLines > materialCodeLinesLimit) {
          if (exceededCodeLinesLibraryMaterial[library].includes(material)) {
            continue
          }
          exceededCodeLinesLibraryMaterial[library].push(material)
        }
      }
    }

    return exceededCodeLinesLibraryMaterial
  }
}
