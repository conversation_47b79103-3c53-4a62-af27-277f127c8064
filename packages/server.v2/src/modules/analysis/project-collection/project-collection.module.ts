import { Module } from '@nestjs/common'
import { ProjectCollectionController } from './project-collection.controller'
import { ProjectCollectionService } from './project-collection.service'
import { CollectionComputeService } from './collection-compute.service'
import { ScanInfoModule } from '../scan-info/scan-info.module'
import { MaterialUsageModule } from '../material-usage/material-usage.module'
import { NewMaterialUsageModule } from '../new-material-usage/new-material-usage.module'
import { MaterialMetaModule } from '../material-meta/material-meta.module'
import { NewCodeLinesModule } from '../new-code-lines/new-code-lines.module'
import { SharedModule } from '@/modules/shared/shared.module'

@Module({
  imports: [
    ScanInfoModule,
    MaterialUsageModule,
    NewMaterialUsageModule,
    MaterialMetaModule,
    NewCodeLinesModule,
    SharedModule,
  ],
  controllers: [ProjectCollectionController],
  providers: [ProjectCollectionService, CollectionComputeService],
})
export class ProjectCollectionModule {}
