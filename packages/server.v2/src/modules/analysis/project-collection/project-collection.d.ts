declare namespace Service.Analysis.ProjectCollection {
  export type ProjectCollection = import('type-fest').OverrideProperties<
    import('@prisma/client').analysis_project_collection,
    {
      projects: Array<IdNameObject>
      materials: Array<IdNameObject>
    }
  >

  export type IdNameObject = import('type-fest').Jsonify<{
    id: number
    name: string
  }>

  export interface CreateParams {
    name: string
    tag?: string[]
    business?: string
    startTime?: number
    endTime?: number
    projects: Array<IdNameObject>
    materials: Array<IdNameObject>
    payload?: import('type-fest').JsonObject
  }

  export interface ListParams {
    tag?: string[]
    business?: string
  }

  export type UpdateParams = import('type-fest').Jsonify<Partial<CreateParams> & {
    id: number
  }>

  export interface AddProjectParams {
    collectionId: number
    projectId: number
    projectName: string
  }

  export interface RemoveProjectParams {
    collectionId: number
    projectId: number
  }

  export interface ProjectPageCoverage {
    projectId: number
    projectName: string
    coverage: {
      usedMaterialPages: number
      total: number
    }
  }

  export interface ScanPageCoverageResult {
    scanId: number
    endTime: number
    projects: ProjectPageCoverage[]
  }

  export interface CollectionNewCodeCoverageItem {
    scan: Service.Analysis.ScanInfo.SerializeAnalysisScanInfo
    totalNewCodeLines: number
    professionProjectNewCodeLines: Service.Analysis.NewCodeLines.NewCodeLinesRecordWithRepoName[]
    totalNewMaterialCodeLines: number
    newLibraryMaterialUsage: Record<string, Record<string, {
      totalCodeLines: number
      totalCount: number
    }>>
    projectNewLibraryMaterialCodeLiens: {
      projectId: number
      projectName: string
      materialUsage: import('@ks-material-middleoffice/measure-sdk').LibraryMaterialUsage
    }[]
    exceededCodeLinesLibraryMaterial: import('@ks-material-middleoffice/measure-sdk').UsedLibraryMaterial
  }
}
