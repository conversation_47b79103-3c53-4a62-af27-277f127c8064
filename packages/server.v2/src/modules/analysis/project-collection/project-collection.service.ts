import assert from 'assert'
import { Injectable } from '@nestjs/common'
import type { JsonValue } from 'type-fest'

import { now } from '@/tools/date'
import { isStringOrNumber, isObject, isString } from '@/tools/type'
import { PrismaClientInstance, genPrimaryIndex } from '@/tools/prisma'
import { RECORD_STATUS } from '@/constants/status'
import { ScanInfoService } from '../scan-info/scan-info.service'
import { MATERIAL_POINTS_BASE_INDEX_TYPE } from '@/shared'
import { omit } from '@/tools/object'

@Injectable()
export class ProjectCollectionService {
  private readonly prisma = PrismaClientInstance
  constructor(private readonly scanInfoService: ScanInfoService) {}

  static isIdNameArray(
    projects: unknown,
  ): projects is { name: string, id: number | string }[] & JsonValue {
    assert(Array.isArray(projects), 'projects should be an array')
    projects.forEach((project) => {
      assert(isObject(project), 'project should be an object')
      assert('name' in project, 'project.name should be a string')
      assert('id' in project, 'project.id should be a number')
      assert(isString(project.name), 'project.name should be a string')
      assert(isStringOrNumber(project.id), 'project.id should be a number')
    })
    return true
  }

  async create(
    data: Service.Analysis.ProjectCollection.CreateParams,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection> {
    assert(
      ProjectCollectionService.isIdNameArray(data.projects ?? []),
      'projects 不是有效的项目集合',
    )
    assert(
      ProjectCollectionService.isIdNameArray(data.materials ?? []),
      'projects 不是有效的项目集合',
    )

    return (await this.prisma.analysis_project_collection.create({
      data: {
        id: genPrimaryIndex(),
        name: data.name,
        business: data.business,
        projects: data.projects,
        materials: data.materials,
        start_time: data.startTime,
        end_time: data.endTime,
        tag: data.tag ?? [],
        viewers: [],
        maintainers: [],
        payload: data.payload ?? {},
        update_time: now(),
        create_time: now(),
      },
    })) as unknown as Service.Analysis.ProjectCollection.ProjectCollection
  }

  async findList(
    listParams: Service.Analysis.ProjectCollection.ListParams,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection[]> {
    return (await this.prisma.analysis_project_collection.findMany({
      where: {
        status: 1,
        tag: { array_contains: listParams.tag },
        business: listParams.business,
      },
    })) as unknown as Service.Analysis.ProjectCollection.ProjectCollection[]
  }

  async findOne(
    id: number,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection | null> {
    return (await this.prisma.analysis_project_collection.findUnique({
      where: { id, status: RECORD_STATUS.EFFECT },
    })) as unknown as Service.Analysis.ProjectCollection.ProjectCollection | null
  }

  async update(
    id: number,
    data: Service.Analysis.ProjectCollection.UpdateParams,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection> {
    return (await this.prisma.analysis_project_collection.update({
      where: { id },
      data: {
        ...omit(data, ['startTime', 'endTime']),
        start_time: data.startTime,
        end_time: data.endTime,
        update_time: now(),
      },
    })) as unknown as Service.Analysis.ProjectCollection.ProjectCollection
  }

  async delete(
    id: number,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection> {
    return (await this.prisma.analysis_project_collection.update({
      where: { id },
      data: { status: 0 },
    })) as unknown as Service.Analysis.ProjectCollection.ProjectCollection
  }

  async addProject(
    data: Service.Analysis.ProjectCollection.AddProjectParams,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection> {
    const collection = await this.findOne(data.collectionId)
    if (!collection) {
      throw new Error('项目集合不存在')
    }

    assert(Array.isArray(collection.projects), 'projects 不是数组')
    assert(
      collection.projects.every(
        p =>
          typeof p === 'object'
          && p !== null
          && 'id' in p
          && typeof p.id === 'number'
          && 'name' in p
          && typeof p.name === 'string',
      ),
      'projects 数组元素类型不正确',
    )
    const projects = collection.projects as Array<{ id: number, name: string }>
    if (!projects.some(p => p.id === data.projectId)) {
      projects.push({ id: data.projectId, name: data.projectName })
    }

    return (await this.prisma.analysis_project_collection.update({
      where: { id: data.collectionId },
      data: {
        projects,
        update_time: now(),
      },
    })) as unknown as Service.Analysis.ProjectCollection.ProjectCollection
  }

  async removeProject(
    data: Service.Analysis.ProjectCollection.RemoveProjectParams,
  ): Promise<Service.Analysis.ProjectCollection.ProjectCollection> {
    const collection = await this.findOne(data.collectionId)
    if (!collection) {
      throw new Error('项目集合不存在')
    }

    assert(Array.isArray(collection.projects), 'projects 不是数组')
    assert(
      collection.projects.every(
        p =>
          typeof p === 'object'
          && p !== null
          && 'id' in p
          && typeof p.id === 'number'
          && 'name' in p
          && typeof p.name === 'string',
      ),
      'projects 数组元素类型不正确',
    )
    const projects = collection.projects as Array<{ id: number, name: string }>
    const updatedProjects = projects.filter(p => p.id !== data.projectId)

    return (await this.prisma.analysis_project_collection.update({
      where: { id: data.collectionId },
      data: {
        projects: updatedProjects,
        update_time: now(),
      },
    })) as unknown as Service.Analysis.ProjectCollection.ProjectCollection
  }

  async findCollectionScanIds(
    collectionId: number,
  ): Promise<Service.Analysis.ScanInfo.SerializeAnalysisScanInfo[]> {
    const detail = await this.findOne(collectionId)
    if (!detail) {
      throw new Error('项目集合不存在')
    }
    const scanInfos = await this.scanInfoService.findByTimeRange({
      startTime: detail.start_time,
      endTime: detail.end_time,
      type: MATERIAL_POINTS_BASE_INDEX_TYPE,
    })
    return scanInfos.list
  }
}
