import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsInt, IsNotEmpty } from 'class-validator'

export class FindOneCodeLinesRecordDTO
implements Service.Analysis.NewCodeLines.FindOneNewCodeLinesRecordParams {
  @ApiProperty({ description: '扫描ID' })
  @IsInt()
  @IsNotEmpty()
  @Type(() => Number)
  scanId: number

  @ApiProperty({ description: '仓库项目ID' })
  @IsInt()
  @IsNotEmpty()
  @Type(() => Number)
  repoProjectId: number
}
