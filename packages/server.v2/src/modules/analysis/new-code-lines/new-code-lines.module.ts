import { Module } from '@nestjs/common'
import { NewCodeLinesController } from './new-code-lines.controller'
import { NewCodeLinesService } from './new-code-lines.service'
import { RepoMetaModule } from '../repo-meta/repo-meta.module'

@Module({
  imports: [RepoMetaModule],
  exports: [NewCodeLinesService],
  controllers: [NewCodeLinesController],
  providers: [NewCodeLinesService],
})
export class NewCodeLinesModule {}
