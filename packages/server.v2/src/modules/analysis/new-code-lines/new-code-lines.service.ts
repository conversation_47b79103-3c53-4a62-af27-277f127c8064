import assert from 'assert'
import { Injectable } from '@nestjs/common'

import { RECORD_STATUS } from '@/constants/status'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { ALL_SOURCE_CODE_BUSINESS, SOURCE_CODE_BUSINESS, now } from '@/shared'
import { chunkArray } from '@/tools/array'
import { limitInvokePromiseAllFulfilled, PromiseAllFulfilledResult } from '@/tools/Promise'
import { RepoMetaService } from '../repo-meta/repo-meta.service'
import { Prisma } from '@prisma/client'

@Injectable()
export class NewCodeLinesService {
  private readonly prisma = PrismaClientInstance

  constructor(private readonly repoMetaService: RepoMetaService) {}

  async create(params: Service.Analysis.NewCodeLines.CreateParams): Promise<Service.Analysis.NewCodeLines.NewCodeLinesRecord> {
    return this.prisma.analysis_new_code_lines.create({
      data: {
        ...params,
        id: genPrimaryIndex(),
        create_time: now(),
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async batchCreate(params: Service.Analysis.NewCodeLines.CreateParams[]): Promise<PromiseAllFulfilledResult<Prisma.BatchPayload>> {
    const create_time = now()
    const chunkedParamsList = chunkArray(params, 20)
    return limitInvokePromiseAllFulfilled(
      chunkedParamsList.map(
        chunkedParams => async () =>
          this.prisma.analysis_new_code_lines.createMany({
            data: chunkedParams.map(param => ({
              ...param,
              id: genPrimaryIndex(),
              create_time,
              status: RECORD_STATUS.EFFECT,
            })),
          }),
      ),
    )
  }

  async batchDelete(params: { scanId: number, businesses: SOURCE_CODE_BUSINESS[] }): Promise<Prisma.BatchPayload> {
    return this.prisma.analysis_new_code_lines.updateMany({
      where: {
        scan_id: params.scanId,
        business: {
          in: params.businesses,
        },
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async findBusinessProfessionProjectNewCodeLines(
    params: Service.Analysis.NewCodeLines.FindBusinessNewCodeLinesRecordParams,
  ): Promise<Service.Analysis.NewCodeLines.NewCodeLinesRecord[]> {
    assert(params.scanId, '扫描ID不能为空')
    assert(params.business, '业务不能为空')
    const records = await this.prisma.analysis_new_code_lines.findMany({
      where: {
        scan_id: params.scanId,
        business: params.business,
        status: RECORD_STATUS.EFFECT,
        repo_project_id: {
          notIn: params.excludeRepoProjectIds ?? [],
        },
      },
    })
    const repoInfos = await this.repoMetaService.findListByProjectIds(
      records.map(item => item.repo_project_id),
    )
    return records.map((item) => {
      const repoInfo = repoInfos.find(
        repoInfo => repoInfo.project_id === item.repo_project_id,
      )
      return {
        ...item,
        repo_name: repoInfo?.name,
      }
    })
  }

  async findBusinessTotalNewCodeLines(
    params: Service.Analysis.NewCodeLines.FindBusinessNewCodeLinesRecordParams,
  ): Promise<number> {
    const records
      = await this.findBusinessProfessionProjectNewCodeLines(params)
    let result = 0
    records.forEach((record) => {
      result += record.new_code_lines
    })
    return result
  }

  async findAllBusiness(
    scanId: number,
  ): Promise<{ business: SOURCE_CODE_BUSINESS, newCodeLines: 0 }[]> {
    assert(scanId, '扫描ID不能为空')
    const record = await this.prisma.analysis_new_code_lines.findMany({
      where: {
        scan_id: scanId,
        status: RECORD_STATUS.EFFECT,
      },
    })
    const result: { business: SOURCE_CODE_BUSINESS, newCodeLines: 0 }[] = ALL_SOURCE_CODE_BUSINESS.map(
      business => ({
        business,
        newCodeLines: 0,
      }),
    )

    for (let i = 0; i < record.length; i++) {
      const { business, new_code_lines } = record[i]
      const businessReport = result.find(item => item.business === business)
      if (businessReport) {
        businessReport.newCodeLines += new_code_lines
      }
    }
    return result
  }

  async findOne(
    params: Service.Analysis.NewCodeLines.FindOneNewCodeLinesRecordParams,
  ): Promise<Service.Analysis.NewCodeLines.NewCodeLinesRecord> | never {
    assert(params.scanId, '扫描ID不能为空')
    assert(params.repoProjectId, '仓库项目ID不能为空')
    const record = await this.prisma.analysis_new_code_lines.findFirstOrThrow({
      where: {
        scan_id: params.scanId,
        repo_project_id: params.repoProjectId,
        status: RECORD_STATUS.EFFECT,
      },
    })
    return record
  }

  async findList(
    params: Service.Analysis.NewCodeLines.FindListNewCodeLinesRecordParams,
  ): Promise<Service.Analysis.NewCodeLines.NewCodeLinesRecord[]> {
    const records = this.prisma.analysis_new_code_lines.findMany({
      where: {
        scan_id: params.scanId,
        repo_project_id: params.repoProjectId,
        status: RECORD_STATUS.EFFECT,
      },
    })
    return records
  }
}
