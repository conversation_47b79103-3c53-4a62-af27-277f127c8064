declare namespace Service.Analysis.NewCodeLines {
  export type NewCodeLinesRecord =
    import('@prisma/client').analysis_new_code_lines

  export type NewCodeLinesRecordWithRepoName = Overwrite<NewCodeLinesRecord, { repo_name: string }>

  export type CreateParams = Required<
    Omit<
      import('@prisma/client').analysis_new_code_lines,
      'id' | 'status' | 'create_time'
    >
  >

  export interface FindOneNewCodeLinesRecordParams {
    scanId: number
    repoProjectId: number
  }

  export interface FindBusinessNewCodeLinesRecordParams {
    scanId: number
    business: import('@/constants/business').Business
    excludeRepoProjectIds?: number[]
  }

  export interface FindListNewCodeLinesRecordParams {
    scanId?: number
    repoProjectId?: number
  }
}
