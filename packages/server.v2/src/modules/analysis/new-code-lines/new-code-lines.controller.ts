import { ApiTags } from '@nestjs/swagger'
import { Controller, Get, Query } from '@nestjs/common'

import { FindOneCodeLinesRecordDTO } from './dto/find-one-code-lines.dto'
import { FindListCodeLinesRecordDTO } from './dto/find-list-code-lies.dto'
import { NewCodeLinesService } from './new-code-lines.service'

@ApiTags('analysis/new-code-lines')
@Controller('analysis/new-code-lines')
export class NewCodeLinesController {
  constructor(private readonly newCodeLinesService: NewCodeLinesService) {}

  @Get()
  async findOne(@Query() query: FindOneCodeLinesRecordDTO): Promise<Service.Analysis.NewCodeLines.NewCodeLinesRecord> | never {
    return await this.newCodeLinesService.findOne(query)
  }

  @Get('list')
  async findList(@Query() query: FindListCodeLinesRecordDTO): Promise<Service.Analysis.NewCodeLines.NewCodeLinesRecord[]> {
    return await this.newCodeLinesService.findList(query)
  }
}
