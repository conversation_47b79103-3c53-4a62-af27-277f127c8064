import { Module } from '@nestjs/common'
import { RawDataService } from './raw-data.service'
import { RawDataController } from './raw-data.controller'
import { RepoMetaModule } from '../repo-meta/repo-meta.module'
import { ScanInfoModule } from '../scan-info/scan-info.module'
import { GitlabModule } from '@/modules/gitlab/gitlab.module'

@Module({
  imports: [RepoMetaModule, ScanInfoModule, GitlabModule],
  controllers: [RawDataController],
  providers: [RawDataService],
  exports: [RawDataService],
})
export class RawDataModule {}
