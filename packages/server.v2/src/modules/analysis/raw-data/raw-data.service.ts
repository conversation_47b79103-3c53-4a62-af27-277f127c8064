import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { APPModeReport } from '@ks-material-middleoffice/measure-sdk'

import {
  ALL_SOURCE_CODE_BUSINESS,
  ALL_REPO_TYPE,
  SOURCE_CODE_BUSINESS,
  now,
  REPO_TYPE,
} from '@/shared'
import { logStringify } from '@/tools/json'
import {
  limitInvokePromiseAllFulfilled,
  PromiseAllFulfilled,
  PromiseAllFulfilledResult,
} from '@/tools/Promise'
import { chunkArray } from '@/tools/array'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { RECORD_STATUS } from '@/constants/status'

import { ScanInfoService } from '../scan-info/scan-info.service'
import { RepoMetaService } from '../repo-meta/repo-meta.service'
import { GitlabService } from '@/modules/gitlab/gitlab.service'
import { loggerInstance } from '@/tools/winston'
import { Prisma } from '@prisma/client'

@Injectable()
export class RawDataService {
  private readonly logger = loggerInstance
  private readonly prisma = PrismaClientInstance
  constructor(
    private readonly repoMetaService: RepoMetaService,
    private readonly scanInfosService: ScanInfoService,
    private readonly gitlabService: GitlabService,
  ) {}

  private async serialize(
    rawData: Service.Analysis.RawData.RawDataRecord,
  ): Promise<Service.Analysis.RawData.SerializeAnalysisRawData> | never {
    try {
      return Object.freeze({
        ...rawData,
        content: rawData.content
          ? (JSON.parse(rawData.content) as APPModeReport)
          : null,
      })
    }
    catch (error) {
      throw new Error(
        `Failed to serialize raw data: ${logStringify({ id: rawData.id, error: error.toString() })}`,
      )
    }
  }

  async create(params: Service.Analysis.RawData.CreateParams): Promise<Service.Analysis.RawData.RawDataRecord> {
    await this.delete({
      scan_id: params.scan_id,
      repo_project_id: params.repo_project_id,
      type: params.type,
      business: params.business,
    })
    return this.prisma.analysis_raw_data.create({
      data: {
        ...params,
        content: params.content,
        id: genPrimaryIndex(),
        status: RECORD_STATUS.EFFECT,
        create_time: now(),
      },
    })
  }

  async batchCreate(params: Service.Analysis.RawData.CreateParams[]): Promise<PromiseAllFulfilledResult<Prisma.BatchPayload>> {
    const create_time = now()
    const chunkedParamsList = chunkArray(params, 20)
    return limitInvokePromiseAllFulfilled(
      chunkedParamsList.map(
        chunkedParams => async () =>
          this.prisma.analysis_raw_data.createMany({
            data: chunkedParams.map(param => ({
              ...param,
              id: genPrimaryIndex(),
              status: RECORD_STATUS.EFFECT,
              create_time,
            })),
          }),
      ),
    )
  }

  batchDelete(params: {
    scanId: number
    businesses: SOURCE_CODE_BUSINESS[]
    repoTypes: REPO_TYPE[]
  }): Prisma.PrismaPromise<Prisma.BatchPayload> {
    const {
      scanId,
      businesses = ALL_SOURCE_CODE_BUSINESS,
      repoTypes = ALL_REPO_TYPE,
    } = params
    return this.prisma.analysis_raw_data.updateMany({
      where: {
        scan_id: scanId,
        business: {
          in: businesses,
        },
        type: {
          in: repoTypes,
        },
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async findOne(id: number): Promise<Service.Analysis.RawData.SerializeAnalysisRawData> {
    const record = await this.prisma.analysis_raw_data.findUniqueOrThrow({
      where: {
        id,
      },
    })
    return this.serialize(record)
  }

  async detail(params: Service.Analysis.RawData.SearchOneRawDataRecordParams): Promise<Service.Analysis.RawData.DetailResponse> {
    assert(params.scanId, '扫描ID不能为空')
    assert(params.repoProjectId, '仓库项目ID不能为空')
    const record = await this.prisma.analysis_raw_data.findFirstOrThrow({
      where: {
        scan_id: params.scanId,
        repo_project_id: params.repoProjectId,
        status: RECORD_STATUS.EFFECT,
      },
    })
    const scanInfo = await this.scanInfosService.findOneById(params.scanId)
    const repoMeta = await this.repoMetaService.findOneByProjectId(
      params.repoProjectId,
    )
    const repoLastUpdateTime
      = await this.gitlabService.getProjectLastCommitTime(params.repoProjectId, {
        since: scanInfo.start_time,
        until: scanInfo.end_time,
      })
    return {
      ...(await this.serialize(record)),
      repo_meta: repoMeta,
      scan_info: scanInfo,
      repo_last_update_time: repoLastUpdateTime,
    }
  }

  async findList(params: Service.Analysis.RawData.FindListRawDataRecordParams): Promise<PromiseAllFulfilledResult<Service.Analysis.RawData.SerializeAnalysisRawData>> {
    let { businesses, repoTypes } = params
    businesses = businesses
      ? Array.isArray(businesses)
        ? businesses
        : [businesses]
      : ALL_SOURCE_CODE_BUSINESS
    repoTypes = repoTypes
      ? Array.isArray(repoTypes)
        ? repoTypes
        : [repoTypes]
      : ALL_REPO_TYPE
    const records = await this.prisma.analysis_raw_data.findMany({
      where: {
        scan_id: params.scanId,
        type: { in: repoTypes },
        business: { in: businesses },
        status: RECORD_STATUS.EFFECT,
      },
    })
    return PromiseAllFulfilled(records.map(record => this.serialize(record)))
  }

  async findListOverview(
    params: Service.Analysis.RawData.FindListRawDataRecordParams,
  ): Promise<{
      list: Service.Analysis.RawData.ListResponse
      errors: Error[]
      total: number
    }> {
    let { businesses, repoTypes } = params
    businesses = businesses
      ? Array.isArray(businesses)
        ? businesses
        : [businesses]
      : ALL_SOURCE_CODE_BUSINESS
    repoTypes = repoTypes
      ? Array.isArray(repoTypes)
        ? repoTypes
        : [repoTypes]
      : ALL_REPO_TYPE
    const records = await this.prisma.analysis_raw_data.findMany({
      select: {
        id: true,
        scan_id: true,
        repo_project_id: true,
        business: true,
        create_time: true,
        type: true,
      },
      where: {
        scan_id: params.scanId,
        type: { in: repoTypes },
        business: { in: businesses },
        status: RECORD_STATUS.EFFECT,
      },
    })
    const scanInfo = await this.scanInfosService.findOneById(params.scanId)

    const repoMetaRecords = await this.repoMetaService.findListByProjectIds(
      records.map(record => record.repo_project_id),
    )

    const { result, errors } = await PromiseAllFulfilled(
      records.map(async (record) => {
        const findIdx = repoMetaRecords.findIndex(
          item => item.project_id === record.repo_project_id,
        )
        if (findIdx !== -1) {
          const repoLastUpdateTime
            = await this.gitlabService.getProjectLastCommitTime(
              record.repo_project_id,
              {
                since: scanInfo.start_time,
                until: scanInfo.end_time,
              },
            )
          const repoMetaRecord = repoMetaRecords[findIdx]
          const result = {
            ...record,
            repo_meta: repoMetaRecord,
            repo_last_update_time: repoLastUpdateTime,
          }
          return result
        }
        else {
          throw new Error(
            `Failed to find repo meta record by project id: ${logStringify({
              project_id: record.repo_project_id,
            })}`,
          )
        }
      }),
    )
    errors.forEach((error) => {
      this.logger.error('Failed to find repo meta record', error)
    })
    return {
      list: result.filter(Boolean),
      errors,
      total: result.length,
    }
  }

  async count(params: Service.Analysis.RawData.FindListRawDataRecordParams): Promise<number> {
    let { businesses, repoTypes } = params
    businesses = businesses
      ? Array.isArray(businesses)
        ? businesses
        : [businesses]
      : ALL_SOURCE_CODE_BUSINESS
    repoTypes = repoTypes
      ? Array.isArray(repoTypes)
        ? repoTypes
        : [repoTypes]
      : ALL_REPO_TYPE
    return this.prisma.analysis_raw_data.count({
      where: {
        scan_id: params.scanId,
        type: {
          in: repoTypes,
        },
        business: {
          in: businesses,
        },
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async delete(params: Partial<Service.Analysis.RawData.RawDataRecord>): Promise<Prisma.BatchPayload> {
    return this.prisma.analysis_raw_data.updateMany({
      where: params,
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }
}
