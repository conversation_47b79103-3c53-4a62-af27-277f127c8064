import { ApiTags } from '@nestjs/swagger'
import { analysis_raw_data, Prisma } from '@prisma/client'
import { Body, Controller, Get, Post, Query } from '@nestjs/common'

import { RawDataService } from './raw-data.service'
import { FindListRawDataRecordDTO } from './dto/find-list-raw-data.dto'
import { FindOneRawDataRecordParams } from './dto/find-one-raw-data.dto'
import { PromiseAllFulfilledResult } from '@/tools/Promise'

@ApiTags('analysis/raw-data')
@Controller('analysis/raw-data')
export class RawDataController {
  constructor(private readonly rawDataService: RawDataService) {}

  @Get('detail')
  async findOne(@Query() query: FindOneRawDataRecordParams): Promise<Service.Analysis.RawData.SerializeAnalysisRawData | null> {
    return this.rawDataService.detail(query)
  }

  @Post('list')
  async list(
    @Body()
    body: FindListRawDataRecordDTO,
  ): Promise<{
      list: Service.Analysis.RawData.ListResponse
      errors: Error[]
      total: number
    }> {
    return this.rawDataService.findListOverview(body)
  }

  @Post('count')
  async count(
    @Body()
    body: FindListRawDataRecordDTO,
  ): Promise<number> {
    return this.rawDataService.count(body)
  }

  @Post('batch-create')
  async batchCreate(
    @Body()
    body: analysis_raw_data[],
  ): Promise<PromiseAllFulfilledResult<Prisma.BatchPayload>> {
    return this.rawDataService.batchCreate(body)
  }
}
