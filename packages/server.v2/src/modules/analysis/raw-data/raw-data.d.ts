declare namespace Service.Analysis.RawData {
  export type RawDataRecord = import('@prisma/client').analysis_raw_data
  export type SerializeAnalysisRawData = import('type-fest').OverrideProperties<
    RawDataRecord,
    {
      content:
        | import('@ks-material-middleoffice/measure-sdk').APPModeReport
        | import('@ks-material-middleoffice/measure-sdk').LibraryModeReport
        | null
    }
  >

  export type CreateParams = Required<
    Omit<
      import('@prisma/client').analysis_raw_data,
      'id' | 'status' | 'create_time'
    >
  >

  export interface SearchOneRawDataRecordParams {
    scanId: number
    repoProjectId: number
  }

  export interface FindListRawDataRecordParams {
    scanId?: number
    businesses?:
      | import('@/constants/business').SOURCE_CODE_BUSINESS_TYPES[]
      | import('@/constants/business').SOURCE_CODE_BUSINESS_TYPES
    repoTypes?:
      | import('@/constants/repo-type').REPO_TYPES[]
      | import('@/constants/repo-type').REPO_TYPES
  }

  export type ListResponseItem = (Omit<
    Service.Analysis.RawData.RawDataRecord,
    'content' | 'status'
  > & {
    repo_meta: Service.Analysis.RepoMeta.RepoMetaRecord
    repo_last_update_time: number
  })
  export type ListResponse = ListResponseItem[]

  export type DetailResponse = SerializeAnalysisRawData & {
    repo_meta: Service.Analysis.RepoMeta.RepoMetaRecord
    scan_info: Service.Analysis.ScanInfo.SerializeAnalysisScanInfo
    repo_last_update_time: number
  }
}
