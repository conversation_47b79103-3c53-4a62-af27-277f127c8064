import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsInt, IsNotEmpty } from 'class-validator'

export class FindOneRawDataRecordParams
implements Service.Analysis.RawData.SearchOneRawDataRecordParams {
  @ApiProperty({ description: '扫描ID' })
  @IsInt()
  @IsNotEmpty()
  @Type(() => Number)
  scanId: number

  @ApiProperty({ description: '项目 gitlab ID' })
  @IsInt()
  @IsNotEmpty()
  @Type(() => Number)
  repoProjectId: number
}
