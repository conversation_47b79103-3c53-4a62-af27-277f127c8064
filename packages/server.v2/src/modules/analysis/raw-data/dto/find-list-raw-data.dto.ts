import { IsIn, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, IsInt } from 'class-validator'
import { Transform, Type } from 'class-transformer'
import { REPO_TYPES } from '@/constants/repo-type'
import { SOURCE_CODE_BUSINESS_TYPES } from '@/shared'

export class FindListRawDataRecordDTO
implements Service.Analysis.RawData.FindListRawDataRecordParams {
  @ValidateIf(obj => obj.scanId !== undefined)
  @IsInt()
  @Type(() => Number)
  scanId?: number

  @ValidateIf(obj => obj.type !== undefined)
  @IsIn(['profession', 'material'])
  repoTypes?: REPO_TYPES[] | REPO_TYPES

  @ValidateIf(obj => obj.business !== undefined)
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  businesses?: SOURCE_CODE_BUSINESS_TYPES[] | SOURCE_CODE_BUSINESS_TYPES
}
