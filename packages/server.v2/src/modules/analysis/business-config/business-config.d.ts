declare namespace Service.Analysis.BusinessConfig {
  export type BusinessConfigRecord = import('@prisma/client').business_config
  export type SerializeBusinessConfigRecord =
    import('type-fest').OverrideProperties<
      BusinessConfigRecord,
      {
        meta: BusinessMeta | null
      }
    >

  export interface BusinessMeta {
    report?: {
      projects?: CoreBusinessProjectConfig[]
    }
    material?: {
      projects?: CoreBusinessProjectConfig[]
    }
  }

  type JsonValue = import('type-fest').JsonValue
  export interface CoreBusinessProjectConfig extends JsonValue {
    title: string
    namespace: string
    git_url: string
    branch?: string
    router_entry?: string[] | string
    pkg_filter?: string
    black_list?: string[]
  }

  export interface FullBusinessProjectConfig extends CoreBusinessProjectConfig {
    project_id: number
    black_pkgs?: string[]
    type: import('@/constants/repo-type').REPO_TYPE
  }
}
