import { uniqBy } from 'lodash'
import { Injectable } from '@nestjs/common'

import { logStringify } from '@/tools/json'
import { loggerInstance } from '@/tools/winston'
import { PrismaClientInstance } from '@/tools/prisma'
import { limitInvokePromiseAllFulfilled } from '@/tools/Promise'
import { RECORD_STATUS } from '@/constants/status'
import { SOURCE_CODE_BUSINESS, REPO_TYPE } from '@/shared'

import { RemoteBusinessConfigService } from './remote-business-config.service'
import { RepoMetaService } from '../repo-meta/repo-meta.service'
import { MaterialService } from '@/modules/material/material.service'
import { reasonableGitUrlParse } from '@/tools/git'
import { KconfService } from '@/modules/kconf/kconf.service'

@Injectable()
export class BusinessConfigService {
  private readonly prisma = PrismaClientInstance
  private readonly logger = loggerInstance
  constructor(
    private readonly remoteBusinessConfigService: RemoteBusinessConfigService,
    private readonly materialService: MaterialService,
    private readonly repoMetaService: RepoMetaService,
    private readonly kconfService: KconfService,
  ) {}

  private async serialize(
    record: Service.Analysis.BusinessConfig.BusinessConfigRecord,
  ): Promise<Service.Analysis.BusinessConfig.SerializeBusinessConfigRecord> {
    try {
      return Object.freeze({
        ...record,
        meta: record.meta
          ? (JSON.parse(
            record.meta,
          ) as Service.Analysis.BusinessConfig.BusinessMeta)
          : null,
      })
    }
    catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Failed to serialize business config: ${logStringify({
            id: record.id,
            error: error.toString(),
          })}`,
        )
      }
    }
  }

  async findOneByBusiness(
    business: SOURCE_CODE_BUSINESS,
  ): Promise<Service.Analysis.BusinessConfig.SerializeBusinessConfigRecord | null> {
    const record = await this.prisma.business_config.findFirst({
      where: { business, status: RECORD_STATUS.EFFECT },
    })
    return record ? this.serialize(record) : null
  }

  async queryBusinessProfessionProjects(
    business: SOURCE_CODE_BUSINESS,
  ): Promise<Service.Analysis.BusinessConfig.FullBusinessProjectConfig[]> {
    const record = await this.findOneByBusiness(business)
    const bizExcludePackages
      = business === SOURCE_CODE_BUSINESS.BIZ
        ? await this.kconfService.getBizExcludePackages()
        : []
    const userConfiguredProfessionProjects
      = record?.meta?.report?.projects ?? []
    const remoteProfessionProjects
      = await this.remoteBusinessConfigService.fetchBusinessRemoteProfessionProjectConfig(
        business,
      )
    const mergedProfessionProjects = uniqBy(
      userConfiguredProfessionProjects.concat(remoteProfessionProjects),
      item => reasonableGitUrlParse(item.git_url).full_name,
    )
    const refreshedRepoMetaProfessionProjects
      = await limitInvokePromiseAllFulfilled(
        mergedProfessionProjects.map(project => async (): Promise<Service.Analysis.BusinessConfig.FullBusinessProjectConfig> => {
          const { clone_url, project_id }
            = await this.repoMetaService.findOrCreateRepoMeta({
              clone_url: project.git_url,
              business,
            })
          let black_pkgs: string[] = []
          if (business === SOURCE_CODE_BUSINESS.BIZ) {
            black_pkgs = bizExcludePackages
              .filter(item => item.gitUrl === project.git_url)
              .map(item => item.pkgName)
            if (black_pkgs.includes('*')) {
              return null
            }
          }
          return {
            ...project,
            git_url: clone_url,
            project_id,
            type: REPO_TYPE.PROFESSION,
            namespace: reasonableGitUrlParse(clone_url).full_name,
            black_pkgs,
          }
        }),
      )

    refreshedRepoMetaProfessionProjects.errors.forEach((error) => {
      this.logger.error('Failed to refresh project git url', error)
    })

    return refreshedRepoMetaProfessionProjects.result.filter(item => !!item)
  }

  async queryBusinessMaterialProjects(
    business: SOURCE_CODE_BUSINESS,
  ): Promise<Service.Analysis.BusinessConfig.FullBusinessProjectConfig[]> {
    const record = await this.findOneByBusiness(business)
    const userConfiguredMaterialProjects
      = record?.meta?.material?.projects ?? []
    const { result: databaseMaterialProjects, errors }
      = await this.materialService.findAllMaterialProjectsByBusiness(business)

    errors.forEach((error) => {
      this.logger.warn('Failed to find material projects', error)
    })

    const mergedMaterialProjects = uniqBy(
      userConfiguredMaterialProjects.concat(databaseMaterialProjects),
      item => reasonableGitUrlParse(item.git_url).full_name,
    )
    const refreshedRepoMetaMaterialProjects
      = await limitInvokePromiseAllFulfilled(
        mergedMaterialProjects.map(project => async (): Promise<Service.Analysis.BusinessConfig.FullBusinessProjectConfig> => {
          const { clone_url, project_id }
            = await this.repoMetaService.findOrCreateRepoMeta({
              clone_url: project.git_url,
              business,
            })
          return {
            ...project,
            project_id,
            type: REPO_TYPE.MATERIAL,
            git_url: clone_url,
            namespace: reasonableGitUrlParse(clone_url).full_name,
          }
        }),
      )
    refreshedRepoMetaMaterialProjects.errors.forEach((error) => {
      this.logger.warn('Failed to refresh project git url', error)
    })

    return refreshedRepoMetaMaterialProjects.result
  }
}
