import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'
import { BusinessConfigService } from './business-config.service'
import { SOURCE_CODE_BUSINESS } from '@/shared'

@ApiTags('analysis/business-config')
@Controller('business-config')
export class BusinessConfigController {
  constructor(private readonly businessConfigService: BusinessConfigService) {}

  @Get('/projects')
  async getBusinessProjects(@Query('business') business: SOURCE_CODE_BUSINESS): Promise<{
    professionProjects: Service.Analysis.BusinessConfig.FullBusinessProjectConfig[]
    materialProjects: Service.Analysis.BusinessConfig.FullBusinessProjectConfig[]
  }> {
    const professionProjects = await this.businessConfigService.queryBusinessProfessionProjects(business)
    const materialProjects = await this.businessConfigService.queryBusinessMaterialProjects(business)
    return {
      professionProjects,
      materialProjects,
    }
  }
}
