import { Module } from '@nestjs/common'
import { BusinessConfigController } from './business-config.controller'
import { BusinessConfigService } from './business-config.service'
import { RemoteBusinessConfigService } from './remote-business-config.service'
import { MaterialModule } from '@/modules/material/material.module'
import { RepoMetaModule } from '../repo-meta/repo-meta.module'
import { KconfModule } from '@/modules/kconf/kconf.module'

@Module({
  imports: [MaterialModule, RepoMetaModule, KconfModule],
  controllers: [BusinessConfigController],
  providers: [BusinessConfigService, RemoteBusinessConfigService],
  exports: [BusinessConfigService, RemoteBusinessConfigService],
})
export class BusinessConfigModule {}
