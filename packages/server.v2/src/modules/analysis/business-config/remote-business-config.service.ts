import axios from 'axios'
import { Injectable } from '@nestjs/common'

import { SOURCE_CODE_BUSINESS } from '@/shared'
import { IS_DEV } from '@/constants/envs'
import { loggerInstance } from '@/tools/winston'
import { getNamespaceFromGitUrl } from '@/tools/git'
import { isEmpty } from 'lodash'

@Injectable()
export class RemoteBusinessConfigService {
  private readonly logger = loggerInstance

  async fetchBusinessRemoteProfessionProjectConfig(
    business: SOURCE_CODE_BUSINESS,
  ): Promise<Service.Analysis.BusinessConfig.CoreBusinessProjectConfig[]> {
    switch (business) {
      case SOURCE_CODE_BUSINESS.ES:
        return this.fetchESProfessionProjects()
      case SOURCE_CODE_BUSINESS.BIZ:
        return this.fetchBizProfessionProjects()
      default:
        return []
    }
  }

  async fetchBizProfessionProjects(): Promise<
    Service.Analysis.BusinessConfig.CoreBusinessProjectConfig[]
  > {
    return axios
      .get(
        IS_DEV
          ? 'https://drow.corp.kuaishou.com/api/measure/apps'
          : 'http://drow.internal/api/measure/apps',
      )
      .then((res) => {
        if (res.data?.result === 1) {
          return (
            res.data?.data
              ?.filter(item => Boolean(item.gitSSHUrl))
              .map((item) => {
                return {
                  title: item.title,
                  namespace: getNamespaceFromGitUrl(item.git_url),
                  git_url: item.git_url,
                  pkg_filter: item.pkg_filter,
                  router_entry: item.router_entry,
                  branch: item.branch,
                  black_list: [
                    ...(isEmpty(item.black_list)
                      ? ['**/*.lc.*']
                      : item.black_list),
                    '**/mock/**',
                    '**/mocks/**',
                  ],
                }
              }) ?? []
          )
        }
        throw new Error(res.data?.msg || '接口错误')
      })
      .catch((error) => {
        this.logger.warn(
          'Fetch Biz profession projects failed, error message is',
          error.message || 'Request Error',
        )
        return []
      })
  }

  async fetchESProfessionProjects(): Promise<
    Service.Analysis.BusinessConfig.CoreBusinessProjectConfig[]
  > {
    return axios
      .get('https://chitu.corp.kuaishou.com/rest/jia/app/all', {
        headers: {
          Authorization: Buffer.from(
            'QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUoxYzJWeWJtRnRaU0k2SW14cGRXeGxhVEV4SWl3aVpHbHpjR3hoZVc1aGJXVWlPaUxsaUpqbm80b2lMQ0poZG1GMFlYSWlPaUpvZEhSd2N6b3ZMM04wWVhScFl5NTVlR2x0WjNNdVkyOXRMMkp6TWk5cmFXMUJkbUYwWVhJdk5HRXlaakE1T1dFM1pUbGtORE14TURnMU9HUTFZekJpWldObU16azBaVFVpTENKdFlXbHNJam9pYkdsMWJHVnBNVEZBYTNWaGFYTm9iM1V1WTI5dElpd2lhV0YwSWpveE56RTVNakUwTkRnMWZRLk40eHA3VlB2MV9Ca2hJZTZjdVN3UUVuSWxRSmRuRVNlN2dlRjNkMlJpQlk=',
            'base64',
          ).toString(),
        },
      })
      .then((res) => {
        if (res.data?.result === 1 && res.data?.data.list) {
          return res.data.data.list
            .filter(
              item =>
                Boolean(item.gitUrl)
                && [
                  'rn-web',
                  'react-web',
                  'react-web-h5',
                  'micro-main',
                  'micro-sub',
                ].includes(item.type),
            )
            .map((item) => {
              return {
                title: item.name || item.desc,
                namespace: getNamespaceFromGitUrl(item.gitUrl),
                git_url: item.gitUrl,
              }
            })
        }
        throw new Error(res.data?.errorMsg || '接口错误')
      })
      .catch((error) => {
        this.logger.warn(
          'Fetch ES profession projects failed, error message is',
          error.message || 'Request Error',
        )
        return []
      })
  }
}
