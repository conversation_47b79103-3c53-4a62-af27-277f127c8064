import assert from 'assert'
import uniqBy from 'lodash/uniqBy'
import { Injectable } from '@nestjs/common'
import { analysis_route_file_usage, Prisma } from '@prisma/client'
import { LibraryMaterialUsage } from '@ks-material-middleoffice/measure-sdk'

import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import {
  limitInvokePromiseAllFulfilled,
  PromiseAllFulfilled,
  PromiseAllFulfilledResult,
} from '@/tools/Promise'
import { RECORD_STATUS } from '@/constants/status'
import { ALL_SOURCE_CODE_BUSINESS, SOURCE_CODE_BUSINESS, now } from '@/shared'
import { mergeLibraryMaterialUsageWithoutClone } from '@/tools/material-usage'
import { loggerInstance } from '@/tools/winston'
import { logStringify } from '@/tools/json'
import { chunkArray } from '@/tools/array'

@Injectable()
export class MaterialUsageService {
  private readonly prisma = PrismaClientInstance
  private readonly logger = loggerInstance

  async serialize(
    record: analysis_route_file_usage,
  ): Promise<Service.Analysis.MaterialUsage.SerializeAnalysisMaterialUsage> {
    try {
      return Object.freeze({
        ...record,
        material_info: record.material_info
          ? (JSON.parse(record.material_info) as LibraryMaterialUsage)
          : {},
        filter_material_info: record.filter_material_info
          ? (JSON.parse(record.filter_material_info) as LibraryMaterialUsage)
          : {},
      })
    }
    catch (error) {
      throw new Error(
        `Failed to serialize material usage: ${logStringify({ id: record.id, error: error.toString() })}`,
      )
    }
  }

  async create(
    params: Service.Analysis.MaterialUsage.CreateParams,
  ): Promise<Service.Analysis.MaterialUsage.MaterialUsageRecord> {
    return this.prisma.analysis_route_file_usage.create({
      data: {
        ...params,
        id: genPrimaryIndex(),
        create_time: now(),
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async batchCreate(
    params: Service.Analysis.MaterialUsage.CreateParams[],
  ): Promise<PromiseAllFulfilledResult<Prisma.BatchPayload>> {
    const create_time = now()
    const chunkedParamsList = chunkArray(params, 20)
    return limitInvokePromiseAllFulfilled(
      chunkedParamsList.map(
        chunkedParams => async () =>
          this.prisma.analysis_route_file_usage.createMany({
            data: chunkedParams.map(param => ({
              ...param,
              id: genPrimaryIndex(),
              create_time,
              status: RECORD_STATUS.EFFECT,
            })),
          }),
      ),
    )
  }

  async batchDelete(params: {
    scanId: number
    businesses: SOURCE_CODE_BUSINESS[]
  }): Promise<Prisma.BatchPayload> {
    return this.prisma.analysis_route_file_usage.updateMany({
      where: {
        scan_id: params.scanId,
        business: {
          in: params.businesses,
        },
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async findListByScanIdAndBusinessUniqueByRepoAndFilePath(
    scanId: number,
    businesses: SOURCE_CODE_BUSINESS[],
  ): Promise<Service.Analysis.MaterialUsage.SerializeAnalysisMaterialUsage[]> {
    const records = await this.prisma.analysis_route_file_usage.findMany({
      where: {
        scan_id: scanId,
        business: {
          in: businesses,
        },
        status: RECORD_STATUS.EFFECT,
      },
    })

    const { errors, result } = await PromiseAllFulfilled(
      records.map(this.serialize),
    )

    errors.forEach((error) => {
      this.logger.warn(
        `Failed to find list by scan id and business: ${error.message}`,
      )
    })

    return uniqBy(
      result,
      item => `${item.repo_project_id}/${item.file_path}`,
    )
  }

  async findProjectMaterialUsageGroupByFilePath(
    params: Service.Analysis.MaterialUsage.FindProjectMaterialUsageParams,
  ): Promise<
      PromiseAllFulfilledResult<Service.Analysis.MaterialUsage.SerializeAnalysisMaterialUsage>
    > {
    assert(params.scanId, '扫描 ID 不能为空')
    assert(params.repoProjectId, '项目 ID 不能为空')

    const records = await this.prisma.analysis_route_file_usage.findMany({
      where: {
        repo_project_id: params.repoProjectId,
        scan_id: params.scanId,
        status: RECORD_STATUS.EFFECT,
      },
      distinct: ['file_path'],
    })

    return PromiseAllFulfilled(records.map(record => this.serialize(record)))
  }

  async findAllMaterialUsageByScanGroupByBusiness(
    scanId: number,
    businesses: SOURCE_CODE_BUSINESS[] = ALL_SOURCE_CODE_BUSINESS,
  ): Promise<Service.Analysis.MaterialUsage.BusinessMaterialUsageReport[]> {
    assert(scanId, '扫描 ID 不能为空')

    const records = await this.prisma.analysis_route_file_usage.findMany({
      where: {
        scan_id: scanId,
        business: {
          in: businesses,
        },
        status: RECORD_STATUS.EFFECT,
      },
      distinct: ['repo_project_id', 'file_path'],
    })

    const result: Service.Analysis.MaterialUsage.BusinessMaterialUsageReport[]
      = businesses.map(business => ({
        business,
        usage: {},
      }))

    const { errors } = await PromiseAllFulfilled(
      records.map(async (record) => {
        const { business, filter_material_info } = await this.serialize(record)
        const businessReport = result.find(
          item => item.business === business,
        )
        assert(businessReport, `未知的业务类型: ${business}`)
        businessReport.usage = mergeLibraryMaterialUsageWithoutClone(
          businessReport.usage,
          filter_material_info,
        )
      }),
    )

    errors.forEach((error) => {
      this.logger.warn(
        `Failed to find all material usage by scan:\n${error.message}`,
      )
    })

    return result
  }

  async findProjectPageCoverage(params: {
    scanId: number
    repoProjectId: number
  }): Promise<{ usedMaterialPages: number, total: number }> {
    const { result: pages }
      = await this.findProjectMaterialUsageGroupByFilePath(params)
    return {
      usedMaterialPages: pages.filter(
        page => Object.keys(page.filter_material_info).length > 0,
      ).length,
      total: pages.length,
    }
  }
}
