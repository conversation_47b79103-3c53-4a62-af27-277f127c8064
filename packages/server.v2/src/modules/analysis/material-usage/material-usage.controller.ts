import { Controller, Get, Param, Query } from '@nestjs/common'
import { PromiseAllFulfilledResult } from '@/tools/Promise'
import { ApiTags } from '@nestjs/swagger'

import { MaterialUsageService } from './material-usage.service'
import { FindProjectMaterialUsageDTO } from './dto/find-project-material-usage.dto'

@ApiTags('analysis/material-usage')
@Controller('material-usage')
export class MaterialUsageController {
  constructor(
    private readonly materialUsageService: MaterialUsageService,
  ) {}

  @Get('project/files')
  findProjectMaterialUsageByFiles(
    @Query() query: FindProjectMaterialUsageDTO,
  ): Promise<
      PromiseAllFulfilledResult<Service.Analysis.MaterialUsage.SerializeAnalysisMaterialUsage>
    > {
    return this.materialUsageService.findProjectMaterialUsageGroupByFilePath(
      query,
    )
  }

  @Get('scan/:scanId')
  findAllMaterialUsageByScan(
    @Param('scanId') scanId: string,
  ): Promise<Service.Analysis.MaterialUsage.BusinessMaterialUsageReport[]> {
    return this.materialUsageService.findAllMaterialUsageByScanGroupByBusiness(
      +scanId,
    )
  }
}
