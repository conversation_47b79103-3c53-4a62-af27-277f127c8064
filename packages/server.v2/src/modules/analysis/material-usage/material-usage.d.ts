declare namespace Service.Analysis.MaterialUsage {
  export type MaterialUsageRecord =
    import('@prisma/client').analysis_route_file_usage

  export type SerializeAnalysisMaterialUsage =
    import('type-fest').OverrideProperties<
      MaterialUsageRecord,
      {
        material_info: import('@ks-material-middleoffice/measure-sdk').LibraryMaterialUsage
        filter_material_info: import('@ks-material-middleoffice/measure-sdk').LibraryMaterialUsage
      }
    >

  export type CreateParams = Required<
    Omit<
      import('@prisma/client').analysis_route_file_usage,
      'id' | 'status' | 'create_time'
    >
  >

  export interface FindProjectMaterialUsageParams {
    scanId: number
    repoProjectId: number
  }

  export interface BusinessMaterialUsageReport {
    business: import('@/constants/business').Business
    usage: import('@ks-material-middleoffice/measure-sdk').LibraryMaterialUsage
  }
}
