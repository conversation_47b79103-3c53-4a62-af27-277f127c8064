import assert from 'assert'
import { ApiTags } from '@nestjs/swagger'
import { Body, Controller, Post } from '@nestjs/common'
import { APPModeReport, LibraryModeReport } from '@ks-material-middleoffice/measure-sdk'

import { loggerInstance } from '@/tools/winston'
import {
  ALL_SOURCE_CODE_BUSINESS,
  ALL_REPO_TYPE,
  SOURCE_CODE_BUSINESS,
  getWeekRange,
  REPO_TYPE,
} from '@/shared'

import { ScanService } from './scan.service'
import { AnalysisService } from './analysis.service'
import { ForwardService } from '../forward/forward.service'
import { ScanInfoService } from './scan-info/scan-info.service'

@ApiTags('analysis')
@Controller('analysis')
export class AnalysisController {
  private readonly logger = loggerInstance
  constructor(
    private readonly analysisService: AnalysisService,
    private readonly scanService: ScanService,
    private readonly forwardService: ForwardService,
    private readonly scanInfoService: ScanInfoService,
  ) {}

  @Post('exec')
  async scanExec(
    @Body() body: Service.Analysis.ScanOneProjectParams<REPO_TYPE>,
  ): Promise<APPModeReport | LibraryModeReport> {
    return this.scanService.scanProject(
      body.type,
      body.gitURL,
      body.startDate,
      body.endDate,
      body.partialOptions,
    )
  }

  @Post('full-process')
  async fullProcess(@Body() body: Service.Analysis.FullProcessAnalysisParams): Promise<void> {
    return this.analysisService.fullProcessAnalysisByParams(body)
  }

  @Post('/distributed/full-process')
  async distributedFullProcess(
    @Body() body: Service.Analysis.FullProcessAnalysisParams,
  ): Promise<void> {
    const {
      time,
      tag = 'material-points-base-index',
      businesses = ALL_SOURCE_CODE_BUSINESS,
      repoTypes = ALL_REPO_TYPE,
    } = body

    const { start, end } = getWeekRange(time)

    // ======================== 创建或更新 scan ========================
    const currentScanInfo = await this.scanInfoService.createOrUpdate({
      type: tag,
      startTime: start,
      endTime: end,
      businesses,
    })

    for (const business of businesses) {
      this.forwardService.forwardOtherService(
        business,
        'api/analysis/full-process',
        {
          time: currentScanInfo.start_time + 1,
          tag,
          businesses: [business],
          repoTypes,
        },
      )
    }
  }

  @Post('full-process-by-scan-id')
  async fullProcessByScanId(
    @Body()
    body: {
      scanId: number
      businesses?: SOURCE_CODE_BUSINESS[]
      repoTypes?: REPO_TYPE[]
    },
  ): Promise<void> {
    return this.analysisService.fullProcessAnalysisByScanId(body.scanId, {
      businesses: body?.businesses,
      repoTypes: body?.repoTypes,
    })
  }

  @Post('/distributed/full-process-by-scan-id')
  async distributedFullProcessByScanId(
    @Body()
    body: {
      scanId: number
      businesses?: SOURCE_CODE_BUSINESS[]
      repoTypes?: REPO_TYPE[]
    },
  ): Promise<void> {
    assert(body.scanId, 'scanId is required')
    const {
      scanId,
      businesses = ALL_SOURCE_CODE_BUSINESS,
      repoTypes = ALL_REPO_TYPE,
    } = body
    for (const business of businesses) {
      this.forwardService.forwardOtherService(
        business,
        'api/analysis/full-process-by-scan-id',
        {
          scanId,
          businesses: [business],
          repoTypes,
        },
      )
    }
  }

  @Post('raw-data')
  async analysisRawData(
    @Body()
    body: {
      scanId: number
      businesses?: SOURCE_CODE_BUSINESS[]
      repoTypes?: REPO_TYPE[]
    },
  ): Promise<void> {
    return this.analysisService.analysisRawData(body.scanId, {
      businesses: body.businesses,
      repoTypes: body.repoTypes,
    })
  }

  @Post('after-raw-data')
  async analysisAfterRawData(
    @Body()
    body: {
      scanId: number
      businesses?: SOURCE_CODE_BUSINESS[]
      repoTypes?: REPO_TYPE[]
    },
  ): Promise<void> {
    return this.analysisService.analysisAfterRawData(body.scanId, {
      businesses: body.businesses,
      repoTypes: body.repoTypes,
    })
  }

  @Post('batch/refresh-by-scan-id-list')
  async targetBusinessFullProcessScanByScanIdList(
    @Body()
    body: {
      scanIds: number[]
      businesses?: SOURCE_CODE_BUSINESS[]
      repoTypes?: REPO_TYPE[]
    },
  ): Promise<void> {
    const { businesses = ALL_SOURCE_CODE_BUSINESS, repoTypes = ALL_REPO_TYPE } = body
    for (const scanId of body.scanIds) {
      await this.analysisService.fullProcessAnalysisByScanId(scanId, {
        businesses: businesses,
        repoTypes: repoTypes,
      })
    }
  }

  @Post('distributed/batch/refresh-by-scan-id-list')
  async refreshScanByScanIdList(
    @Body()
    body: {
      scanIds: number[]
      businesses?: SOURCE_CODE_BUSINESS[]
      repoTypes?: REPO_TYPE[]
    },
  ): Promise<void> {
    const {
      scanIds,
      businesses = ALL_SOURCE_CODE_BUSINESS,
      repoTypes = ALL_REPO_TYPE,
    } = body
    for (const business of businesses) {
      this.forwardService.forwardOtherService(
        business,
        'api/analysis/batch/refresh-by-scan-id-list',
        {
          scanIds,
          businesses: [business],
          repoTypes,
        },
      )
    }
  }
}
