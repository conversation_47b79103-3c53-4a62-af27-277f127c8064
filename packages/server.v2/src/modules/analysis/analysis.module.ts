import { Module } from '@nestjs/common'

import { AnalysisController } from './analysis.controller'
import { AnalysisService } from './analysis.service'
import { BusinessConfigModule } from './business-config/business-config.module'
import { ScanInfoModule } from './scan-info/scan-info.module'
import { MaterialModule } from '../material/material.module'
import { ScanService } from './scan.service'
import { RawDataModule } from './raw-data/raw-data.module'
import { MaterialMetaModule } from './material-meta/material-meta.module'
import { CodeLinesModule } from './code-lines/code-lines.module'
import { NewCodeLinesModule } from './new-code-lines/new-code-lines.module'
import { MaterialUsageModule } from './material-usage/material-usage.module'
import { NewMaterialUsageModule } from './new-material-usage/new-material-usage.module'
import { ComputeModule } from '../compute/compute.module'
import { ForwardModule } from '../forward/forward.module'
import { ProjectCollectionModule } from './project-collection/project-collection.module'

@Module({
  imports: [
    ScanInfoModule,
    BusinessConfigModule,
    MaterialModule,
    RawDataModule,
    MaterialMetaModule,
    CodeLinesModule,
    NewCodeLinesModule,
    MaterialUsageModule,
    NewMaterialUsageModule,
    ComputeModule,
    ForwardModule,
    ProjectCollectionModule,
  ],
  controllers: [AnalysisController],
  providers: [AnalysisService, ScanService],
  exports: [AnalysisService, ScanService],
})
export class AnalysisModule {}
