import assert from 'assert'
import { JsonValue } from 'type-fest'
import { Injectable } from '@nestjs/common'
import {
  LibraryMaterialUsage,
  isAppModeReport,
  isLibraryModeReport,
} from '@ks-material-middleoffice/measure-sdk'

import { loggerInstance } from '@/tools/winston'
import { limitInvokePromiseAllFulfilled } from '@/tools/Promise'
import { eraseLibraryMaterialUsageWithoutClone } from '@/tools/material-usage'
import { logStringify, safeStringifyMaybeNull } from '@/tools/json'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import {
  ALL_SOURCE_CODE_BUSINESS,
  ALL_REPO_TYPE,
  SOURCE_CODE_BUSINESS,
  getWeekRange,
  REPO_TYPE,
} from '@/shared'

import { ScanInfoService } from './scan-info/scan-info.service'
import { BusinessConfigService } from './business-config/business-config.service'
import { MaterialService } from '../material/material.service'
import { ScanService } from './scan.service'
import { RawDataService } from './raw-data/raw-data.service'
import { MaterialMetaService } from './material-meta/material-meta.service'
import { CodeLinesService } from './code-lines/code-lines.service'
import { NewCodeLinesService } from './new-code-lines/new-code-lines.service'
import { MaterialUsageService } from './material-usage/material-usage.service'
import { NewMaterialUsageService } from './new-material-usage/new-material-usage.service'
import { ComputeService } from '../compute/compute.service'
import { omit } from '@/tools/object'
import { isObject } from '@/tools/type'

@Injectable()
export class AnalysisService {
  private readonly prisma = PrismaClientInstance
  private readonly logger = loggerInstance
  constructor(
    private readonly materialService: MaterialService,
    private readonly scanInfoService: ScanInfoService,
    private readonly businessConfigService: BusinessConfigService,
    private readonly scanService: ScanService,
    private readonly rawDataService: RawDataService,
    private readonly materialMetaService: MaterialMetaService,
    private readonly codeLinesService: CodeLinesService,
    private readonly newCodeLinesService: NewCodeLinesService,
    private readonly materialUsageService: MaterialUsageService,
    private readonly newMaterialUsageService: NewMaterialUsageService,
    private readonly computeService: ComputeService,
  ) {}

  async fullProcessAnalysisByParams(
    params: Service.Analysis.FullProcessAnalysisParams,
  ): Promise<void> {
    const {
      time,
      tag = 'test',
      businesses = ALL_SOURCE_CODE_BUSINESS,
      repoTypes = ALL_REPO_TYPE,
    } = params
    const { start, end } = getWeekRange(time)

    // ======================== 创建或更新 scan ========================
    const currentScanInfo = await this.scanInfoService.createOrUpdate({
      type: tag,
      startTime: start,
      endTime: end,
      businesses,
    })

    // ======================== 按照部门分开扫描 ========================
    await this.analysisRawData(currentScanInfo.id, {
      businesses: businesses,
      repoTypes: repoTypes,
    })

    // ======================== 全量物料信息 ========================
    const allMaterial = await this.materialService.findAllBusinessComponent()
    return this.analysisAfterRawData(currentScanInfo.id, {
      businesses: businesses,
      repoTypes: repoTypes,
      allMaterial,
    })
  }

  async fullProcessAnalysisByScanId(
    scanId: number,
    options?: { businesses?: SOURCE_CODE_BUSINESS[], repoTypes?: REPO_TYPE[] },
  ): Promise<void> {
    const scanInfo = await this.scanInfoService.findOneById(scanId)

    if (!scanInfo) {
      throw new Error(`Scan info not found: ${logStringify({ scanId })}`)
    }

    const {
      businesses = scanInfo.scan_range as SOURCE_CODE_BUSINESS[],
      repoTypes = ALL_REPO_TYPE,
    } = options || {}

    // ======================== 更新 scan info ========================
    await this.scanInfoService.update(scanId, {
      scan_range: JSON.stringify([
        ...new Set([...businesses, ...scanInfo.scan_range]),
      ]),
    })

    // ======================== 按照部门分开扫描 ========================
    await this.analysisRawData(scanId, {
      businesses: businesses,
      repoTypes: repoTypes,
    })

    // ======================== 全量物料信息 ========================
    const allMaterial = await this.materialService.findAllBusinessComponent()
    return this.analysisAfterRawData(scanId, {
      businesses: businesses,
      repoTypes: repoTypes,
      allMaterial,
    })
  }

  async analysisRawData(
    scanId: number,
    options?: {
      businesses?: SOURCE_CODE_BUSINESS[]
      repoTypes?: REPO_TYPE[]
    },
  ): Promise<void> {
    const scanInfo = await this.scanInfoService.findOneById(scanId)

    if (!scanInfo) {
      throw new Error(`Scan info not found: ${logStringify({ scanId })}`)
    }

    this.logger.info(`
      Start to analysis raw data: ${logStringify({
        scanId,
        start_time: scanInfo.start_time,
        end_time: scanInfo.end_time,
        options,
      })}`)

    const { end_time, start_time, scan_range } = scanInfo
    const { businesses = scan_range as SOURCE_CODE_BUSINESS[], repoTypes = ALL_REPO_TYPE }
      = options ?? {}

    // ======================== 按照部门分开扫描 ========================
    await limitInvokePromiseAllFulfilled(
      businesses.map(
        business => async (): Promise<void> => {
          // 1. 查找项目
          const professionProjects = repoTypes.includes(REPO_TYPE.PROFESSION)
            ? await this.businessConfigService.queryBusinessProfessionProjects(
              business,
            )
            : []
          const materialProjects = repoTypes.includes(REPO_TYPE.MATERIAL)
            ? await this.businessConfigService.queryBusinessMaterialProjects(
              business,
            )
            : []

          const allProjects = [...professionProjects, ...materialProjects]

          this.logger.info(
            `Find projects: ${logStringify({
              scanId,
              businesses,
              repoTypes,
              length: allProjects.length,
              urls: allProjects.map(project => project.git_url),
            })}`,
          )

          this.logger.info(
            `Start to clear history raw data: ${logStringify({
              scanId,
              businesses,
              repoTypes,
            })}`,
          )

          await this.rawDataService.batchDelete({
            scanId: scanId,
            businesses: [business],
            repoTypes,
          })

          // 2. 扫描并生成底表数据
          for (let i = 0; i < allProjects.length; i++) {
            const project = allProjects[i]
            try {
              const rawData = await this.scanService.scanProject(
                project.type,
                project.git_url,
                start_time,
                end_time,
                {
                  branch: project.branch,
                  entry: project.router_entry,
                  blackList: project.black_list,
                  pkgFilter: project.pkg_filter,
                  blackPkgs: project.black_pkgs,
                },
              )

              // 3. 插入底表记录
              this.logger.info(
                `Start to create raw data: ${logStringify({
                  scanId: scanId,
                  businesses,
                  repoTypes,
                  project: project as unknown as JsonValue,
                })}`,
              )
              await this.rawDataService.create({
                scan_id: scanId,
                repo_project_id: project.project_id,
                business,
                content: safeStringifyMaybeNull(
                  rawData as unknown as JsonValue,
                ),
                type: project.type,
              })
            }
            catch (error) {
              this.logger.error(
                `Scan project error: ${logStringify({
                  project: project as unknown as JsonValue,
                  error: error.toString(),
                })}`,
              )
            }
          }
        },
        2,
      ),
    )
  }

  async analysisAfterRawData(
    scanId: number,
    options?: {
      businesses?: SOURCE_CODE_BUSINESS[]
      repoTypes?: REPO_TYPE[]
      allMaterial?: Service.Material.MaterialInfoForAnalysis[]
    },
  ): Promise<void> {
    const {
      businesses = ALL_SOURCE_CODE_BUSINESS,
      repoTypes = ALL_REPO_TYPE,
      allMaterial = await this.materialService.findAllBusinessComponent(),
    } = options ?? {}

    // ======================== 创建明细表 ========================
    this.logger.info(
      `Start to analysis raw data: ${logStringify({
        scanId,
        businesses,
        repoTypes,
      })}`,
    )
    await this.analysisDetailInfo(scanId, {
      businesses: businesses,
      repoTypes: repoTypes,
      allMaterial,
    })

    // ======================== 创建新增物料使用记录 ========================
    this.logger.info(
      `Start to analysis new material usage: ${logStringify({
        scanId,
        businesses,
      })}`,
    )
    await this.analysisNewMaterialUsage(scanId, {
      businesses: businesses,
    })

    // ======================== 处理有的物料库直接导出第三方物料库的情况 ========================
    this.logger.info(
      `Start to sync material meta to export all from another package: ${logStringify(
        {
          scanId,
        },
      )}`,
    )
    await this.materialMetaService.syncMaterialMateToExportAllFromAnotherPackage(
      scanId,
      businesses,
    )

    // ======================== 指标计算 ========================
    this.logger.info(
      `Start to compute: ${logStringify({
        scanId,
      })}`,
    )
    await this.computeService.computeNewCodeCoverage(scanId, { businesses })
    await this.computeService.computeMaterialPointsBaseInfo(scanId)
  }

  async analysisDetailInfo(
    scanId: number,
    options?: {
      businesses?: SOURCE_CODE_BUSINESS[]
      repoTypes?: REPO_TYPE[]
      allMaterial?: Service.Material.MaterialInfoForAnalysis[]
    },
  ): Promise<void> {
    const {
      businesses = ALL_SOURCE_CODE_BUSINESS,
      repoTypes = ALL_REPO_TYPE,
      allMaterial = await this.materialService.findAllBusinessComponent(),
    } = options || {}

    if (repoTypes.includes(REPO_TYPE.MATERIAL)) {
      await this.analysisMaterialRawData(scanId, {
        businesses,
        allMaterial,
      })
    }
    if (repoTypes.includes(REPO_TYPE.PROFESSION)) {
      await this.analysisProfessionRawData(scanId, {
        businesses,
        allMaterial,
      })
    }
  }

  async analysisMaterialRawData(
    scanId: number,
    options?: {
      businesses?: SOURCE_CODE_BUSINESS[]
      allMaterial?: Service.Material.MaterialInfoForAnalysis[]
    },
  ): Promise<void> {
    const {
      businesses = ALL_SOURCE_CODE_BUSINESS,
      allMaterial = await this.materialService.findAllBusinessComponent(),
    } = options || {}
    const { result: materialRawDataRecords, errors: findRawDataErrors }
      = await this.rawDataService.findList({
        scanId,
        businesses,
        repoTypes: [REPO_TYPE.MATERIAL],
      })
    findRawDataErrors.forEach((error) => {
      this.logger.warn(
        `Find material raw data error: ${logStringify({
          scanId,
          businesses,
          error: {
            message: error.message,
            stack: error.stack,
          },
        })}`,
      )
    })

    this.logger.info(
      `Start to clear history 'material meta' records: ${logStringify({
        scanId,
        businesses,
      })}`,
    )
    await this.materialMetaService.batchDelete({
      scanId,
      businesses,
    })

    // ======================== 解析物料底表数据 ========================
    const { errors: generateMaterialMetaErrors }
      = await limitInvokePromiseAllFulfilled(
        materialRawDataRecords.map(record => async (): Promise<void> => {
          if (!record.content) {
            throw new Error(
              `Material raw data is empty: ${logStringify({
                scanId,
                businesses,
                rawDataId: record.id,
              })}`,
            )
          }

          assert(
            isLibraryModeReport(record.content),
            `content is not LibraryModeReport ${logStringify({
              rawDataId: record.id,
              scanId,
            })}`,
          )

          const packages = record.content.packages
          const needAddMaterialMeta: Service.Analysis.MaterialMeta.CreateParams[]
            = []
          for (let j = 0; j < packages.length; j++) {
            const pkg = packages[j]
            const targetMaterial = allMaterial.find(
              material => material.packageName === pkg.name,
            )
            if (!targetMaterial) continue
            if (!pkg || !pkg.materials.length) continue
            const componentBundleType = targetMaterial.componentBundleType
            if (componentBundleType === 'SLMC') {
              // 单包多组件
              for (let m = 0; m < pkg?.materials.length; m++) {
                const material = pkg.materials[m]
                // 非类型才入库
                if (material.isType === false) {
                  const third_usage = safeStringifyMaybeNull(
                    material.thirdMaterialUsage,
                  )
                  const filter_third_usage = safeStringifyMaybeNull(
                    await this.filterUsefulMaterialUsage(
                      material.thirdMaterialUsage,
                      allMaterial,
                    ),
                  )
                  needAddMaterialMeta.push({
                    scan_id: scanId,
                    business: record.business,
                    project_id: record.repo_project_id,
                    package: pkg.name,
                    type: 'SLMC',
                    code_lines: material.codeLines,
                    file_path: material.path,
                    material_name: material.name,
                    variable_name: material.variableName,
                    third_usage,
                    filter_third_usage,
                  })
                }
              }
            }
            else if (componentBundleType === 'SLSC') {
              // 单包单组件
              const names = pkg?.materials?.map(i => i.name)
              if (names.includes('default')) {
                // 找到了，放进去，用default这个component的代码行
                const index = names.indexOf('default')
                const material = pkg.materials[index]
                // 非类型才入库
                if (material.isType === false) {
                  const third_usage = safeStringifyMaybeNull(
                    material.thirdMaterialUsage,
                  )
                  const filter_third_usage = safeStringifyMaybeNull(
                    await this.filterUsefulMaterialUsage(
                      material.thirdMaterialUsage,
                      allMaterial,
                    ),
                  )
                  needAddMaterialMeta.push({
                    scan_id: scanId,
                    business: record.business,
                    project_id: record.repo_project_id,
                    package: pkg.name,
                    type: 'SLSC',
                    code_lines: material.codeLines,
                    file_path: material.path,
                    material_name: material.name,
                    variable_name: material.variableName,
                    third_usage,
                    filter_third_usage,
                  })
                }
              }
              else {
                // 没找到，强插一条，代码行用仓库的代码行
                needAddMaterialMeta.push({
                  scan_id: scanId,
                  business: record.business,
                  project_id: record.repo_project_id,
                  package: pkg.name,
                  type: 'detail',
                  code_lines: pkg.codeLines,
                  file_path: '.',
                  material_name: 'default',
                  variable_name: 'default',
                  third_usage: '{}',
                  filter_third_usage: '{}',
                })
              }
            }
          }

          this.logger.info(
            `Start to create 'material meta' records: ${logStringify({
              scanId,
              businesses,
              repoProjectId: record.repo_project_id,
            })}`,
          )
          await this.materialMetaService.batchCreate(needAddMaterialMeta)
        }),
        4,
      )

    generateMaterialMetaErrors.forEach((error) => {
      this.logger.error(
        `Generate material meta error: ${logStringify({
          scanId,
          businesses,
          error: {
            message: error.message,
            stack: error.stack,
          },
        })}`,
      )
    })
  }

  async analysisProfessionRawData(
    scanId: number,
    options?: {
      businesses?: SOURCE_CODE_BUSINESS[]
      allMaterial?: Service.Material.MaterialInfoForAnalysis[]
    },
  ): Promise<void> {
    const {
      businesses = ALL_SOURCE_CODE_BUSINESS,
      allMaterial = await this.materialService.findAllBusinessComponent(),
    } = options || {}
    const { result: professionRawDataRecords, errors: findRawDataErrors }
      = await this.rawDataService.findList({
        scanId,
        businesses,
        repoTypes: [REPO_TYPE.PROFESSION],
      })

    findRawDataErrors.forEach((error) => {
      this.logger.warn(
        `Find profession raw data error: ${logStringify({
          scanId,
          businesses,
          error: {
            message: error.message,
            stack: error.stack,
          },
        })}`,
      )
    })

    this.logger.info(
      `Start to clear history 'material usage'/'code lines'/'new code lines' records : ${logStringify(
        {
          scanId,
          businesses,
        },
      )}`,
    )

    await this.materialUsageService.batchDelete({
      scanId,
      businesses,
    })

    await this.newCodeLinesService.batchDelete({
      scanId,
      businesses,
    })

    await this.codeLinesService.batchDelete({
      scanId,
      businesses,
    })

    // ======================== 解析项目底表数据 ========================
    const { errors: analysisErrors } = await limitInvokePromiseAllFulfilled(
      professionRawDataRecords.map(record => async (): Promise<void> => {
        if (!record.content) {
          throw new Error(
            `Profession raw data is empty: ${logStringify({
              scanId,
              businesses,
              rawDataId: record.id,
            })}`,
          )
        }

        assert(
          isAppModeReport(record.content),
          `content is not AppModeReport ${logStringify({
            rawDataId: record.id,
            scanId,
          })}`,
        )

        // 过程指标物料信息明细表
        const packages = record.content.packages

        const materialUsageRecords: Service.Analysis.MaterialUsage.CreateParams[]
          = []

        for (let j = 0; j < packages.length; j++) {
          const pkg = packages[j]
          if (!pkg || !pkg.pages?.length) continue
          for (let m = 0; m < pkg.pages.length; m++) {
            const page = pkg.pages[m]
            const fileLength = page.files.length
            const routerPath = page.browserPath + '::' + genPrimaryIndex()
            for (let n = 0; n < fileLength; n++) {
              const file = page.files[n]
              const material_info = safeStringifyMaybeNull(
                file.libraryMaterialUsage,
              )
              const filter_material_info = safeStringifyMaybeNull(
                await this.filterUsefulMaterialUsage(
                  file.libraryMaterialUsage,
                  allMaterial,
                ),
              )

              materialUsageRecords.push({
                scan_id: scanId,
                business: record.business,
                repo_project_id: record.repo_project_id,
                package: pkg.name,
                route_path: routerPath,
                file_path: file.path,
                file_create_time: page.createTime,
                material_info,
                filter_material_info,
              })
            }
          }
        }

        const newCodeLinesRecord: Service.Analysis.NewCodeLines.CreateParams = {
          business: record.business,
          repo_project_id: record.repo_project_id,
          scan_id: scanId,
          new_code_lines: record.content.newCodeLines,
          start_commit_hash: record.content.startCommitHash,
          end_commit_hash: record.content.endCommitHash,
        }

        const codeLinesRecord: Service.Analysis.CodeLines.CreateParams = {
          business: record.business,
          repo_project_id: record.repo_project_id,
          scan_id: scanId,
          code_lines: record.content.codeLines,
        }

        this.logger.info(
          `Start to create 'material usage'/'code lines'/'new code lines' records: ${logStringify({ repoProjectId: record.repo_project_id })}`,
        )
        await this.materialUsageService.batchCreate(
          materialUsageRecords.filter(Boolean),
        )
        await this.newCodeLinesService.create(newCodeLinesRecord)
        await this.codeLinesService.create(codeLinesRecord)
      }),
      4,
    )

    analysisErrors.forEach((error) => {
      this.logger.error(
        `Analysis profession raw data error: ${logStringify({
          scanId,
          businesses,
          error: {
            message: error.message,
            stack: error.stack,
          },
        })}`,
      )
    })
  }

  async analysisNewMaterialUsage(
    scanId: number,
    options?: {
      businesses?: SOURCE_CODE_BUSINESS[]
    },
  ): Promise<void> {
    const { businesses = ALL_SOURCE_CODE_BUSINESS } = options || {}
    const lastScanInfo = await this.scanInfoService.findLastOne(scanId)
    if (!lastScanInfo) return

    const lastScanId = lastScanInfo.id
    const currentScanMaterialUsageRecords
      = await this.materialUsageService.findListByScanIdAndBusinessUniqueByRepoAndFilePath(
        scanId,
        businesses,
      )

    const lastScanMaterialUsageRecords
      = await this.materialUsageService.findListByScanIdAndBusinessUniqueByRepoAndFilePath(
        lastScanId,
        businesses,
      )

    this.logger.info(
      `Start to clear history 'new material usage' records: ${logStringify({
        scanId,
        businesses,
      })}`,
    )
    await this.newMaterialUsageService.batchDelete({
      scanId,
      businesses,
    })

    const { errors } = await limitInvokePromiseAllFulfilled(
      currentScanMaterialUsageRecords.map(
        currentMaterialUsageRecord => async (): Promise<void> => {
          const lastMaterialUsageRecord = lastScanMaterialUsageRecords.find(
            lastRecord =>
              lastRecord.repo_project_id
              === currentMaterialUsageRecord.repo_project_id
              && lastRecord.file_path === currentMaterialUsageRecord.file_path,
          )
          let lastMaterialUsage: LibraryMaterialUsage = {}
          try {
            lastMaterialUsage
              = lastMaterialUsageRecord.filter_material_info ?? {}
          }
          catch (error) {
            this.logger.warn(
              `Failed to get last material usage: ${logStringify({
                scanId,
                businesses,
                repoProjectId: currentMaterialUsageRecord.repo_project_id,
                filePath: currentMaterialUsageRecord.file_path,
                error: error.toString(),
              })}`,
            )
          }
          let currentMaterialUsage: LibraryMaterialUsage = {}
          try {
            currentMaterialUsage
              = currentMaterialUsageRecord.filter_material_info ?? {}
          }
          catch (error) {
            this.logger.error(
              `Failed to get current material usage: ${logStringify({
                scanId,
                businesses,
                repoProjectId: currentMaterialUsageRecord.repo_project_id,
                filePath: currentMaterialUsageRecord.file_path,
                error: error.toString(),
              })}`,
            )
          }
          const newMaterialUsage
            = NewMaterialUsageService.clearUselessMaterialInfoWithoutClone(
              eraseLibraryMaterialUsageWithoutClone(
                currentMaterialUsage,
                lastMaterialUsage,
              ),
            )

          if (
            isObject(newMaterialUsage)
            && Object.keys(newMaterialUsage).length
          ) {
            const result: Service.Analysis.NewMaterialUsage.CreateParams = {
              start_scan_id: lastScanId,
              end_scan_id: scanId,
              repo_project_id: currentMaterialUsageRecord.repo_project_id,
              package: currentMaterialUsageRecord.package,
              route_path: currentMaterialUsageRecord.route_path,
              business: currentMaterialUsageRecord.business,
              file_path: currentMaterialUsageRecord.file_path,
              material_info: JSON.stringify(newMaterialUsage),
            }
            this.logger.info(
              `Start to create 'new material usage' records: ${logStringify({
                scanId,
                businesses,
                ...omit(result, ['material_info']),
              })}`,
            )
            await this.newMaterialUsageService.create(result)
          }
        },
      ),
      4,
    )

    errors.forEach((error) => {
      this.logger.error(
        `Analysis new material usage error: ${logStringify({
          scanId,
          businesses,
          error: {
            message: error.message,
            stack: error.stack,
          },
        })}`,
      )
    })
  }

  async filterUsefulMaterialUsage(
    currentMaterialUsage: LibraryMaterialUsage,
    allMaterial?: Service.Material.MaterialInfoForAnalysis[],
  ): Promise<LibraryMaterialUsage> {
    allMaterial
      = allMaterial ?? (await this.materialService.findAllBusinessComponent())
    const libraryNames = Object.keys(currentMaterialUsage)

    // 根据查出来的物料中台的数据过滤非物料中台的组件
    for (const libraryName of libraryNames) {
      // 使用的包在物料中台中
      const materialInPlatform = allMaterial.find(
        item => item.packageName === libraryName,
      )
      if (materialInPlatform) {
        const targetMaterialUsage = currentMaterialUsage[libraryName]
        // 接下来判断组件库类型然后计算里面的子组件是否在物料中台上
        if (materialInPlatform.componentBundleType === 'SLSC') {
          // 里面数据结构一定是{default}
          // FIXME: 有不是 default 的情况，需要排除
          for (const componentName in targetMaterialUsage) {
            if (!materialInPlatform.components.includes(componentName)) {
              delete currentMaterialUsage[libraryName][componentName]
            }
          }
        }
        else if (materialInPlatform.componentBundleType === 'SLMC') {
          // 如果子组件没有录入到物料中台
          for (const componentName in targetMaterialUsage) {
            if (!materialInPlatform.components.includes(componentName)) {
              delete currentMaterialUsage[libraryName][componentName]
            }
          }
        }
      }
      else {
        // 当前页面使用的组件不在物料中台中
        delete currentMaterialUsage[libraryName]
      }
    }
    // { "@na":{}, "pk2": {} } => {}
    for (const key in currentMaterialUsage) {
      const item = currentMaterialUsage[key]
      if (Object.keys(item)?.length === 0) {
        delete currentMaterialUsage[key]
      }
    }
    return currentMaterialUsage ?? {}
  }
}
