declare namespace Service.Config {
  export type ConfigRecord = import('@prisma/client').business_config

  export type SerializeConfig = import('type-fest').OverrideProperties<ConfigRecord, {
    meta: ConfigDetail
  }>

  export interface ConfigDetail {
    subscription?: {
      path: string
    }
    report?: {
      projects: {
        title: string
        namespace: string
        git_url: string
        branch: string
      }[]
    }
    material?: {
      projects: {
        title: string
        branch: string
        namespace: string
        git_url: string
        router_entry: string[]
        component_folder_map: Record<string, string>
      }[]
    }
  }
}
