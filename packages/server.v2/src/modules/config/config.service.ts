import { RECORD_STATUS } from '@/constants/status'
import { BUSINESS } from '@/shared'
import { PrismaClientInstance } from '@/tools/prisma'
import { Injectable } from '@nestjs/common'

@Injectable()
export class ConfigService {
  private readonly prisma = PrismaClientInstance

  async serialize(
    record: Service.Config.ConfigRecord,
  ): Promise<Service.Config.SerializeConfig> {
    try {
      return Object.freeze({
        ...record,
        meta: record.meta
          ? (JSON.parse(
            record.meta,
          ) as Service.Config.ConfigDetail)
          : null,
      })
    }
    catch (error) {
      if (error instanceof Error) {
        console.error(
          `Failed to serialize config: ${JSON.stringify({
            id: record.id,
            error: error.toString(),
          })}`,
        )
      }
    }
  }

  async findConfigByBusiness(business: BUSINESS): Promise<Service.Config.SerializeConfig> {
    const config = await this.prisma.business_config.findFirstOrThrow({
      where: {
        business,
        status: RECORD_STATUS.EFFECT,
      },
    })
    return this.serialize(config)
  }
}
