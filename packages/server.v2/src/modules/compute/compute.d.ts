declare namespace Service.Compute {
  export interface MaterialUsageReport {
    id: number
    start_time: number
    end_time: number
    display_txt: string
    usage: LibraryUsage[]
  }

  export interface LibraryUsage {
    library: string
    business: string
    materials: MaterialUsage[]
  }

  export interface MaterialUsage {
    materialId: number
    name: string
    codeLines: number
    count: number
    directUseCount: number
    indirectUseCount: number
    massScore: number
    thirdPartyUsage: import('@ks-material-middleoffice/measure-sdk').LibraryMaterialUsage
    department: string
  }
}
