import NP from 'number-precision'
import { JsonObject } from 'type-fest'
import cloneDeep from 'lodash/cloneDeep'
import { Injectable } from '@nestjs/common'

import { isNumber } from '@/tools/type'
import { uploadToCDN } from '@/tools/cdn'
import { logStringify } from '@/tools/json'
import { ALL_SOURCE_CODE_BUSINESS, SOURCE_CODE_BUSINESS } from '@/shared'
import { eraseLibraryMaterialUsageWithoutClone } from '@/tools/material-usage'

import { ResultService } from '../analysis/result/result.service'
import { NewCodeLinesService } from '../analysis/new-code-lines/new-code-lines.service'
import { MaterialMetaService } from '../analysis/material-meta/material-meta.service'
import { MaterialUsageService } from '../analysis/material-usage/material-usage.service'
import { NewMaterialUsageService } from '../analysis/new-material-usage/new-material-usage.service'
import { ScanInfoService } from '../analysis/scan-info/scan-info.service'
import { MaterialService } from '../material/material.service'
import { MaterialPointsStatisticsCacheService } from '../shared/material-points-statistics-cache.service'
import { DEFAULT_MATERIAL_CODE_LINES_LIMIT } from '../analysis/project-collection/collection-compute.service'

@Injectable()
export class ComputeService {
  constructor(
    private readonly resultService: ResultService,
    private readonly newCodeLinesService: NewCodeLinesService,
    private readonly materialMetaService: MaterialMetaService,
    private readonly materialUsageService: MaterialUsageService,
    private readonly newMaterialUsageService: NewMaterialUsageService,
    private readonly scanInfoService: ScanInfoService,
    private readonly materialService: MaterialService,
    private readonly materialUsageCache: MaterialPointsStatisticsCacheService,
  ) {}

  static readonly MATERIAL_POINTS = [
    { NC: { min: 0, max: 2 }, points: 0 },
    { NC: { min: 2, max: 2500 }, points: 1 },
    { NC: { min: 2500, max: 5000 }, points: 2 },
    { NC: { min: 5000, max: 10000 }, points: 3 },
    { NC: { min: 10000, max: 20000 }, points: 4 },
    { NC: { min: 20000, max: 50000 }, points: 5 },
    { NC: { min: 50000, max: 100000 }, points: 6 },
    { NC: { min: 100000, max: 200000 }, points: 7 },
    { NC: { min: 200000, max: 300000 }, points: 8 },
    { NC: { min: 300000, max: 400000 }, points: 9 },
    { NC: { min: 400000, max: 500000 }, points: 10 },
    { NC: { min: 500000 }, points: 11 },
  ]

  async computeNewCodeCoverage(
    scanId: number,
    options?: {
      businesses?: SOURCE_CODE_BUSINESS[]
      materialCodeLinesLimit?: number
      multipleOfIndirectReference?: number
    },
  ): Promise<{ business: SOURCE_CODE_BUSINESS, newCodeCoverage: number }[]> {
    const {
      businesses = ALL_SOURCE_CODE_BUSINESS,
      materialCodeLinesLimit = DEFAULT_MATERIAL_CODE_LINES_LIMIT,
      multipleOfIndirectReference,
    } = options || {}
    // ======================== 预处理 ========================
    this.materialMetaService.manuallyCalibrateSomeData()

    const allBusinessNewCodeLines
      = await this.newCodeLinesService.findAllBusiness(scanId)
    const allMaterialUsage
      = await this.materialUsageService.findAllMaterialUsageByScanGroupByBusiness(
        scanId,
      )
    const allNewMaterialUsage
      = await this.newMaterialUsageService.findAllNewMaterialUsageByScanGroupByBusiness(
        scanId,
      )
    const allMaterialMeta
      = await this.materialMetaService.findAllMaterialInfoByScanId(scanId)

    const result = businesses.map(business => ({
      business,
      newCodeCoverage: 0,
    }))

    for (let i = 0; i < businesses.length; i++) {
      const business = businesses[i]
      const currentBusinessNewCodeLines = allBusinessNewCodeLines.find(
        item => item.business === business,
      ).newCodeLines
      const currentMaterialUsage = allMaterialUsage.find(
        item => item.business === business,
      ).usage
      const newMaterialUsage = allNewMaterialUsage.find(
        item => item.business === business,
      ).usage
      const lastScanMaterialUsage = eraseLibraryMaterialUsageWithoutClone(
        cloneDeep(currentMaterialUsage),
        newMaterialUsage,
      )

      const currentMaterialUsageCodeLines
        = await this.materialMetaService.computeLibraryMaterialCodeLines(
          await this.materialMetaService.mergeThirdUsage(
            currentMaterialUsage,
            allMaterialMeta,
            multipleOfIndirectReference,
          ),
          allMaterialMeta,
        )
      const lastMaterialUsageCodeLines
        = await this.materialMetaService.computeLibraryMaterialCodeLines(
          await this.materialMetaService.mergeThirdUsage(
            lastScanMaterialUsage,
            allMaterialMeta,
            multipleOfIndirectReference,
          ),
          allMaterialMeta,
        )

      let currentBusinessNewMaterialCodeLines = 0
      const walkedMaterial: string[] = []
      const currentMaterialUsageNewCodeLines = cloneDeep(
        currentMaterialUsageCodeLines,
      )
      for (const [library, materialCodeLines] of Object.entries(
        currentMaterialUsageNewCodeLines,
      )) {
        for (const [material, codeLines] of Object.entries(materialCodeLines)) {
          let limitedCodeLines
            = codeLines > materialCodeLinesLimit
              ? materialCodeLinesLimit
              : codeLines
          if (walkedMaterial.includes(`${library}/${material}`)) continue
          walkedMaterial.push(`${library}/${material}`)
          if (library in lastMaterialUsageCodeLines) {
            if (material in lastMaterialUsageCodeLines[library]) {
              const codeLines = lastMaterialUsageCodeLines[library][material]
              const lastLimitedCodeLines
                = codeLines > materialCodeLinesLimit
                  ? materialCodeLinesLimit
                  : codeLines
              limitedCodeLines -= lastLimitedCodeLines
            }
          }
          if (limitedCodeLines < 0) {
            limitedCodeLines = 0
          }
          currentBusinessNewMaterialCodeLines += limitedCodeLines
          materialCodeLines[material] = limitedCodeLines
        }
      }
      const newCodeCoverage = NP.divide(
        currentBusinessNewMaterialCodeLines,
        currentBusinessNewMaterialCodeLines + currentBusinessNewCodeLines,
      )
      result[i].newCodeCoverage = newCodeCoverage
      await this.resultService.createResult({
        scanId,
        business,
        type: 'NEW_CODE_COVERAGE',
        value: newCodeCoverage || 0,
        content: {
          currentBusinessNewMaterialCodeLines,
          currentBusinessNewCodeLines,
          newMaterialUsage,
          currentMaterialUsageNewCodeLines,
        },
      })
    }

    return result
  }

  async computeMaterialPointsBaseInfo(scanId: number): Promise<string> {
    // 0. 拿到本次扫描的基础信息
    const { start_time, end_time, display_txt }
      = await this.scanInfoService.findOneById(scanId)
    // 1. 找到所有在物料中台的物料
    const allMaterials = await this.materialService.findAllBusinessComponent()

    // 2. 找到本次 scan 算出来的物料行数和物料引用情况
    const currentLibraryMaterialMeta
      = await this.materialMetaService.findAllMaterialInfoByScanId(scanId)

    // 3. 通过 1 的数据过滤 2 的结果，并计算出物料的行数，生成一个空的物料使用情况
    const resultMaterialUsage: Service.Compute.MaterialUsageReport = {
      id: scanId,
      start_time,
      end_time,
      display_txt,
      usage: [],
    }

    for (const componentInfo of allMaterials) {
      const { materialId, packageName, componentsWithId, componentBundleType }
        = componentInfo
      const initMaterials: Service.Compute.MaterialUsage[] = []
      const user
        = await this.materialService.queryUserInfoByMaterialId(materialId)
      if (componentBundleType === 'SLMC') {
        componentsWithId.forEach((component) => {
          initMaterials.push({
            materialId: component.id,
            name: component.name,
            codeLines: 0,
            count: 0,
            directUseCount: 0,
            indirectUseCount: 0,
            thirdPartyUsage: {},
            department: user?.department,
            massScore: 0,
          })
        })
      }
      else if (componentBundleType === 'SLSC') {
        initMaterials.push({
          materialId,
          name: 'default',
          codeLines: 0,
          count: 0,
          directUseCount: 0,
          indirectUseCount: 0,
          thirdPartyUsage: null,
          department: user?.department,
          massScore: 0,
        })
      }
      resultMaterialUsage.usage.push({
        library: packageName,
        business: componentInfo.business as SOURCE_CODE_BUSINESS,
        materials: initMaterials,
      })
    }

    // 初始化行数及第三方使用情况
    for (const library of currentLibraryMaterialMeta) {
      for (const material of library.materials) {
        const { codeLines, third_usage } = material
        const libUsage
          = resultMaterialUsage.usage.find(
            lib =>
              lib.library === library.packageName
              && lib.business === library.business,
          )
          || resultMaterialUsage.usage.find(
            lib => lib.library === library.packageName,
          )
        if (!libUsage) continue
        const materialUsage = libUsage.materials.find(
          mat => mat.name === material.materialName,
        )
        if (materialUsage) {
          materialUsage.codeLines = codeLines
          materialUsage.thirdPartyUsage = third_usage
        }
      }
    }

    // 4. 找到本次 scan 所有页面的物料使用情况
    const allBusinessLibraryMaterialUsage
      = await this.materialUsageService.findAllMaterialUsageByScanGroupByBusiness(
        scanId,
      )

    for (const businessLibraryMaterialUsage of allBusinessLibraryMaterialUsage) {
      const { usage } = businessLibraryMaterialUsage
      for (const [libraryName, materialUsage] of Object.entries(usage)) {
        const libUsage
          = resultMaterialUsage.usage.find(
            lib =>
              lib.library === libraryName
              && lib.business === businessLibraryMaterialUsage.business,
          )
          || resultMaterialUsage.usage.find(lib => lib.library === libraryName)
        if (!libUsage) continue
        for (const [materialName, count] of Object.entries(materialUsage)) {
          const material = libUsage.materials.find(
            mat => mat.name === materialName,
          )
          if (material && isNumber(+count) && isNaN(+count) === false) {
            material.count += +count
            material.directUseCount += +count
          }
        }
      }
    }

    // 4.3 计算间接使用物料的情况
    for (let j = 0; j < resultMaterialUsage.usage.length; j++) {
      const currentWalkLibUsage = resultMaterialUsage.usage[j]
      for (let k = 0; k < currentWalkLibUsage.materials.length; k++) {
        const currentWalkMaterial = currentWalkLibUsage.materials[k]
        const currentWalkMaterialThirdUsage
          = currentWalkMaterial.thirdPartyUsage
        if (!currentWalkMaterialThirdUsage) continue
        for (const [thirdLibraryName, thirdMaterials] of Object.entries(
          currentWalkMaterialThirdUsage,
        )) {
          // 优先同部门的物料
          const usedThirdLib
            = resultMaterialUsage.usage.find(
              lib =>
                lib.library === thirdLibraryName
                && lib.business === currentWalkLibUsage.business,
            )
            || resultMaterialUsage.usage.find(
              lib => lib.library === thirdLibraryName,
            )
          if (!usedThirdLib) continue
          for (const [indirectUsedMaterialName, count] of Object.entries(
            thirdMaterials,
          )) {
            const indirectUsedMaterial = usedThirdLib.materials.find(
              mat => mat.name === indirectUsedMaterialName,
            )
            if (
              indirectUsedMaterial
              && isNumber(+count)
              && isNaN(+count) === false
            ) {
              indirectUsedMaterial.count += +count * currentWalkMaterial.count
              indirectUsedMaterial.indirectUseCount
                += +count * currentWalkMaterial.count
            }
          }
        }
      }
    }

    // ======================== 计算物料成熟度 ========================
    for (const library of resultMaterialUsage.usage) {
      for (const material of library.materials) {
        const { codeLines, count } = material
        const sumCodeLines = codeLines * count
        const materialPoints = ComputeService.MATERIAL_POINTS.find(
          item =>
            sumCodeLines >= item.NC.min
            && (sumCodeLines < item.NC.max || !item.NC.max),
        )
        material.massScore = materialPoints?.points ?? 0
      }
    }

    const CDNURL = await uploadToCDN({
      dir: '/material-count-and-codeLines',
      filename: `test-material-usage-${resultMaterialUsage.id}-${resultMaterialUsage.display_txt.split(' ').join('-')}.json`,
      content: logStringify(resultMaterialUsage as unknown as JsonObject),
    })
    await this.materialUsageCache.setCache(scanId, CDNURL)

    return CDNURL
  }
}
