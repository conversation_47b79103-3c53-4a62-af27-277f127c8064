import { Module } from '@nestjs/common'
import { ComputeController } from './compute.controller'
import { ComputeService } from './compute.service'
import { LowCodeComputeService } from './low-code.compute.service'
import { ResultModule } from '../analysis/result/result.module'
import { NewCodeLinesModule } from '../analysis/new-code-lines/new-code-lines.module'
import { MaterialMetaModule } from '../analysis/material-meta/material-meta.module'
import { MaterialUsageModule } from '../analysis/material-usage/material-usage.module'
import { NewMaterialUsageModule } from '../analysis/new-material-usage/new-material-usage.module'
import { ScanInfoModule } from '../analysis/scan-info/scan-info.module'
import { SharedModule } from '../shared/shared.module'
import { KconfModule } from '../kconf/kconf.module'
import { MaterialReferModule } from '../material/material-refer/material-refer.module'
import { MaterialModule } from '../material/material.module'

@Module({
  imports: [
    ResultModule,
    NewCodeLinesModule,
    MaterialMetaModule,
    MaterialUsageModule,
    NewMaterialUsageModule,
    ScanInfoModule,
    KconfModule,
    SharedModule,
    MaterialReferModule,
    MaterialModule,
  ],
  controllers: [ComputeController],
  providers: [ComputeService, LowCodeComputeService],
  exports: [ComputeService, LowCodeComputeService],
})
export class ComputeModule {}
