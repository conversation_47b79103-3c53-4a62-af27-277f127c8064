import NP from 'number-precision'
import { JsonValue } from 'type-fest'
import { Injectable } from '@nestjs/common'

import { KconfService } from '../kconf/kconf.service'
import { SharedService } from '../shared/shared.service'
import { MaterialReferService } from '../material/material-refer/material-refer.service'
import { FangZhouMaterialReferService } from '../material/material-refer/fangzhou.material-refer.service'

@Injectable()
export class LowCodeComputeService {
  constructor(
    private readonly kconfService: KconfService,
    private readonly sharedService: SharedService,
    private readonly materialReferService: MaterialReferService,
  ) {}

  static readonly TOTAL_MATERIAL_REUSE_RATE = 'TOTAL_MATERIAL_REUSE_RATE'
  async createTotalMaterialReuseRate(value: number, payload: JsonValue): Promise<Service.Shared.SharedRecord> {
    await this.sharedService.deleteSameDayRecords(
      LowCodeComputeService.TOTAL_MATERIAL_REUSE_RATE,
    )
    return this.sharedService.create({
      key: LowCodeComputeService.TOTAL_MATERIAL_REUSE_RATE,
      value,
      payload,
    })
  }

  async computeTotalMaterialReuseRate(): Promise<Service.Shared.SharedRecord> {
    const fangzhouMaterialUsage = await this.collectFangzhouMaterialUsage()
    const kaelMaterialUsage = await this.collectKaelMaterialUsage()
    const totalMaterialReuseRate = NP.divide(
      NP.plus(fangzhouMaterialUsage.numerator + kaelMaterialUsage.numerator),
      NP.plus(
        fangzhouMaterialUsage.denominator + kaelMaterialUsage.denominator,
      ),
    )
    return this.createTotalMaterialReuseRate(totalMaterialReuseRate, {
      fangzhou: fangzhouMaterialUsage,
      kael: kaelMaterialUsage,
    })
  }

  async collectFangzhouMaterialUsage(): Promise<{
    numerator: number
    denominator: number
    payload: JsonValue
  }> {
    const allFangzhouOfficialMaterialNamespace
      = await this.kconfService.getFangZhouAllowNamespaces()
    const fangzhouMaterialReferCount
      = await this.materialReferService.findMaterialReferCountGroupByRefBusiness(
        'fangzhou',
      )
    const ignoreInternalMaterialReferCount = fangzhouMaterialReferCount.filter(
      (item) => {
        return !FangZhouMaterialReferService.TRANSFORMED_FANGZHOU_INTERNAL_MATERIAL_NAMESPACE.includes(
          item.namespace,
        )
      },
    )
    const numerator = ignoreInternalMaterialReferCount
      .filter(item =>
        allFangzhouOfficialMaterialNamespace.includes(item.namespace),
      )
      .reduce((acc, cur) => acc + cur._sum.refer_count, 0)
    const denominator = ignoreInternalMaterialReferCount.reduce(
      (acc, cur) => acc + cur._sum.refer_count,
      0,
    )
    return {
      numerator,
      denominator,
      payload: ignoreInternalMaterialReferCount,
    }
  }

  async collectKaelMaterialUsage(): Promise<{
    numerator: number
    denominator: number
    payload: JsonValue
  }> {
    const kaelMaterialReferCount
      = await this.materialReferService.findMaterialReferCountGroupByRefBusiness(
        'kael',
      )
    const numerator = kaelMaterialReferCount
      .filter(
        item =>
          item.namespace.startsWith('@es/tianhe-pro-materials/')
          || item.namespace.startsWith('@es/tianhe-basic-materials/'),
      )
      .reduce((acc, cur) => acc + cur._sum.refer_count, 0)
    const denominator = kaelMaterialReferCount.reduce(
      (acc, cur) => acc + cur._sum.refer_count,
      0,
    )
    return { numerator, denominator, payload: kaelMaterialReferCount }
  }
}
