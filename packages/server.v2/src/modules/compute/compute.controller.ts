import { isNumber } from 'lodash'
import { ApiTags } from '@nestjs/swagger'
import { Body, Controller, Post } from '@nestjs/common'

import { SOURCE_CODE_BUSINESS } from '@/shared'
import { limitInvokePromiseAllFulfilled, PromiseAllFulfilledResult } from '@/tools/Promise'

import { ComputeService } from './compute.service'
import { ScanInfoService } from '../analysis/scan-info/scan-info.service'
import { LowCodeComputeService } from './low-code.compute.service'

@ApiTags('compute')
@Controller('compute')
export class ComputeController {
  constructor(
    private readonly computeService: ComputeService,
    private readonly lowCodeComputeService: LowCodeComputeService,
    private readonly scanInfoService: ScanInfoService,
  ) {}

  @Post('new-code-coverage')
  async computeNewCodeCoverage(
    @Body('scanIds') scanIds?: number | number[],
    @Body('options')
    options?: {
      materialCodeLinesLimit?: number
      multipleOfIndirectReference?: number
    },
  ): Promise<Array<{
      scanId: number
      data: {
        business: SOURCE_CODE_BUSINESS
        newCodeCoverage: number
      }[]
    }>> {
    if (!scanIds) {
      scanIds = (
        await this.scanInfoService.findMaterialPointsBaseScanList()
      ).list.map(item => item.id)
    }
    const finalScanIds = Array.isArray(scanIds) ? scanIds : [scanIds]
    const result = []
    for (let i = 0; i < finalScanIds.length; i++) {
      const scanId = finalScanIds[i]
      const currentScanNewCodeCoverage
        = await this.computeService.computeNewCodeCoverage(+scanId, options)
      result.push({
        scanId: +scanId,
        data: currentScanNewCodeCoverage,
      })
    }
    return result
  }

  @Post('material-points-base-info')
  async computeMaterialPointsBaseInfo(
    @Body('scanIds') scanIds: number | number[],
  ): Promise<PromiseAllFulfilledResult<string>> {
    if (isNumber(scanIds)) {
      scanIds = [scanIds]
    }
    return limitInvokePromiseAllFulfilled(
      scanIds.map(scanId => async () => {
        return this.computeService.computeMaterialPointsBaseInfo(scanId)
      }),
      4,
    )
  }

  @Post('low-code-total-material-reuse-rate')
  async computeTotalMaterialReuseRate(): Promise<Service.Shared.SharedRecord> {
    return this.lowCodeComputeService.computeTotalMaterialReuseRate()
  }
}
