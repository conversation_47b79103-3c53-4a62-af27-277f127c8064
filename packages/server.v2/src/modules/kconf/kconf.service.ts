import { createKconf } from '@infra-node/kconf'
import { Injectable } from '@nestjs/common'

import { IS_CORP, IS_STAGING } from '@/constants/envs'
import { loggerInstance } from '@/tools/winston'
import { isArray, isObject, isStringArray } from '@/tools/type'
import bizExcludePackagesStagingJSON from './biz-exclude-packages.staging'

@Injectable()
export class KconfService {
  private readonly kconfInstance = createKconf({
    env: IS_CORP ? 'prod' : 'staging',
  })

  private readonly logger = loggerInstance

  async getFangZhouAllowNamespaces(): Promise<string[]> {
    const result: string[] = []
    try {
      const config = await this.kconfInstance.getJSONValue(
        'platecoDev.kwaishopPower.materialMiddleOfficeScanProjectConfig',
      )

      if (
        isObject(config)
        && 'FZAllowNamespace' in config
        && isStringArray(config.FZAllowNamespace)
      ) {
        result.push(...config.FZAllowNamespace)
      }
      else {
        throw Error('FZAllowNamespace is not a string array')
      }
    }
    catch (e) {
      if (e instanceof Error) {
        this.logger.error(
          'Fail to get fangzhou material namespace config, error is '
          + e.toString(),
        )
      }
    }

    return result
  }

  async getBizExcludePackages(): Promise<Service.Kconf.ExcludePackage[]> {
    const result: Service.Kconf.ExcludePackage[] = []
    try {
      const config = IS_STAGING
        ? bizExcludePackagesStagingJSON
        : await this.kconfInstance.getJSONValue(
          'ad.frontend.drow-material-measure-apps',
        )

      if (isObject(config) && 'excludePackages' in config) {
        const excludePackages = config.excludePackages
        if (isArray(excludePackages)) {
          excludePackages.forEach((item: unknown) => {
            if (isObject(item) && 'gitUrl' in item && 'pkgName' in item) {
              result.push(item as unknown as Service.Kconf.ExcludePackage)
            }
          })
        }
      }
      else {
        throw Error('excludePackages is not an array')
      }
    }
    catch (e) {
      if (e instanceof Error) {
        this.logger.error(
          'Fail to get biz biz exclude packages, error is ' + e.toString(),
        )
      }
    }

    return result
  }
}
