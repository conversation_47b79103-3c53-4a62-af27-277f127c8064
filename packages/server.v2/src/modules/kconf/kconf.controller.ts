import { Controller, Get } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'
import { KconfService } from './kconf.service'

@ApiTags('kconf')
@Controller('kconf')
export class KconfController {
  constructor(private readonly kconfService: KconfService) {}

  @Get('fangzhou/namespaces')
  async getFangZhouAllowNamespaces(): Promise<string[]> {
    return this.kconfService.getFangZhouAllowNamespaces()
  }

  @Get('biz/exclude-packages')
  async getBizIgnorePackages(): Promise<Service.Kconf.ExcludePackage[]> {
    return this.kconfService.getBizExcludePackages()
  }
}
