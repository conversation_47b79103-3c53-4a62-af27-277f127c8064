import dayjs from 'dayjs'
import { Injectable } from '@nestjs/common'

import { DateParam, now } from '@/shared'
import { RECORD_STATUS } from '@/constants/status'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { Prisma } from '@prisma/client'

@Injectable()
export class SharedService {
  private readonly prisma = PrismaClientInstance

  async query(key: string): Promise<Service.Shared.SharedRecord> {
    return this.prisma.shared.findFirst({
      where: {
        key,
        status: RECORD_STATUS.EFFECT,
      },
      orderBy: {
        create_time: 'desc',
      },
    })
  }

  async create(record: Service.Shared.SharedCreateParams): Promise<Service.Shared.SharedRecord> {
    return this.prisma.shared.create({
      data: {
        id: genPrimaryIndex(),
        key: record.key,
        value: record.value,
        payload: record.payload,
        create_time: now(),
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async deleteSameDayRecords(key: string, date?: DateParam): Promise<Prisma.BatchPayload> {
    const day = dayjs(date)
    return this.prisma.shared.updateMany({
      where: {
        key,
        create_time: {
          gte: day.startOf('day').valueOf(),
          lt: day.endOf('day').valueOf(),
        },
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async createAndDeleteSameKeyRecords(
    record: Service.Shared.SharedCreateParams,
  ): Promise<Service.Shared.SharedRecord> {
    await this.prisma.shared.updateMany({
      where: {
        key: record.key,
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
    return this.create(record)
  }
}
