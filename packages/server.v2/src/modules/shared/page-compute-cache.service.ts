import { JsonValue } from 'type-fest'
import { Prisma } from '@prisma/client'
import { Injectable } from '@nestjs/common'

import { now } from '@/shared'
import { uploadToCDN } from '@/tools/cdn'
import { logStringify } from '@/tools/json'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'

import { RECORD_STATUS } from '@/constants/status'

@Injectable()
export class PageComputeCacheService {
  private prisma = PrismaClientInstance

  private CACHE_RECORD_KEY = 'CACHE_RECORD'

  async getCache(cacheId: number, expirationTimestamp: number): Promise<Service.Shared.SharedRecord | null> {
    return this.prisma.shared.findFirst({
      where: {
        key: this.CACHE_RECORD_KEY,
        payload: {
          equals: {
            cacheId,
          },
        },
        status: RECORD_STATUS.EFFECT,
        create_time: {
          gte: expirationTimestamp,
        },
      },
      orderBy: {
        create_time: 'desc',
      },
    })
  }

  async setCache(cacheId: number, value: JsonValue): Promise<Service.Shared.SharedRecord> {
    await this.deleteCache(cacheId)
    const cacheValueCDN = await uploadToCDN({
      dir: '/material-platform-cache-store',
      filename: `cache-${cacheId}-${now()}.json`,
      content: logStringify(value),
    })
    return this.prisma.shared.create({
      data: {
        id: genPrimaryIndex(),
        key: this.CACHE_RECORD_KEY,
        value: cacheValueCDN,
        create_time: now(),
        status: RECORD_STATUS.EFFECT,
        payload: {
          cacheId,
        },
      },
    })
  }

  async deleteCache(cacheId: number): Promise<Prisma.BatchPayload> {
    return this.prisma.shared.updateMany({
      where: {
        key: this.CACHE_RECORD_KEY,
        payload: {
          equals: {
            cacheId,
          },
        },
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async clearAllCache(): Promise<Prisma.BatchPayload> {
    return this.prisma.shared.updateMany({
      where: {
        key: this.CACHE_RECORD_KEY,
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }
}
