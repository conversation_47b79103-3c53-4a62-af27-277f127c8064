import { Module } from '@nestjs/common'
import { SharedController } from './shared.controller'
import { SharedService } from './shared.service'
import { PageComputeCacheService } from './page-compute-cache.service'
import { MaterialPointsStatisticsCacheService } from './material-points-statistics-cache.service'
import { ScanInfoModule } from '../analysis/scan-info/scan-info.module'

@Module({
  imports: [ScanInfoModule],
  controllers: [SharedController],
  providers: [
    SharedService,
    PageComputeCacheService,
    MaterialPointsStatisticsCacheService,
  ],
  exports: [
    SharedService,
    PageComputeCacheService,
    MaterialPointsStatisticsCacheService,
  ],
})
export class SharedModule {}
