import { JsonValue } from 'type-fest'
import { Injectable } from '@nestjs/common'

import { isObject, isString, isStringOrNumber, now } from '@/shared'
import { RECORD_STATUS } from '@/constants/status'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { fetchFromCDN } from '@/tools/cdn'
import { PromiseAllFulfilled } from '@/tools/Promise'
import { ScanInfoService } from '../analysis/scan-info/scan-info.service'
import { Prisma } from '@prisma/client'

@Injectable()
export class MaterialPointsStatisticsCacheService {
  private prisma = PrismaClientInstance

  static readonly SCANNING_CACHE_KEY = 'SCANNING_CACHE_KEY'

  constructor(private readonly scanInfoService: ScanInfoService) {}

  async getCaches(): Promise<
    {
      scan: Service.Analysis.ScanInfo.SerializeAnalysisScanInfo
      content: string
    }[]
  > {
    const records = await this.prisma.shared.findMany({
      where: {
        key: MaterialPointsStatisticsCacheService.SCANNING_CACHE_KEY,
        status: RECORD_STATUS.EFFECT,
      },
      orderBy: {
        create_time: 'desc',
      },
    })
    const { result } = await PromiseAllFulfilled(
      records.map(async (record) => {
        if (
          isString(record.value)
          && record.value.startsWith('http')
          && isObject(record.payload)
          && 'scanId' in record.payload
          && isStringOrNumber(record.payload.scanId)
        ) {
          const content = record.value
          const scan = await this.scanInfoService.findOneById(
            +record.payload.scanId,
          )

          if (content && scan) {
            return {
              scan,
              content,
            }
          }
        }

        return null
      }),
    )

    return result
      .filter(item => item !== null)
      .sort((a, b) => {
        return b.scan.start_time - a.scan.start_time
      })
  }

  async getCache(
    scanId: number,
  ): Promise<Service.Compute.MaterialUsageReport | null> {
    const record = await this.prisma.shared.findFirst({
      where: {
        key: MaterialPointsStatisticsCacheService.SCANNING_CACHE_KEY,
        payload: {
          equals: {
            scanId,
          },
        },
        status: RECORD_STATUS.EFFECT,
      },
      orderBy: {
        create_time: 'desc',
      },
    })
    if (
      !record?.value
      || !isString(record.value)
      || !record.value.startsWith('http')
    )
      return null
    try {
      const report = await fetchFromCDN<Service.Compute.MaterialUsageReport>(
        record.value,
      )
      return report
    }
    catch (_) {
      return null
    }
  }

  async setCache(scanId: number, value: JsonValue): Promise<Service.Shared.SharedRecord> {
    await this.deleteCache(scanId)
    return this.prisma.shared.create({
      data: {
        id: genPrimaryIndex(),
        key: MaterialPointsStatisticsCacheService.SCANNING_CACHE_KEY,
        value: value,
        create_time: now(),
        status: RECORD_STATUS.EFFECT,
        payload: {
          scanId,
        },
      },
    })
  }

  async deleteCache(scanId: number): Promise<Prisma.BatchPayload> {
    return this.prisma.shared.updateMany({
      where: {
        key: MaterialPointsStatisticsCacheService.SCANNING_CACHE_KEY,
        payload: {
          equals: {
            scanId,
          },
        },
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async clearAllCache(): Promise<Prisma.BatchPayload> {
    return this.prisma.shared.updateMany({
      where: {
        key: MaterialPointsStatisticsCacheService.SCANNING_CACHE_KEY,
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }
}
