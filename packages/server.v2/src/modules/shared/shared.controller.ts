import { ApiTags } from '@nestjs/swagger'
import { Body, Controller, Get, Post } from '@nestjs/common'

import { SharedService } from './shared.service'
import { PageComputeCacheService } from './page-compute-cache.service'
import { MaterialPointsStatisticsCacheService } from './material-points-statistics-cache.service'
import { Prisma } from '@prisma/client'

@ApiTags('shared')
@Controller('shared')
export class SharedController {
  constructor(
    private readonly sharedService: SharedService,
    private readonly pageComputeCacheService: PageComputeCacheService,
    private readonly materialPointsStatisticsCacheService: MaterialPointsStatisticsCacheService,
  ) {}

  @Post('create')
  async create(@Body() record: Service.Shared.SharedCreateParams): Promise<Service.Shared.SharedRecord> {
    return this.sharedService.create(record)
  }

  @Post('clear-all-cache')
  async clearCache(): Promise<Prisma.BatchPayload> {
    return this.pageComputeCacheService.clearAllCache()
  }

  @Get('material-points-statistics/all')
  async materialPointsStatistics(): Promise<
    {
      scan: Service.Analysis.ScanInfo.SerializeAnalysisScanInfo
      content: string
    }[]
  > {
    return this.materialPointsStatisticsCacheService.getCaches()
  }
}
