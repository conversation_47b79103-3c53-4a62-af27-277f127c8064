import { Controller, Get, Param } from '@nestjs/common'

import { PromiseAllFulfilledResult } from '@/tools/Promise'
import { UserService } from './user.service'

@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('/username')
  async findUserByUsername(
    @Param('username') username: string,
  ): Promise<Service.User.UserRecord> {
    return this.userService.findOrRegisterUserByUsername(username)
  }

  @Get('/refresh-warn-users')
  async refreshWarnUsers(): Promise<PromiseAllFulfilledResult<Service.User.UserRecord>> {
    return this.userService.refreshWarnUsers()
  }
}
