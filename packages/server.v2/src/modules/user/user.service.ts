import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { Injectable } from '@nestjs/common'
import { ForwardOpenApiService } from '../forward/open-api.service'
import { now } from '@/shared'
import { RECORD_STATUS } from '@/constants/status'
import {
  limitInvokePromiseAllFulfilled,
  PromiseAllFulfilledResult,
} from '@/tools/Promise'

@Injectable()
export class UserService {
  private readonly prisma = PrismaClientInstance
  constructor(private readonly forwardOpenAPIService: ForwardOpenApiService) {}

  async findOrRegisterUserByUsername(
    username: string,
  ): Promise<Service.User.UserRecord> {
    try {
      return await this.findOneByUsername(username)
    }
    catch (_) {
      return await this.registerUser(username)
    }
  }

  async findById(id: number): Promise<Service.User.UserRecord | null> {
    if (!id) return null
    return await this.prisma.user.findUnique({
      where: {
        id,
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async registerUser(username: string): Promise<Service.User.UserRecord> {
    try {
      const oldUserRecord = await this.findOneByUsername(username)
      if (oldUserRecord) {
        return oldUserRecord
      }
    }
    catch {
      const userInfo = await this.forwardOpenAPIService.fetchUserInfo(username)
      return await this.prisma.user.create({
        data: {
          id: genPrimaryIndex(),
          user_name: username,
          user_id: userInfo.id,
          email: userInfo.email,
          avatar: userInfo.avatarUrl,
          create_time: now(),
          department: userInfo.orgDisplayName,
          name: userInfo.name,
          status: RECORD_STATUS.EFFECT,
        },
      })
    }
  }

  async refreshUser(username: string): Promise<Service.User.UserRecord> {
    const old = await this.prisma.user.findFirst({
      where: {
        user_name: username,
      },
    })
    if (!old) {
      return this.registerUser(username)
    }
    else {
      const newInfo = await this.forwardOpenAPIService.fetchUserInfo(username)
      return await this.prisma.user.update({
        where: {
          id: old.id,
        },
        data: {
          user_id: newInfo.id,
          email: newInfo.email,
          avatar: newInfo.avatarUrl,
          department: newInfo.orgDisplayName,
          name: newInfo.name,
          status: RECORD_STATUS.EFFECT,
        },
      })
    }
  }

  async findOneByUsername(
    username: string,
  ): Promise<Service.User.UserRecord> | never {
    try {
      return await this.prisma.user.findFirstOrThrow({
        where: {
          user_name: username,
          status: RECORD_STATUS.EFFECT,
        },
      })
    }
    catch (_) {
      throw new Error(`User ${username} not found`)
    }
  }

  async refreshWarnUsers(): Promise<
    PromiseAllFulfilledResult<Service.User.UserRecord>
  > {
    const warnUsers = await this.prisma.user.findMany({
      where: {
        name: 'undefined',
      },
    })

    return await limitInvokePromiseAllFulfilled(
      warnUsers.map(user => () => this.refreshUser(user.user_name)),
    )
  }
}
