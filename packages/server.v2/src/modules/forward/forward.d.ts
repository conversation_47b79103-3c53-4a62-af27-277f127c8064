declare namespace Service.Forward {
  export namespace Tianhe {
    export interface AppInfo {
      id: number
      name: string
      appKey: string
      desc: string
      logo: string
      status: number
      creator: string
      createTime: number
      updateTime: number
      pageCount: number
      config: string
      businessDomainName: string
      // administrators: [
      //   {
      //     userName: 'zhaoguochen';
      //     cnName: '赵国臣';
      //     headUrl: 'https://static.yximgs.com/udata/pkg/KS-IS-AVATAR-PRODUCTION/44064/AEC7FA8FD5F3A641BC9C7B3DD0A3F4DC_compressed_100';
      //     managerName: 'wanglongsheng';
      //     managerNameCn: '王龙生';
      //     department: '商城经营平台技术组';
      //     email: '<EMAIL>';
      //   },
      // ];
      // members: [
      //   {
      //     userName: 'zhaoguochen';
      //     cnName: '赵国臣';
      //     headUrl: 'https://static.yximgs.com/udata/pkg/KS-IS-AVATAR-PRODUCTION/44064/AEC7FA8FD5F3A641BC9C7B3DD0A3F4DC_compressed_100';
      //     managerName: 'wanglongsheng';
      //     managerNameCn: '王龙生';
      //     department: '商城经营平台技术组';
      //     email: '<EMAIL>';
      //   },
      // ];
      // businessDomainId: 28;
      // source: 2;
      // domainLabelInfo: [];
      // isTest: false;
      // routeInfo: {
      //   'kwaishop-tianhe-kwaishop-era-page-render-service-pc': 'https://git.corp.kuaishou.com/plateco-dev-fe/kwaishop-tech/tianhe/kwaishop-tianhe-app-group/kwaishop-tianhe-kwaishop-era-page-render-service-pc.git';
      // };
    }

    export interface PageInfo {
      id: number
      name: string
      desc: string
      creator: string
      createTime: number
      updateTime: number
      status: number

      // ext: JSON;
      // supportDataSource: false;
      // locking: 2;
      // groupList: '';
      // type: '';
      // version: '1.0.2';
      // pageCode: 'home';
      // pageGitUrl: '';
      // protocol: JSON;
      // assets: JSON;
      // lockUser: '';
      // preUrl: '';
      // appKey: 'brand_pc';
    }
  }

  export namespace LingZhu {
    export type DomainDirectory = {
      label: string
      value: number
      children?: DomainDirectory[]
    }
  }

  export namespace OpenAPI {
    export interface User {
      /** 员工Id */
      kwaiUserId: string
      /** 英文名 */
      username: string
      id: string
      /** 中文名 */
      name: string
      /** 头像 */
      avatarUrl: string
      /** 头像缩略图 */
      thumbnailAvatarUrl: string
      /** 部门 */
      orgDisplayName: string
      /** 性别，M 男 F 女 */
      gender: string
      /** 邮箱 */
      email: string
      /** 状态 A 在职 T 离职 F 冻结 */
      statusCode: string
      /** 别名 */
      alias: string
    }
  }
}
