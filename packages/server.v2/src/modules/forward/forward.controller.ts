import { Body, Controller, Get, Param, Post } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'
import type { JsonValue } from 'type-fest'

import { SOURCE_CODE_BUSINESS } from '@/shared'

import { ForwardService } from './forward.service'
import { ForwardTianheService } from './tianhe.service'
import { ForwardLingZhuService } from './lingzhu.service'
import { ForwardOpenApiService } from './open-api.service'
import {
  PromiseAllFulfilled,
  PromiseAllFulfilledResult,
} from '@/tools/Promise'

@ApiTags('forward')
@Controller('forward')
export class ForwardController {
  constructor(
    private readonly forwardService: ForwardService,
    private readonly forwardTianheService: ForwardTianheService,
    private readonly forwardLingZhuService: ForwardLingZhuService,
    private readonly forwardOpenAPIService: ForwardOpenApiService,
  ) {}

  @Post('es/:path')
  async forwardEs(
    @Body() body: JsonValue,
    @Param('path') path: string,
  ): Promise<unknown> {
    return this.forwardService.forwardOtherService(
      SOURCE_CODE_BUSINESS.ES,
      path,
      body,
    )
  }

  @Post('biz/:path')
  async forwardBiz(
    @Body() body: JsonValue,
    @Param('path') path: string,
  ): Promise<unknown> {
    return this.forwardService.forwardOtherService(
      SOURCE_CODE_BUSINESS.BIZ,
      path,
      body,
    )
  }

  @Post('locallife/:path')
  async forwardLocalLife(
    @Body() body: JsonValue,
    @Param('path') path: string,
  ): Promise<unknown> {
    return this.forwardService.forwardOtherService(
      SOURCE_CODE_BUSINESS.LOCAL_LIFE,
      path,
      body,
    )
  }

  @Post('locallifeClient/:path')
  async forwardLocalLifeClient(
    @Body() body: JsonValue,
    @Param('path') path: string,
  ): Promise<unknown> {
    return this.forwardService.forwardOtherService(
      SOURCE_CODE_BUSINESS.LOCAL_LIFE_CLIENT,
      path,
      body,
    )
  }

  @Post('tianhe/app')
  async queryTianheAppInfo(
    @Body('appKey') appKey: string,
  ): Promise<Service.Forward.Tianhe.AppInfo> | never {
    return this.forwardTianheService.queryTianheAppInfo(appKey)
  }

  @Post('tianhe/apps')
  async queryTianheAppsInfo(
    @Body() body: string[],
  ): Promise<PromiseAllFulfilledResult<Service.Forward.Tianhe.AppInfo>> {
    return PromiseAllFulfilled(
      body.map(appKey => this.forwardTianheService.queryTianheAppInfo(appKey)),
    )
  }

  @Post('tianhe/page')
  async queryTianhePageInfo(
    @Body('appKey') appKey: string,
    @Body('pageCode') pageCode: string,
  ): Promise<Service.Forward.Tianhe.PageInfo> | never {
    return this.forwardTianheService.queryTianhePageInfo(appKey, pageCode)
  }

  @Post('tianhe/pages')
  async queryTianhePagesInfo(
    @Body()
    body: {
      appKey: string
      pageCode: string
    }[],
  ): Promise<PromiseAllFulfilledResult<Service.Forward.Tianhe.PageInfo>> {
    return PromiseAllFulfilled(
      body.map(({ appKey, pageCode }) =>
        this.forwardTianheService.queryTianhePageInfo(appKey, pageCode),
      ),
    )
  }

  @Get('lingzhu/domain-directory')
  async queryLingZhuDomainDirectory():
    | Promise<Service.Forward.LingZhu.DomainDirectory>
    | never {
    return this.forwardLingZhuService.queryLingZhuDomainDirectory()
  }

  @Post('open-api/user')
  async fetchUserInfo(
    @Body() params: { username: string },
  ): Promise<Service.Forward.OpenAPI.User> | never {
    return this.forwardOpenAPIService.fetchUserInfo(params.username)
  }
}
