import axios from 'axios'
import { isObject } from 'lodash'
import { Injectable } from '@nestjs/common'

import { IS_CORP } from '@/constants/envs'

import { SharedService } from '../shared/shared.service'
import { loggerInstance } from '@/tools/winston'

const LING_ZHU_HOST = IS_CORP
  ? 'https://merchant-lego.corp.kuaishou.com'
  : 'https://lego.staging.kuaishou.com'

@Injectable()
export class ForwardLingZhuService {
  private readonly logger = loggerInstance
  constructor(private readonly sharedService: SharedService) {}

  static readonly DOMAIN_DIRECTORY_CACHE_KEY = 'LING_ZHU_DOMAIN_DIRECTORY'
  async queryLingZhuDomainDirectory(): Promise<Service.Forward.LingZhu.DomainDirectory> | never {
    try {
      const result = await axios({
        method: 'POST',
        url: `${LING_ZHU_HOST}/gateway/lowcode/common/query`,
        data: { request: { queryType: 'QUERY_ARCH_DOMAIN' } },
        headers: {
          'Content-Type': 'application/json',
          'Cookie': 'userName=yingpengsha',
        },
      })

      if (
        isObject(result.data)
        && 'result' in result.data
        && result.data.result === 1
        && 'data' in result.data
        && 'label' in result.data.data
        && 'value' in result.data.data
      ) {
        this.sharedService.createAndDeleteSameKeyRecords({
          key: ForwardLingZhuService.DOMAIN_DIRECTORY_CACHE_KEY,
          value: result.data.data,
        })
        return result.data.data as Service.Forward.LingZhu.DomainDirectory
      }

      throw new Error('HTTP response is not expected')
    }
    catch (error) {
      this.logger.warn('Fail to query lingzhu domain directory', error)
      const cached = await this.sharedService.query(ForwardLingZhuService.DOMAIN_DIRECTORY_CACHE_KEY)
      if (cached) {
        this.logger.error('Fail to query lingzhu domain directory, use cache instead')
        return cached.value as Service.Forward.LingZhu.DomainDirectory
      }
      throw new Error('Fail to query lingzhu domain directory')
    }
  }
}
