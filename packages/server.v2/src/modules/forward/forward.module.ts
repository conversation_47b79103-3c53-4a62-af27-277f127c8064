import { Module } from '@nestjs/common'

import { ForwardController } from './forward.controller'
import { ForwardService } from './forward.service'
import { ForwardTianheService } from './tianhe.service'
import { ForwardOpenApiService } from './open-api.service'
import { ForwardLingZhuService } from './lingzhu.service'
import { SharedModule } from '../shared/shared.module'

@Module({
  imports: [SharedModule],
  controllers: [ForwardController],
  providers: [ForwardService, ForwardTianheService, ForwardOpenApiService, ForwardLingZhuService],
  exports: [ForwardService, ForwardTianheService, ForwardOpenApiService, ForwardLingZhuService],
})
export class ForwardModule {}
