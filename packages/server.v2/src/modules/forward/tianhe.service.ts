import axios from 'axios'
import { isObject } from 'lodash'
import { Injectable } from '@nestjs/common'

const TIANHE_PRT_HOST = 'https://legoprt.corp.kuaishou.com'
const TIANHE_COOKIE = [
  'userName=yingpengsha',
].join('; ')

@Injectable()
export class ForwardTianheService {
  async queryTianheAppInfo(appKey: string): Promise<Service.Forward.Tianhe.AppInfo> | never {
    const result = await axios({
      method: 'POST',
      url: `${TIANHE_PRT_HOST}/gateway/galax/application/manage/get`,
      data: {
        appKey,
      },
      headers: {
        'Content-Type': 'application/json',
        'Cookie': TIANHE_COOKIE,
      },
    })
    if (
      isObject(result.data)
      && 'result' in result.data
      && result.data.result === 1
      && 'data' in result.data
      && 'application' in result.data.data
    ) {
      return result.data.data.application as Service.Forward.Tianhe.AppInfo
    }
    throw new Error(`Not found app info: ${appKey}`)
  }

  async queryTianhePageInfo(appKey: string, pageCode: string): Promise<Service.Forward.Tianhe.PageInfo> | never {
    const result = await axios({
      method: 'POST',
      url: `${TIANHE_PRT_HOST}/gateway/galax/page/manage/get`,
      data: {
        appKey,
        pageCode,
      },
      headers: {
        'Content-Type': 'application/json',
        'Cookie': TIANHE_COOKIE,
      },
    })
    if (
      isObject(result.data)
      && 'result' in result.data
      && result.data.result === 1
      && 'data' in result.data
    ) {
      return result.data.data as Service.Forward.Tianhe.PageInfo
    }
    throw new Error(`Not found page info: ${appKey}/page/${pageCode}`)
  }
}
