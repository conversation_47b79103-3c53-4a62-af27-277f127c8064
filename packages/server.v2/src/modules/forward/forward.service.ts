import { readJSONSync } from 'fs-extra'
import { Injectable } from '@nestjs/common'

import { IS_PROD } from '@/constants/envs'
import { resolve } from 'path'
import { PROJECT_ROOT } from '@/constants/path'
import { SOURCE_CODE_BUSINESS } from '@/shared'
import { JsonValue } from 'type-fest'
import axios from 'axios'

@Injectable()
export class ForwardService {
  get ports(): Record<string, number> {
    if (IS_PROD) {
      return readJSONSync(resolve(PROJECT_ROOT, 'ports.json'))
    }
    else {
      return {}
    }
  }

  forwardOtherService(business: SOURCE_CODE_BUSINESS, path: string, body: JsonValue): Promise<unknown> {
    if (!(business in this.ports)) {
      throw new Error(`Invalid business: ${business}`)
    }

    return axios.post(`http://localhost:${this.ports[business]}/${path}`, body)
  }
}
