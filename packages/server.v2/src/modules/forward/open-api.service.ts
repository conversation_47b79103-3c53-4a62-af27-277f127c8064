import { IS_PROD } from '@/constants/envs'
import { Injectable } from '@nestjs/common'
import axios from 'axios'
const TOKEN_URL_MAP = {
  STAGING:
    'https://is-gateway-test.corp.kuaishou.com/token/get?appKey=7c359cdc-d311-4d66-842c-e09050677a91',
  PROD: 'http://openapi-gateway.internal/token/get?appKey=a455ea51-cad7-4972-89ff-fe6a99e0e67c&secretKey=5735724dbd8e1cf1ed2044106e58c505',
}

const OPEN_API_URL_MAP = {
  STAGING: 'https://is-gateway-test.corp.kuaishou.com/openapi',
  PROD: 'http://openapi-gateway.internal/openapi',
}

@Injectable()
export class ForwardOpenApiService {
  async fetchUserInfo(username: string): Promise<Service.Forward.OpenAPI.User> {
    return new Promise((resolve, reject) => {
      const env = IS_PROD ? 'PROD' : 'STAGING'

      axios
        .get(TOKEN_URL_MAP[env])
        .then((token) => {
          if (
            token.status !== 200
            || token.data?.code !== 0
            || !token.data?.result?.accessToken
          ) {
            throw new Error('授权失败，调用 open api 错误')
          }

          const { accessToken } = token.data.result
          return axios({
            url: `${OPEN_API_URL_MAP[env]}/v2/user/user/${username}`,
            method: 'GET',
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          })
        })
        .then((usersInfo) => {
          console.log(usersInfo.data)
          if (
            usersInfo.status !== 200
            || usersInfo.data?.status !== 0
            || !usersInfo.data?.data
          ) {
            throw new Error('用户不存在，请确认')
          }

          resolve(usersInfo.data.data)
        })
        .catch(err => reject(err))
    })
  }
}
