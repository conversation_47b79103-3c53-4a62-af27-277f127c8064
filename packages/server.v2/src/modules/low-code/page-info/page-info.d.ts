declare namespace Service.LowCode.PageInfo {

  export type PageInfoRecord = import('@prisma/client').lowcode_page_info

  export interface ListParams extends Service.__OldPaginationParams {
    business: string
    schema?: boolean
  }

  export interface FindOneParams {
    business: string
    url: string
    version: string
  }

  export type CreatePageInfoFromPlatformReportParams =
    import('type-fest').OverrideProperties<
      import('@prisma/client').Prisma.lowcode_page_infoCreateInput,
      'id' | 'status' | 'create_time' | 'update_time',
      {
        payload: import('type-fest').JsonValue
      }
    >

  export namespace Tianhe {
    export interface PageIdentifier {
      appKey: string
      pageCode: string
    }
  }
}
