import { Module } from '@nestjs/common'
import { LowCodePageInfoController } from './page-info.controller'
import { LowCodePageInfoService } from './page-info.service'
import { ForwardModule } from '@/modules/forward/forward.module'
import { TianhePageInfoService } from './tianhe.page-info.service'
import { SharedModule } from '@/modules/shared/shared.module'
import { FangZhouPageInfoService } from './fangzhou.page-info.service'

@Module({
  imports: [ForwardModule, SharedModule],
  controllers: [LowCodePageInfoController],
  providers: [
    LowCodePageInfoService,
    TianhePageInfoService,
    FangZhouPageInfoService,
  ],
  exports: [
    LowCodePageInfoService,
    TianhePageInfoService,
    FangZhouPageInfoService,
  ],
})
export class LowCodePageInfoModule {}
