import { RECORD_STATUS } from '@/constants/status'
import { ForwardTianheService } from '@/modules/forward/tianhe.service'
import { LOW_CODE_BUSINESS, now } from '@/shared'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { isString } from '@/tools/type'
import { loggerInstance } from '@/tools/winston'
import { Injectable } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { isObject } from 'lodash'

@Injectable()
export class LowCodePageInfoService {
  private readonly prisma = PrismaClientInstance
  private readonly logger = loggerInstance

  constructor(private readonly forwardTianheService: ForwardTianheService) {}

  async findOne(params: Service.LowCode.PageInfo.FindOneParams): Promise<Service.LowCode.PageInfo.PageInfoRecord | null> {
    const { business, url, version } = params
    return this.prisma.lowcode_page_info.findFirst({
      where: {
        business,
        url,
        version,
      },
    })
  }

  async tianheAppList(): Promise<{ appKey: string, appName: string }[]> {
    const tianhePageRecords = await this.prisma.lowcode_page_info.findMany({
      where: {
        business: LOW_CODE_BUSINESS.KAEL,
        status: RECORD_STATUS.EFFECT,
      },
    })
    const appList: { appKey: string, appName: string }[] = []
    for (const tianhePage of tianhePageRecords) {
      const { payload } = tianhePage
      if (
        isObject(payload)
        && 'appKey' in payload
        && isString(payload.appKey)
        && 'appName' in payload
        && isString(payload.appName)
        && !appList.some(app => app.appKey === payload.appKey)
      ) {
        appList.push({
          appKey: payload.appKey,
          appName: payload.appName,
        })
      }
    }
    return appList
  }

  async clearUselessPage(): Promise<number> {
    // 删除 url、version、business 字段重复的记录，只保留 update_time 最新的
    const duplicates = await this.prisma.$queryRaw<{ id: bigint }[]>`
      SELECT l1.id
      FROM lowcode_page_info l1
      JOIN (
        SELECT url, version, business, MAX(update_time) as max_update_time
        FROM lowcode_page_info
        WHERE status = ${RECORD_STATUS.EFFECT} and business = ${LOW_CODE_BUSINESS.KAEL}
        GROUP BY url, version, business
      ) l2
      ON l1.url = l2.url AND l1.version = l2.version AND l1.business = l2.business
      WHERE l1.status = ${RECORD_STATUS.EFFECT}
        AND l1.update_time < l2.max_update_time
    `

    if (duplicates.length > 0) {
      // bigint to number
      const duplicateIds = duplicates.map((d: { id: bigint }) =>
        d.id.toString(),
      )
      await this.prisma.lowcode_page_info.deleteMany({
        where: {
          id: { in: duplicateIds.map(Number) },
        },
      })
    }

    return duplicates.length
  }

  async clearLowCodePageSchema(business: LOW_CODE_BUSINESS): Promise<number> {
    let deletedCount = 0
    let notNullCount = await this.prisma.lowcode_page_info.count({
      where: {
        business: business,
        schema: { not: null },
      },
    })
    while (notNullCount > 0) {
      const ids = await this.prisma.lowcode_page_info.findMany({
        where: {
          business: business,
          schema: { not: null },
        },
        select: { id: true },
        take: 10,
      })
      await this.prisma.lowcode_page_info.updateMany({
        where: {
          id: { in: ids.map(i => i.id) },
        },
        data: {
          schema: null,
        },
      })
      deletedCount += ids.length
      notNullCount = await this.prisma.lowcode_page_info.count({
        where: {
          business: business,
          schema: { not: null },
        },
      })
    }
    return deletedCount
  }

  async clear(where: Prisma.lowcode_page_infoWhereInput): Promise<Prisma.BatchPayload> {
    return this.prisma.lowcode_page_info.updateMany({
      where: {
        status: RECORD_STATUS.EFFECT,
        ...where,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async update(id: number, params: Prisma.lowcode_page_infoUpdateInput): Promise<Service.LowCode.PageInfo.PageInfoRecord> {
    return this.prisma.lowcode_page_info.update({
      where: { id },
      data: {
        ...params,
        update_time: now(),
      },
    })
  }

  async createPageInfoFromPlatformReport(
    params: Service.LowCode.PageInfo.CreatePageInfoFromPlatformReportParams,
  ): Promise<Service.LowCode.PageInfo.PageInfoRecord> {
    const { business, url, version, payload } = params
    await this.prisma.lowcode_page_info.updateMany({
      where: {
        business,
        url,
        version,
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })

    if (business === LOW_CODE_BUSINESS.KAEL && 'appKey' in payload) {
      const appInfo = await this.forwardTianheService.queryTianheAppInfo(
        payload.appKey,
      )
      if (appInfo) {
        Object.assign(payload, {
          appName: appInfo.name,
        })
      }
    }

    if (
      business === LOW_CODE_BUSINESS.KAEL
      && 'appKey' in payload
      && 'pageCode' in payload
    ) {
      const pageInfo = await this.forwardTianheService.queryTianhePageInfo(
        payload.appKey,
        payload.pageCode,
      )

      if (pageInfo) {
        Object.assign(payload, {
          pageId: pageInfo.id,
          pageName: pageInfo.name,
          pageDesc: pageInfo.desc,
          pageCreator: pageInfo.creator,
          pageUpdateTime: pageInfo.updateTime,
          pageCreateTime: pageInfo.createTime,
        })
      }
    }

    return this.prisma.lowcode_page_info.create({
      data: {
        ...params,
        schema: isObject(params.schema)
          ? JSON.stringify(params.schema)
          : isString(params.schema)
            ? params.schema
            : '{}',
        id: genPrimaryIndex(),
        status: RECORD_STATUS.EFFECT,
        create_time: params.create_time ?? now(),
        update_time: now(),
      },
    })
  }

  async findPageInfoByAppKeyAndPageCode(appKey: string, pageCode: string): Promise<Service.LowCode.PageInfo.PageInfoRecord | null> {
    return this.prisma.lowcode_page_info.findFirst({
      where: {
        business: LOW_CODE_BUSINESS.KAEL,
        status: RECORD_STATUS.EFFECT,
        url: {
          contains: `${appKey}/page/${pageCode}`,
        },
      },
      orderBy: {
        create_time: 'desc',
      },
    })
  }

  async clearNonExpectationPage(): Promise<Prisma.BatchPayload> {
    return this.prisma.lowcode_page_info.updateMany({
      where: {
        status: RECORD_STATUS.EFFECT,
        business: LOW_CODE_BUSINESS.KAEL,
        url: {
          not: {
            startsWith: 'http',
          },
        },
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async findNewestTianhePageURLList(beforeTime: number = now()): Promise<
    {
      url: string
      version: string
    }[]
  > {
    const results = await this.prisma.$queryRaw<
      { url: string, version: string }[]
    >`
    SELECT p1.url, p1.version
    FROM lowcode_page_info p1
    WHERE p1.create_time = (
        SELECT create_time
        FROM lowcode_page_info p2
        WHERE p2.url = p1.url
          AND p2.business = ${LOW_CODE_BUSINESS.KAEL}
          AND p2.create_time <= ${beforeTime}
          AND p2.status = ${RECORD_STATUS.EFFECT}
        ORDER BY p2.create_time DESC
        LIMIT 1
    )
  `
    return results
  }
}
