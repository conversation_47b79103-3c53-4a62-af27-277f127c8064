import { Body, Controller, Get, Post } from '@nestjs/common'
import { LowCodePageInfoService } from './page-info.service'
import { LOW_CODE_BUSINESS } from '@/shared'
import { TianhePageInfoService } from './tianhe.page-info.service'
import { FangZhouPageInfoService } from './fangzhou.page-info.service'
import { isMainService } from '@/main'
import { PromiseAllFulfilled } from '@/tools/Promise'
import { Cron } from '@nestjs/schedule'
import { Prisma } from '@prisma/client'

@Controller('low-code/page-info')
export class LowCodePageInfoController {
  constructor(
    private readonly pageInfoService: LowCodePageInfoService,
    private readonly tianhePageInfoService: TianhePageInfoService,
    private readonly fangzhouPageInfoService: FangZhouPageInfoService,
  ) {}

  @Post('clear-useless')
  async clearUselessPage(): Promise<string> {
    const deletedCount = await this.pageInfoService.clearUselessPage()
    return `已删除 ${deletedCount} 条重复记录`
  }

  @Post('clear-low-code-page-schema')
  async clearKaelPageSchema(@Body() body: { business: LOW_CODE_BUSINESS }): Promise<string> {
    const deletedCount = await this.pageInfoService.clearLowCodePageSchema(
      body.business,
    )
    return `已清空 ${deletedCount} 条 kael 页面 schema`
  }

  @Get('tianhe/app-list')
  async tianheAppList(): Promise<{ appKey: string, appName: string }[]> {
    return this.pageInfoService.tianheAppList()
  }

  @Post('tianhe/clear-non-expectation-page')
  async clearNonExpectationPage(): Promise<Prisma.BatchPayload> {
    return this.pageInfoService.clearNonExpectationPage()
  }

  @Get('tianhe/newest-page-info')
  async tianhePageInfo(): Promise<{
    url: string
    version: string
  }[]> {
    return this.pageInfoService.findNewestTianhePageURLList()
  }

  @Get('fangzhou/page-count-growth')
  async fangzhouPageCountGrowth(): Promise<{
    count: number
    time: number
  }[]> {
    return this.fangzhouPageInfoService.computeTianhePageCountGrowth()
  }

  @Post('tianhe/page-count-growth')
  async tianhePageCountGrowth(): Promise<{
    count: number
    time: number
  }[]> {
    return this.tianhePageInfoService.computeTianhePageCountGrowth()
  }

  @Cron('0 0 0 * * *')
  @Post('daily/store-page-count-growths')
  async storePageCountGrowths(): Promise<{
    tianhe: {
      count: number
      time: number
    }[]
    fangzhou: {
      count: number
      time: number
    }[]
  }> {
    if (isMainService()) {
      const {
        result: [tianhe, fangzhou],
      } = await PromiseAllFulfilled([
        this.tianhePageInfoService.computeTianhePageCountGrowth(),
        this.fangzhouPageInfoService.computeTianhePageCountGrowth(),
      ])
      return {
        tianhe,
        fangzhou,
      }
    }
  }
}
