import dayjs from 'dayjs'
import { Injectable } from '@nestjs/common'

import { PrismaClientInstance } from '@/tools/prisma'
import { SharedService } from '@/modules/shared/shared.service'
import { RECORD_STATUS } from '@/constants/status'
import { LOW_CODE_BUSINESS } from '@/shared'

@Injectable()
export class TianhePageInfoService {
  private readonly prisma = PrismaClientInstance
  constructor(private readonly sharedService: SharedService) {}

  static normalizeTianheURL(url: string): {
    appKey: string
    pageCode: string
    httpURL: string
  } {
    let httpURL = ''
    if (url.startsWith('http')) {
      httpURL = url
    }
    else if (url.includes('/page/')) {
      httpURL = `https://tianhe.corp.kuaishou.com/application/${url}`
    }
    if (!httpURL) {
      throw new Error('Invalid URL')
    }
    const pathChunk = httpURL.split('/')
    const lastPageIndex = pathChunk.reduce((acc, chunk, index) => {
      if (chunk.includes('page')) {
        return index
      }
      return acc
    }, -1)
    if (lastPageIndex === -1) {
      throw new Error('Invalid URL')
    }
    const pageCode = pathChunk[lastPageIndex + 1]
    const appKey = pathChunk[lastPageIndex - 1]

    return {
      appKey,
      pageCode,
      httpURL,
    }
  }

  static synthesizeTianheURL(appKey: string, pageCode: string): string {
    return `https://tianhe.corp.kuaishou.com/application/${appKey}/page/${pageCode}`
  }

  async computeTianhePageCountGrowth(): Promise<
    {
      count: number
      time: number
    }[]
  > {
    // 第一步：从数据库查询初步数据
    const initialData = await this.prisma.lowcode_page_info.findMany({
      where: {
        status: RECORD_STATUS.DELETE,
        business: LOW_CODE_BUSINESS.KAEL,
        url: {
          startsWith: 'https://tianhe.corp.kuaishou.com',
        },
        create_time: {
          not: 0,
        },
      },
      select: {
        url: true,
        create_time: true,
      },
    })

    // 第二步：使用 JavaScript 来处理最小 create_time 和分组
    const urlToMinTime = initialData.reduce(
      (acc, { url, create_time }) => {
        const idx = acc.findIndex(item => item.url === url)
        if (idx === -1) {
          acc.push({
            url,
            createTime: create_time,
          })
        }
        else {
          // 如果当前时间小于已有的时间，则更新时间
          if (create_time < acc[idx].createTime) {
            acc[idx].createTime = create_time
          }
        }
        return acc
      },
      [] as { url: string, createTime: number }[],
    )

    // 第三步：将时间转换为日期并进行进一步分组计数
    const dateCounts = urlToMinTime.reduce(
      (acc, { createTime }) => {
        const date = dayjs(createTime).startOf('day').valueOf()
        const idx = acc.findIndex(item => item.time === date)
        if (idx === -1) {
          acc.push({
            count: 1,
            time: date,
          })
        }
        else {
          acc[idx].count += 1
        }
        return acc
      },
      [] as { count: number, time: number }[],
    )

    // 第四步：返回结果
    this.sharedService.createAndDeleteSameKeyRecords({
      key: 'TIANHE_PAGE_COUNT_GROWTH',
      value: dateCounts,
    })
    return dateCounts
  }

  async searchPageInfo(params: Service.LowCode.PageInfo.Tianhe.PageIdentifier): Promise<Service.LowCode.PageInfo.PageInfoRecord | null> {
    const pageURL = TianhePageInfoService.synthesizeTianheURL(
      params.appKey,
      params.pageCode,
    )

    return this.prisma.lowcode_page_info.findFirst({
      where: {
        business: LOW_CODE_BUSINESS.KAEL,
        url: pageURL,
        status: RECORD_STATUS.EFFECT,
      },
      orderBy: {
        create_time: 'desc',
      },
    })
  }
}
