import dayjs from 'dayjs'
import { Injectable } from '@nestjs/common'

import { PrismaClientInstance } from '@/tools/prisma'
import { SharedService } from '@/modules/shared/shared.service'
import { RECORD_STATUS } from '@/constants/status'
import { LOW_CODE_BUSINESS } from '@/shared'

@Injectable()
export class FangZhouPageInfoService {
  private readonly prisma = PrismaClientInstance
  constructor(private readonly sharedService: SharedService) {}

  async computeTianhePageCountGrowth(): Promise<
    {
      count: number
      time: number
    }[]
  > {
    // 第一步：从数据库查询初步数据
    const initialData = await this.prisma.lowcode_page_info.findMany({
      where: {
        status: RECORD_STATUS.EFFECT,
        business: LOW_CODE_BUSINESS.FANGZHOU,
        update_time: {
          not: 0,
        },
      },
      select: {
        url: true,
        update_time: true,
      },
    })

    // 第二步：使用 JavaScript 来处理最小 update_time 和分组
    const urlToMinTime = initialData.reduce(
      (acc, { url, update_time }) => {
        const idx = acc.findIndex(item => item.url === url)
        if (idx === -1) {
          acc.push({
            url,
            createTime: update_time,
          })
        }
        else {
          // 如果当前时间小于已有的时间，则更新时间
          if (update_time < acc[idx].createTime) {
            acc[idx].createTime = update_time
          }
        }
        return acc
      },
      [] as { url: string, createTime: number }[],
    )

    // 第三步：将时间转换为日期并进行进一步分组计数
    const dateCounts = urlToMinTime.reduce(
      (acc, { createTime }) => {
        const date = dayjs(createTime).startOf('day').valueOf()
        const idx = acc.findIndex(item => item.time === date)
        if (idx === -1) {
          acc.push({
            count: 1,
            time: date,
          })
        }
        else {
          acc[idx].count += 1
        }
        return acc
      },
      [] as { count: number, time: number }[],
    )

    // 第四步：返回结果
    this.sharedService.createAndDeleteSameKeyRecords({
      key: 'FANGZHOU_PAGE_COUNT_GROWTH',
      value: dateCounts,
    })
    return dateCounts
  }
}
