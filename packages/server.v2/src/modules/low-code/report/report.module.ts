import { Module } from '@nestjs/common'
import { UserModule } from '@/modules/user/user.module'

import { ReportController } from './report.controller'
import { ReportService } from './report.service'
import { TianheReportService } from './tianhe-report.service'
import { LowCodePageInfoModule } from '../page-info/page-info.module'
import { MaterialReferModule } from '@/modules/material/material-refer/material-refer.module'

@Module({
  imports: [UserModule, LowCodePageInfoModule, MaterialReferModule],
  controllers: [ReportController],
  providers: [ReportService, TianheReportService],
  exports: [ReportService, TianheReportService],
})
export class ReportModule {}
