import { Injectable } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { now } from 'lodash'

import { RECORD_STATUS } from '@/constants/status'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'

@Injectable()
export class ReportService {
  private readonly prisma = PrismaClientInstance
  static readonly REPORT_TYPE = {
    /** 复杂度等级 */
    LEVEL: 'level',
    /** 搭建时长 */
    BUILD_TIME: 'build_time',
  }

  static readonly LEVEL_MAP = {
    'L1': 1,
    'L2': 2,
    'L3': 3,
    'L4': 4,
    'L5': 5,
    'L5+': 5.5,
  }

  async createReport(params: Service.LowCode.Report.CreateNormalReportParams): Promise<Service.LowCode.Report.ReportRecord> {
    await this.prisma.lowcode_build_report.updateMany({
      where: {
        page_id: params.page_id,
        type: params.type,
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })

    return this.prisma.lowcode_build_report.create({
      data: {
        ...params,
        id: genPrimaryIndex(),
        update_time: now(),
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async clearReport(where: Prisma.lowcode_build_reportWhereInput): Promise<Prisma.BatchPayload> {
    return this.prisma.lowcode_build_report.updateMany({
      where,
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }
}
