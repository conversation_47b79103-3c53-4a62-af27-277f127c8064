import {
  decodeComponentType,
  genComponentChildrenIncludeProps,
} from '@kael/schema-utils'
import axios from 'axios'
import assert from 'assert'
import get from 'lodash/get'
import { JsonObject } from 'type-fest'
import { Injectable } from '@nestjs/common'
import { getPageLevelFromSchema } from '@kael/complexity'

import { CODE_TYPE } from '@/constants/code-type'
import { isArray, isNumber, isObject, isString } from '@/tools/type'
import { limitInvokePromiseAllFulfilled } from '@/tools/Promise'
import { logStringify, safeParseMaybeNull } from '@/tools/json'
import { loggerInstance } from '@/tools/winston'
import { LOW_CODE_BUSINESS, now } from '@/shared'
import { BLOB_STORE_DOMAIN } from '@/constants/envs'

import { UserService } from '@/modules/user/user.service'
import { MaterialReferService } from '@/modules/material/material-refer/material-refer.service'
import { ReportService } from './report.service'
import { LowCodePageInfoService } from '../page-info/page-info.service'
import { TianhePageInfoService } from '../page-info/tianhe.page-info.service'

@Injectable()
export class TianheReportService {
  private readonly logger = loggerInstance

  constructor(
    private readonly userService: UserService,
    private readonly lowCodePageInfoService: LowCodePageInfoService,
    private readonly reportService: ReportService,
    private readonly materialReferService: MaterialReferService,
  ) {}

  static combineTianhePageSchemaURL(
    appKey: string,
    pageCode: string,
    version: string,
  ): string {
    return `${BLOB_STORE_DOMAIN}/image-tianhe/%22${[
      appKey,
      pageCode,
      version,
    ].join('_')}%22`
  }

  static async fetchTianhePageSchema(
    appKey: string,
    pageCode: string,
    version: string,
  ): Promise<JsonObject> {
    const blobStore = TianheReportService.combineTianhePageSchemaURL(
      appKey,
      pageCode,
      version,
    )
    const { data: rawSchema } = await axios.get(blobStore)
    const schema = isString(rawSchema)
      ? safeParseMaybeNull(rawSchema)
      : rawSchema
    if (!isObject(schema)) {
      throw new Error('Invalid schema')
    }
    else {
      return schema as JsonObject
    }
  }

  async clearTianhePageReports(): Promise<void> {
    await this.lowCodePageInfoService.clear({
      business: LOW_CODE_BUSINESS.KAEL,
      // TODO: add more conditions, url like `https://tianhe.corp.kuaishou.com/application%`
    })
    await this.materialReferService.clear({
      ref_business: LOW_CODE_BUSINESS.KAEL,
    })
  }

  async refreshTianhePageReport(
    params: Service.LowCode.Report.RefreshTianhePageReportItem,
  ): Promise<void> {
    const {
      appKey,
      pageCode,
      version: _version,
      create_time,
      creator,
    } = params
    const version = isNumber(_version) ? `${_version}` : _version
    const httpURL = TianhePageInfoService.synthesizeTianheURL(appKey, pageCode)
    const schema = await TianheReportService.fetchTianhePageSchema(
      appKey,
      pageCode,
      version,
    )
    return this.createTianhePageReport({
      ref_page: httpURL,
      kael_schema: schema,
      version,
      creator,
      create_time,
    })
  }

  async createTianhePageReport(body: Service.LowCode.Report.TianhePageReport): Promise<void> {
    const {
      ref_page,
      version,
      creator,
      kael_schema,
      create_time = now(),
    } = body
    const kaelSchema = isString(kael_schema)
      ? safeParseMaybeNull(kael_schema)
      : kael_schema
    assert(isObject(kaelSchema), 'Invalid schema')
    const creatorInfo
      = await this.userService.findOrRegisterUserByUsername(creator)
    const { appKey, pageCode, httpURL }
      = TianhePageInfoService.normalizeTianheURL(ref_page)

    const schemaURL = TianheReportService.combineTianhePageSchemaURL(
      appKey,
      pageCode,
      version,
    )

    // 录入页面信息
    const lowCodePageRecord
      = await this.lowCodePageInfoService.createPageInfoFromPlatformReport({
        business: LOW_CODE_BUSINESS.KAEL,
        url: httpURL,
        version,
        schema: schemaURL,
        payload: {
          appKey,
          pageCode,
        },
        create_time,
      })

    // 统计组件使用次数
    const libraryMaterialUseCount: Record<
      string,
      Record<string, { count: number }>
    > = {}
    const waitForProcessComponents
      = 'view' in kaelSchema ? [kaelSchema.view] : []
    while (waitForProcessComponents.length) {
      const currentDeepComponents = waitForProcessComponents.splice(
        0,
        waitForProcessComponents.length,
      )
      const nextDeepComponents = []
      for (const component of currentDeepComponents) {
        if (
          component
          && isObject(component)
          && 'type' in component
          && isString(component.type)
        ) {
          const [libType, compType] = decodeComponentType(component.type)
          if (!(libType in libraryMaterialUseCount)) {
            libraryMaterialUseCount[libType] = {}
          }
          if (!(compType in libraryMaterialUseCount[libType])) {
            libraryMaterialUseCount[libType][compType] = { count: 1 }
          }
          else {
            const preCount
              = libraryMaterialUseCount[libType][compType].count || 0
            libraryMaterialUseCount[libType][compType] = {
              ...(libraryMaterialUseCount[libType][compType] || {}),
              count: preCount + 1,
            }
          }

          for (const child of genComponentChildrenIncludeProps(
            component as { type: string },
          )) {
            nextDeepComponents.push(child)
          }
        }
      }

      waitForProcessComponents.push(...nextDeepComponents)
    }

    // 页面编辑时间
    await this.reportService.createReport({
      page_id: lowCodePageRecord.id,
      type: ReportService.REPORT_TYPE.BUILD_TIME,
      /** 指标值 */
      value: get(
        kaelSchema,
        ['designerState', 'editTime', 'duration'],
        get(kaelSchema, ['editTime'], 0),
      ),
      update_time: create_time,
    })

    // 复杂度计算
    let count = 0
    let level = 'L1'
    try {
      const result = getPageLevelFromSchema(kaelSchema)
      count = result.count
      level = result.level
    }
    catch (error) {
      throw new Error(
        `Failed to calculate complexity: ${error}, ${logStringify({
          appKey,
          pageCode,
          version,
        })}`,
      )
    }

    // 录入复杂度信息
    await this.reportService.createReport({
      page_id: lowCodePageRecord.id,
      type: ReportService.REPORT_TYPE.LEVEL,
      value: ReportService.LEVEL_MAP[level] ?? 0,
      update_time: create_time,
    })

    this.logger.info(
      `success to create report for page ${lowCodePageRecord.id}, level: ${ReportService.LEVEL_MAP[level]}`,
    )

    // 更新页面信息
    await this.lowCodePageInfoService.update(lowCodePageRecord.id, {
      content: JSON.stringify({
        report: {
          complex_value: count ?? 0,
          level: level ?? 0,
          build_time: get(
            kaelSchema,
            ['designerState', 'editTime', 'duration'],
            get(kaelSchema, ['editTime'], 0),
          ),
        },
      }),
    })

    // 引用关系
    const refers: Service.Material.Refer.CreateParams[] = []
    for (const [libraryType, materialMap] of Object.entries(
      libraryMaterialUseCount,
    )) {
      const targetLib
        = 'materialLibs' in kaelSchema && isArray(kaelSchema.materialLibs)
          ? kaelSchema.materialLibs.find(
            lib =>
              isObject(lib) && 'name' in lib && lib.name === libraryType,
          )
          : undefined

      for (const [materialType, { count }] of Object.entries(materialMap)) {
        let namespace = `${libraryType}/${materialType}`
        let materialVersion = '0.0.0'
        if (
          isObject(targetLib)
          && 'schemaJson' in targetLib
          && isObject(targetLib.schemaJson)
          && 'components' in targetLib.schemaJson
          && isArray(targetLib.schemaJson.components)
        ) {
          const targetComponent = targetLib.schemaJson.components.find(
            component =>
              isObject(component)
              && 'moduleCode' in component
              && isString(component.moduleCode)
              && component.moduleCode === materialType,
          )
          if (targetComponent && isObject(targetComponent)) {
            namespace
              = 'componentName' in targetComponent
              && isString(targetComponent.componentName)
                ? targetComponent.componentName
                : `${libraryType}/${materialType}`
            materialVersion
              = 'moduleVersion' in targetComponent
                ? isNumber(targetComponent.moduleVersion)
                  ? `${targetComponent.moduleVersion}`
                  : isString(targetComponent.moduleVersion)
                    ? targetComponent.moduleVersion
                    : '0.0.0'
                : '0.0.0'
          }
        }

        refers.push({
          namespace,
          version: materialVersion,
          material_id: 0, // 不限制是否在物料中台录入过
          ref_page: httpURL,
          ref_page_version: version,
          ref_business: LOW_CODE_BUSINESS.KAEL,
          type: MaterialReferService.REFER_TYPES.REFER,
          refer_count: count || 1,
          code_type: CODE_TYPE.LOW_CODE,
          creator_id: creatorInfo.id,
          create_time,
        })
      }
    }

    await this.materialReferService.clearTargetPageRefer({
      business: LOW_CODE_BUSINESS.KAEL,
      page: httpURL,
      ref_page_version: version,
    })
    await limitInvokePromiseAllFulfilled(
      refers.map(refer => (): Promise<Service.Material.Refer.Refer> => {
        return this.materialReferService.createRefer(refer)
      }),
    )
  }
}
