import { Body, Controller, Post } from '@nestjs/common'
import { isString } from '@/tools/type'
import { safeParseMaybeNull } from '@/tools/json'

import { TianheReportService } from './tianhe-report.service'
import { now } from 'lodash'
import { limitInvokePromiseAllFulfilled, PromiseAllFulfilledResult } from '@/tools/Promise'

@Controller('low-code/report')
export class ReportController {
  constructor(private readonly tianheReportService: TianheReportService) {}

  @Post('tianhe/page/report')
  async createTianhePageReport(
    @Body() body: Service.LowCode.Report.TianhePageReport,
  ): Promise<void> {
    return this.tianheReportService.createTianhePageReport({
      ...body,
      create_time: body.create_time || now(),
      kael_schema: isString(body.kael_schema)
        ? safeParseMaybeNull(body.kael_schema)
        : body.kael_schema,
    })
  }

  @Post('tianhe/page/clear')
  async clearTianhePageReport(): Promise<void> {
    return this.tianheReportService.clearTianhePageReports()
  }

  @Post('tianhe/page/refresh')
  async refreshTianhePageReport(
    @Body() body: Service.LowCode.Report.RefreshTianhePageReportItem[],
  ): Promise<PromiseAllFulfilledResult<void>> {
    console.log(body)
    return limitInvokePromiseAllFulfilled(
      body.map(
        param => () =>
          this.tianheReportService.refreshTianhePageReport(param),
      ),
    )
  }
}
