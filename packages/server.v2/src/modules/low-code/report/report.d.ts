declare namespace Service.LowCode.Report {
  export type ReportRecord = import('@prisma/client').lowcode_build_report
  export type CreateNormalReportParams = Pick<
    import('@prisma/client').Prisma.lowcode_build_reportCreateInput,
    'page_id' | 'type' | 'update_time' | 'value'
  >

  export interface RefreshTianhePageReportItem {
    appKey: string
    pageCode: string
    version: string
    creator: string
    create_time: number
  }

  export interface TianhePageReport {
    ref_page: string
    version: string
    kael_schema: import('type-fest').JsonValue
    creator: string
    create_time?: number
  }
}
