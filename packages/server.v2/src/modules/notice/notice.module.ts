import { forwardRef, Module } from '@nestjs/common'
import { NoticeController } from './notice.controller'
import { NoticeService } from './notice.service'
import { ConfigModule } from '../config/config.module'
import { MaterialModule } from '../material/material.module'
import { MaterialConvertModule } from '../material/material-convert/material-convert.module'

@Module({
  imports: [
    ConfigModule,
    forwardRef(() => MaterialModule),
    forwardRef(() => MaterialConvertModule),
  ],
  controllers: [NoticeController],
  providers: [NoticeService],
  exports: [NoticeService],
})
export class NoticeModule {}
