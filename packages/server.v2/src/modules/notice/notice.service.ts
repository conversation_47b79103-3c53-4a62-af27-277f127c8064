import { Injectable } from '@nestjs/common'
import { ConfigService } from '../config/config.service'
import { MATERIAL_TYPE } from '@/shared'
import { MaterialService } from '../material/material.service'
import axios from 'axios'
import { loggerInstance } from '@/tools/winston'
import { MaterialConvertService } from '../material/material-convert/material-convert.service'

export enum MATERIAL_OPERATION_TYPES {
  /** 新建物料 */
  CREATE = 'create',
  /** 发布新版本 */
  PUBLISH = 'publish',
  DELETE = 'delete',
  UPDATE = 'update',
}

@Injectable()
export class NoticeService {
  private readonly logger = loggerInstance
  constructor(
    private readonly configService: ConfigService,
    private readonly materialService: MaterialService,
    private readonly materialConvertService: MaterialConvertService,
  ) {}

  async notice(
    action: MATERIAL_OPERATION_TYPES,
    materialId: number,
    version?: string,
  ): Promise<void> {
    const materialDetail = await this.materialService.detail({
      id: materialId,
      version,
    })

    if (!materialDetail) throw new Error('物料不存在')
    const targetBusinessConfig = await this.configService.findConfigByBusiness(
      materialDetail.business,
    )
    if (
      targetBusinessConfig.meta.subscription
      && targetBusinessConfig.meta.subscription.path
    ) {
      const logInfo = [
        {
          key: '物料名',
          value: materialDetail.title,
        },
        {
          key: 'Namespace',
          value: materialDetail.namespace,
        },
        {
          key: '版本',
          value: version ?? materialDetail.version,
        },
        {
          key: '业务方',
          value: materialDetail.business,
        },
        {
          key: '通知方',
          value: targetBusinessConfig.meta.subscription.path,
        },
      ]

      this.logger.info(
        `物料变更(${action})通知开始：${logInfo
          .map(item => `${item.key}：${item.value}`)
          .join('；')}`,
      )
      axios
        .post(targetBusinessConfig.meta.subscription.path, {
          action,
          material: materialDetail,
        })
        .catch((e) => {
          this.logger.warn(
            `物料变更通知失败，错误原因是${e.message}。${logInfo
              .map(item => `${item.key}：${item.value}`)
              .join('；')}`,
          )
        })
    }

    if (
      [
        MATERIAL_OPERATION_TYPES.PUBLISH,
        MATERIAL_OPERATION_TYPES.CREATE,
      ].includes(action)
    ) {
      switch (materialDetail.type) {
        case MATERIAL_TYPE.COMPONENT:
          this.materialConvertService.triggerAutoConvert(
            materialId,
            version ?? materialDetail.version,
          )
          break
        case MATERIAL_TYPE.COM_LIB:
          this.materialConvertService.triggerAutoConvertForComLib(
            materialId,
            version ?? materialDetail.version,
          )
          break
        default:
          break
      }
    }
  }
}
