import { JsonValue } from 'type-fest'
import { ApiTags } from '@nestjs/swagger'
import { Controller, Get, Query } from '@nestjs/common'

import { isObject } from '@/shared'
import { safeParseMaybeNull } from '@/tools/json'

import { SearchPageReportParamsDto } from './dto/search-page-report.dto'
import { TianhePageInfoService } from '@/modules/low-code/page-info/tianhe.page-info.service'

@ApiTags('天河')
@Controller('open/tianhe')
export class OpenForTianheController {
  constructor(private readonly tianhePageInfoService: TianhePageInfoService) {}

  @Get('/page-report')
  async pageReport(@Query() params: SearchPageReportParamsDto): Promise<JsonValue> {
    const record = await this.tianhePageInfoService.searchPageInfo(params)
    if (!record) return null

    const result = safeParseMaybeNull(record.content)
    if (!isObject(result) || !('report' in result)) return null
    return result.report
  }
}
