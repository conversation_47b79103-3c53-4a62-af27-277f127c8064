declare namespace Service.Open.Material {

  export interface CreateMaterialParams {
    schema: GeneralMaterialSchema.Schema
    content?: Base.JSONObject
    meta?: Base.JSONObject
    creator_id?: number
  }

  export interface CreateMaterialResponse {
    materialId: number
    materialPubId: number
  }

  export type BatchCreateMaterialParams = CreateMaterialParams[]

  export interface BatchCreateMaterialItem {
    success: boolean
    data?: CreateMaterialResponse
    error?: string
    index: number
  }

  export type BatchCreateMaterialResponse = BatchCreateMaterialItem[]

  export interface CreateMaterialLibParams {
    schema: GeneralMaterialSchema.LibrarySchema
    content?: Base.JSONObject
    meta?: Base.JSONObject
    creator_id?: number
  }

  export interface CreateMaterialLibResponse {
    materialLibId: number
    materialLibPubId: number
  }
}
