import { Module } from '@nestjs/common'
import { OpenMaterialController } from './material.controller'
import { OpenMaterialService } from './material.service'
import { MaterialTagModule } from '@/modules/material/material-tag/material-tag.module'
import { MaterialPublishModule } from '@/modules/material/material-publish/material-publish.module'
import { UserModule } from '@/modules/user/user.module'
import { MaterialMetaModule } from '@/modules/material/material-meta/material-meta.module'
import { MaterialModule } from '@/modules/material/material.module'
import { MaterialTagRelationModule } from '@/modules/material/material-tag-relation/material-tag-relation.module'
import { MaterialMetaRelationModule } from '@/modules/material/material-meta-relation/material-meta-relation.module'
import { NoticeModule } from '@/modules/notice/notice.module'

@Module({
  imports: [
    MaterialTagModule,
    MaterialTagRelationModule,
    MaterialPublishModule,
    MaterialMetaModule,
    MaterialMetaRelationModule,
    UserModule,
    MaterialMetaModule,
    MaterialModule,
    NoticeModule,
  ],
  controllers: [OpenMaterialController],
  providers: [OpenMaterialService],
})
export class OpenMaterialModule {}
