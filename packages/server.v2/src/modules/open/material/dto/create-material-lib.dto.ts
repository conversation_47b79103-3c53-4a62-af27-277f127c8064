import { ApiProperty } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import {
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  ValidateNested,
} from 'class-validator'

import { GeneralMaterialLibrarySchemaDTO } from '@/features/general-material-schema/general-material-schema.dto'

export class CreateMaterialLibDTO
implements Service.Open.Material.CreateMaterialLibParams {
  @ApiProperty()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => GeneralMaterialLibrarySchemaDTO)
  readonly schema: GeneralMaterialLibrarySchemaDTO

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string' && !isNaN(Number(value))) {
      return Number(value)
    }
    return value
  })
  readonly creator_id?: number

  @ApiProperty()
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  readonly content?: Base.JSONObject

  @ApiProperty()
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  readonly meta?: Base.JSONObject
}
