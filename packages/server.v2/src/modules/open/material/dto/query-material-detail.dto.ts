import { MATERIAL_VERSION_TYPE } from '@/constants/material'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsNumber, IsObject, IsString } from 'class-validator'

export class QueryMaterialDetailParamsDTO
implements Service.Open.Material.DetailQueries {
  @ApiProperty({ description: '物料ID' })
  @IsNumber()
  @Type(() => Number)
  id?: number

  @ApiProperty({ description: '物料命名空间' })
  @IsString()
  @Type(() => String)
  namespace?: string

  @ApiProperty({ description: '物料版本' })
  @IsString()
  @Type(() => String)
  version?: string

  @ApiProperty({ description: '详情选项' })
  @IsObject()
  options?: {
    withVersions?: boolean
    typeOfVersions?: MATERIAL_VERSION_TYPE
  }
}
