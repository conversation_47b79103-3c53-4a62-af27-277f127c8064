import { MATERIAL_TYPE, MATERIAL_VERSION_TYPE } from '@/constants/material'
import { BUSINESS } from '@/shared'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsObject, IsString } from 'class-validator'

export class QueryMaterialListParamsDTO
implements Service.Open.Material.ListQueries {
  @ApiProperty({ description: '物料命名空间' })
  @IsString()
  @Type(() => String)
  namespace?: string

  @ApiProperty({ description: '物料类型' })
  @IsEnum(MATERIAL_TYPE)
  type?: MATERIAL_TYPE

  @ApiProperty({ description: '物料特征' })
  @IsObject()
  tags?: {
    domain?: number
    business?: BUSINESS
    category?: string
    platform?: string
    meta?: Record<string, string>
  }

  @ApiProperty({ description: '详情选项' })
  @IsObject()
  detailOptions?: {
    withVersions?: boolean
    typeOfVersions?: MATERIAL_VERSION_TYPE
  }

  @ApiProperty({ description: '页码' })
  pageNum?: number

  @ApiProperty({ description: '每页数量' })
  pageSize?: number
}
