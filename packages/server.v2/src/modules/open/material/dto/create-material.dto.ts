import { ApiProperty } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import {
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  ValidateNested,
} from 'class-validator'

import { GeneralMaterialSchemaDTO } from '@/features/general-material-schema/general-material-schema.dto'

export class CreateMaterialDTO
implements Service.Open.Material.CreateMaterialParams {
  @ApiProperty()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => GeneralMaterialSchemaDTO)
  readonly schema: GeneralMaterialSchemaDTO

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string' && !isNaN(Number(value))) {
      return Number(value)
    }
    return value
  })
  readonly creator_id?: number

  @ApiProperty()
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  readonly content?: Base.JSONObject

  @ApiProperty()
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  readonly meta?: Base.JSONObject
}
