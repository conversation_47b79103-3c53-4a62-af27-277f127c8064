import { ApiTags } from '@nestjs/swagger'
import { Body, Controller, Post } from '@nestjs/common'

import { OpenMaterialService } from './material.service'
import { CreateMaterialDTO } from './dto/create-material.dto'
import { CreateMaterialLibDTO } from './dto/create-material-lib.dto'

@ApiTags('物料')
@Controller('open/material')
export class OpenMaterialController {
  constructor(private readonly openMaterialService: OpenMaterialService) {}

  @Post('/create')
  async create(
    @Body() params: CreateMaterialDTO,
  ): Promise<Service.Open.Material.CreateMaterialResponse> {
    return this.openMaterialService.create(params)
  }

  @Post('/create/batch')
  async batchCreate(
    @Body() params: Service.Open.Material.BatchCreateMaterialParams,
  ): Promise<Service.Open.Material.BatchCreateMaterialResponse> {
    return this.openMaterialService.batchCreate(params)
  }

  @Post('/create-lib')
  async createLib(
    @Body() params: CreateMaterialLibDTO,
  ): Promise<Service.Open.Material.CreateMaterialLibResponse> {
    return this.openMaterialService.createLib(params)
  }

  @Post('/detail')
  async detail(
    // @Query() queries: QueryMaterialDetailParamsDTO,
    @Body() queries: Service.Material.DetailQueries,
  ): Promise<Service.Material.MaterialDetail> {
    return this.openMaterialService.detail(queries)
  }

  @Post('/list')
  async list(
    // @Query() queries: QueryMaterialListParamsDTO,
    @Body() queries: Service.Material.ListQueries,
  ): Promise<Service.Material.MaterialListResult> {
    return this.openMaterialService.list(queries)
  }
}
