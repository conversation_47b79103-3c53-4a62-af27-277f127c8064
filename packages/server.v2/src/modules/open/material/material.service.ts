import { isObject } from 'lodash'
import { Injectable } from '@nestjs/common'
import { validate } from 'class-validator'
import { plainToClass } from 'class-transformer'
import assert from 'assert'

import { safeStringifyMaybeNull } from '@/tools/json'
import { PromiseAllFulfilled } from '@/tools/Promise'
import {
  BUNDLE_TYPES,
  flattenComponentField,
  isArray,
  MATERIAL_TYPE,
  PLATFORMS,
} from '@/shared'

import { MaterialService } from '@/modules/material/material.service'
import { MaterialPublishService } from '@/modules/material/material-publish/material-publish.service'
import { MaterialTagRelationService } from '@/modules/material/material-tag-relation/material-tag-relation.service'
import { MaterialMetaService } from '@/modules/material/material-meta/material-meta.service'
import { MaterialMetaRelationService } from '@/modules/material/material-meta-relation/material-meta-relation.service'
import {
  MATERIAL_OPERATION_TYPES,
  NoticeService,
} from '@/modules/notice/notice.service'
import { UserService } from '@/modules/user/user.service'

import { CreateMaterialDTO } from './dto/create-material.dto'

@Injectable()
export class OpenMaterialService {
  constructor(
    private readonly materialService: MaterialService,
    private readonly materialMetaService: MaterialMetaService,
    private readonly materialPublishService: MaterialPublishService,
    private readonly materialTagRelationService: MaterialTagRelationService,
    private readonly materialMetaRelationService: MaterialMetaRelationService,
    private readonly noticeService: NoticeService,
    private readonly userService: UserService,
  ) {}

  detail = this.materialService.detail.bind(this.materialService)

  list = this.materialService.list.bind(this.materialService)

  async create(
    params: Service.Open.Material.CreateMaterialParams,
  ): Promise<Service.Open.Material.CreateMaterialResponse> | never {
    const { schema, content, meta: rawMeta, creator_id } = params
    const { packageName, componentName, componentBundleType, version, author }
      = schema

    let currentAction: MATERIAL_OPERATION_TYPES
      = MATERIAL_OPERATION_TYPES.CREATE

    const operatorId
      = creator_id
        || (
          await this.userService
            .findOrRegisterUserByUsername(author)
            .catch(() => null)
        )?.id
    assert(
      operatorId,
      '找不到有效的创建者标识（`schema.author`/`creator_id`）',
    )

    // 基本字段信息
    const namespace
      = componentBundleType === BUNDLE_TYPES.SLMC
        ? `${packageName}/${componentName}`
        : packageName
    const title = schema.componentChineseName
    const description = schema.description
    const type = MATERIAL_TYPE.COMPONENT
    const business = schema.tags.business
    const git_url = schema.repository
      ? isObject(schema.repository)
        ? safeStringifyMaybeNull(schema.repository)
        : schema.repository
      : schema.gitUrl
    const platform = Array.isArray(schema.tags.platform)
      ? schema.tags.platform.join(',')
      : schema.tags.platform
    const meta = safeStringifyMaybeNull(rawMeta)
    const domain = isArray(schema.tags.domain)
      ? schema.tags.domain.join(',')
      : '0'

    //  确认是否存在已有物料
    const existMaterial: Service.Material.SerializeMaterialRecord | null
      = await this.materialService
        .findOneByNamespace(namespace)
        .catch(() => null)

    let materialId: number | null = existMaterial?.id
    // 根据物料是否已经存在决定更新还是创建
    if (existMaterial) {
      const existVersion
        = await this.materialPublishService.existTargetMaterialVersion(
          existMaterial.id,
          version,
        )

      // 如果版本已经存在，则抛出错误
      if (existVersion) {
        throw new Error(`物料 ${namespace} 的 ${version} 版本已存在`)
      }
      // 更新物料
      currentAction = MATERIAL_OPERATION_TYPES.UPDATE
      await this.materialService
        .updateMaterialBaseInfo({
          id: existMaterial.id,

          version,
          title: title ?? existMaterial.title,
          description: description ?? existMaterial.description,
          type,
          business: business ?? existMaterial.business,
          git_url: git_url ?? existMaterial.git_url,
          platform: platform ?? existMaterial.platform,
          meta,
          domain,

          updater_id: operatorId,
        })
        .catch((error) => {
          throw new Error('更新物料失败: ' + error.message)
        })
    }
    else {
      // 创建物料
      currentAction = MATERIAL_OPERATION_TYPES.CREATE
      const material = await this.materialService
        .createMaterialBaseInfo({
          namespace,
          version,
          title,
          description,
          type,
          business,
          git_url,
          platform: platform ?? PLATFORMS.PC,
          meta,
          domain,

          creator_id: operatorId,
        })
        .catch((error) => {
          throw new Error('创建物料失败: ' + error.message)
        })
      materialId = material.id
    }

    // 创建物料版本
    const nextVersion = await this.materialPublishService.createMaterialVersion(
      {
        material_id: materialId,
        version,
        schema: schema,
        content: content,
        preview_img: schema.thumbnailUrl,
        readme: schema.instructionUrl,
        creator_id: +operatorId,
      },
    )

    // 创建物料分类
    if (schema.tags.category) {
      await this.materialTagRelationService.updateMaterialTagsWithTagValues(
        materialId,
        isArray(schema.tags.category)
          ? schema.tags.category
          : [schema.tags.category],
        operatorId,
      )
    }

    // 创建物料自定义分类
    if (schema.tags.meta) {
      const metaList = await this.materialMetaService.findMetaDetailByBusiness(
        business,
      )
      const metaValues = Object.entries(schema.tags.meta)
        .map(([key, value]) => {
          const meta = metaList.find(item => item.value === key)
          if (!meta) return
          const currentMetaValues = meta.items.filter(item =>
            value.includes(item.value),
          )
          return currentMetaValues
        })
        .flat()
      await this.materialMetaRelationService.clearMaterialMetaRelations(
        materialId,
      )
      await this.materialMetaRelationService.createMaterialMetaRelations(
        materialId,
        metaValues.map(item => item.id),
        operatorId,
      )
    }

    this.noticeService.notice(currentAction, materialId, version)

    return {
      materialId,
      materialPubId: nextVersion.id,
    }
  }

  async batchCreate(
    materials: Service.Open.Material.BatchCreateMaterialParams,
  ): Promise<Service.Open.Material.BatchCreateMaterialResponse> {
    // 使用PromiseAllFulfilled并发执行所有创建操作
    const { result: completedResults } = await PromiseAllFulfilled(
      materials.map(async (materialData, index) => {
        try {
          // 手动校验每个物料参数
          const materialDto = plainToClass(CreateMaterialDTO, materialData)
          const validationErrors = await validate(materialDto)

          if (validationErrors.length > 0) {
            // 格式化校验错误信息
            const errorMessages = validationErrors
              .map((error) => {
                const constraints = error.constraints || {}
                return `${error.property}: ${Object.values(constraints).join(
                  ', ',
                )}`
              })
              .join('; ')

            return {
              success: false as const,
              error: `参数校验失败: ${errorMessages}`,
              index,
            }
          }

          // 校验通过，调用原有的create方法
          const result = await this.create(materialDto)
          return {
            success: true as const,
            data: result,
            index,
          }
        }
        catch (error) {
          return {
            success: false as const,
            error: error instanceof Error ? error.message : String(error),
            index,
          }
        }
      }),
    )

    return completedResults
  }

  async createLib(
    params: Service.Open.Material.CreateMaterialLibParams,
  ): Promise<Service.Open.Material.CreateMaterialLibResponse> | never {
    const { schema, content, meta: rawMeta, creator_id } = params
    const { packageName: namespace, author, version } = schema

    let currentAction: MATERIAL_OPERATION_TYPES
      = MATERIAL_OPERATION_TYPES.CREATE

    const operatorId
      = creator_id
        || (
          await this.userService
            .findOrRegisterUserByUsername(author)
            .catch(() => null)
        )?.id
    assert(
      operatorId,
      '找不到有效的创建者标识（`schema.author`/`creator_id`）',
    )

    // 校验子组件信息
    const components = flattenComponentField(schema.components)
    await Promise.all(
      components.map(async (component) => {
        const componentMaterial
          = await this.materialService.existMaterialByNamespaceAndVersion(
            component.namespace,
            component.version,
          )
        if (!componentMaterial) {
          throw new Error(
            `物料库中的物料 ${component.namespace}@${component.version} 不存在，请先发布物料。`,
          )
        }
      }),
    )

    // 基本字段信息
    const title = schema.componentChineseName
    const description = schema.description
    const type = MATERIAL_TYPE.COM_LIB
    const business = schema.tags.business
    const git_url = schema.repository
      ? isObject(schema.repository)
        ? safeStringifyMaybeNull(schema.repository)
        : schema.repository
      : schema.gitUrl
    const platform = Array.isArray(schema.tags.platform)
      ? schema.tags.platform.join(',')
      : schema.tags.platform
    const meta = safeStringifyMaybeNull(rawMeta)
    const domain = isArray(schema.tags.domain)
      ? schema.tags.domain.join(',')
      : '0'

    //  确认是否存在已有物料
    const existMaterialLib: Service.Material.SerializeMaterialRecord | null
      = await this.materialService
        .findOneByNamespace(namespace)
        .catch(() => null)

    let materialLibId: number | null = existMaterialLib?.id
    if (existMaterialLib) {
      const existVersion
        = await this.materialPublishService.existTargetMaterialVersion(
          existMaterialLib.id,
          version,
        )
      if (existVersion) {
        throw new Error(`物料库 ${namespace} 的 ${version} 版本已存在`)
      }
      await this.materialService
        .updateMaterialBaseInfo({
          id: existMaterialLib.id,
          version,
          title,
          description,
          type,
          business,
          git_url,
          platform,
          meta,
          domain,

          updater_id: operatorId,
        })
        .catch((error) => {
          throw new Error('更新物料库失败: ' + error.message)
        })
      currentAction = MATERIAL_OPERATION_TYPES.UPDATE
    }
    else {
      const materialLib = await this.materialService
        .createMaterialBaseInfo({
          namespace,
          version,
          title,
          description,
          type,
          business,
          git_url,
          platform,
          meta,
          domain,
          creator_id: operatorId,
        })
        .catch((error) => {
          throw new Error('创建物料库失败: ' + error.message)
        })
      materialLibId = materialLib.id
      currentAction = MATERIAL_OPERATION_TYPES.CREATE
    }

    // 创建物料版本
    const nextVersion = await this.materialPublishService.createMaterialVersion(
      {
        material_id: materialLibId,
        version,
        schema: schema,
        content: content,
        preview_img: schema.thumbnailUrl,
        readme: schema.instructionUrl,
        creator_id: +operatorId,
      },
    )

    // 创建物料分类
    if (schema.tags.category) {
      await this.materialTagRelationService.updateMaterialTagsWithTagValues(
        materialLibId,
        isArray(schema.tags.category)
          ? schema.tags.category
          : [schema.tags.category],
        operatorId,
      )
    }

    // 创建物料自定义分类
    if (schema.tags.meta) {
      const metaList = await this.materialMetaService.findMetaDetailByBusiness(
        business,
      )
      const metaValues = Object.entries(schema.tags.meta)
        .map(([key, value]) => {
          const meta = metaList.find(item => item.value === key)
          if (!meta) return
          const currentMetaValues = meta.items.filter(item =>
            value.includes(item.value),
          )
          return currentMetaValues
        })
        .flat()
      await this.materialMetaRelationService.clearMaterialMetaRelations(
        materialLibId,
      )
      await this.materialMetaRelationService.createMaterialMetaRelations(
        materialLibId,
        metaValues.map(item => item.id),
        operatorId,
      )
    }

    this.noticeService.notice(currentAction, materialLibId, version)

    return {
      materialLibId,
      materialLibPubId: nextVersion.id,
    }
  }
}
