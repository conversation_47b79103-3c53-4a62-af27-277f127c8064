import { Module } from '@nestjs/common'
import { OpenController } from './open.controller'
import { OpenService } from './open.service'
import { OpenForTianheModule } from './tianhe/tianhe.module'
import { OpenMaterialModule } from './material/material.module'

@Module({
  controllers: [OpenController],
  providers: [OpenService],
  imports: [OpenForTianheModule, OpenMaterialModule],
})
export class OpenModule {}
