import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'

import { SOURCE_CODE_BUSINESS } from '@/shared'
import { PromiseAllFulfilledResult } from '@/tools/Promise'

import { MaterialService } from './material.service'
import { KaelMaterialService } from './kael.material.service'

@ApiTags('material')
@Controller('material')
export class MaterialController {
  constructor(
    private readonly materialService: MaterialService,
    private readonly kaelMaterialService: KaelMaterialService,
  ) {}

  @Get('components')
  async findAllBusinessComponent(): Promise<
    Service.Material.MaterialInfoForAnalysis[]
  > {
    return await this.materialService.findAllBusinessComponent()
  }

  @Get('components/business')
  async findAllComponentMaterialByBusiness(
    @Query('business') business: SOURCE_CODE_BUSINESS,
  ): Promise<
      PromiseAllFulfilledResult<Service.Material.MaterialWithLastPubRecord>
    > {
    return await this.materialService.findAllComponentMaterialByBusiness(
      business,
    )
  }

  @Post('/detail')
  async detail(
    // @Query() queries: QueryMaterialDetailParamsDTO,
    @Body() queries: Service.Material.DetailQueries,
  ): Promise<Service.Material.MaterialDetail> {
    return this.materialService.detail(queries)
  }

  @Post('/list')
  async list(
    @Body() queries: Service.Material.ListQueries,
  ): Promise<Service.Material.MaterialListResult> {
    return this.materialService.list(queries)
  }

  @Get('old/list')
  async findList(
    @Query() params: Service.Material.ListParams,
  ): Promise<Service.Material.ListQueryResult> {
    return await this.materialService.oldList({
      ...params,
      page: +params.page || 1,
      pageSize: +params.pageSize || 10,
    })
  }

  @Get('kael/base-info/namespace')
  async searchMaterialBaseInfoByNamespace(
    @Query('namespace') namespace: string,
  ): Promise<Service.Material.MaterialBaseInfo | null> {
    return this.kaelMaterialService.searchMaterialBaseInfoByNamespace(
      namespace,
    )
  }

  @Post('transform/tags')
  async transformTags(
    @Body('tags') tags: GeneralMaterialSchema.Tags,
  ): Promise<Service.Material.MaterialDetail['tags']> {
    return this.materialService.transformTags(tags)
  }
}
