import { forwardRef, Module } from '@nestjs/common'
import { MaterialConvertController } from './material-convert.controller'
import { MaterialConvertService } from './material-convert.service'
import { MaterialModule } from '../material.module'
import { MaterialConvertScriptModule } from '../material-convert-script/material-convert-script.module'
import { MaterialPublishModule } from '../material-publish/material-publish.module'
import { MaterialTagRelationModule } from '../material-tag-relation/material-tag-relation.module'
import { MaterialMetaRelationModule } from '../material-meta-relation/material-meta-relation.module'
import { UserModule } from '@/modules/user/user.module'

@Module({
  imports: [
    forwardRef(() => MaterialModule),
    forwardRef(() => MaterialPublishModule),
    forwardRef(() => MaterialTagRelationModule),
    MaterialMetaRelationModule,
    MaterialConvertScriptModule,
    UserModule,
  ],
  controllers: [MaterialConvertController],
  providers: [MaterialConvertService],
  exports: [MaterialConvertService],
})
export class MaterialConvertModule {}
