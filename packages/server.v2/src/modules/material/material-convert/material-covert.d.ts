declare namespace Service.Material.Convert {
  export type ConvertRecord = import('@prisma/client').material_convert
  export interface TriggerConvertParams {
    materialId: number
    version: string
    targetBusiness: import('@/shared').BUSINESS
    creatorId: number
  }

  export interface CreateConvertParams
    extends Omit<
      import('@prisma/client').Prisma.material_convertCreateArgs['data'],
      'id' | 'create_time' | 'status'
    > {}
}
