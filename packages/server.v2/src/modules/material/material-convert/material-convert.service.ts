import axios from 'axios'
import assert from 'assert'
import { Injectable } from '@nestjs/common'

import {
  ALL_BUSINESS,
  ASSET_DOWNLOAD_TYPE,
  BUSINESS,
  flattenComponentField,
  now,
  SOURCE_CODE_BUSINESS_NAME_MAP,
} from '@/shared'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { loggerInstance } from '@/tools/winston'
import { RECORD_STATUS } from '@/constants/status'
import { CONVERT_STATUS } from '@/shared'
import { convertCDNUrl2IdcUrl, uploadToCDN } from '@/tools/cdn'

import { MaterialConvertScriptService } from '../material-convert-script/material-convert-script.service'
import { MaterialPublishService } from '../material-publish/material-publish.service'
import { MaterialTagRelationService } from '../material-tag-relation/material-tag-relation.service'
import { MaterialMetaRelationService } from '../material-meta-relation/material-meta-relation.service'
import { UserService } from '@/modules/user/user.service'
import { MaterialService } from '../material.service'

@Injectable()
export class MaterialConvertService {
  private readonly prisma = PrismaClientInstance
  private readonly logger = loggerInstance

  constructor(
    private readonly materialService: MaterialService,
    private readonly materialPublishService: MaterialPublishService,
    private readonly materialTagRelationService: MaterialTagRelationService,
    private readonly materialConvertScriptService: MaterialConvertScriptService,
    private readonly materialMetaRelationService: MaterialMetaRelationService,
    private readonly userService: UserService,
  ) {}

  async triggerConvert(
    params: Service.Material.Convert.TriggerConvertParams,
  ): Promise<{
      id: number
      convertedMaterialNamespace: string
    }> {
    const { materialId, version, targetBusiness, creatorId } = params

    const [materialRawRecord, materialPublishRecord] = await Promise.all([
      this.materialService.findRawRecordById(materialId),
      this.materialPublishService.findOnePublishByMaterialInfo({
        materialId,
        version,
      }),
    ])

    assert(
      materialPublishRecord,
      `物料（id: ${materialId}）或指定版本（${version}）不存在`,
    )
    assert(
      materialRawRecord.business !== targetBusiness,
      `物料（id: ${materialId}）已是目标业务（${targetBusiness}）`,
    )
    assert(
      materialPublishRecord.schema,
      `物料（id: ${materialId}）当前版本（${version}）的 schema 不存在`,
    )
    assert(
      materialPublishRecord.schema.assetDownloadType
      !== ASSET_DOWNLOAD_TYPE.NPM,
      `物料（id: ${materialId}）当前版本（${version}）的资源产物为 NPM 类型的物料暂不能进行转换`,
    )
    assert(
      materialPublishRecord.schema.assetDownloadUrl,
      `物料（id: ${materialId}）当前版本（${version}）的资源产物（assetDownloadUrl）不存在`,
    )

    const user = await this.userService.findById(creatorId)
    assert(user, `创建者（id: ${creatorId}）不存在`)

    const convertScript
      = await this.materialConvertScriptService.findTargetBusinessConvertScript(
        targetBusiness,
      )
    assert(convertScript, `目标业务（${targetBusiness}）的转换脚本不存在`)

    const { id: convertId } = await this.createConvert({
      material_id: materialId,
      version,
      business: targetBusiness,
      creator_id: creatorId,
      script: convertScript,
      result: CONVERT_STATUS.LOADING,
    })

    const scriptContent = await axios
      .get(convertCDNUrl2IdcUrl(convertScript))
      .then(res => res.data)

    let newMaterialNamespace = ''

    const materialTags = (
      await this.materialTagRelationService.findMaterialTags(materialId)
    ).map(item => ({
      id: item.id,
      title: item.title,
    }))

    const materialMeta = (
      await this.materialMetaRelationService.findMaterialMetaRelations(
        materialId,
      )
    ).map((meta) => {
      return {
        id: meta.id,
        title: meta.title,
        key: meta.value,
        values: meta.items.map(metaValue => ({
          id: metaValue.id,
          title: metaValue.title,
          key: metaValue.value,
        })),
      }
    })

    await eval(scriptContent)(
      {
        ...materialRawRecord,
        ...materialPublishRecord,
        platform: materialRawRecord.platform?.split(','),
        schema: materialPublishRecord.schema,
        content: materialPublishRecord.content,
        tags: materialTags,
        extra_metas: materialMeta,
      },
      {
        business: targetBusiness,
        businessTitle: SOURCE_CODE_BUSINESS_NAME_MAP[targetBusiness],
        creatorId,
        creatorName: user.user_name,
        axios,
        convertCDNUrl2IdcUrl,
      },
    )
      .then(async (bundle) => {
        if ('namespace' in bundle) {
          newMaterialNamespace = bundle.namespace
        }
        const url = await uploadToCDN({
          dir: `/convert/${convertId}`,
          filename: `${materialRawRecord.namespace}-convert.json`,
          content: JSON.stringify(bundle, null, 2),
        })
        return this.setConvertSuccess(convertId, url)
      })
      .catch(async (error) => {
        return this.setConvertFailed(convertId, error.message)
      })

    return { id: convertId, convertedMaterialNamespace: newMaterialNamespace }
  }

  async triggerAutoConvert(
    materialId: number,
    version: string,
    defaultConvertBusiness?: BUSINESS[],
  ): Promise<void> {
    const currentMaterialDetail = await this.materialService.detail({
      id: materialId,
      version,
    })

    assert(
      currentMaterialDetail,
      `物料（id: ${materialId}）或指定版本（${version}）不存在`,
    )
    const convertedMaterial
      = await this.materialService.findConvertedMaterialByOriginMaterialId(
        materialId,
      )

    const targetBusinesses: BUSINESS[] = Array.from(
      new Set(
        [
          ...defaultConvertBusiness,
          ...convertedMaterial.map(item => item.business),
          ...(currentMaterialDetail.__meta.autoConvert ?? []),
        ].filter(item => ALL_BUSINESS.includes(item as BUSINESS)),
      ),
    ) as BUSINESS[]

    await Promise.allSettled(
      targetBusinesses.map(business =>
        this.triggerConvert({
          materialId,
          version,
          targetBusiness: business,
          creatorId: currentMaterialDetail.creator_id,
        }),
      ),
    )
  }

  async triggerAutoConvertForComLib(
    materialId: number,
    version: string,
  ): Promise<void> {
    const currentMaterialDetail = await this.materialService.detail({
      id: materialId,
      version,
    })

    assert(
      currentMaterialDetail,
      `物料库（id: ${materialId}）或指定版本（${version}）不存在`,
    )

    const {
      currentVersion: { schema },
    } = currentMaterialDetail
    const components = flattenComponentField(schema.components)
    await Promise.allSettled(
      components.map(async (component) => {
        const materialDetail = await this.materialService.detail({
          namespace: component.namespace,
          version: component.version,
        })
        if (!materialDetail) {
          this.logger.warn(
            `组件库（${currentMaterialDetail.title}/${currentMaterialDetail.namespace}）自动转换：组件库中组件 ${component.namespace} 对应版本 ${component.version} 不存在`,
          )
        }
        else {
          await this.triggerAutoConvert(
            materialDetail.id,
            materialDetail.version,
            currentMaterialDetail.__meta.autoConvert as BUSINESS[],
          )
        }
      }),
    )
  }

  async createConvert(
    params: Service.Material.Convert.CreateConvertParams,
  ): Promise<Service.Material.Convert.ConvertRecord> {
    return await this.prisma.material_convert.create({
      data: {
        id: genPrimaryIndex(),
        ...params,
        create_time: now(),
        result: CONVERT_STATUS.LOADING,
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async setConvertSuccess(
    convertId: number,
    bundle: string,
  ): Promise<Service.Material.Convert.ConvertRecord> {
    return await this.prisma.material_convert.update({
      where: { id: convertId },
      data: {
        bundle,
        result: CONVERT_STATUS.SUCCESS,
      },
    })
  }

  async setConvertFailed(
    convertId: number,
    reason: string,
  ): Promise<Service.Material.Convert.ConvertRecord> {
    return await this.prisma.material_convert.update({
      where: { id: convertId },
      data: {
        bundle: '',
        result: CONVERT_STATUS.FAILED,
        reason,
      },
    })
  }
}
