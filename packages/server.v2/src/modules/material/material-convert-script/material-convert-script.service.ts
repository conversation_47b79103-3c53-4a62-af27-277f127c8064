import { RECORD_STATUS } from '@/constants/status'
import { BUSINESS } from '@/shared'
import { PrismaClientInstance } from '@/tools/prisma'
import { Injectable } from '@nestjs/common'

@Injectable()
export class MaterialConvertScriptService {
  private readonly prisma = PrismaClientInstance

  async findTargetBusinessConvertScript(business: BUSINESS): Promise<string | null> {
    const convertScript = await this.prisma.material_convert_script.findFirst({
      where: {
        business,
        status: RECORD_STATUS.EFFECT,
      },
    })
    return convertScript?.script
  }
}
