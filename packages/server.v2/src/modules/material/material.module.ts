import { Module } from '@nestjs/common'

import { MaterialController } from './material.controller'
import { MaterialService } from './material.service'
import { MaterialPublishModule } from './material-publish/material-publish.module'
import { KaelMaterialService } from './kael.material.service'
import { UserModule } from '../user/user.module'
import { FangzhouMaterialService } from './fangzhou.material.service'
import { MaterialTagModule } from './material-tag/material-tag.module'
import { MaterialMetaModule } from './material-meta/material-meta.module'
import { ForwardModule } from '../forward/forward.module'

@Module({
  imports: [
    MaterialPublishModule,
    UserModule,
    MaterialTagModule,
    MaterialMetaModule,
    ForwardModule,
  ],
  exports: [
    MaterialService,
    KaelMaterialService,
    FangzhouMaterialService,
  ],
  controllers: [MaterialController],
  providers: [
    MaterialService,
    KaelMaterialService,
    FangzhouMaterialService,
  ],
})
export class MaterialModule {}
