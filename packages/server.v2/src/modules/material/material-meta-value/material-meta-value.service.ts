import { Injectable } from '@nestjs/common'
import { PrismaClientInstance } from '@/tools/prisma'
import { RECORD_STATUS } from '@/constants/status'

@Injectable()
export class MaterialMetaValueService {
  private readonly prisma = PrismaClientInstance

  findListByIds(ids: number[]): Promise<Service.Material.MetaValue.MetaValueRecord[]> {
    return this.prisma.material_meta_value.findMany({
      where: {
        id: { in: ids },
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  findListByMetaId(metaId: number): Promise<Service.Material.MetaValue.MetaValueRecord[]> {
    return this.prisma.material_meta_value.findMany({
      where: {
        meta_id: metaId,
        status: RECORD_STATUS.EFFECT,
      },
    })
  }
}
