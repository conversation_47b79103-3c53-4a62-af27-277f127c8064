import { <PERSON>, Post } from '@nestjs/common'
import { MaterialIntegralService } from './material-integral.service'
import { Cron } from '@nestjs/schedule'
import { isMainService } from '@/main'

@Controller('material/integral')
export class MaterialIntegralController {
  constructor(
    private readonly materialIntegralService: MaterialIntegralService,
  ) {}

  @Cron('0 0 4 * * *')
  @Post('refresh-material-integral')
  async refreshMaterialIntegral(): Promise<number> {
    if (isMainService()) {
      return this.materialIntegralService.refreshMaterialIntegral()
    }
  }
}
