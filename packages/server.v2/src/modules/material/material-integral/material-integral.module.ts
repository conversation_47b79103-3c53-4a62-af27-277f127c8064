import { Module } from '@nestjs/common'
import { MaterialIntegralService } from './material-integral.service'
import { MaterialIntegralController } from './material-integral.controller'
import { MaterialReferModule } from '../material-refer/material-refer.module'
import { ScanInfoModule } from '@/modules/analysis/scan-info/scan-info.module'
import { SharedModule } from '@/modules/shared/shared.module'

@Module({
  imports: [MaterialReferModule, ScanInfoModule, SharedModule],
  providers: [MaterialIntegralService],
  controllers: [MaterialIntegralController],
  exports: [MaterialIntegralService],
})
export class MaterialIntegralModule {}
