import { Prisma } from '@prisma/client'
import { Injectable } from '@nestjs/common'

import { now } from '@/shared'
import { RECORD_STATUS } from '@/constants/status'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'

import { StatisticsMaterialReferService } from '../material-refer/statistics.material-refer.service'
import { ScanInfoService } from '@/modules/analysis/scan-info/scan-info.service'
import { MaterialPointsStatisticsCacheService } from '@/modules/shared/material-points-statistics-cache.service'

export type IntegralTypes =
  (typeof MaterialIntegralService.INTEGRAL_TYPE)[keyof typeof MaterialIntegralService.INTEGRAL_TYPE]

@Injectable()
export class MaterialIntegralService {
  private readonly prisma = PrismaClientInstance

  constructor(
    private readonly scanInfoService: ScanInfoService,
    private readonly statistMaterialReferService: StatisticsMaterialReferService,
    private readonly materialPointsStatisticsCacheService: MaterialPointsStatisticsCacheService,
  ) {}

  static readonly INTEGRAL_TYPE = {
    REFER_COUNT: 'REFER_COUNT',
    MASS_SCORE: 'MASS_SCORE',
  } as const

  async createMaterialIntegral(
    materialId: number,
    integralType: IntegralTypes,
    integral: number,
  ): Promise<Service.Material.Integral.MaterialIntegralRecord> {
    await this.clearMaterialIntegral(materialId, integralType)
    return this.prisma.material_integral.create({
      data: {
        id: genPrimaryIndex(),
        material_id: materialId,
        integral_type: integralType,
        integral,
        create_time: now(),
        update_time: now(),
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async batchCreateMaterialIntegral(
    integralType: IntegralTypes,
    params: {
      materialId: number
      integral: number
    }[],
  ): Promise<Prisma.BatchPayload> {
    await this.batchClearMaterialIntegral(
      params.map(item => item.materialId),
      integralType,
    )
    const time = now()
    return this.prisma.material_integral.createMany({
      data: params.map(item => ({
        id: genPrimaryIndex(),
        material_id: item.materialId,
        integral_type: integralType,
        integral: item.integral,
        create_time: time,
        update_time: time,
        status: RECORD_STATUS.EFFECT,
      })),
    })
  }

  async findMaterialIntegral(materialId: number): Promise<Service.Material.Integral.MaterialIntegralRecord[]> {
    return this.prisma.material_integral.findMany({
      where: {
        material_id: materialId,
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async clearMaterialIntegral(
    materialId: number,
    integralType?: IntegralTypes,
  ): Promise<Prisma.BatchPayload> {
    return this.prisma.material_integral.updateMany({
      where: {
        material_id: materialId,
        status: RECORD_STATUS.EFFECT,
        integral_type: integralType,
      },
      data: {
        status: RECORD_STATUS.DELETE,
        update_time: now(),
      },
    })
  }

  async batchClearMaterialIntegral(
    materialIds: number[],
    integralType?: IntegralTypes,
  ): Promise<Prisma.BatchPayload> {
    return this.prisma.material_integral.updateMany({
      where: {
        material_id: {
          in: materialIds,
        },
        integral_type: integralType,
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
        update_time: now(),
      },
    })
  }

  async refreshMaterialIntegral(): Promise<number> {
    const lowCodeResult = await this.refreshLowCodeMaterialIntegralDaily()
    const sourceCodeResult
      = await this.refreshSourceCodeMaterialIntegralDaily()
    return lowCodeResult.count + sourceCodeResult.count
  }

  async refreshLowCodeMaterialIntegralDaily(): Promise<Prisma.BatchPayload> {
    await this.prisma.material_integral.updateMany({
      where: {
        integral_type: MaterialIntegralService.INTEGRAL_TYPE.REFER_COUNT,
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })

    const referCounts
      = await this.statistMaterialReferService.dailyReferStatistics()

    return this.prisma.material_integral.createMany({
      data: referCounts.map(item => ({
        id: genPrimaryIndex(),
        material_id: item.materialId,
        integral_type: MaterialIntegralService.INTEGRAL_TYPE.REFER_COUNT,
        integral: item.referCount,
        create_time: now(),
        update_time: now(),
        status: RECORD_STATUS.EFFECT,
      })),
    })
  }

  async refreshSourceCodeMaterialIntegralDaily(): Promise<Prisma.BatchPayload> {
    const scan = await this.scanInfoService.findNewestScanInfoByType()
    if (!scan) return
    const report = await this.materialPointsStatisticsCacheService.getCache(
      scan.id,
    )

    const createIntegralParams: {
      materialId: number
      integral: number
    }[] = []
    for (const library of report.usage) {
      for (const material of library.materials) {
        createIntegralParams.push({
          materialId: material.materialId,
          integral: material.massScore,
        })
      }
    }

    return this.batchCreateMaterialIntegral(
      MaterialIntegralService.INTEGRAL_TYPE.MASS_SCORE,
      createIntegralParams,
    )
  }
}
