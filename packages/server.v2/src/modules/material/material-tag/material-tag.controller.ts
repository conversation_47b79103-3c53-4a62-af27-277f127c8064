import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { MaterialTagService } from './material-tag.service'
import { SearchTagListDTO } from './dto/search-tag-list.dto'
import { CreateTagDTO } from './dto/create-tag.dto'
import { DeleteTagDTO } from './dto/delete-tag.dto'
import { EnableDTO } from './dto/enable-tag.dto'
import { UpdateTagDTO } from './dto/update-tag.dto'

@Controller('material/tag')
export class MaterialTagController {
  constructor(private readonly materialTagService: MaterialTagService) {}

  @Get('list')
  async list(
    @Query() params: SearchTagListDTO,
  ): Promise<Service.Material.Tag.TagRecord[]> {
    return this.materialTagService.findAll(params)
  }

  @Post('create')
  async create(
    @Body() params: CreateTagDTO,
  ): Promise<Service.Material.Tag.TagRecord> {
    return this.materialTagService.create(params)
  }

  @Post('delete')
  async delete(
    @Body() { id }: DeleteTagDTO,
  ): Promise<Service.Material.Tag.TagRecord> {
    return this.materialTagService.delete(id)
  }

  @Post('enable')
  async enable(
    @Body() body: EnableDTO,
  ): Promise<Service.Material.Tag.TagRecord> {
    return this.materialTagService.enable(body)
  }

  @Post('update')
  async update(
    @Body() params: UpdateTagDTO,
  ): Promise<Service.Material.Tag.TagRecord> {
    return this.materialTagService.update(params)
  }
}
