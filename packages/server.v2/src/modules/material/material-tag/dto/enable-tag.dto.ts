import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsNumber } from 'class-validator'

import { RECORD_STATUS } from '@/constants/status'

export class EnableDTO implements Service.Material.Tag.EnableParams {
  @ApiProperty({
    description: '分类id',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  id: number

  @ApiProperty({
    description: '目标状态',
    example: 1,
  })
  @IsEnum(RECORD_STATUS)
  @IsNotEmpty()
  target_status: number
}
