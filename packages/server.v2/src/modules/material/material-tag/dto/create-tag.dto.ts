import { <PERSON><PERSON>otE<PERSON>y, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator'
import { Type } from 'class-transformer'
import { ApiProperty } from '@nestjs/swagger'

export class CreateTagDTO implements Service.Material.Tag.CreateParams {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Type(() => String)
  readonly title: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Type(() => String)
  readonly value: string

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  readonly order: number

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  readonly creator_id: number
}
