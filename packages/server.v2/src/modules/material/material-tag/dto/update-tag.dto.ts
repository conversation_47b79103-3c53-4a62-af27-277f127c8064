import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsNotEmpty, IsNumber, IsString } from 'class-validator'

export class UpdateTagDTO implements Service.Material.Tag.UpdateParams {
  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  id: number

  @ApiProperty()
  @IsString()
  @Type(() => String)
  title?: string

  @ApiProperty()
  @IsString()
  @Type(() => String)
  value?: string

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  order?: number
}
