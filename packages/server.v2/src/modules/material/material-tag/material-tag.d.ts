declare namespace Service.Material.Tag {
  export type TagRecord = import('@prisma/client').material_tag

  export interface SearchParams {
    title?: string
    status?: import('@/constants/status').RECORD_STATUS
  }

  export interface CreateParams
    extends Required<Omit<TagRecord, 'id' | 'create_time' | 'status'>> {}

  export interface UpdateParams
    extends Required<Pick<TagRecord, 'id'>>,
    Partial<Pick<TagRecord, 'title' | 'value' | 'order'>> {}

  export interface EnableParams {
    id: number
    target_status: import('@/constants/status').RECORD_STATUS
  }
}
