import { RECORD_STATUS } from '@/constants/status'
import { now } from '@/shared'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { Injectable } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { isNumber } from 'lodash'

@Injectable()
export class MaterialTagService {
  private readonly prisma = PrismaClientInstance

  findAll(
    params: Service.Material.Tag.SearchParams = {},
  ): Promise<Service.Material.Tag.TagRecord[]> {
    const conditions: Prisma.material_tagWhereInput = {}
    if ('title' in params && params.title) {
      conditions.title = {
        contains: params.title,
      }
    }
    if ('status' in params && isNumber(params.status)) {
      conditions.status = params.status
    }
    return this.prisma.material_tag.findMany({
      where: conditions,
      orderBy: {
        order: 'asc',
      },
    })
  }

  findListByValues(
    values: string[],
  ): Promise<Service.Material.Tag.TagRecord[]> {
    return this.prisma.material_tag.findMany({
      where: {
        value: {
          in: values,
        },
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  findTagByValue(value: string): Promise<Service.Material.Tag.TagRecord> {
    return this.prisma.material_tag.findFirst({
      where: {
        value,
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  findListByIds(ids: number[]): Promise<Service.Material.Tag.TagRecord[]> {
    return this.prisma.material_tag.findMany({
      where: {
        id: { in: ids },
      },
    })
  }

  create(
    params: Service.Material.Tag.CreateParams,
  ): Promise<Service.Material.Tag.TagRecord> {
    return this.prisma.material_tag.create({
      data: {
        ...params,
        id: genPrimaryIndex(),
        status: RECORD_STATUS.EFFECT,
        create_time: now(),
      },
    })
  }

  update(
    params: Service.Material.Tag.UpdateParams,
  ): Promise<Service.Material.Tag.TagRecord> {
    return this.prisma.material_tag.update({
      where: {
        id: params.id,
      },
      data: {
        ...params,
      },
    })
  }

  enable(
    params: Service.Material.Tag.EnableParams,
  ): Promise<Service.Material.Tag.TagRecord> {
    return this.prisma.material_tag.update({
      where: {
        id: params.id,
      },
      data: {
        status: params.target_status,
      },
    })
  }

  delete(id: number): Promise<Service.Material.Tag.TagRecord> {
    return this.prisma.material_tag.update({
      where: {
        id,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }
}
