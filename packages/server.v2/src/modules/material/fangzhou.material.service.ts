import { Injectable } from '@nestjs/common'

import { RECORD_STATUS } from '@/constants/status'
import { PrismaClientInstance } from '@/tools/prisma'
import { WEBSITE_DOMAIN } from '@/constants/domain'
import { UserService } from '../user/user.service'
import { LOW_CODE_BUSINESS } from '@/shared'
import { transformBigIntToNumber } from '@/tools/object'

@Injectable()
export class FangzhouMaterialService {
  private readonly prisma = PrismaClientInstance

  constructor(private readonly userService: UserService) {}

  async searchMaterialBaseInfoByNamespace(namespace: string): Promise<Service.Material.MaterialBaseInfo | null> {
    const fangzhouNamespace = `${namespace}(%fangzhou)%`

    let material = (
      await this.prisma.$queryRaw<Service.Material.MaterialRecord>`
      select * from material
      where
        (namespace like ${fangzhouNamespace} or namespace = ${namespace})
        and business = ${LOW_CODE_BUSINESS.FANGZHOU}
        and status = ${RECORD_STATUS.EFFECT}
    `
    )?.[0]

    if (!material) {
      return null
    }
    else {
      material = transformBigIntToNumber(material)
      const creator = await this.userService.findById(material.creator_id)
      return {
        id: material.id,
        name: material.title,
        newestVersion: material.version,
        previewURL: `${WEBSITE_DOMAIN}/detail?material_id=${material.id}`,
        createTime: material.create_time,
        updateTime: material.update_time,
        creator: {
          name: creator?.name,
          username: creator?.user_name,
        },
      }
    }
  }
}
