import axios from 'axios'
import assert from 'assert'
import { Prisma } from '@prisma/client'
import { Injectable } from '@nestjs/common'
import { get, isEmpty, isString } from 'lodash'

import {
  ALL_BUSINESS,
  ALL_SOURCE_CODE_BUSINESS,
  PLATFORMS,
  BUSINESS,
  BUSINESS_KEY_MAP,
  BUSINESS_NAME,
  SOURCE_CODE_BUSINESS,
  isArray,
  isBusiness,
  isNumber,
  isObject,
  isSourceCodeBusiness,
  now,
} from '@/shared'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { loggerInstance } from '@/tools/winston'
import {
  limitInvokePromiseAllFulfilled,
  PromiseAllFulfilled,
  PromiseAllFulfilledResult,
} from '@/tools/Promise'
import { logStringify, safeParseMaybeNull } from '@/tools/json'
import { reasonableGitUrlParse } from '@/tools/git'
import {
  ALL_MATERIAL_TYPE,
  ALL_PLATFORM,
  MATERIAL_TYPE,
  PLATFORM_NAMES,
} from '@/constants/material'
import { RECORD_STATUS } from '@/constants/status'
import { IS_DEV } from '@/constants/envs'

import { MaterialPublishService } from './material-publish/material-publish.service'
import { MaterialTagService } from './material-tag/material-tag.service'
import { MaterialMetaService } from './material-meta/material-meta.service'
import { UserService } from '../user/user.service'
import { ForwardLingZhuService } from '../forward/lingzhu.service'

@Injectable()
export class MaterialService {
  private readonly prisma = PrismaClientInstance
  private readonly logger = loggerInstance

  constructor(
    private readonly materialPubService: MaterialPublishService,
    private readonly materialTagService: MaterialTagService,
    private readonly materialMetaService: MaterialMetaService,
    private readonly userService: UserService,
    private readonly forwardLingZhuService: ForwardLingZhuService,
  ) {}

  serialize(
    record: Service.Material.MaterialRecord,
  ): Service.Material.SerializeMaterialRecord {
    return {
      ...record,
      meta: safeParseMaybeNull(
        record.meta,
      ) as Service.Material.SerializeMaterialMeta,
    }
  }

  async createMaterialBaseInfo(
    params: Service.Material.CreateMaterialParams,
  ): Promise<Service.Material.MaterialRecord> {
    const { namespace, version, title, type, business, platform, creator_id }
      = params
    assert(namespace, 'namespace is required')
    assert(version, 'version is required')
    assert(title, 'title is required')
    assert(
      ALL_MATERIAL_TYPE.includes(type as MATERIAL_TYPE),
      'type is required, and must be one of the following: '
      + ALL_MATERIAL_TYPE.join(', '),
    )
    assert(
      ALL_BUSINESS.includes(business as BUSINESS),
      'business is required, and must be one of the following: '
      + ALL_BUSINESS.join(', '),
    )
    assert(
      ALL_PLATFORM.includes(platform as PLATFORMS),
      'platform is required, and must be one of the following: '
      + ALL_PLATFORM.join(', '),
    )
    assert(creator_id, 'creator_id is required')

    const material = await this.prisma.material.create({
      data: {
        id: genPrimaryIndex(),
        namespace,
        version,
        title,
        description: params.description ?? '',
        type,
        business,
        domain: params.domain ?? '0',
        git_url: params.git_url ?? '',
        platform,
        meta: params.meta ?? '',
        creator_id,
        create_time: now(),
        updater_id: creator_id,
        update_time: now(),
        status: RECORD_STATUS.EFFECT,
      },
    })
    return material
  }

  async updateMaterialBaseInfo(
    params: Partial<Service.Material.UpdateMaterialRecordParams>,
  ): Promise<Service.Material.MaterialRecord> {
    const { id, updater_id, ...rest } = params
    assert(id, 'id is required')
    assert(updater_id, 'updater_name is required')
    return this.prisma.material.update({
      where: { id },
      data: {
        ...rest,
        updater_id,
        update_time: now(),
      },
    })
  }

  async existMaterialByNamespace(namespace: string): Promise<boolean> {
    const count = await this.prisma.material.count({
      where: { namespace, status: RECORD_STATUS.EFFECT },
    })
    return count > 0
  }

  async existMaterialByNamespaceAndVersion(
    namespace: string,
    version: string,
  ): Promise<boolean> {
    const material = await this.findOneByNamespace(namespace).catch(() => null)
    if (!material) {
      return false
    }
    return this.materialPubService.existTargetMaterialVersion(
      material.id,
      version,
    )
  }

  async findOneByNamespace(
    namespace: string,
  ): Promise<Service.Material.SerializeMaterialRecord> | never {
    const material = await this.prisma.material.findFirst({
      where: {
        namespace,
        status: RECORD_STATUS.EFFECT,
      },
    })
    if (!material) {
      throw new Error(`cannot find material: ${namespace}`)
    }
    return this.serialize(material)
  }

  async findRawRecordById(
    id: number,
  ): Promise<Service.Material.MaterialRecord> {
    return this.prisma.material.findUnique({
      where: { id },
    })
  }

  async findConvertedMaterialByOriginMaterialId(
    originMaterialId: number,
  ): Promise<Service.Material.SerializeMaterialRecord[]> {
    const convertedMaterials = await this.prisma.material.findMany({
      where: {
        meta: {
          contains: `"ref_source":"convert","ref_material_id":${originMaterialId}`,
        },
        status: RECORD_STATUS.EFFECT,
      },
    })
    return convertedMaterials.map(this.serialize)
  }

  async oldList(
    params: Service.Material.ListParams,
  ): Promise<Service.Material.ListQueryResult> {
    const { page = 1, pageSize = 10, query, business } = params
    const where: Prisma.materialWhereInput = {
      namespace: {
        contains: query,
      },
      status: RECORD_STATUS.EFFECT,
    }

    if (business) {
      console.log(business)
      where.business = {
        in: [business],
      }
    }

    const totalCount = await this.prisma.material.count({ where })
    const totalPages = Math.ceil(totalCount / pageSize)

    const records = await this.prisma.material.findMany({
      where,
      orderBy: { create_time: 'desc' },
      skip: (page - 1) * pageSize,
      take: +pageSize,
    })

    return {
      data: records.map(this.serialize),
      pagination: {
        currentPage: page,
        pageSize,
        totalCount,
        totalPages,
      },
    }
  }

  async list(
    queries: Service.Material.ListQueries,
  ): Promise<Service.Material.MaterialListResult> {
    const {
      keyword = '',
      namespace,
      type,
      tags,
      pageNum = 1,
      pageSize = 10,
    } = queries
    assert(pageNum > 0, 'pageNum must be greater than 0')
    const skip = (pageNum - 1) * pageSize
    const take = pageSize
    const leftJoins: Prisma.Sql[] = []
    const conditions: Prisma.Sql[] = [
      Prisma.sql`material.status = ${RECORD_STATUS.EFFECT}`,
    ]

    // ======================== 关键词搜索， ========================
    if (keyword.trim()) {
      const trimmedKeyword = keyword.trim()
      conditions.push(
        Prisma.sql`(material.title LIKE ${`%${trimmedKeyword}%`} OR material.namespace LIKE ${`%${trimmedKeyword}%`} OR material.description LIKE ${`%${trimmedKeyword}%`})`,
      )
    }

    // ======================== namespace ========================
    if (namespace) {
      assert(isString(namespace), 'namespace must be string')
      conditions.push(Prisma.sql`material.namespace LIKE ${`%${namespace}%`}`)
    }
    // ======================== type ========================
    if (type) {
      assert(
        [MATERIAL_TYPE.COMPONENT, MATERIAL_TYPE.COM_LIB].includes(type),
        'type must be \'component\' or \'com_lib\'',
      )
      conditions.push(Prisma.sql`material.type = ${type}`)
    }
    // ======================== tags ========================
    if (isObject(tags)) {
      // ======================== tags.domain ========================
      if ('domain' in tags && tags.domain) {
        assert(
          isNumber(tags.domain) || isString(tags.domain),
          'tags.domain must be ',
        )
        if (tags.domain > 0) {
          conditions.push(
            Prisma.sql`FIND_IN_SET(${tags.domain.toString()}, material.domains)`,
          )
        }
      }
      // ======================== tags.business ========================
      if ('business' in tags && tags.business) {
        assert(
          isBusiness(tags.business),
          `business must be ${ALL_BUSINESS.map(bu => `'${bu}'`).join('or')}`,
        )
        conditions.push(Prisma.sql`material.business = ${tags.business}`)
      }
      // ======================== tags.category ========================
      if ('category' in tags && tags.category) {
        const targetCategory = await this.materialTagService.findTagByValue(
          tags.category,
        )
        assert(targetCategory, `cannot find category '${tags.category}'`)
        leftJoins.push(
          Prisma.sql`LEFT JOIN material_tag_relation ON material.id = material_tag_relation.material_id AND material_tag_relation.status = ${RECORD_STATUS.EFFECT}`,
        )
        conditions.push(
          Prisma.sql`material_tag_relation.tag_id = ${targetCategory.id}`,
        )
      }
      // ======================== tags.platform ========================
      if ('platform' in tags && tags.platform) {
        assert(isString(tags.platform), 'tags.platform must be string')
        conditions.push(Prisma.sql`material.platform = ${tags.platform}`)
      }
      // ======================== tags.meta ========================
      if ('meta' in tags) {
        assert(isObject(tags.meta), 'tags.meta must be object')
        const selectedMaterialMetaIds: number[] = []
        for (const targetMetaKey in tags.meta) {
          const targetMetaValue = tags.meta[targetMetaKey]
          if (targetMetaKey && targetMetaValue) {
            const metaDetail
              = await this.materialMetaService.findMetaDetailByIdOrValue({
                value: targetMetaKey,
              })
            if (metaDetail) {
              const metaItem = metaDetail.items.find(
                item => item.value === targetMetaValue,
              )
              if (metaItem) {
                selectedMaterialMetaIds.push(metaItem.id)
              }
            }
          }
        }
        if (selectedMaterialMetaIds.length) {
          leftJoins.push(
            Prisma.sql`LEFT JOIN material_meta_value_relation ON material.id = material_meta_value_relation.material_id AND material_meta_value_relation.status = ${RECORD_STATUS.EFFECT}`,
          )
          conditions.push(
            Prisma.sql`material_meta_value_relation.meta_value_id in (${selectedMaterialMetaIds.join(
              ',',
            )})`,
          )
        }
      }
    }

    // 构建查询 SQL
    const whereClause = conditions.length
      ? Prisma.sql`WHERE ${Prisma.join(conditions, ' AND ')}`
      : Prisma.empty

    // 计算总数
    const [{ total: _bigIntTotal }] = await this.prisma.$queryRaw<
      [{ total: number }]
    >`SELECT COUNT(material.id) as total
        FROM material
        ${leftJoins.length ? Prisma.join(leftJoins) : Prisma.empty}
        ${whereClause}
      `

    const total = Number(_bigIntTotal)

    if (total === 0 || skip >= total) {
      return {
        list: [],
        total,
        pageNum,
        pageSize,
        totalPage: Math.ceil(total / pageSize),
        hasNext: false,
        hasPrev: total > 0,
      }
    }

    // 先查询符合条件的 ID，再关联其他数据
    const materialIds = (
      await this.prisma.$queryRaw<{ id: number }[]>`
          SELECT material.id, material.update_time
          FROM material
          ${leftJoins.length ? Prisma.join(leftJoins) : Prisma.empty}
          ${whereClause}
          ORDER BY material.update_time DESC
          LIMIT ${skip}, ${take}
        `
    ).map(({ id }) => Number(id))

    if (materialIds.length === 0) {
      return {
        list: [],
        total,
        pageNum,
        pageSize,
        totalPage: Math.ceil(total / pageSize),
        hasNext: false,
        hasPrev: total > 0,
      }
    }

    console.time('Execution Time')
    const materials = await limitInvokePromiseAllFulfilled(
      materialIds.map(
        materialId => async (): Promise<Service.Material.MaterialDetail> =>
          this.detail({
            id: materialId,
            options: {
              withVersions: queries.detailOptions?.withVersions,
              typeOfVersions: queries.detailOptions?.typeOfVersions,
            },
          }),
      ),
    )
    console.timeEnd('Execution Time')

    return {
      list: materials.result,
      total,
      pageNum,
      pageSize,
      totalPage: Math.ceil(total / pageSize),
      hasNext: skip + take < total,
      hasPrev: skip > 0,
      warnings: materials.errors.map(error => error.message),
    }
  }

  async findAllBusinessComponent(): Promise<
    Service.Material.MaterialInfoForAnalysis[]
  > {
    if (IS_DEV) {
      return (
        await axios.get(
          'https://w1.beckwai.com/kos/nlav12333/fangzhou/material-middleoffice/2024-08-22.json',
        )
      ).data as Service.Material.MaterialInfoForAnalysis[]
    }
    const records = await this.prisma.material.findMany({
      where: {
        business: {
          in: ALL_SOURCE_CODE_BUSINESS as unknown as string[],
        },
        type: MATERIAL_TYPE.COMPONENT,
        status: RECORD_STATUS.EFFECT,
      },
    })
    const { result: components } = await limitInvokePromiseAllFulfilled(
      records.map(
        record =>
          async (): Promise<Service.Material.MaterialWithLastPubRecord> => {
            const latest_pub
              = await this.materialPubService.findOnePublishByMaterialInfo({
                materialId: record.id,
                version: record.version,
              })
            return Object.freeze({
              ...this.serialize(record),
              latest_pub,
            })
          },
      ),
    )

    const result: Service.Material.MaterialInfoForAnalysis[] = []
    for (let i = 0; i < components.length; i++) {
      const {
        id: materialId,
        business,
        latest_pub: { schema },
      } = components[i]

      if (
        'packageName' in schema
        && 'componentName' in schema
        && 'componentBundleType' in schema
      ) {
        assert(isString(schema.packageName), 'packageName is required')
        assert(isString(schema.componentName), 'componentName is required')
        assert(isSourceCodeBusiness(business), 'business is required')
        assert(
          isString(schema.componentBundleType)
          && ['SLSC', 'SLMC'].includes(schema.componentBundleType),
          'componentBundleType is required',
        )
        const index = result.findIndex(
          item =>
            item.packageName === schema.packageName
            && item.business === business
            && item.componentBundleType === schema.componentBundleType,
        )
        if (index === -1) {
          result.push({
            materialId,
            packageName: schema.packageName,
            business,
            componentBundleType: schema.componentBundleType as 'SLSC' | 'SLMC',
            components: [schema.componentName],
            componentsWithId: [
              {
                id: materialId,
                name: schema.componentName,
              },
            ],
          })
        }
        else if (schema.componentBundleType === 'SLMC') {
          result[index].components.push(schema.componentName)
          result[index].componentsWithId.push({
            id: materialId,
            name: schema.componentName,
          })
        }
      }
    }
    return result
  }

  async findAllComponentMaterialByBusiness(
    business: SOURCE_CODE_BUSINESS,
  ): Promise<
      PromiseAllFulfilledResult<Service.Material.MaterialWithLastPubRecord>
    > {
    const records = await this.prisma.material.findMany({
      where: {
        business,
        type: MATERIAL_TYPE.COMPONENT,
        status: RECORD_STATUS.EFFECT,
      },
    })
    return await PromiseAllFulfilled(
      records.map(async (record) => {
        const latest_pub
          = await this.materialPubService.findOnePublishByMaterialInfo({
            materialId: record.id,
            version: record.version,
          })
        return Object.freeze({
          ...this.serialize(record),
          latest_pub,
        })
      }),
    )
  }

  async findAllMaterialProjectsByBusiness(
    business: SOURCE_CODE_BUSINESS,
  ): Promise<
      PromiseAllFulfilledResult<Service.Analysis.BusinessConfig.CoreBusinessProjectConfig>
    > {
    const { result } = await this.findAllComponentMaterialByBusiness(business)
    return PromiseAllFulfilled(
      result.map(async (item) => {
        const newestGitURL = item.latest_pub.schema.gitUrl || item.git_url
        assert(
          newestGitURL && typeof newestGitURL === 'string',
          `物料发布信息中缺少 git_url: ${logStringify({
            materialId: item.id,
            version: item.version,
          })}`,
        )
        const namespace = reasonableGitUrlParse(newestGitURL).full_name
        return {
          namespace,
          title: item.title,
          git_url: newestGitURL,
          router_entry: undefined,
          branch: undefined,
          black_list: undefined,
          pkg_filter: undefined,
        }
      }),
    )
  }

  async queryUserInfoByMaterialId(
    materialId: number,
  ): Promise<Pick<
    Service.User.UserRecord,
    'id' | 'name' | 'user_name' | 'department'
  > | null> {
    const material = await this.prisma.material.findUnique({
      where: {
        id: materialId,
      },
    })

    if (!material) {
      return null
    }
    return this.prisma.user.findFirst({
      select: {
        id: true,
        name: true,
        user_name: true,
        department: true,
      },
      where: {
        id: material.creator_id,
      },
    })
  }

  async detail(
    queries: Service.Material.DetailQueries,
  ): Promise<Service.Material.MaterialDetail> {
    assert(queries.id || queries.namespace, 'id or namespace is required')
    const { withVersions = false, typeOfVersions } = queries.options ?? {}
    const material = this.serialize(
      await this.prisma.material.findFirst({
        where: {
          id: queries.id,
          namespace: queries.namespace,
          status: RECORD_STATUS.EFFECT,
        },
      }),
    )
    if (!material) {
      throw new Error(
        `cannot find material: ${queries.id || queries.namespace}`,
      )
    }
    const version = queries.version || material.version
    const materialVersion = await this.materialPubService
      .findOnePublishByMaterialInfo({
        materialId: material.id,
        version,
      })
      .catch(() => {
        throw new Error(
          `cannot find material version: ${material.namespace}@${version}`,
        )
      })
    let creator: Service.User.UserRecord | null = null
    let updater: Service.User.UserRecord | null = null
    if (material.creator_id === materialVersion.creator_id) {
      creator = updater = await this.userService.findById(material.creator_id)
    }
    else {
      const [creatorRecord, updaterRecord] = await Promise.all([
        this.userService.findById(material.creator_id),
        this.userService.findById(materialVersion.creator_id),
      ])
      creator = creatorRecord
      updater = updaterRecord
    }

    /** @type {*} */
    const result: Service.Material.MaterialDetail = {
      id: material.id,
      namespace: material.namespace,
      version: materialVersion.version,
      business: material.business as BUSINESS,
      type: material.type as MATERIAL_TYPE,
      description: get(
        materialVersion,
        'schema.description',
        material.description,
      ),
      gitUrl: get(materialVersion, 'schema.gitUrl', material.git_url),
      title: get(
        materialVersion,
        'schema.componentChineseName',
        material.title,
      ),
      tags: await this.transformTags(
        get(materialVersion, 'schema.tags', {}) as GeneralMaterialSchema.Tags,
      ),
      currentVersion: await this.materialPubService.normalize(materialVersion),
      ...(withVersions
        ? {
          historyVersions: (
            await this.materialPubService.findVersionListByMaterialId(
              material.id,
              typeOfVersions,
            )
          ).result,
        }
        : {}),
      creator: creator?.user_name,
      creator_id: creator?.id,
      creator_name: creator?.name,
      create_time: material.create_time,
      updater: updater?.user_name,
      updater_id: updater?.id,
      updater_name: updater?.name,
      update_time: materialVersion.create_time,

      __meta: material.meta,
    }

    return result
  }

  async transformTags(
    tags: GeneralMaterialSchema.Tags,
  ): Promise<Service.Material.MaterialDetail['tags']> {
    const result: Service.Material.MaterialDetail['tags'] = {
      domain: [],
      platform: [],
      business: [],
      category: [],
      meta: [],
    }
    if (!tags || isEmpty(tags)) return result
    // ======================== domain ========================
    const domainValue = get(tags, 'domain', [0])
    if (isArray(domainValue) && domainValue.length > 0) {
      if (domainValue.length === 1 && +domainValue[0] === 0) {
        result.domain.push({
          id: 0,
          key: 0,
          label: '全部',
        })
      }
      else {
        let domainDirectory: Service.Forward.LingZhu.DomainDirectory[]
          = (await this.forwardLingZhuService.queryLingZhuDomainDirectory())
            ?.children ?? []
        const fullPath = domainValue.map((id) => {
          const domain = domainDirectory.find(item => item.value === id)
          domainDirectory = domain?.children ?? []
          return {
            id,
            key: id,
            label: domain?.label ?? '',
          }
        })
        result.domain.push(...fullPath)
      }
    }
    // ======================== platform ========================
    let platformValue = get(tags, 'platform', [])
    if (!isArray(platformValue)) platformValue = [platformValue]
    result.platform.push(
      ...platformValue.map(key => ({
        id: key,
        key: key,
        label: PLATFORM_NAMES[key as PLATFORMS],
      })),
    )

    // ======================== business ========================
    let businessValue = get(tags, 'business', [])
    if (!isArray(businessValue)) businessValue = [businessValue]
    result.business.push(
      ...businessValue.map(key => ({
        id: key,
        key: key,
        label: BUSINESS_NAME[BUSINESS_KEY_MAP[key]],
      })),
    )

    // ======================== categories ========================
    let categoryValue = get(tags, 'category', [])
    if (!isArray(categoryValue)) categoryValue = [categoryValue]
    const categories = await this.materialTagService.findAll()
    result.category.push(
      ...categoryValue.map(key => ({
        id: categories.find(item => item.value === key)?.id ?? -1,
        key: key,
        label: categories.find(item => item.value === key)?.title ?? '',
      })),
    )

    // ======================== meta ========================
    const metaValue = get(tags, 'meta', {})
    const metaVertex: Service.Material.TransformedTag[] = []
    for (const [metaKey, _metaValue] of Object.entries(metaValue)) {
      const metaValue = isArray(_metaValue) ? _metaValue : [_metaValue]
      const metaDetail
        = await this.materialMetaService.findMetaDetailByIdOrValue({
          value: metaKey,
        })
      if (!metaDetail) continue
      metaVertex.push({
        id: metaDetail.id,
        key: metaKey,
        label: metaDetail.title,
        children: metaValue.map((item) => {
          const currentTagOption = metaDetail.items.find(
            tag => tag.value === item,
          )
          return {
            id: currentTagOption?.id ?? -1,
            key: item as string,
            label: currentTagOption?.title ?? '',
          }
        }),
      })
    }
    result.meta.push(...metaVertex)

    return result
  }
}
