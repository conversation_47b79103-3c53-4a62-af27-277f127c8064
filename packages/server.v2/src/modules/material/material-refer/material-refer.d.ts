declare namespace Service.Material.Refer {
  export type Refer = import('@prisma/client').material_refer
  export type CreateParams = Omit<
    import('@prisma/client').Prisma.material_referCreateInput,
    'id' | 'status'
  >

  export interface ClearTargetPageReferParams {
    business: string
    page: string
    ref_page_version: string
  }

  export interface TianheAppPageReferUsage {
    appKey: string
    appName: string
    pages: {
      pageUrl: string
      pageName: string
      materials: {
        namespace: string
        count: number
      }[]
    }[]
  }

  export interface DailyMaterialUsageGrowth {
    time: number
    date: string
    value: number
  }
}
