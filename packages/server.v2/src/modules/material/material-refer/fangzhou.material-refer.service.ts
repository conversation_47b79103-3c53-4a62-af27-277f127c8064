import { RECORD_STATUS } from '@/constants/status'
import { LOW_CODE_BUSINESS } from '@/shared'
import { PrismaClientInstance } from '@/tools/prisma'
import { Injectable } from '@nestjs/common'
import { FangzhouMaterialService } from '../fangzhou.material.service'
import { limitInvokePromiseAllFulfilled } from '@/tools/Promise'

@Injectable()
export class FangZhouMaterialReferService {
  private readonly prisma = PrismaClientInstance

  constructor(
    private readonly fangzhouMaterialService: FangzhouMaterialService,
  ) {}

  static readonly FANGZHOU_INTERNAL_MATERIAL_NAMESPACE = [
    'mybricks.core-comlib.fn',
    'mybricks.core-comlib.var',
    'mybricks.core-comlib.type-change',
    'mybricks.core-comlib.connector',
    'mybricks.core-comlib.frame-input',
    'mybricks.core-comlib.frame-output',
    'mybricks.core-comlib.scenes',
    'mybricks.core-comlib.defined-com',
    'mybricks.core-comlib.module',
    'mybricks.core-comlib.group',
    'mybricks.core-comlib.selection',
  ]

  static fangzhouNamespaceTransformer(rawNamespace: string): string {
    return `@fangzhou/component-${rawNamespace.replace(
      /(\w*)[-\\.](\w*)/g,
      (_, $2, $3) => {
        return $2 + $3[0].toUpperCase() + $3.slice(1)
      },
    )}`
  }

  static readonly TRANSFORMED_FANGZHOU_INTERNAL_MATERIAL_NAMESPACE
    = FangZhouMaterialReferService.FANGZHOU_INTERNAL_MATERIAL_NAMESPACE.map(
      FangZhouMaterialReferService.fangzhouNamespaceTransformer,
    )

  async statisticsNewestMaterialReferCount(): Promise<
    { materialId: number, referCount: number }[]
  > {
    const newestReferRecords = await this.prisma.material_refer.groupBy({
      by: ['namespace'],
      where: {
        ref_business: LOW_CODE_BUSINESS.FANGZHOU,
        status: RECORD_STATUS.EFFECT,
      },
      _sum: {
        refer_count: true,
      },
    })

    return (
      await limitInvokePromiseAllFulfilled(
        newestReferRecords.map(item => async () => {
          const material
            = await this.fangzhouMaterialService.searchMaterialBaseInfoByNamespace(
              item.namespace,
            )
          if (!material) return null
          return {
            materialId: material.id,
            referCount: item._sum.refer_count,
          }
        }),
      )
    ).result.filter(item => item !== null)
  }
}
