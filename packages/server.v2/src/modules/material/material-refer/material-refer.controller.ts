import dayjs from 'dayjs'
import { ApiTags } from '@nestjs/swagger'
import { Body, Controller, Get, Post, Query } from '@nestjs/common'

import { DateParam } from '@/shared'

import { MaterialReferService } from './material-refer.service'
import { TianheMaterialReferService } from './tianhe.material-refer.service'
import { StatisticsMaterialReferService } from './statistics.material-refer.service'
import { CODE_TYPE } from '@/constants/code-type'
import { SourceCodeMaterialReferService } from './source-code.material-refer.service'

@ApiTags('material/refer')
@Controller('material/refer')
export class MaterialReferController {
  constructor(
    private readonly materialReferService: MaterialReferService,
    private readonly tianheMaterialReferService: TianheMaterialReferService,
    private readonly StatisticsMaterialReferService: StatisticsMaterialReferService,
    private readonly sourceCodeMaterialReferService: SourceCodeMaterialReferService,
  ) {}

  @Get('business/material-refer-count')
  async findMaterialReferCountGroupByRefBusiness(
    @Query('refBusiness') refBusiness: 'kael' | 'fangzhou',
  ): Promise<
      {
        namespace: string
        _sum: {
          refer_count: number
        }
      }[]
    > {
    return this.materialReferService.findMaterialReferCountGroupByRefBusiness(
      refBusiness,
    )
  }

  @Get('list')
  async findMaterialReferList(
    @Query('id') id: number,
    @Query('code_type') code_type?: CODE_TYPE,
    @Query('source_type') source_type?: 'namespace' | 'package',
    @Query('pageSize') pageSize?: number,
    @Query('pageNum') pageNum?: number,
  ): Promise<Service.PaginationResult<Service.Material.Refer.Refer>> {
    return this.materialReferService.findMaterialReferList({
      id,
      source_type,
      code_type,
      pageSize,
      pageNum,
    })
  }

  @Post('tianhe/materials-refer-count')
  async findTianheMaterialsReferCount(
    @Body('namespaces') namespaces: string[],
    @Body('appKeys') appKeys: string[],
    @Body('invert') invert: boolean,
    @Body('beforeTime') beforeTime: DateParam,
  ): Promise<
      {
        namespace: string
        count: number
      }[]
    > {
    return this.tianheMaterialReferService.findTianheMaterialRefers(
      namespaces,
      appKeys,
      invert ?? false,
      beforeTime ? dayjs(beforeTime).valueOf() : undefined,
    )
  }

  @Post('tianhe/material-refer-pages')
  async findTianheMaterialReferPages(
    @Body('namespace') namespace: string,
    @Body('appKeys') appKeys?: string[],
  ): Promise<Service.Material.Refer.Refer[]> {
    return this.tianheMaterialReferService.findTianheMaterialReferPages(
      namespace,
      appKeys ?? [],
    )
  }

  @Post('tianhe/app-page-refer-usage')
  async findTianheAppPageReferUsage(
    @Body('appKeys') appKeys: string[],
  ): Promise<Service.Material.Refer.TianheAppPageReferUsage[]> {
    return this.tianheMaterialReferService.findTianheAppsMaterialRefers(
      appKeys,
    )
  }

  @Post('tianhe/material-usage-growth')
  async computeMaterialUsageGrowth(
    @Body('namespaces') namespace: string[] = [],
    @Body('appKeys') appKeys: string[] = [],
  ): Promise<
      (Service.Material.Refer.DailyMaterialUsageGrowth & {
        detail: {
          page: string
          pageVersion: string
          namespace: string
          count: number
        }[]
      })[]
    > {
    return this.tianheMaterialReferService.computeTianheMaterialsUsageGrowth(
      namespace,
      appKeys,
    )
  }

  @Post('tianhe/material-use-rate-growth')
  async computeMaterialUseRateGrowth(
    @Body('namespaces') namespaces: string[] = [],
    @Body('appKeys') appKeys: string[] = [],
  ): Promise<Service.Material.Refer.DailyMaterialUsageGrowth[]> {
    return this.tianheMaterialReferService.computeTianheMaterialsUseRateGrowth(
      namespaces,
      appKeys,
    )
  }

  @Get('daily-material-refer-stats')
  async computeDailyMaterialReferStats(): Promise<
    {
      materialId: number
      referCount: number
    }[]
  > {
    return this.StatisticsMaterialReferService.dailyReferStatistics()
  }

  @Post('batch-create-material-refer-from-source-code')
  async batchCreateMaterialReferFromSourceCode(
    @Body() body: { scanId?: number },
  ): Promise<void> {
    return this.sourceCodeMaterialReferService.batchCreateMaterialReferFromSourceCodeRecords(
      body.scanId,
    )
  }
}
