import dayjs from 'dayjs'
import { Prisma } from '@prisma/client'
import { Injectable } from '@nestjs/common'

import { loggerInstance } from '@/tools/winston'
import { PrismaClientInstance } from '@/tools/prisma'
import { transformBigIntToNumber } from '@/tools/object'
import { limitInvokePromiseAllFulfilled } from '@/tools/Promise'
import { isArray, isObject, isString, LOW_CODE_BUSINESS, now } from '@/shared'
import { RECORD_STATUS } from '@/constants/status'

import { LowCodePageInfoService } from '@/modules/low-code/page-info/page-info.service'
import { MaterialReferService } from './material-refer.service'
import { KaelMaterialService } from '../kael.material.service'
import { TianhePageInfoService } from '@/modules/low-code/page-info/tianhe.page-info.service'

@Injectable()
export class TianheMaterialReferService {
  private readonly logger = loggerInstance
  private readonly prisma = PrismaClientInstance
  constructor(
    private readonly materialReferService: MaterialReferService,
    private readonly lowCodePageInfoService: LowCodePageInfoService,
    private readonly kaelMaterialService: KaelMaterialService,
  ) {}

  static readonly TIANHE_BANNED_MATERIAL_NAMESPACES = [
    '@es/tianhe-basic-materials/FormItem',
    '@es/tianhe-pro-materials/ProTableColumn',
    '@es/tianhe-basic-materials/TableColumn',
    '@es/tianhe-basic-materials/Row',
    '@es/tianhe-basic-materials/Root',
    '@es/tianhe-basic-materials/Col',
    '@es/tianhe-basic-materials/Text',
    '@es/tianhe-basic-materials/Container',
    '@es/tianhe-basic-materials/Icon',
    '@es/tianhe-basic-materials/Image',
    '@es/tianhe-basic-materials/Iframe',
    '@es/tianhe-basic-materials/Video',
    '@es/tianhe-pro-materials/ProFormItem',
  ]

  async findTianheMaterialRefers(
    namespaces: string[],
    appKeys: string[],
    invert: boolean,
    beforeTime = now(),
  ): Promise<{ namespace: string, count: number }[]> {
    let baseWhere: Prisma.material_referWhereInput = {
      ref_business: LOW_CODE_BUSINESS.KAEL,
      status: RECORD_STATUS.EFFECT,
      create_time: {
        lte: beforeTime,
      },
      AND: {
        namespace: {
          notIn: TianheMaterialReferService.TIANHE_BANNED_MATERIAL_NAMESPACES,
        },
      },
    }

    const namespaceConditions: Prisma.material_referWhereInput[] = []
    if (namespaces.length > 0) {
      const exactMatchNamespaces = namespaces.filter(
        namespace => !namespace.endsWith('%'),
      )
      const startsWithNamespaces = namespaces.filter(namespace =>
        namespace.endsWith('%'),
      )

      if (exactMatchNamespaces.length > 0) {
        if (invert) {
          namespaceConditions.push({
            namespace: {
              notIn: exactMatchNamespaces,
            },
          })
        }
        else {
          namespaceConditions.push({
            namespace: {
              in: exactMatchNamespaces,
            },
          })
        }
      }

      if (startsWithNamespaces.length > 0) {
        if (invert) {
          namespaceConditions.push(
            ...startsWithNamespaces.map(namespace => ({
              namespace: {
                not: {
                  startsWith: namespace.slice(0, -1),
                },
              },
            })),
          )
        }
        else {
          namespaceConditions.push(
            ...startsWithNamespaces.map(namespace => ({
              namespace: {
                startsWith: namespace.slice(0, -1),
              },
            })),
          )
        }
      }
    }

    const appKeyConditions: Prisma.material_referWhereInput[] = []
    if (appKeys.length > 0) {
      appKeyConditions.push(
        ...appKeys.map(appKey => ({
          ref_page: {
            contains: `${appKey}/page/`,
          },
        })),
      )
    }

    // 合并 OR 条件
    if (namespaceConditions.length > 0 || appKeyConditions.length > 0) {
      if (invert) {
        baseWhere = {
          ...baseWhere,
          AND: [...namespaceConditions, ...appKeyConditions],
        }
      }
      else {
        baseWhere = {
          ...baseWhere,
          OR: [...namespaceConditions, ...appKeyConditions],
        }
      }
    }

    // 一次性查询所有匹配的记录
    const records: Service.Material.Refer.Refer[]
      = await this.prisma.material_refer.findMany({
        where: baseWhere,
      })

    const referCountMap: { namespace: string, count: number }[] = namespaces
      .filter(namespace => !namespace.endsWith('%'))
      .map((namespace) => {
        return {
          namespace,
          count: 0,
        }
      })
    records.forEach((record) => {
      // 只计算页面最新版本的引用
      if (
        records.some(
          anotherRecord =>
            anotherRecord.ref_page === record.ref_page
            && +anotherRecord.ref_page_version > +record.ref_page_version,
        )
      ) {
        return
      }
      const index = referCountMap.findIndex(
        item => item.namespace === record.namespace,
      )
      if (index === -1) {
        referCountMap.push({
          namespace: record.namespace,
          count: record.refer_count,
        })
      }
      else {
        referCountMap[index].count += record.refer_count
      }
    })

    return referCountMap.sort((a, b) => b.count - a.count)
  }

  async findTianheMaterialReferPages(
    namespace: string,
    appKeys: string[],
    beforeTime = now(),
  ): Promise<Service.Material.Refer.Refer[]> {
    const pageList
      = await this.lowCodePageInfoService.findNewestTianhePageURLList(beforeTime)

    if (pageList.length === 0) return []

    const filteredPageList
      = appKeys.length > 0
        ? pageList.filter(page =>
          appKeys.some(appKey => page.url.includes(`${appKey}/page/`)),
        )
        : pageList

    // 为每个页面和版本创建一个包含两个参数的元组
    const inClauseElements = filteredPageList.map(page => ({
      url: page.url,
      version: page.version,
    }))

    // 构建查询
    const records = await this.prisma.$queryRaw<Service.Material.Refer.Refer[]>(
      Prisma.sql`SELECT * FROM material_refer
      WHERE namespace = ${namespace}
        AND ref_business = ${LOW_CODE_BUSINESS.KAEL}
        AND status = ${RECORD_STATUS.EFFECT}
        AND (${Prisma.join(
          inClauseElements.map(
            el =>
              Prisma.sql`(ref_page, ref_page_version) = (${el.url}, ${el.version})`,
          ),
          ' OR ',
        )})`,
    )

    return records.map(transformBigIntToNumber)
  }

  async findTianheAppsMaterialRefers(appKeys: string[]): Promise<Service.Material.Refer.TianheAppPageReferUsage[]> {
    let records: Service.Material.Refer.Refer[] = []

    if (appKeys.length > 0) {
      // 使用 $queryRaw 进行查询并获取每个 ref_page 最大版本的记录
      const sql = `
        SELECT * FROM material_refer AS mr
        WHERE mr.ref_business = '${LOW_CODE_BUSINESS.KAEL}'
          AND mr.status = ${RECORD_STATUS.EFFECT}
          AND (${appKeys.map(appKey => `mr.ref_page LIKE '%${appKey}/page/%'`).join(' OR ')})
          AND mr.namespace NOT IN (${TianheMaterialReferService.TIANHE_BANNED_MATERIAL_NAMESPACES.map(item => `'${item}'`).join(',')})
          AND mr.ref_page_version = (
            SELECT MAX(sub_mr.ref_page_version)
            FROM material_refer AS sub_mr
            WHERE sub_mr.ref_page = mr.ref_page
          );
      `
      const result
        = await this.prisma.$queryRawUnsafe<Service.Material.Refer.Refer[]>(sql)
      records = result
    }
    else {
      // 无 appKeys 时，查询所有符合条件且版本最新的记录
      const sql = `
        SELECT * FROM material_refer AS mr
        WHERE mr.ref_business = '${LOW_CODE_BUSINESS.KAEL}'
          AND mr.status = ${RECORD_STATUS.EFFECT}
          AND mr.namespace NOT IN (${TianheMaterialReferService.TIANHE_BANNED_MATERIAL_NAMESPACES.map(item => `'${item}'`).join(',')})
          AND mr.ref_page_version = (
            SELECT MAX(sub_mr.ref_page_version)
            FROM material_refer AS sub_mr
            WHERE sub_mr.ref_page = mr.ref_page
          );
      `
      records
        = await this.prisma.$queryRawUnsafe<Service.Material.Refer.Refer[]>(sql)
    }

    records = records.map(item => ({
      ...item,
      id: +item.id.toString(),
      material_id: +item.material_id.toString(),
      refer_count: +item.refer_count.toString(),
      creator_id: +item.creator_id.toString(),
      create_time: +item.create_time.toString(),
    }))

    const appPageMaterialMap: Service.Material.Refer.TianheAppPageReferUsage[]
      = []
    await limitInvokePromiseAllFulfilled(
      records.map(record => async (): Promise<void> => {
        const { appKey, pageCode } = TianhePageInfoService.normalizeTianheURL(
          record.ref_page,
        )
        const pageInfoRecord
          = await this.lowCodePageInfoService.findPageInfoByAppKeyAndPageCode(
            appKey,
            pageCode,
          )
        const appName
          = pageInfoRecord
          && isObject(pageInfoRecord)
          && isObject(pageInfoRecord.payload)
          && 'appName' in pageInfoRecord.payload
          && isString(pageInfoRecord.payload.appName)
            ? pageInfoRecord.payload.appName
            : ''
        const pageName
          = pageInfoRecord
          && isObject(pageInfoRecord)
          && isObject(pageInfoRecord.payload)
          && 'pageName' in pageInfoRecord.payload
          && isString(pageInfoRecord.payload.pageName)
            ? pageInfoRecord.payload.pageName
            : ''
        if (!appPageMaterialMap.some(item => item.appKey === appKey)) {
          appPageMaterialMap.push({
            appKey,
            appName,
            pages: [],
          })
        }
        const appPage = appPageMaterialMap.find(
          item => item.appKey === appKey,
        )
        if (!appPage.pages.some(item => item.pageUrl === record.ref_page)) {
          appPage.pages.push({
            pageUrl: record.ref_page,
            pageName,
            materials: [],
          })
        }
        const page = appPage.pages.find(
          item => item.pageUrl === record.ref_page,
        )
        const materialIndex = page.materials.findIndex(
          item => item.namespace === record.namespace,
        )
        if (materialIndex === -1) {
          page.materials.push({
            namespace: record.namespace,
            count: record.refer_count,
          })
        }
        else {
          page.materials[materialIndex].count += record.refer_count
        }
      }),
    )

    return appPageMaterialMap
  }

  async computeTianheMaterialsUsageGrowth(
    namespaces: string[],
    appKeys: string[],
  ): Promise<(Service.Material.Refer.DailyMaterialUsageGrowth & {
      detail: {
        page: string
        pageVersion: string
        namespace: string
        count: number
      }[]
    })[]> {
    this.logger.info('computeTianheMaterialUsageGrowth')
    const baseWhere: Prisma.material_referWhereInput = {
      ref_business: LOW_CODE_BUSINESS.KAEL,
      status: RECORD_STATUS.EFFECT,
    }

    const filteredNamespaces = namespaces.filter(
      namespace =>
        !namespace.endsWith('%')
        && !TianheMaterialReferService.TIANHE_BANNED_MATERIAL_NAMESPACES.includes(
          namespace,
        ),
    )

    const wildcardNamespaces = namespaces
      .filter(namespace => namespace.endsWith('%'))
      .map(namespace => ({
        namespace: {
          startsWith: namespace.slice(0, -1),
        },
      }))

    const where: Prisma.material_referWhereInput = {
      ...baseWhere,
      AND: [
        {
          namespace: {
            notIn: TianheMaterialReferService.TIANHE_BANNED_MATERIAL_NAMESPACES,
          },
        },
      ],
    }

    // ======================== namespace 过滤 ========================
    if (filteredNamespaces.length > 0 || wildcardNamespaces.length > 0) {
      where.OR = []

      if (filteredNamespaces.length > 0) {
        where.OR.push({
          namespace: {
            in: filteredNamespaces,
          },
        })
      }

      if (wildcardNamespaces.length > 0) {
        where.OR.push(...wildcardNamespaces)
      }
    }

    let records: Service.Material.Refer.Refer[] = []

    if (appKeys.length > 0 && isArray(where.AND)) {
      // 使用 OR 来合并所有的 appKey 条件，减少多次数据库查询
      where.AND.push({
        OR: appKeys.map(appKey => ({
          ref_page: {
            contains: `${appKey}/page/`,
          },
        })),
      })
    }

    // 通过 orderBy 在数据库层面进行排序，避免内存中的排序操作
    records = await this.prisma.material_refer.findMany({
      where,
      orderBy: {
        create_time: 'asc', // 按创建时间升序排列
      },
    })

    const materialUsageGrowth: (Service.Material.Refer.DailyMaterialUsageGrowth & {
      detail: {
        page: string
        pageVersion: string
        namespace: string
        count: number
      }[]
    })[] = []
    this.logger.info(`records.length: ${records.length}`)
    for (const record of records) {
      const createTime = +record.create_time
      const createDate = dayjs(createTime).format('YYYY-MM-DD')

      // ======================== 创建或找到对应结果 ========================
      const index = materialUsageGrowth.findIndex(
        item => item.date === createDate,
      )
      if (index === -1) {
        materialUsageGrowth.push({
          time: createTime,
          date: createDate,
          value: 0,
          detail:
            materialUsageGrowth.length === 0
              ? []
              : materialUsageGrowth[materialUsageGrowth.length - 1]!.detail,
        })
      }
      const dateIndex = materialUsageGrowth.findIndex(
        item => item.date === createDate,
      )
      const dateItem = materialUsageGrowth[dateIndex]

      // ======================== 删除同页面旧版本的统计 ========================
      dateItem.detail = dateItem.detail.filter(
        item =>
          item.page !== record.ref_page
          || +item.pageVersion >= +record.ref_page_version,
      )

      // ======================== 添加新统计 ========================
      const page = dateItem.detail.find(
        item =>
          item.page === record.ref_page
          && item.pageVersion === record.ref_page_version
          && item.namespace === record.namespace,
      )
      if (!page) {
        dateItem.detail.push({
          page: record.ref_page,
          pageVersion: record.ref_page_version,
          namespace: record.namespace,
          count: record.refer_count,
        })
      }
    }

    // ======================== 统一计算 count ========================
    for (const daily of materialUsageGrowth) {
      daily.value = daily.detail.reduce((prev, current) => {
        return prev + current.count
      }, 0)
      delete daily.detail
    }
    this.logger.info('computeTianheMaterialUsageGrowth done')

    return materialUsageGrowth
  }

  async computeTianheMaterialsUseRateGrowth(
    namespaces: string[],
    appKeys: string[],
  ): Promise<Service.Material.Refer.DailyMaterialUsageGrowth[]> {
    const targetNamespaceGrowth = await this.computeTianheMaterialsUsageGrowth(
      namespaces,
      appKeys,
    )
    const allNamespaceGrowth = await this.computeTianheMaterialsUsageGrowth(
      [],
      appKeys,
    )

    this.logger.info('computeTianheMaterialUseRateGrowth')
    this.logger.info(
      `targetNamespaceGrowth.length: ${targetNamespaceGrowth.length}`,
    )
    this.logger.info(`allNamespaceGrowth.length: ${allNamespaceGrowth.length}`)

    const useRateGrowth: Service.Material.Refer.DailyMaterialUsageGrowth[] = []
    for (const daily of allNamespaceGrowth) {
      const targetDailyIndex = targetNamespaceGrowth.findIndex(
        (item, idx, self) => {
          if (item.date === daily.date) {
            return true
          }
          else {
            const haveNextDaily = self.length > idx + 1
            if (haveNextDaily) {
              const nextItem = self[idx + 1]
              if (nextItem && nextItem.date > daily.date) {
                return true
              }
            }
            else {
              return true
            }
          }
        },
      )
      const targetDaily = targetNamespaceGrowth[targetDailyIndex]
      targetNamespaceGrowth.splice(targetDailyIndex, 1)
      if (targetDaily) {
        useRateGrowth.push({
          time: daily.time,
          date: daily.date,
          value: targetDaily.value / daily.value,
        })
      }
    }
    return useRateGrowth
  }

  async statisticsNewestMaterialReferCount(): Promise<
    {
      materialId: number
      referCount: number
    }[]
  > {
    const newestPages
      = await this.lowCodePageInfoService.findNewestTianhePageURLList()

    if (newestPages.length === 0) return []
    const inClauseElements = newestPages.map(page => ({
      url: page.url,
      version: page.version,
    }))

    const newestReferRecords = await this.prisma.$queryRaw<
      { namespace: string, refer_count_sum: number }[]
    >(
      Prisma.sql`
      SELECT
        namespace,
        SUM(refer_count) AS refer_count_sum
      FROM
        material_refer
      WHERE ref_business = ${LOW_CODE_BUSINESS.KAEL}
        AND status = ${RECORD_STATUS.EFFECT}
        AND (${Prisma.join(
          inClauseElements.map(
            el =>
              Prisma.sql`(ref_page, ref_page_version) = (${el.url}, ${el.version})`,
          ),
          ' OR ',
        )})
      GROUP BY namespace`,
    )

    return (
      await limitInvokePromiseAllFulfilled(
        newestReferRecords.map(item => async () => {
          const material
            = await this.kaelMaterialService.searchMaterialBaseInfoByNamespace(
              item.namespace,
            )
          if (!material) return null
          return {
            materialId: material.id,
            referCount: item.refer_count_sum,
          }
        }),
      )
    ).result.filter(item => item !== null)
  }
}
