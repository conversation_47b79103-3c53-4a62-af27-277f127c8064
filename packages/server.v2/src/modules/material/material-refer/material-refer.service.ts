import { Prisma } from '@prisma/client'
import { Injectable } from '@nestjs/common'

import { RECORD_STATUS } from '@/constants/status'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { CODE_TYPE } from '@/constants/code-type'
import { MaterialService } from '../material.service'

@Injectable()
export class MaterialReferService {
  private readonly prisma = PrismaClientInstance

  constructor(private readonly materialService: MaterialService) {}

  static readonly REFER_TYPES = {
    REFER: 'refer',
  }

  async findMaterialReferList(params: {
    id: number
    source_type?: 'namespace' | 'package'
    code_type?: CODE_TYPE
    pageSize?: number
    pageNum?: number
  }): Promise<Service.PaginationResult<Service.Material.Refer.Refer>> {
    const {
      id,
      source_type = 'namespace',
      code_type = CODE_TYPE.LOW_CODE,
      pageSize = 10,
      pageNum = 1,
    } = params
    const material = await this.materialService.detail({
      id,
    })
    let queryWhere: Prisma.material_referFindManyArgs['where']
    if (code_type === CODE_TYPE.LOW_CODE) {
      queryWhere = {
        namespace: material.namespace.replace(/(\([^()]*\))+$/g, ''), // 低代码场景，去除命名空间中的参数
        status: RECORD_STATUS.EFFECT,
        code_type: CODE_TYPE.LOW_CODE,
      }
    }
    else {
      queryWhere = {
        namespace:
          source_type === 'package'
            ? material.currentVersion.schema.packageName
            : material.namespace,
        status: RECORD_STATUS.EFFECT,
        code_type: CODE_TYPE.PRO_CODE,
      }
    }
    const count = await this.prisma.material_refer.count({
      where: queryWhere,
    })
    const list = await this.prisma.material_refer.findMany({
      where: queryWhere,
      skip: (pageNum - 1) * pageSize,
      take: pageSize,
    })
    return {
      list,
      total: count,
      pageNum,
      pageSize,
      totalPage: Math.ceil(count / pageSize),
      hasNext: pageNum < Math.ceil(count / pageSize),
      hasPrev: pageNum > 1,
    }
  }

  async findMaterialReferCountGroupByRefBusiness(
    refBusiness: 'kael' | 'fangzhou',
  ): Promise<
      {
        namespace: string
        _sum: {
          refer_count: number
        }
      }[]
    > {
    const result = await this.prisma.material_refer.groupBy({
      by: ['namespace'],
      where: {
        ref_business: refBusiness,
        status: RECORD_STATUS.EFFECT,
      },
      _sum: {
        refer_count: true,
      },
    })
    return result.map((item) => {
      item._sum.refer_count = item._sum.refer_count || 0
      return item
    })
  }

  async clear(
    where: Prisma.material_referWhereInput,
  ): Promise<Prisma.BatchPayload> {
    return this.prisma.material_refer.updateMany({
      where: {
        status: RECORD_STATUS.EFFECT,
        ...where,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async clearTargetPageRefer(
    params: Service.Material.Refer.ClearTargetPageReferParams,
  ): Promise<Prisma.BatchPayload> {
    return this.prisma.material_refer.updateMany({
      where: {
        status: RECORD_STATUS.EFFECT,
        ref_business: params.business,
        ref_page: params.page,
        ref_page_version: params.ref_page_version
          ? params.ref_page_version + ''
          : undefined,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }

  async createRefer(
    params: Service.Material.Refer.CreateParams,
  ): Promise<Service.Material.Refer.Refer> {
    await this.prisma.material_refer.updateMany({
      where: {
        // 相同物料
        namespace: params.namespace,
        // 相同页面
        ref_page: params.ref_page,
        ref_page_version: params.ref_page_version + '',
        status: RECORD_STATUS.EFFECT,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
    return this.prisma.material_refer.create({
      data: {
        ...params,
        ref_page_version: params.ref_page_version
          ? params.ref_page_version + ''
          : undefined,
        id: genPrimaryIndex(),
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async batchCreate(
    params: Service.Material.Refer.CreateParams[],
  ): Promise<Prisma.BatchPayload> {
    return this.prisma.material_refer.createMany({
      data: params.map(param => ({
        ...param,
        id: genPrimaryIndex(),
        status: RECORD_STATUS.EFFECT,
      })),
    })
  }
}
