import { Injectable } from '@nestjs/common'
import { FangZhouMaterialReferService } from './fangzhou.material-refer.service'
import { TianheMaterialReferService } from './tianhe.material-refer.service'

@Injectable()
export class StatisticsMaterialReferService {
  constructor(
    private readonly fangzhouMaterialReferService: FangZhouMaterialReferService,
    private readonly tianheMaterialReferService: TianheMaterialReferService,
  ) {}

  async dailyReferStatistics(): Promise<
    { materialId: number, referCount: number }[]
  > {
    const fangzhouReferCount
      = await this.fangzhouMaterialReferService.statisticsNewestMaterialReferCount()
    const tianheReferCount
      = await this.tianheMaterialReferService.statisticsNewestMaterialReferCount()
    return fangzhouReferCount.concat(tianheReferCount)
  }
}
