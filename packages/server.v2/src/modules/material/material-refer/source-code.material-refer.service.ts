import { RECORD_STATUS } from '@/constants/status'
import { MaterialUsageService } from '@/modules/analysis/material-usage/material-usage.service'
import { RawDataService } from '@/modules/analysis/raw-data/raw-data.service'
import { PrismaClientInstance } from '@/tools/prisma'
import { Injectable } from '@nestjs/common'
import { get, now } from 'lodash'
import { MaterialService } from '../material.service'
import { CODE_TYPE, REPO_TYPE } from '@/shared'
import { IS_CORP } from '@/constants/envs'
import { MaterialReferService } from './material-refer.service'
import { ScanInfoService } from '@/modules/analysis/scan-info/scan-info.service'
import { RepoMetaService } from '@/modules/analysis/repo-meta/repo-meta.service'

@Injectable()
export class SourceCodeMaterialReferService {
  private readonly prisma = PrismaClientInstance

  constructor(
    private readonly rawDataService: RawDataService,
    private readonly materialUsageService: MaterialUsageService,
    private readonly materialService: MaterialService,
    private readonly materialReferService: MaterialReferService,
    private readonly scanInfoService: ScanInfoService,
    private readonly repoMetaService: RepoMetaService,
  ) {}

  async batchCreateMaterialReferFromSourceCodeRecords(
    scanId?: number,
  ): Promise<void> {
    if (!scanId) {
      const scanInfo = await this.scanInfoService.findNewestScanInfoByType()
      if (scanInfo) {
        scanId = scanInfo.id
      }
    }
    await this.materialReferService.clear({
      code_type: CODE_TYPE.PRO_CODE,
    })
    this.batchCreateMaterialReferFromMaterialUsage(scanId)
    this.batchCreateMaterialReferFromRawData(scanId)
  }

  async batchCreateMaterialReferFromMaterialUsage(
    scanId: number,
  ): Promise<void> {
    const count = await this.prisma.analysis_route_file_usage.count({
      where: {
        scan_id: scanId,
        status: RECORD_STATUS.EFFECT,
      },
    })
    if (count === 0) {
      return
    }

    let offset = 0
    // 分批处理，每次 50 个
    while (offset < count) {
      const materialRefers: Service.Material.Refer.CreateParams[] = []
      const records = await this.prisma.analysis_route_file_usage.findMany({
        where: {
          scan_id: scanId,
          status: RECORD_STATUS.EFFECT,
        },
        skip: offset,
        take: 50,
      })

      const materialCache = new Map<string, number>()
      const createTime = now()

      for (const record of records) {
        const { filter_material_info }
          = await this.materialUsageService.serialize(record)
        for (const [libraryName, libraryUsage] of Object.entries(
          filter_material_info,
        )) {
          for (const [materialName, count] of Object.entries(libraryUsage)) {
            let materialId: number
            if (materialCache.has(materialName)) {
              materialId = materialCache.get(materialName)
            }
            else {
              try {
                const { id } = await this.materialService.detail({
                  namespace: `${libraryName}/${materialName}`,
                })
                materialCache.set(materialName, id)
              }
              catch (_) {
                materialCache.set(materialName, 0)
              }
            }

            if (materialId) {
              materialRefers.push({
                code_type: CODE_TYPE.PRO_CODE,
                namespace: `${libraryName}/${materialName}`,
                refer_count: count,
                material_id: materialId,
                create_time: createTime,
                creator_id: IS_CORP ? 562173078089797 : 540869873102917, // 'yingpengsha'
                ref_business: record.business,
                ref_page: `${record.package}?repo_project_id=${record.repo_project_id}&file_path=${record.file_path}`,
                // version
                // ref_page_version
                // type
              })
            }
          }
        }
      }

      await this.materialReferService.batchCreate(materialRefers)

      offset += 50
    }
  }

  async batchCreateMaterialReferFromRawData(scanId: number): Promise<void> {
    const count = await this.prisma.analysis_raw_data.count({
      where: {
        scan_id: scanId,
        status: RECORD_STATUS.EFFECT,
      },
    })

    if (count === 0) {
      return
    }

    let offset = 0
    // 分批处理，每次 50 个
    while (offset < count) {
      const materialRefers: Service.Material.Refer.CreateParams[] = []
      const records = await this.prisma.analysis_raw_data.findMany({
        where: {
          scan_id: scanId,
          type: REPO_TYPE.PROFESSION,
          status: RECORD_STATUS.EFFECT,
        },
        skip: offset,
        take: 50,
      })

      const createTime = now()

      for (const record of records) {
        const isRecordedDependency: string[] = []
        const { content, repo_project_id } = await this.rawDataService.findOne(record.id)
        const ref_page = (await this.repoMetaService.findOne({
          project_id: repo_project_id,
        })).clone_url

        const { packages } = content
        for (const currentPackage of packages) {
          const dependencies: string[] = get(
            currentPackage,
            'dependencies',
            [],
          )
          dependencies.forEach(async (dependency) => {
            if (isRecordedDependency.includes(dependency)) return
            isRecordedDependency.push(dependency)
            materialRefers.push({
              code_type: CODE_TYPE.PRO_CODE,
              namespace: dependency,
              refer_count: 1,
              create_time: createTime,
              creator_id: IS_CORP ? 562173078089797 : 540869873102917, // 'yingpengsha'
              ref_business: record.business,
              ref_page,
              material_id: 0,
              // version
              // ref_page_version
              // type
            })
          })
        }
      }

      await this.materialReferService.batchCreate(materialRefers)

      offset += 50
    }
  }
}
