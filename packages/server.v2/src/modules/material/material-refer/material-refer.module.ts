import { Module } from '@nestjs/common'

import { MaterialReferController } from './material-refer.controller'
import { MaterialReferService } from './material-refer.service'
import { StatisticsMaterialReferService } from './statistics.material-refer.service'
import { SourceCodeMaterialReferService } from './source-code.material-refer.service'
import { TianheMaterialReferService } from './tianhe.material-refer.service'
import { FangZhouMaterialReferService } from './fangzhou.material-refer.service'

import { MaterialModule } from '../material.module'
import { LowCodePageInfoModule } from '@/modules/low-code/page-info/page-info.module'
import { ScanInfoModule } from '@/modules/analysis/scan-info/scan-info.module'
import { RawDataModule } from '@/modules/analysis/raw-data/raw-data.module'
import { MaterialUsageModule } from '@/modules/analysis/material-usage/material-usage.module'
import { RepoMetaModule } from '@/modules/analysis/repo-meta/repo-meta.module'

@Module({
  imports: [
    LowCodePageInfoModule,
    MaterialModule,
    ScanInfoModule,
    RawDataModule,
    MaterialUsageModule,
    RepoMetaModule,
  ],
  controllers: [MaterialReferController],
  providers: [
    MaterialReferService,
    TianheMaterialReferService,
    StatisticsMaterialReferService,
    FangZhouMaterialReferService,
    SourceCodeMaterialReferService,
  ],
  exports: [
    MaterialReferService,
    TianheMaterialReferService,
    StatisticsMaterialReferService,
    FangZhouMaterialReferService,
    SourceCodeMaterialReferService,
  ],
})
export class MaterialReferModule {}
