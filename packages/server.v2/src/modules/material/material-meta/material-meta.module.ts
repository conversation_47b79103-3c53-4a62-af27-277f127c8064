import { Module } from '@nestjs/common'
import { MaterialMetaController } from './material-meta.controller'
import { MaterialMetaService } from './material-meta.service'
import { MaterialMetaValueModule } from '../material-meta-value/metarial-meta-value.module'

@Module({
  imports: [MaterialMetaValueModule],
  controllers: [MaterialMetaController],
  providers: [MaterialMetaService],
  exports: [MaterialMetaService],
})
export class MaterialMetaModule {}
