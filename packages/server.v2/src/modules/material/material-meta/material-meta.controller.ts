import { Controller, Get, Query } from '@nestjs/common'
import { MaterialMetaService } from './material-meta.service'

@Controller('material/meta')
export class MaterialMetaController {
  constructor(private readonly materialMetaService: MaterialMetaService) {}

  @Get('detail')
  async getMetaDetail(
    @Query() query: Service.Material.Meta.GetMetaOptions,
  ): Promise<Service.Material.Meta.MetaDetail | null> {
    return this.materialMetaService.findMetaDetailByIdOrValue(query)
  }
}
