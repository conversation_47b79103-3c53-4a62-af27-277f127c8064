import { RECORD_STATUS } from '@/constants/status'
import { BUSINESS } from '@/shared'
import { PrismaClientInstance } from '@/tools/prisma'
import { Injectable } from '@nestjs/common'
import { MaterialMetaValueService } from '../material-meta-value/material-meta-value.service'

@Injectable()
export class MaterialMetaService {
  private readonly prisma = PrismaClientInstance

  constructor(private readonly materialMetaValueService: MaterialMetaValueService) {}

  async findMetaRecordByIds(ids: number[]): Promise<Service.Material.Meta.MetaRecord[]> {
    return this.prisma.material_meta.findMany({
      where: {
        id: { in: ids },
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async findMetaDetailByBusiness(business: BUSINESS): Promise<Service.Material.Meta.MetaDetail[]> {
    const metaKeys = await this.prisma.material_meta.findMany({
      where: {
        status: RECORD_STATUS.EFFECT,
        business,
      },
    })
    return Promise.all(metaKeys.map(async (meta) => {
      const items = await this.materialMetaValueService.findListByMetaId(meta.id)
      return Object.assign(meta, {
        items,
      })
    }))
  }

  async findMetaDetailByIdOrValue(
    params: Service.Material.Meta.GetMetaOptions,
  ): Promise<Service.Material.Meta.MetaDetail | null> {
    const meta = await this.prisma.material_meta.findFirst({
      where: {
        status: RECORD_STATUS.EFFECT,
        ...params,
      },
    })
    if (!meta) return null
    const items = await this.materialMetaValueService.findListByMetaId(meta.id)
    return Object.assign(meta, {
      items,
    })
  }
}
