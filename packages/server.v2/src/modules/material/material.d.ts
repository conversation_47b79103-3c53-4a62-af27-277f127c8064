declare namespace Service.Material {
  export type MaterialRecord = import('@prisma/client').material
  export type SerializeMaterialRecord = import('type-fest').OverrideProperties<
    MaterialRecord,
    {
      meta?: SerializeMaterialMeta
    }
  >

  export interface SerializeMaterialMeta {
    virtual?: boolean

    isCloudComponent?: boolean

    autoConvert?: string[] // Business[]
    ref_source?: 'convert'
    ref_material_id?: number
  }

  export type MaterialWithLastPubRecord = SerializeMaterialRecord & {
    latest_pub: Service.Material.Publish.SerializePublishRecord
  }

  export interface ListParams extends Service.__OldPaginationParams {
    query?: string
    business?: BUSINESS
  }

  export interface ListQueryResult {
    data: SerializeMaterialRecord[]
    pagination: Service.PaginationResult
  }

  export interface MaterialInfoForAnalysis {
    materialId: number
    packageName: string
    business: BUSINESS
    componentBundleType: 'SLSC' | 'SLMC'
    components: string[]
    componentsWithId: {
      id: number
      name: string
    }[]
  }

  export interface MaterialBaseInfo {
    id: number
    name: string
    newestVersion: string
    previewURL: string
    createTime: string
    updateTime: string
    creator: {
      name?: string
      username?: string
    }
  }

  export interface DetailOptions {
    /**
     * 是否展示历史版本
     */
    withVersions?: boolean
    /**
     * 只展示“正式版本”或“非正式版本”，为空则不进行区分
     */
    typeOfVersions?: import('@/constants/material').MATERIAL_VERSION_TYPE
  }

  export interface DetailQueries {
    id?: number
    namespace?: string
    version?: string
    options?: DetailOptions
  }

  export interface MaterialDetail {
    id: number
    namespace: string
    version: string
    business: import('@/constants/business').BUSINESS
    type: import('@/constants/material').MATERIAL_TYPE
    title: string
    description: string
    gitUrl: string
    tags: Record<
      import('type-fest').KeysOfUnion<GeneralMaterialSchema.Tags>,
      TransformedTag[]
    >
    currentVersion: Service.Material.Publish.NormalizePublishRecord
    historyVersions?: Service.Material.Publish.NormalizePublishRecord[]
    updater: string
    updater_id: number
    updater_name: string
    update_time: number
    creator: string
    creator_id: number
    creator_name: string
    create_time: number

    __meta: Service.Material.SerializeMaterialMeta
  }

  export type CreateMaterialParams = Pick<
    MaterialRecord,
    | 'namespace'
    | 'version'
    | 'title'
    | 'type'
    | 'business'
    | 'platform'
    | 'creator_id'
  > &
  Partial<
    Pick<MaterialRecord>,
      'description' | 'domain' | 'git_url' | 'meta'
  >

  export type UpdateMaterialRecordParams = Pick<
    MaterialRecord,
    'id' | 'updater_id'
  > &
  Partial<
    Pick<MaterialRecord>,
    | 'version'
    | 'title'
    | 'description'
    | 'type'
    | 'business'
    | 'domain'
    | 'git_url'
    | 'platform'
    | 'meta'
  >

  export interface TransformedTag {
    id: number | string
    key: number | string
    label: string
    children?: TransformedTag[]
  }

  export interface ListQueries extends Partial<Service.PaginationParams> {
    keyword?: string
    namespace?: string
    type?: import('@/constants/material').MATERIAL_TYPE
    tags?: {
      domain?: number
      business?: import('@/constants/business').BUSINESS
      category?: string
      platform?: string
      meta?: Record<string, string>
    }
    detailOptions?: Service.Material.DetailOptions
  }

  export type MaterialListResult = Service.PaginationResult<MaterialDetail>
}
