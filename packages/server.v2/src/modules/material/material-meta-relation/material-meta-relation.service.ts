import { RECORD_STATUS } from '@/constants/status'
import { now } from '@/shared'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { Injectable } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { MaterialMetaService } from '../material-meta/material-meta.service'
import { MaterialMetaValueService } from '../material-meta-value/material-meta-value.service'

@Injectable()
export class MaterialMetaRelationService {
  private readonly prisma = PrismaClientInstance
  constructor(
    private readonly materialMetaService: MaterialMetaService,
    private readonly materialMetaValueService: MaterialMetaValueService,
  ) {}

  async findMaterialMetaRelations(
    materialId: number,
  ): Promise<Service.Material.Meta.MetaDetail[]> {
    const metaValueRelations
      = await this.prisma.material_meta_value_relation.findMany({
        where: {
          material_id: materialId,
          status: RECORD_STATUS.EFFECT,
        },
      })
    const metaValues = await this.materialMetaValueService.findListByIds(
      metaValueRelations.map(relation => relation.meta_value_id),
    )
    const metaKeys = await this.materialMetaService.findMetaRecordByIds(
      Array.from(new Set(metaValues.map(value => value.meta_id))),
    )

    return metaKeys.map((meta) => {
      const items = metaValues.filter(value => value.meta_id === meta.id)
      return Object.assign(meta, {
        items,
      })
    })
  }

  createMaterialMetaRelations(
    materialId: number,
    metaValueIds: number[],
    creatorId: number = -1,
  ): Prisma.PrismaPromise<Prisma.BatchPayload> {
    return this.prisma.material_meta_value_relation.createMany({
      data: metaValueIds.map(metaValueId => ({
        id: genPrimaryIndex(),
        material_id: materialId,
        meta_value_id: metaValueId,
        status: RECORD_STATUS.EFFECT,
        creator_id: creatorId,
        create_time: now(),
      })),
      skipDuplicates: true,
    })
  }

  clearMaterialMetaRelations(
    materialId: number,
  ): Prisma.PrismaPromise<Prisma.BatchPayload> {
    return this.prisma.material_meta_value_relation.updateMany({
      where: {
        material_id: materialId,
      },
      data: {
        status: RECORD_STATUS.DISABLED,
      },
    })
  }
}
