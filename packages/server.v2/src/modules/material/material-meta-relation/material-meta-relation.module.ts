import { Module } from '@nestjs/common'
import { MaterialMetaRelationController } from './material-meta-relation.controller'
import { MaterialMetaRelationService } from './material-meta-relation.service'
import { MaterialMetaModule } from '../material-meta/material-meta.module'
import { MaterialMetaValueModule } from '../material-meta-value/metarial-meta-value.module'

@Module({
  imports: [MaterialMetaModule, MaterialMetaValueModule],
  controllers: [MaterialMetaRelationController],
  providers: [MaterialMetaRelationService],
  exports: [MaterialMetaRelationService],
})
export class MaterialMetaRelationModule {}
