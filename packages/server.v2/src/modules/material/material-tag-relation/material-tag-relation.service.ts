import { Injectable } from '@nestjs/common'
import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { RECORD_STATUS } from '@/constants/status'
import { Prisma } from '@prisma/client'
import { now } from '@/shared'
import { MaterialTagService } from '../material-tag/material-tag.service'

@Injectable()
export class MaterialTagRelationService {
  private readonly prisma = PrismaClientInstance
  constructor(private readonly materialTagService: MaterialTagService) {}

  async updateMaterialTagsWithTagValues(
    materialId: number,
    tagValues: string[],
    creatorId: number = -1,
  ): Promise<Prisma.BatchPayload> {
    const tags = await this.materialTagService.findListByValues(tagValues)
    await this.clearMaterialTagRelations(materialId)
    return this.createMaterialTagRelations(
      materialId,
      tags.map(tag => tag.id),
      creatorId,
    )
  }

  createMaterialTagRelations(
    materialId: number,
    tagIds: number[],
    creatorId: number = -1,
  ): Prisma.PrismaPromise<Prisma.BatchPayload> {
    return this.prisma.material_tag_relation.createMany({
      data: tagIds.map(tagId => ({
        id: genPrimaryIndex(),
        material_id: materialId,
        tag_id: tagId,
        status: RECORD_STATUS.EFFECT,
        creator_id: creatorId,
        create_time: now(),
      })),
      skipDuplicates: true,
    })
  }

  async findMaterialTags(materialId: number): Promise<Service.Material.Tag.TagRecord[]> {
    const relations = await this.prisma.material_tag_relation.findMany({
      where: {
        material_id: materialId,
        status: RECORD_STATUS.EFFECT,
      },
    })
    return this.materialTagService.findListByIds(
      relations.map(relation => relation.tag_id),
    )
  }

  clearMaterialTagRelations(
    materialId: number,
  ): Prisma.PrismaPromise<Prisma.BatchPayload> {
    return this.prisma.material_tag_relation.updateMany({
      where: {
        material_id: materialId,
      },
      data: {
        status: RECORD_STATUS.DELETE,
      },
    })
  }
}
