import assert from 'assert'
import semver from 'semver'
import { Injectable } from '@nestjs/common'

import { genPrimaryIndex, PrismaClientInstance } from '@/tools/prisma'
import { RECORD_STATUS } from '@/constants/status'
import { MATERIAL_VERSION_TYPE } from '@/constants/material'
import {
  PromiseAllFulfilled,
  PromiseAllFulfilledResult,
} from '@/tools/Promise'
import { isReleaseVersion } from '@/tools/version'
import { UserService } from '@/modules/user/user.service'
import { JsonObject, JsonValue } from 'type-fest'
import { safeParseMaybeNull, safeStringifyMaybeNull } from '@/tools/json'
import { now } from '@/shared'

@Injectable()
export class MaterialPublishService {
  private readonly prisma = PrismaClientInstance

  constructor(private readonly userService: UserService) {}

  private serialize(
    record: Service.Material.Publish.PublishRecord,
  ): Service.Material.Publish.SerializePublishRecord {
    try {
      return Object.freeze({
        ...record,
        schema: record.schema
          ? (safeParseMaybeNull(
            record.schema,
          ) as unknown as GeneralMaterialSchema.Schema | null)
          : null,
        content: record.content
          ? (safeParseMaybeNull(record.content) as JsonValue)
          : null,
      })
    }
    catch (error) {
      throw new Error(
        `Failed to serialize material pub: ${JSON.stringify(
          { id: record.id, error },
          null,
          2,
        )}`,
      )
    }
  }

  async normalize(
    record: Service.Material.Publish.SerializePublishRecord,
  ): Promise<Service.Material.Publish.NormalizePublishRecord> {
    const user = await this.userService.findById(record.creator_id)
    return {
      id: record.id,
      material_id: record.material_id,
      version: record.version,
      readme: record.readme,
      preview_img: record.preview_img,
      schema: record.schema,
      content: record.content,
      create_time: record.create_time,
      creator_name: user?.name,
      creator_id: user?.id,
      creator: user?.user_name,
    }
  }

  async createMaterialVersion(
    params: Service.Material.Publish.CreateMaterialVersionParams,
  ): Promise<Service.Material.Publish.PublishRecord> {
    return this.prisma.material_pub.create({
      data: {
        id: genPrimaryIndex(),
        material_id: params.material_id,
        version: params.version,
        schema: safeStringifyMaybeNull(params.schema as unknown as JsonObject),
        content: safeStringifyMaybeNull(params.content),
        readme: params.readme,
        preview_img: Array.isArray(params.preview_img)
          ? safeStringifyMaybeNull(params.preview_img)
          : params.preview_img,
        creator_id: params.creator_id,
        create_time: now(),
        status: RECORD_STATUS.EFFECT,
      },
    })
  }

  async existTargetMaterialVersion(
    materialId: number,
    version: string,
  ): Promise<boolean> {
    const count = await this.prisma.material_pub.count({
      where: { material_id: materialId, version, status: RECORD_STATUS.EFFECT },
    })
    return count > 0
  }

  async findRawRecordByMaterialIdAndVersion(
    materialId: number,
    version: string,
  ): Promise<Service.Material.Publish.PublishRecord> {
    return this.prisma.material_pub.findFirst({
      where: {
        material_id: materialId,
        version,
      },
    })
  }

  async findOnePublishByMaterialInfo(
    params: Service.Material.Publish.FindOnePublishByMaterialInfo,
  ): Promise<Service.Material.Publish.SerializePublishRecord> {
    assert(params.materialId, '物料 ID 不能为空')
    assert(params.version, '版本号不能为空')
    const record = await this.prisma.material_pub.findFirstOrThrow({
      where: {
        material_id: params.materialId,
        version: params.version,
        status: RECORD_STATUS.EFFECT,
      },
      orderBy: { create_time: 'desc' },
    })

    return this.serialize(record)
  }

  async findVersionListByMaterialId(
    materialId: number,
    typeOfVersions?: MATERIAL_VERSION_TYPE,
  ): Promise<
      PromiseAllFulfilledResult<Service.Material.Publish.NormalizePublishRecord>
    > {
    const records = await this.prisma.material_pub.findMany({
      where: {
        material_id: materialId,
        status: RECORD_STATUS.EFFECT,
      },
      orderBy: { create_time: 'desc' },
    })
    const sortedAndFilteredRecords = records
      .filter((record) => {
        if (typeOfVersions) {
          if (typeOfVersions === MATERIAL_VERSION_TYPE.RELEASE) {
            return isReleaseVersion(record.version)
          }
          else {
            return !isReleaseVersion(record.version)
          }
        }
        return true
      })
      .sort((a, b) => {
        try {
          return semver.compare(b.version, a.version) || b.create_time - a.create_time
        }
        catch (_) {
          return b.create_time - a.create_time
        }
      })
    return PromiseAllFulfilled(
      sortedAndFilteredRecords.map(record =>
        this.normalize(this.serialize(record)),
      ),
    )
  }
}
