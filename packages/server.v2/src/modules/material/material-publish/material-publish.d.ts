declare namespace Service.Material.Publish {
  export type PublishRecord = import('@prisma/client').material_pub
  export type SerializePublishRecord = import('type-fest').OverrideProperties<
    PublishRecord,
    {
      schema: GeneralMaterialSchema.Schema | GeneralMaterialSchema.LibrarySchema | null
      content: import('type-fest').JsonValue | null
    }
  >
  export type NormalizePublishRecord = Pick<
    SerializePublishRecord,
    | 'id'
    | 'material_id'
    | 'version'
    | 'readme'
    | 'preview_img'
    | 'schema'
    | 'content'
    | 'create_time'
  > & {
    creator: string
    creator_id: number
    creator_name: string
  }

  export interface CreateMaterialVersionParams extends Pick<
    PublishRecord,
    | 'material_id'
    | 'readme'
    | 'version'
    | 'creator_id'
  > {
    preview_img: string | string[]
    schema: GeneralMaterialSchema.Schema | GeneralMaterialSchema.LibrarySchema
    content: import('type-fest').JsonValue | null
  }

  export interface FindOnePublishByMaterialInfo {
    materialId: number
    version: string
  }
}
