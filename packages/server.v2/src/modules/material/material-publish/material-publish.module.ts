import { Module } from '@nestjs/common'
import { MaterialPublishController as MaterialPublishController } from './material-publish.controller'
import { MaterialPublishService as MaterialPublishService } from './material-publish.service'
import { UserModule } from '@/modules/user/user.module'

@Module({
  imports: [UserModule],
  controllers: [MaterialPublishController],
  providers: [MaterialPublishService],
  exports: [MaterialPublishService],
})
export class MaterialPublishModule {}
