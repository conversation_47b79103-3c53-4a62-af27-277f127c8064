import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsInt, IsNotEmpty, IsString } from 'class-validator'

export class FindOnePublishByMaterialInfoDTO
implements Service.Material.Publish.FindOnePublishByMaterialInfo {
  @ApiProperty({ description: '物料ID' })
  @IsInt()
  @IsNotEmpty()
  @Type(() => Number)
  materialId: number

  @ApiProperty({ description: '版本号' })
  @IsString()
  @IsNotEmpty()
  version: string
}
