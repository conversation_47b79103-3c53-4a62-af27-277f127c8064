import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'
import { MaterialPublishService } from './material-publish.service'
import { FindOnePublishByMaterialInfoDTO } from './dto/find-one-publish-by-material-info.dto'

@ApiTags('material/publish')
@Controller('material/publish')
export class MaterialPublishController {
  constructor(
    private readonly materialPublishService: MaterialPublishService,
  ) {}

  @Get()
  async findOnePublishByMaterialIdAndVersion(
    @Query() query: FindOnePublishByMaterialInfoDTO,
  ): Promise<Service.Material.Publish.SerializePublishRecord> {
    return this.materialPublishService.findOnePublishByMaterialInfo(query)
  }
}
