try {
  const { LibraryModeBootstrap } = require('@ks-material-middleoffice/measure-sdk');
  const path = require('path');
  const PROJECT_PATH = path.resolve(__dirname, '../../../../../scan/projects');
  const dayjs = require('dayjs');
  const option = JSON.parse(decodeURIComponent(process.argv[2]));
  const filteredProject = [];
  const projectNameList = [];

  LibraryModeBootstrap({
    cwd: path.join(PROJECT_PATH, option.project.namespace),
    entries: option.project.router_entry ? option.project.router_entry : ['./src/index.*'],
    sinceDate: dayjs(option.start).format('YYYY-MM-DD'),
    endDate: dayjs(option.end).format('YYYY-MM-DD'),
    packageFilter: pkg => {
      projectNameList.push(pkg.name);
      const isVue = pkg.dependencies.some(dep => dep === 'vue');
      if (isVue) {
        const { files, ...otherPkg } = pkg;
        filteredProject.push(otherPkg);
      }

      return !isVue;
    }
  })
    .then(res => {
      console.log('__STANDARD_OUTPUT:' + JSON.stringify({ ...res, projectNameList, filteredProject }) + ':STANDARD_OUTPUT_END__');
    })
    .catch(error => console.log('扫描任务报错，错误原因是', error));
} catch (e) {
  console.log('扫描任务报错，错误原因是', e);
}