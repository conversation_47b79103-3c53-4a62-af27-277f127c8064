import { isDev } from '../env';

const child_process = require('child_process');

export const __old_runScanWorker__ = (
	workerData,
	isPageProject,
): Promise<AnyType> =>
	new Promise((resolve, reject) => {
		try {
			const workerProcess = child_process.exec(
				`node .${isDev() ? '/nodejs' : ''}/common/util/scan-work.old/scan${isPageProject ? '' : '-material'}.js ${encodeURIComponent(JSON.stringify(workerData))}`,
				(error, stdout) => {
					if (error) {
						console.log('执行扫描子进程错误，错误原因是', error);
						reject(error);
						return;
					}

					if (stdout?.includes('__STANDARD_OUTPUT')) {
						const regex = /__STANDARD_OUTPUT:(.+):STANDARD_OUTPUT_END__/;
						const match = stdout.match(regex);

						if (match) {
							resolve(JSON.parse(match[1]));
						} else {
							console.log(
								'扫描输出错误，不包含标准输出内容，原始输出内容是：',
								stdout,
							);
							reject(stdout);
						}
					} else {
						reject(stdout);
					}
				},
			);

			workerProcess.on('exit', function (code) {
				if (code !== 0) {
					reject('子进程异常退出，退出码是' + code);
				}
			});
		} catch (e) {
			console.log('执行扫描子进程错误，错误原因是', e);
			reject(
				'执行扫描子进程错误，错误原因是' +
          (typeof e === 'string' ? e : e.message),
			);
		}
	});
