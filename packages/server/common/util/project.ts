import axios from 'axios';
import { getNamespaceFromGitUrl } from './git';
import { isDev } from './env';

export const getBizProjects = () => {
	return axios.get(isDev() ? 'https://drow.corp.kuaishou.com/api/measure/apps' : 'http://drow.internal/api/measure/apps')
		.then(res => {
			if (res.data?.result === 1) {
				return res.data?.data?.filter(item => Boolean(item.gitSSHUrl)).map(item => {
					return {
						title: item.title,
						namespace: getNamespaceFromGitUrl(item.git_url),
						git_url: item.git_url,
						pkg_filter: item.pkg_filter,
						router_entry: item.router_entry,
						branch: item.branch,
						black_list: item.black_list,
					};
				}) ?? [];
			} else {
				console.log('获取商业化项目失败，错误原因是', res.data?.msg || '接口错误');
				return [];
			}
		})
		.catch(error => {
			console.log('获取商业化项目失败，错误原因是', error.message || '接口错误');
			console.log(error);
			return [];
		});
};

export const getEsProjects = async () => {
	try {
		const response = await axios.get('https://chitu.corp.kuaishou.com/rest/jia/app/all', {
			headers: {
				'Authorization': Buffer.from('QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUoxYzJWeWJtRnRaU0k2SW14cGRXeGxhVEV4SWl3aVpHbHpjR3hoZVc1aGJXVWlPaUxsaUpqbm80b2lMQ0poZG1GMFlYSWlPaUpvZEhSd2N6b3ZMM04wWVhScFl5NTVlR2x0WjNNdVkyOXRMMkp6TWk5cmFXMUJkbUYwWVhJdk5HRXlaakE1T1dFM1pUbGtORE14TURnMU9HUTFZekJpWldObU16azBaVFVpTENKdFlXbHNJam9pYkdsMWJHVnBNVEZBYTNWaGFYTm9iM1V1WTI5dElpd2lhV0YwSWpveE56RTVNakUwTkRnMWZRLk40eHA3VlB2MV9Ca2hJZTZjdVN3UUVuSWxRSmRuRVNlN2dlRjNkMlJpQlk=', 'base64').toString(),
			}
		});

		let res = [];
		if (response.data?.result === 1 && response.data?.data) {
			res = (response.data.data.list ?? [])
				.filter(item => Boolean(item.gitUrl) && ['react-web', 'react-web-h5', 'micro-main', 'micro-sub'].includes(item.type))
				.map(item => {
					return {
						title: item.name || item.desc,
						namespace: getNamespaceFromGitUrl(item.gitUrl),
						git_url: item.gitUrl,
					};
				});
		} else {
			console.log('获取电商前端项目失败，错误原因是', response.data?.errorMsg || '接口错误');
		}

		return res;
	} catch (error) {
		console.log('获取电商前端项目失败，错误原因是', error.message || '接口错误');
		console.log(error);
		return [];
	}
};