import { init } from "@mybricks/rocker-commons";
import * as path from "path";
import { MidLog } from "@rockerjs/midlog";
import * as fs from "fs";

const logDir = path.join(__dirname, "../../../../logs");

export function initLogger() {
  try {
    // 确保日志目录存在
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    const applicationLogDir = path.join(logDir, "application");
    if (!fs.existsSync(applicationLogDir)) {
      fs.mkdirSync(applicationLogDir, { recursive: true });
    }

    console.log(`[Logger] 初始化日志系统，日志目录: ${logDir}`);

    MidLog.config({
      env: process.env.NODE_ENV || "dev",
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      vtrace: () => {},
      appender: [
        {
          type: "TRACE",
          rollingFile: true,
          logdir: applicationLogDir,
          name: "info.log",
        },
        {
          type: "DEBUG",
          rollingFile: true,
          logdir: applicationLogDir,
          name: "info.log",
        },
        {
          type: "INFO",
          rollingFile: true,
          logdir: applicationLogDir,
          name: "info.log",
        },
        {
          type: "WARN",
          rollingFile: true,
          logdir: applicationLogDir,
          name: "info.log",
        },
        {
          type: "ERROR",
          rollingFile: true,
          logdir: applicationLogDir,
          name: "info.log",
        },
        {
          type: "FATAL",
          rollingFile: true,
          logdir: applicationLogDir,
          name: "info.log",
        },
      ],
    });

    init({
      // @ts-ignore
      Logger: () => {
        return new MidLog();
      },
    });

    console.log("[Logger] 日志系统初始化完成");
  } catch (error) {
    console.error("[Logger] 日志系统初始化失败:", error);
    throw error;
  }
}
