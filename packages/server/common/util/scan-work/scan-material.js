try {
  const {
    LibraryModeBootstrap,
  } = require('@ks-material-middleoffice/measure-sdk');
  const path = require('path');
  const dayjs = require('dayjs');

  const option = JSON.parse(decodeURIComponent(process.argv[2]));
  const folderName = (option.git_url || '')
    .replace('http://git.corp.kuaishou.com', '.')
    .replace('https://git.corp.kuaishou.com', '.')
    .replace('*************************:', './')
    .replace(/\.git$/, '');
  const PROJECT_PATH = path.resolve(
    __dirname,
    '../../../../../scan/projects',
    folderName,
  );

  const filteredProject = [];
  const projectNameList = [];

  LibraryModeBootstrap({
    cwd: PROJECT_PATH,
    entries: option.router_entry ? option.router_entry : ['./src/index.*'],
    sinceDate: dayjs(option.start_time).format('YYYY-MM-DD'),
    endDate: dayjs(option.end_time).format('YYYY-MM-DD'),
    blacklist: option.black_list || [],
    branch: option.branch,
    packageFilter: (pkg) => {
      projectNameList.push(pkg.name);
      const isVue = pkg.dependencies.some((dep) => dep === 'vue');
      if (isVue) {
        const { files, ...otherPkg } = pkg;
        filteredProject.push(otherPkg);
      }

      return !isVue;
    },
  })
    .then((res) => {
      console.log(
        '__STANDARD_OUTPUT:' +
          JSON.stringify({ ...res, projectNameList, filteredProject }) +
          ':STANDARD_OUTPUT_END__',
      );
    })
    .catch((error) => console.log('扫描任务报错，错误原因是', error));
} catch (e) {
  console.log('扫描任务报错，错误原因是', e);
}
