import {
	APPModeReport,
	LibraryModeReport,
} from '@ks-material-middleoffice/measure-sdk';
import { isDev } from '../env';

const child_process = require('child_process');

export interface ScanWorkerParams {
	git_url: string;
  start_time: number;
  end_time: number;
	router_entry?: string | string[];
  black_list?: string[];
	pkg_filter?: string;
	branch?: string;
}

export const runScanWorker = <Type extends 'profession' | 'material'>(
	type: Type,
	params: ScanWorkerParams,
): Promise<
  (Type extends 'profession' ? APPModeReport : LibraryModeReport) & {
    projectNameList: string[];
    filteredProject: string[];
  }
> =>
		new Promise((resolve, _reject) => {
			const scriptFolder = isDev() ? '/nodejs' : '';
			const scriptName = type === 'profession' ? 'scan' : 'scan-material';
			const fullScriptPath = `.${scriptFolder}/common/util/scan-work/${scriptName}.js`;
			const scriptParams = encodeURIComponent(JSON.stringify(params));

			const reject = (messageOrError: any, error?: any) => {
				if (messageOrError && error) {
					console.log(messageOrError, error);
					_reject(error);
				} else {
					_reject(messageOrError);
				}
			};

			try {
				const workerProcess = child_process.exec(
					`node ${fullScriptPath} ${scriptParams}`,
					{ maxBuffer: 1024 * 1024 * 10 },
					(error, stdout) => {
						if (error) {
							reject('执行扫描子进程错误，错误原因是', error);
							return;
						}

						if (stdout?.includes('__STANDARD_OUTPUT')) {
							const regex = /__STANDARD_OUTPUT:(.+):STANDARD_OUTPUT_END__/;
							const match = stdout.match(regex);

							if (match) {
								resolve(JSON.parse(match[1]));
							} else {
								reject(
									'扫描输出错误，不包含标准输出内容，原始输出内容是：',
									stdout,
								);
							}
						} else {
							reject(stdout);
						}
					},
				);

				workerProcess.on('exit', function (code) {
					if (code !== 0) {
						reject('子进程异常退出，退出码是' + code);
					}
				});
			} catch (e) {
				reject(
					'执行扫描子进程错误，错误原因是' +
          (typeof e === 'string' ? e : e.message),
				);
			}
		});
