import { parse } from "json5";

/** parse JSON string，同时 catch 错误 */
export const safeParse = (content: string, defaultValue = {}) => {
  try {
    return parse(content) ?? {};
  } catch (e) {
    console.log("parse 数据错误，错误原因是", e);
    console.log("parse 数据错误，原始数据是", content);
    return defaultValue;
  }
};

export const safeStringify = (content: AnyType) => {
  try {
    // eslint-disable-next-line no-control-regex
    return JSON.stringify(content)?.replace(/\u000a|\u0009|\u0008|\u000d/g, "");
  } catch {
    return content;
  }
};

/** 编码 */
export const safeEncodeURIComponent = (content: string) => {
  try {
    return encodeURIComponent(content);
  } catch {
    return content ?? "";
  }
};

/** 解码 */
export const safeDecodeURIComponent = (content: string) => {
  try {
    return decodeURIComponent(content);
  } catch {
    return content ?? "";
  }
};

export function getNextVersion(version, max = 100) {
  if (!version) return "1.0.0";
  const vAry: any[] = version.split(".");
  let carry = false;
  const isMaster = vAry.length === 3;
  if (!isMaster) {
    max = -1;
  }

  for (let i = vAry.length - 1; i >= 0; i--) {
    const res: number = Number(vAry[i]) + 1;
    if (i === 0) {
      vAry[i] = res;
    } else {
      if (res === max) {
        vAry[i] = 0;
        carry = true;
      } else {
        vAry[i] = res;
        carry = false;
      }
    }
    if (!carry) break;
  }

  return vAry.join(".");
}
