import axios from 'axios';
import { isProd } from './env';

const TOKEN_URL_MAP = {
	'STAGING': 'https://is-gateway-test.corp.kuaishou.com/token/get?appKey=35f77112-2bea-44d3-9742-cc3d771c5568',
	'PROD': 'http://openapi-gateway.internal/token/get?appKey=a455ea51-cad7-4972-89ff-fe6a99e0e67c&secretKey=5735724dbd8e1cf1ed2044106e58c505'
};

const OPEN_API_URL_MAP = {
	'STAGING': 'https://is-gateway-test.corp.kuaishou.com/openapi',
	'PROD': 'http://openapi-gateway.internal/openapi'
};

interface UserInfo {
	/** 员工Id */
	kwaiUserId: string;
	/** 英文名 */
	username: string;
	id: string;
	/** 中文名 */
	name: string;
	/** 头像 */
	avatarUrl: string;
	/** 头像缩略图 */
	thumbnailAvatarUrl: string;
	/** 部门 */
	orgDisplayName: string;
	/** 性别，M 男 F 女 */
	gender: string;
	/** 邮箱 */
	email: string;
	/** 状态 A 在职 T 离职 F 冻结 */
	statusCode: string;
	/** 别名 */
	alias: string;
}

export const getUserByAPI = async (username: string): Promise<UserInfo> => {
	return new Promise((resolve, reject) => {
		const env = isProd() ? 'PROD' : 'STAGING';

		axios.get(TOKEN_URL_MAP[env])
			.then(token => {
				if (token.status !== 200 || token.data?.code !== 0 || !token.data?.result?.accessToken) {
					reject('授权失败，调用openapi错误');
				}

				const { accessToken } = token.data.result;

				axios({
					url: `${OPEN_API_URL_MAP[env]}/v2/user/user/${username}`,
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${accessToken}`
					}
				})
					.then(usersInfo => {
						if (usersInfo.status !== 200 || usersInfo.data?.status !== 0 || !usersInfo.data?.data) {
							reject('用户不存在，请确认');
						}

						resolve(usersInfo.data.data);
					})
					.catch(err => reject(err.message));
			})
			.catch(err => reject(err.message));
	});
};
