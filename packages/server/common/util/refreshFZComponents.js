(function (self, callback) {
  if (typeof exports == "object" && typeof module == "object") {
    module.exports = callback();
  } else if (typeof define == "function" && define.amd) {
    define([], callback);
  } else {
    var libs = callback();
    for (var key in libs) {
      (typeof exports == "object" ? exports : self)[key] = libs[key];
    }
  }
})(self, () => {
  "use strict";
  var returnValue = {};
  var inputProxy = null;
  var outputProxy = null;
  var proxyCallbackMap = {};
  var preProps = {};
  var latestProps = {};
  var initData = "__INIT_DATA";
  typeof Symbol != "undefined" && Symbol.toStringTag && Object.defineProperty(returnValue, Symbol.toStringTag, {value: "Module"});
  Object.defineProperty(returnValue, "__esModule", {value: true});
  Object.defineProperty(
    returnValue,
    "__COMPONENT_NAME__",
    {
      enumerable: true,
      get: () => function (props = {}) {
        Object.keys(props || {}).forEach((key) => {
          const curKey = key.replace(/[-\\.]/g, '');
          if (props[curKey] !== preProps[curKey]) {
            proxyCallbackMap[curKey] && proxyCallbackMap[curKey](props[curKey], outputProxy);
          }
        });
        preProps = { ...props };
        latestProps = props;
        if (!inputProxy) {
          inputProxy = new Proxy(
            preProps,
            {
              ownKeys(target) {
                return Object.keys(target);
              },
              get(target, key) {
                const curKey = key.replace(/[-\\.]/g, '');
                if (curKey === 'hasOwnProperty') {
                  target[curKey] = k => k in target;
                } else {
                  target[curKey] = cb => {
                    proxyCallbackMap[curKey] = cb;
                    cb(latestProps[curKey], outputProxy);
                  };
                }

                return target[curKey];
              }
            }
          );
        }
        if (!outputProxy) {
          outputProxy = new Proxy(
            preProps,
            {
              ownKeys(target) {
                return Object.keys(target);
              },
              get(target, key) {
                const curKey = key.replace(/[-\\.]/g, '');
                if (curKey === 'hasOwnProperty') {
                  target[curKey] = k => k in target;
                } else {
                  target[curKey] = value => {
                    latestProps[curKey] && latestProps[curKey](value);
                  };
                }

                return target[curKey];
              }
            }
          );
        }
        var comDefs = {};
        "__COM_DEFS_CODE__"
        var RenderCom = "__CLOUD_COMPONENT_RUNTIME__";

        return RenderCom({
          inputs: inputProxy,
          outputs: outputProxy,
          data: initData,
          env: {
            renderCom: (json, opts) => props.render(json, { env: {}, comDefs, ...(opts || {}) })
          }
        });
      }
    }
  );
  return returnValue;
});