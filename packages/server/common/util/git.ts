/** 拉取、更新项目 */
import fs from 'fs';
import path from 'path';
import child_process from 'child_process';
import { GIT_TOKEN } from '../const';

export const cloneProject = (option: { path: string; gitUrl: string; }) => {
	const { path: rootPath, gitUrl } = option;
	let currentGitUrl = gitUrl
		.replace(/\/+$/g, '')
		.replace(/[?|#].*$/, '');

	if (currentGitUrl.startsWith('https://') && !currentGitUrl.endsWith('.git')) {
		currentGitUrl += '.git';
	} else if (currentGitUrl.startsWith('git@')) {
		currentGitUrl = currentGitUrl.replace('*************************:', 'https://git.corp.kuaishou.com/');
	}

	const folderName = currentGitUrl
		.replace('https://git.corp.kuaishou.com', '.')
		.replace('*************************:', './')
		.replace(/\.git$/, '');
	const namespace = getNamespaceFromGitUrl(currentGitUrl);
	// 判断是否存在项目目录
	if (fs.existsSync(path.join(rootPath, folderName))) {
		// 存在则更新项目
		const log = child_process.execSync(
			'git fetch origin',
			{ cwd: path.join(rootPath, folderName) }
		);
		console.log(`项目 ${namespace} 拉取日志：${Buffer.from(log).toString('utf8')}`);
	} else {
		const parentPath = path.join(rootPath, folderName.split('/').slice(0, -1).join('/'));
		fs.mkdirSync(parentPath, { recursive: true });
		// 不存在则拉取项目
		const log = child_process.execSync(
			`git clone https://oauth2:${GIT_TOKEN}@${currentGitUrl.replace('https://', '')}`,
			{ cwd: parentPath }
		);
		console.log(`项目 ${namespace} 初始化日志：${Buffer.from(log).toString('utf8')}`);
	}
};

export function extractGitRepoPath(url: string): string | null {
	// 匹配 git URL 的正则表达式，支持以下格式：
	// https://github.com/user/repo.git
	// https://github.com/user/repo
	// git://github.com/user/repo.git
	// git://github.com/user/repo
	// **************:user/repo.git
	// **************:user/repo
	const regex = /(?:.+:\/\/|git@)([^/]+)[/:]([^/]+)\/(.*?)(?:.git)?$/;
	const match = url.match(regex);
	// 如果匹配成功，返回路径信息
	if (match) {
		return match[2] + '/' + match[3];
	}
	// 如果匹配失败，返回 null
	return null;
}

/**
 *  提取 git 文件夹名，支持以下三种类型
 *  https://git.corp.kuaishou.com/plateco-dev-fe/kwaishop-tech/kpro-kp-BaseInfo.git
 *  https://git.corp.kuaishou.com/plateco-dev-fe/kwaishop-tech/kpro-kp-BaseInfo
 *  *************************:plateco-dev-fe/kwaishop-tech/form-platform.git
 */

export const getNamespaceFromGitUrl = (url: string) => {
	const match = url.replace(/\/+$/g, '').replace(/.git$/, '').match(/[\\/:]([^\\/:]+)$/);

	return match[1];
};