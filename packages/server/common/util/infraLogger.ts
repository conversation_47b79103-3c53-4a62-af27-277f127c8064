import { Logger } from "@infra-node/logger";
import { isProd, isStaging } from ".";

function infraLoggerFactory() {
  let infraLogger;

  return {
    getInstance() {
      if (!infraLogger) {
        infraLogger = new Logger({
          appName: "fangzhou-material-middleoffice",
          namespace: "node.perf.fangzhou-material-middleoffice",
          isConsole: !isStaging() || !isProd(),
        });
      }
      return infraLogger;
    },
  };
}

const infraLoggerSingleton = infraLoggerFactory();
let infraLogger: Logger = infraLoggerSingleton.getInstance();
export { infraLogger };
