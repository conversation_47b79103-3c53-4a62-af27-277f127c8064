(() => async function convert(material, config) {
  const EditorTypeEnum = {
    Text: 'Text',
    Switch: 'Switch',
    Event: '_Event',
    Number: 'Number',
    Select: 'Select',
    Icon: 'Icon',
    Code: 'Code'
  };
  const SchemaEnum = {
    Number: { type: 'number' },
    String: { type: 'string' },
    Boolean: { type: 'boolean' },
    Array: { type: 'array', items: { type: 'any' } }
  }
  const addInput = (inputs, { comments, id, useIO, ...res }) => {
    inputs.push({ id, title: !useIO ? `设置${comments || id}` : comments || id, useIO, ...res });
  };
  const addOutput = (outputs, { comments, id, ...res }) => {
    outputs.push({ id, title: comments || id, ...res });
  };
  const addSlot = (slots, { comments, id }) => {
    slots.push({ id, title: comments || id });
  };
  const addEditor = (editor_list, { comments, id, type, options }) => {
    const temp = { key: id, type, title: comments || id };
    if (options) {
      temp.options = options;
    }
    editor_list.push(temp);
  };
  const getOptionsFromMembers = members => {
    const res = [];
    if (Array.isArray(members)) {
      members.forEach((item) => {
        if (item.value !== undefined) {
          res.push({ label: item.value, value: item.value });
        }
      });
    }

    return res;
  };
  const TSTypeReferenceCase = (item) => {
    const {
      identifier: id,
      comments,
      interfaceName,
      typeName,
      schema
    } = item;
    switch (typeName) {
      case 'TIcon':
        addEditor(newContent.editor_list,{ comments, id, type: EditorTypeEnum.Icon });
        break;
      case 'json':
      case 'java':
      case 'javascript':
        addEditor(newContent.editor_list, {
          comments,
          id,
          type: EditorTypeEnum.Code,
          options: {
            theme: 'light',
            title: comments || id,
            language: typeName,
            width: 600,
            minimap: { enabled: false },
          },
        });
        break;
      default:
        break;
    }
    switch (interfaceName) {
      case 'React.ReactNode':
      case 'ReactNode':
        if (typeName === 'TIcon') {
          break;
        }
        addSlot(newContent.slots, { id, comments });
        break;
      default:
        addInput(newContent.inputs, { comments, id, schema });
        break;
    }
  };
  const functionCase = (item) => {
    const { identifier: id, comments, defaultValue, schema } = item;
    if (/^on.+$/.test(id)) {
      addOutput(newContent.outputs, { comments, id, schema });
      addEditor(newContent.editor_list, { comments, id, type: EditorTypeEnum.Event });
    } else {
      addEditor(newContent.editor_list, {
        comments,
        id,
        type: EditorTypeEnum.Code,
        options: {
          theme: 'light',
          title: comments || id,
          language: 'javascript',
          width: 600,
          minimap: { enabled: false },
        },
      });
      newContent.data[id] = defaultValue || encodeURIComponent('(...arg) => {\n  return arg\n}');
    }
  };

  const {
    schema,
    preview_img,
    version,
    namespace,
    title,
    readme,
    description,
    git_url,
    platform,
    meta
  } = material;
  const { business, creatorId, creatorName, axios } = config;

  const newMaterial = {
    namespace: `${namespace}(${business})`,
    title,
    readme,
    description,
    git_url,
    platform,
    preview_img,
    version,
    meta,
    schema,
    creator_id: creatorId
  };
  const newContent = {
    title: newMaterial.title,
    namespace: newMaterial.namespace,
    version: newMaterial.version,
    description: newMaterial.description,
    author: creatorId,
    author_name: creatorName,
    data: {},
    runtime: '',
    editor_list: [],
    outputs: [],
    inputs: [],
    slots: [],
  };

  schema.props.forEach((item) => {
    const { identifier: id, comments, type, defaultValue, members, schema } = item;
    if (defaultValue !== undefined && defaultValue !== '') {
      newContent.data[id] = defaultValue;
    }
    if (id === 'value') {
      const inputId = `_get${id}`;
      addInput(newContent.inputs,{
        comments: `读取${comments || id}`,
        id: inputId,
        useIO: true,
        targetKey: id,
        rels: [inputId],
      });
      addOutput(newContent.outputs,{ comments: `输出${comments || id}`, id: inputId, useIO: true });
    }

    switch (type) {
      case 'TSTypeReference': TSTypeReferenceCase(item); break;
      case 'union':
        const options = getOptionsFromMembers(members);
        if (options.length) {
          addEditor(newContent.editor_list, { comments, id, type: EditorTypeEnum.Select, options });
        }
        addInput(newContent.inputs, { comments, id, schema: schema ?? SchemaEnum.Array });
        break;
      case 'array':
        addInput(newContent.inputs, { comments, id, schema: schema ?? SchemaEnum.Array });
        break;
      case 'string':
        addEditor(newContent.editor_list, { comments, id, type: EditorTypeEnum.Text });
        addInput(newContent.inputs, { comments, id, schema: schema ?? SchemaEnum.String });
        break;
      case 'number':
        addEditor(newContent.editor_list,{  comments, id, type: EditorTypeEnum.Number });
        addInput(newContent.inputs, { comments, id, schema: schema ?? SchemaEnum.Number });
        break;
      case 'boolean':
        addEditor(newContent.editor_list, { comments, id, type: EditorTypeEnum.Switch });
        addInput(newContent.inputs, { comments, id, schema: schema ?? SchemaEnum.Boolean });
        break;
      case 'function': functionCase(item); break;
      default: addInput(newContent.inputs, { comments, id, schema }); break;
    }
  });

  const { description: oDescription, author, author_name, editor_list, ...comConfig } = newContent;
  const IconKeys = (editor_list || []).filter(item => item.type === EditorTypeEnum.Icon).map(item => item.key);
  const { data: runtime } = await axios.get(schema.assetDownloadUrl);
  const isMultiComponent = schema.componentBundleType !== 'SLMC';
  const runtimeHocString = `
    const runtimeHOC = (comDef, comp, IconKeys) => ({ env, data, style, inputs, outputs, slots }) => {
      const { edit, runtime } = env;
      const debug = !!(runtime && runtime.debug);
      const [mount, setMount] = React.useState(false);
      const [rData, setRData] = React.useState({});

      const tempData = {};
      const codeProps = {};
      (comDef.editors || [])
        .filter(({ type }) => type === "Code")
        .forEach(({ key }) => {
          try {
            const jsCode = Babel.transform(decodeURIComponent(data[key]), {
              presets: ['env', 'react'],
              comments: false,
            });
            codeProps[key] = eval(jsCode.code);
          } catch (error) {
            console.error(error)
            codeProps[key] = () => { };
          }
        });
      (IconKeys || []).forEach((key) => {
        if (data[key] && window["icons"][data[key]]) {
          tempData[key] = React.createElement(window["icons"][data[key]]);
        }
      });

      const init = () => {
        (comDef.outputs || [])
          .filter((item) => !item.useIO)
          .forEach((item) => {
            const { id } = item;
            tempData[id] = (...arg) => {
              if (env.runtime && outputs[id]) {
                if (!!arg && arg.length > 1) {
                  outputs[id](arg);
                } else {
                  outputs[id](arg.length > 0 ? arg[0] : void 0);
                }
              }
            };
          });
        (comDef.slots || []).forEach((item) => {
          const { id } = item;
          if (slots[id]) {
            if (env.runtime && slots[id].size > 0) {
              tempData[id] = slots[id].render();
            }
            if (env.edit) {
              tempData[id] = slots[id].render();
            }
          }
        });
        (comDef.inputs || [])
          .filter((item) => !!item.useIO)
          .forEach((item) => {
            const { id, targetKey } = item;
            inputs[id] &&
              inputs[id](() => {
                if (outputs[id]) {
                  outputs[id](rData[targetKey]);
                }
              });
          });
      };
      init();
      React.useEffect(() => {
        if (runtime) {
          (comDef.inputs || [])
            .filter((item) => !item.useIO)
            .forEach((item) => {
              const { id } = item;
              inputs[id] &&
                inputs[id]((res) => {
                  setRData((oldVal) => {
                    if (oldVal.hasOwnProperty(id) && oldVal[id] === res) {
                      return oldVal;
                    }
                    return {
                      ...oldVal,
                      [id]: res,
                    };
                  });
                });
            });
        }
      }, [rData]);

      React.useEffect(() => {
        if ((debug || edit) && window["m-ui"] && window["m-ui"].Modal) {
          window["m-ui"].Modal.defaultProps.getContainer = () => {
            return env.canvasElement || document.body;
          };
        }
        setMount(true);
      }, []);

      if (style && style.display === "none") {
        return null;
      }

      return mount
        ? React.createElement(comp, {
          ...data,
          ...tempData,
          ...rData,
          ...codeProps,
        })
        : null;
    };
  `;
  const getRealLibString = `
    function getRealLib(lib) {
      function isMemo(temp) {
        return !!(temp && temp.$$typeof);
      }
      if (typeof lib === 'function' || isMemo(lib)) {
        return lib;
      }
      if (lib && (typeof lib.default === 'function' || isMemo(lib.default))) {
        return lib.default;
      }
      return lib[Object.keys(lib)[0]];
    }
  `;
  newContent.runtime = encodeURIComponent(`
    ${getRealLibString}
    ;${runtime};
    const lib = window['${schema.libraryName}']${isMultiComponent ? `['${schema.componentName}']` : ''};
    const sourceLib = getRealLib(lib);
    const comDef = ${JSON.stringify(comConfig)};
    ${runtimeHocString}
    window.fangzhouComDef = {
      default: runtimeHOC(comDef, sourceLib, ${JSON.stringify(IconKeys)})
    };
  `);
  newContent.editor = encodeURIComponent(`
    function createEditors(editList, rootTitle, extraConfig) {
      const defaultEditTabs = [
        { title: rootTitle, keyList: (editList || []).map((item) => item.key) },
      ];
      const editTabs = extraConfig
        ? extraConfig.editTabs || defaultEditTabs
        : defaultEditTabs;
    
      function getEditor(item) {
        const edits = [];
        const { key, type, title, options } = item;
        switch (type) {
          case "_Event":
            edits.push({
              title,
              type,
              options: () => {
                return {
                  outputId: key,
                };
              },
            });
            break;
          case "Number":
            edits.push({
              title,
              type: "Text",
              options: { type: "number" },
              value: {
                get({ data }) {
                  return data[key];
                },
                set({ data }, result) {
                  const val = parseInt(result, 10);
                  data[key] = isNaN(val) ? undefined : val;
                },
              },
            });
            break;
          default:
            edits.push({
              title,
              type,
              options,
              value: {
                get({ data }) {
                  return data[key];
                },
                set({ data }, result) {
                  data[key] = result;
                },
              },
            });
            break;
        }
        return edits[0];
      }
    
      return {
        ":root": ({}, ...cateList) => {
          (editTabs || []).forEach((cateInfo, idx) => {
            cateList[idx].title = cateInfo.title;
            cateList[idx].items = cateInfo.keyList
              .map((temp) => {
                const { key, ...customConfig } =
                  typeof temp === "object" ? temp : { key: temp };
                const item = editList.find((item) => item.key === key);
                if (item) {
                  return { ...getEditor(item), ...customConfig };
                }
                return null;
              })
              .filter((item) => !!item);
          });
          return { title: rootTitle };
        },
      };
    }
    const upgradeHOC = (comDef) => ({ data, input, output, slot }) => {
      (comDef.inputs || []).forEach((item) => {
        const { id, title } = item;
        if (id && !input.get(id)) {
          input.add(id, title, { type: "any" });
        }
      });
      (comDef.outputs || []).forEach((item) => {
        const { id, title } = item;
        if (id && !output.get(id)) {
          output.add(id, title, { type: "any" });
        }
      });
      (comDef.slots || []).forEach((item) => {
        const { id, title } = item;
        if (id && !slot.get(id)) {
          slot.add(id, title);
        }
      });
      Object.keys(comDef.data || {}).forEach((key) => {
        if (!data.hasOwnProperty(key)) {
          data[key] = comDef.data[key];
        }
      });
  
      return true;
    }
    const comDef = ${JSON.stringify({ ...comConfig })};
    comDef.editors = createEditors(comDef.editors || [], comDef.title, ${JSON.stringify(schema.extraConfig)});
    comDef.upgrade = upgradeHOC(comDef);
    ${runtimeHocString}
    ${getRealLibString}
    ;${runtime};
    const lib = window['${schema.libraryName}']${isMultiComponent ? `['${schema.componentName}']` : ''};
    const sourceLib = getRealLib(lib);
    comDef.runtime = runtimeHOC(comDef, sourceLib, ${JSON.stringify(IconKeys)});
    window.fangzhouComDef = { default: comDef };
  `);

  return { ...newMaterial, content: newContent, schema }
})();