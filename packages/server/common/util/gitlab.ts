import axios from 'axios';
import { extractGitRepoPath } from './git';
const temp = Buffer.from('YWNjZXNzX3Rva2VuPTh6UU53YjdrVmRkNzFpTVR6OEpv', 'base64').toString();
const BASE_URL = 'https://git.corp.kuaishou.com';

export function queryRepoInfoByUrl(url: string) {
	// const path = extractGitRepoPath(url);//
	// 地址1：*************************:plateco-dev-fe/kwaishop-cps/kwaishop-cps-vue-to-react-pc.git
	// 地址2：https://git.corp.kuaishou.com/plateco-dev-fe/kwaishop-cps/kwaishop-cps-vue-to-react-pc.git
	let path;
	if(url.indexOf('git@') !== -1) {
		path = url.substring(url.indexOf(':') + 1, url.indexOf('.git'));
	} else if(url.indexOf('https://') !== -1) {
		path = url.split('https://git.corp.kuaishou.com/')[1].split('.git')[0];
	}
	// console.log('111', path);
	return new Promise((resolve, reject) => {
		const addr = `${BASE_URL}/api/v4/projects/${encodeURIComponent(path)}?${temp}`;
		axios.get(addr).then((data) => {
			resolve(data.data);
		}).catch(err => {
			if(err.response.status === 404) {
				console.log('当前项目未找到：', url);
				resolve(null);
			} else {
				reject(err);
			}
		});
	});
}
