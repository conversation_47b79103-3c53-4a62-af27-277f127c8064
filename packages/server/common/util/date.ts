import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');

/** 获取上一周起止时间 */
export const getPreWeekRange = (initDate?: AnyType) => {
	const startOfLastWeek = dayjs(initDate).add(-1, 'week').startOf('week'); // 获取上一周的起始时间
	const endOfLastWeek = dayjs(initDate).add(-1, 'week').endOf('week'); // 获取上一周的结束时间

	return { start: startOfLastWeek.valueOf(), end: endOfLastWeek.valueOf() };
};

/** 获取当天的起止时间 */
export const getPreDayRange = (initDate?: AnyType) => {
	const startOfLastDay = dayjs(initDate).add(-1, 'day').startOf('day'); // 获取上一周的起始时间
	const endOfLastDay = dayjs(initDate).add(-1, 'day').endOf('day'); // 获取上一周的结束时间

	return { start: startOfLastDay.valueOf(), end: endOfLastDay.valueOf() };
};

export function formatTime(timestamp: number): string {
	const time = dayjs(timestamp);
	return time.format('YYYYMMDD HH:mm:ss');
}

export const getOneDayRange = (initDate: string) => {
	const startOfDay = dayjs(initDate).startOf('day').valueOf();
	const endOfDay = dayjs(initDate).endOf('day').valueOf();

	return { start: startOfDay, end: endOfDay };
};

/**
 * 获取指定日期范围的时间戳列表（按天维度，每一天的0点）
 * @param startDate 
 * @returns 
 */
export function generateTimestamps(startDate: number | string | Date, endDate?: number | string | Date) {
	const timestamps = [];
	const start = new Date(startDate);
	const end = endDate ? new Date(endDate) : new Date();

	start.setHours(0, 0, 0, 0);
	end.setHours(0, 0, 0, 0);

	for (let date = start; date <= end; date.setDate(date.getDate() + 1)) {
		timestamps.push(date.getTime());
	}

	return timestamps;
}