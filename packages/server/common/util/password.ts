import { isProd } from './env';

type StringOrObjectString = string | { [key: string]: string };
/** 将Base64编码的字符串转换回普通字符串*/
export function decodeFromBase64(input: StringOrObjectString) {
	try {
		if (typeof input === 'object') {
			const result = {};
			Object.keys(input).forEach((key) => {
				result[key] = decodeFromBase64(input[key]);
			});
			return result;
		} else {
			// 如果输入是字符串，直接解码
			return Buffer.from(input, 'base64').toString('utf-8');
		}
	} catch (e) {
		console.error('Error decoding from Base64', e);
	}
}

/** 将普通字符串成转换Base64编码的字符串*/
export function encodeToBase64(input) {
	try {
		if (typeof input === 'object') {
			const result = {};
			Object.keys(input).forEach((key) => {
				result[key] = encodeToBase64(input[key]);
			});
			return result;
		} else {
			// 如果输入是字符串，直接解码
			return Buffer.from(input, 'utf-8').toString('base64');
		}
	} catch (e) {
		console.error('Error decoding from Base64', e);
	}
}

// 获取mysql的账密信息
export function getMySQLHost(): string {
	return decodeFromBase64(
		!isProd()
			? 'cHVibGljLXhtLWQtY2RzLXN0YWdpbmctbm9kZTI4LmlkY2hiMWF6MS5oYjEua3dhaWRjLmNvbQ==' // 开发环境
			: 'Y2x1c3RlcjAxLnByb3h5c3FsLmtzcWwuaW50ZXJuYWw=', // 正式环境,
	) as string;
}

export function getMySQLUser(): string {
	return decodeFromBase64(
		!isProd() ? 'Z2lmc2hvd18xNTMyOF92MV9ydw==' : 'Z2lmc2hvd18xMzAwMF92MV9ydw==',
	) as string;
}

export function getMySQLPassword(): string {
	return decodeFromBase64(
		!isProd()
			? 'S3YxclUxaW1kcGdHZVpTQWhUTUxRenNWeWpheDV2WW8='
			: 'S3YxOXZMckhoOGNRMFNvZDZtWUdwSUZDdDNKd2t1S3o=',
	) as string;
}

export function getMySQLPort(): string {
	return decodeFromBase64(!isProd() ? 'MTUzMjg=' : 'NjAzMg==') as string;
}

// 获取cdn配置
export function getCDNOrigin(): string {
	return decodeFromBase64(
		!isProd()
			? 'aHR0cHM6Ly9rY2RuLmNvcnAua3VhaXNob3UuY29t' //开发环境
			: 'aHR0cDovL2tjZG4uaW50ZXJuYWw=', //测试环境
	) as string;
}

export function getCDNToken(): string {
	return decodeFromBase64(
		!isProd()
			? 'MTIzMzNfYzNmY2NlNzA3ZmIxZTIxOGZlY2EyNTEwY2ZmOWQyYzU='
			: 'MTIzMzNfYzNmY2NlNzA3ZmIxZTIxOGZlY2EyNTEwY2ZmOWQyYzU=',
	) as string;
}

export function getCDNPid(): string {
	return decodeFromBase64(
		!isProd() ? 'a3dhaXNob3Atd2ViLWRldg==' : 'a3dhaXNob3Atd2ViLWRldg==',
	) as string;
}
