import path from 'path';
import fs from 'fs';
import {
	encodeTo<PERSON><PERSON><PERSON>,
	get<PERSON><PERSON><PERSON><PERSON><PERSON>,
	getCD<PERSON><PERSON>,
	getCDNToken,
	getMySQLHost,
	getMySQLPassword,
	getMySQLPort,
	getMySQLUser,
} from '../util/password';

export * from './material';

export const TIMEOUT_TIME = 60 * 1000;
export const PAGE_NUM = 1;
export const PAGE_SIZE = 20;
export const GIT_TOKEN = Buffer.from(
	'VVhtUFMxZXhVVU43N3VqbVR5UDE=',
	'base64',
).toString();

if (!fs.existsSync(path.resolve(__dirname, '../../../../../scan'))) {
	fs.mkdirSync(path.resolve(__dirname, '../../../../../scan'));
}
if (!fs.existsSync(path.resolve(__dirname, '../../../../../scan/projects'))) {
	fs.mkdirSync(path.resolve(__dirname, '../../../../../scan/projects'));
}
export const PROJECT_PATH = path.resolve(
	__dirname,
	'../../../../scan/projects',
);

if (!fs.existsSync(path.resolve(__dirname, '../../../../../scan/logs'))) {
	fs.mkdirSync(path.resolve(__dirname, '../../../../../scan/logs'));
}
export const SCAN_LOGS_PATH = path.resolve(__dirname, '../../../../../scan/logs');

export const LEVEL_MAP = {
	L1: 1,
	L2: 2,
	L3: 3,
	L4: 4,
	L5: 5,
	'L5+': 5.5,
};

// mysql密码等配置
export const MYSQL_PASSWORD = {
	dbType: 'MYSQL',
	database: 'gifshow',
	host: getMySQLHost(),
	user: getMySQLUser(),
	password: getMySQLPassword(),
	port: getMySQLPort(),
};

export const CDN_CONFIG = {
	origin: getCDNOrigin(),
	token: getCDNToken(),
	pid: getCDNPid(),
};
