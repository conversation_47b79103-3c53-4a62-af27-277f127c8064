/** 物料类型，组件库/组件 */
export enum MaterialType {
  /** 组件库 */
  COM_LIB = 'com_lib',
  /** 组件 */
  COMPONENT = 'component',
}

/** 生效状态 */
export enum EffectStatus {
  /** 删除 */
  DELETE = -1,
  /** 禁用 */
  DISABLED = 0,
  /** 生效中 */
  EFFECT = 1,
}

/** BU 枚举 */
export enum Business {
  /** 方舟 */
  FANG_ZHOU = 'fangzhou',
  /** 本地生活 */
  LOCAL_LIFE = 'locallife',
  /** 本地生活客户端 */
  LOCAL_LIFE_CLIENT = 'locallifeClient',
  /** 电商前端 */
  ES = 'es',
  /** 海外前端 */
  KWAI_ES = 'kwaies',
  /** 商业化 */
  BIZ = 'biz',
  /** 千象 */
  KAEL = 'kael',
  /** 测试 */
  TEST = 'test',
}

/** 物料被使用的引用类型 */
export enum RefType {
  REFER = 'refer'
}

/** 报表数据类型 */
export enum ReportType {
  /** 北极星指标 */
  POLAR_STAR = 'polar_star',
  /** 页面覆盖率 */
  PAGE_COVERAGE_RATE = 'page_coverage_rate',
  /** 新增页面覆盖率 */
  NEW_PAGE_COVERAGE_RATE = 'new_page_coverage_rate',
  /** 项目覆盖率 */
  PROJECT_COVERAGE_RATE = 'project_coverage_rate',
  /** 成本效益 */
  COST_EFFECTIVENESS = 'cost_effectiveness',
  /** 复用率 */
  REUSE_RATE = 'reuse_rate'
}

/** 低代码指标类型 */
export enum LowCodeBuildReportType {
  /** 复杂度等级 */
  LEVEL = 'level',
  /** 搭建时长 */
  BUILD_TIME = 'build_time',
}

/** 物料操作变更类型 */
export enum MaterialOperateAction {
  /** 新建物料 */
  CREATE = 'create',
  /** 发布新版本 */
  PUBLISH = 'publish',
  DELETE = 'delete',
  UPDATE = 'update',
}

/** 新版本报表数据类型 */
export enum NewReportType {
  /** 新增代码复用率 */
  NEW_CODE_REUSE_RATE = 'newCodeReuseRate',
  /** 代码复用率 */
  CODE_REUSE_RATE = 'codeReuseRate',
  /** 页面覆盖率 */
  PAGE_REUSE_RATE = 'pageReuseRate',
  /** 新增页面覆盖率 */
  NEW_PAGE_REUSE_RATE = 'newPageReuseRate',
  /** 项目覆盖率 */
  PROJECT_REUSE_RATE = 'projectReuseRate',
}