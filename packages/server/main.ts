import { NestFactory } from "@nestjs/core";
import { NestExpressApplication } from "@nestjs/platform-express";
import cookieParser from "cookie-parser";
import compression from "compression";
import bodyParser from "body-parser";
import { Logger } from "@mybricks/rocker-commons";
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import * as swStats from '@yingpengsha/swagger-stats';
import cors from "cors";

import IndexModule from "./index.module";
import healthMiddleware from "./middleware/health.middleware";
import CustomLogger from "./service/logger/logger";
import { initLogger } from "./common/util";
import { globalLogic } from "./init";
import { registerMiddleware } from "./middleware/register.middleware";
import { staticMiddleware } from "./middleware/static.middleware";
import { ProxyV2Middleware } from "./middleware/proxy.v2.middleware";
import { join } from "path";

async function bootstrap() {
  try {
    console.log('[Server] 开始初始化服务器...');
    initLogger();
    console.log('[Server] 日志系统初始化完成');

    await globalLogic();
    console.log('[Server] 全局逻辑初始化完成');

    const app = await NestFactory.create<NestExpressApplication>(IndexModule, {
      logger: new CustomLogger(),
    });
    console.log('[Server] NestJS 应用创建完成');

    app.use(bodyParser.json({ limit: "100mb" }));
    app.use(healthMiddleware);
    app.use(staticMiddleware);

    app.use(
      cors({
        origin: true,
        methods: "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS",
        allowedHeaders:
        "X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Observe",
        credentials: true,
      }),
    );

    app.use(cookieParser());
    app.use(compression());
    app.use(registerMiddleware);

    // ======================== swagger ========================
    const config = new DocumentBuilder()
      .setTitle('v1 APIs')
      .setDescription('v1 APIs')
      .setVersion('1.0')
      .build()

    const document = SwaggerModule.createDocument(app, config)
    SwaggerModule.setup('/api/v1/docs', app, document)
    console.log('[Server] Swagger 文档初始化完成');

    // ======================== 监控面板 ========================
    // 合并 v1 v2 swagger json
    const v2Document = await ProxyV2Middleware.v2SwaggerJSON();
    const mergedDocument = {
      ...document,
      paths: {
        ...document.paths,
        ...v2Document.paths,
      },
      components: {
        schemas: {
          ...document.components?.schemas,
          ...v2Document.components?.schemas,
        }
      },
    }
    app.use(swStats.getMiddleware({
      swaggerSpec: mergedDocument,
      uriPath: '/api/stats',
      version: '1.0.0',
      storeFilePath: join(__dirname, '../../logs/swagger-stats.json')
    }));
    console.log('[Server] 监控面板初始化完成');

    const port =
      process.env?.[process.env.port_name] || process.env?.AUTO_PORT0 || 8080;
    console.log(`[Server] 准备启动服务器，端口: ${port}`);
    Logger.info(`[server start] listen: ${port}`);
    await app.listen(port);
    console.log(`[Server] 服务器启动成功，监听端口: ${port}`);
  } catch (error) {
    console.error('[Server] 服务器启动失败:', error);
    Logger.error(`[server start] error: ${error?.message}`);
    Logger.error(`[server start] error stack: ${error?.stack?.toString()}`);
    throw error;
  }
}

try {
  bootstrap();
} catch (e) {
  console.error('[Server] 服务器启动过程中发生未捕获的异常:', e);
  Logger.error(`[server start] error: ${e?.message}`);
  Logger.error(`[server start] error stack: ${e?.stack?.toString()}`);
  process.exit(1);
}
