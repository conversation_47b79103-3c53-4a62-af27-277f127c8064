{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "esModuleInterop": true,
    "emitDecoratorMetadata": true,
    "resolveJsonModule": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2017",
    "rootDir": ".",
    "baseUrl": ".",
    "incremental": true,
    "skipLibCheck": true,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "@global-material-middleoffice/server-v2/shared": ["../server.v2/src/shared.ts"],
      "@global-material-middleoffice/server-v2/*": ["../server.v2/src/*"]
    }
  },
}
