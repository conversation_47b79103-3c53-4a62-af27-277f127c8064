<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="analysis_raw_data">
    <select id="queryByScanIdAndType">
        select
            *
        from analysis_raw_data
        where status = 1 and scan_id = #{scanId} and type = #{type}
    </select>
    <insert id="create">
        insert into analysis_raw_data (
            id,
            scan_id,
            business,
            type,
            repo_project_id,
            content,
            create_time,
            status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{scan_id},
            #{business},
            #{type},
            #{repo_project_id},
            #{content},
            #{create_time},
            #{status}
        </trim>
    </insert>

    <select id="queryByScanId">
        select
            *
        from analysis_raw_data
        where status = 1 and scan_id = #{scanId}
    </select>

    <select id="queryByScanIdAndBusinessList">
        select
            *
        from analysis_raw_data
        where status = 1 and scan_id = #{scanId}
        and business in
        <foreach collection="businessList" item="business" open="(" separator="," close=")" index="index">
            #{business}
        </foreach>
    </select>

    <update id="deleteByScanId">
        update analysis_raw_data set
        status=-1
        where scan_id = #{scanId} and status=1
    </update>

    <update id="deleteByScanIdAndBusinessList">
        update analysis_raw_data set
        status = -1
        where scan_id = #{scanId} and status = 1 and business in
        <foreach collection="businessList" item="business" open="(" separator="," close=")" index="index">
            #{business}
        </foreach>
        and type in
        <foreach collection="repoRange" item="type" open="(" separator="," close=")" index="index">
            #{type}
        </foreach>
    </update>
</mapper>