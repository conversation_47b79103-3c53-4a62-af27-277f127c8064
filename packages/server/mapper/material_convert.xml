<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_convert">
  <insert id="create">
    insert into material_convert (
      id,
      material_id,
      version,
      business,
      script,
      bundle,
      result,
      reason,
      creator_id,
      create_time,
      status
    )
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id},
      #{material_id},
      #{version},
      #{business},
      #{script},
      #{bundle},
      #{result},
      #{reason},
      #{creator_id},
      #{create_time},
      #{status}
    </trim>
  </insert>

  <update id="delete">
    update material_convert set status = -1 where id = #{id}
  </update>

  <update id="update">
    update material_convert set
    <if test="bundle != undefined">bundle = #{bundle},</if>
    <if test="result != undefined">result = #{result},</if>
    <if test="reason != undefined">reason = #{reason},</if>
    id = #{id}
    where id = #{id}
  </update>

  <select id="detail">
    SELECT * FROM material_convert WHERE id = #{id}
  </select>

  <select id="query">
    select
      material_convert.*,
      user.name as creator_name,
      user.avatar as creator_avatar,
      user.user_name as creator_username,
      material.title as material_title
    from material, material_convert left join user on material_convert.creator_id = user.id where
      material.id = material_convert.material_id
    <if test="material_id != undefined">
      and material_id = #{material_id}
    </if>
    <if test="status != undefined">
      and material_convert.status = #{status}
    </if>
    <if test="business !== undefined">
      and material.business = #{business}
    </if>
    order by material_convert.create_time desc
    LIMIT #{pageSize} OFFSET #{offset}
  </select>

  <select id="queryListTotal">
    select count(*) as total from material, material_convert where
    material.id = material_convert.material_id
    <if test="material_id != undefined">
      and material_convert.material_id = #{material_id}
    </if>
    <if test="status != undefined">
      and material_convert.status = #{status}
    </if>
    <if test="business !== undefined">
      and material_convert.business = #{business}
    </if>
  </select>
</mapper>