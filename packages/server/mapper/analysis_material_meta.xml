<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="analysis_material_meta">
    <insert id="insert">
        insert into analysis_material_meta (
            id,
            business,
            scan_id,
            project_id,
            package,
            type,
            material_name,
            variable_name,
            code_lines,
            file_path,
            third_usage,
            filter_third_usage,
            status,
            create_time
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{business},
            #{scan_id},
            #{project_id},
            #{package},
            #{type},
            #{material_name},
            #{variable_name},
            #{code_lines},
            #{file_path},
            #{third_usage},
            #{filter_third_usage},
            #{status},
            #{create_time}
        </trim>
    </insert>

    <update id="update_material_codelines">
        update analysis_material_meta set
        code_lines = #{codeLines}
        where id = #{id}
    </update>

    <update id="deleteByScanId">
        update analysis_material_meta set
        status=-1
        where scan_id = #{scanId} and status=1
    </update>

    <select id="queryMaterialMeta">
        SELECT
            *
        FROM
            analysis_material_meta
        WHERE
            status = 1
            AND scan_id = #{scanId}
        ORDER BY id DESC
    </select>

    <select id="queryByScanIdAndPackages">
        SELECT
            *
        FROM
            analysis_material_meta
        WHERE
            status = 1
            AND scan_id = #{scanId}
            AND package in
        <foreach collection="packages" item="package" open="(" separator="," close=")" index="index">
            #{package}
        </foreach>
        ORDER BY id DESC
    </select>

    <update id="deleteByScanIdAndBusinessList">
        update analysis_material_meta set
        status = -1
        where scan_id = #{scanId} and status = 1 and business in
        <foreach collection="businessList" item="business" open="(" separator="," close=")" index="index">
            #{business}
        </foreach>
    </update>

    <select id="queryMaterialMetaByPackageName">
        SELECT
            *
        FROM
            analysis_material_meta
        WHERE
            status = 1
            AND scan_id = #{scanId}
            AND package = #{packageName}
        ORDER BY id DESC
    </select>
</mapper>