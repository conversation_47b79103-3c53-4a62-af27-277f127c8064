<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_meta_value_relation">
    <insert id="create">
        insert into material_meta_value_relation (
        id,
        meta_value_id,
        material_id,
        creator_id,
        create_time,
        status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{meta_value_id},
            #{material_id},
            #{creator_id},
            #{create_time},
            #{status}
        </trim>
    </insert>

    <update id="delete">
        update material_meta_value_relation set status = -1 where id = #{id}
    </update>

    <update id="deleteByMaterialId">
        update material_meta_value_relation set status = -1 where material_id = #{id}
    </update>

    <update id="update">
        update material_meta_value_relation set
        <if test="material_id">material_id = #{material_id},</if>
        <if test="meta_value_id != undefined">meta_value_id = #{meta_value_id},</if>
        <if test="status != undefined">status = #{status},</if>
        id = #{id}
        where id = #{id}
    </update>

    <select id="detail">
        SELECT * FROM material_meta_value_relation WHERE id = #{id}
    </select>

    <select id="query">
        select * from material_meta_value_relation where
            status = #{status}
        <if test="material_id !== undefined">
            and material_id = #{material_id}
        </if>
        <if test="meta_value_id !== undefined">
            and meta_value_id = #{meta_value_id}
        </if>
        order by create_time desc
    </select>

    <select id="queryByMaterialIds">
        select * from material_meta_value_relation where
            status = 1
            and material_id in
        <foreach collection="ids" item="id" open="( " separator="," close=" )" index="index">
            #{id}
        </foreach>
        order by create_time desc
    </select>
</mapper>