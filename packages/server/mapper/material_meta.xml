<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_meta">
    <insert id="create">
        insert into material_meta (
        id,
        title,
        value,
        business,
        creator_id,
        create_time,
        meta,
        status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{title},
            #{value},
            #{business},
            #{creator_id},
            #{create_time},
            #{meta},
            #{status}
        </trim>
    </insert>

    <update id="delete">
        update material_meta set status = -1 where id = #{id}
    </update>

    <update id="update">
        update material_meta set
        <if test="title">title = #{title},</if>
        <if test="value">value = #{value},</if>
        <if test="status != undefined">status = #{status},</if>
        <if test="meta != undefined">meta = #{meta},</if>
        id = #{id}
        where id = #{id}
    </update>

    <select id="detail">
        SELECT * FROM material_meta WHERE id = #{id}
    </select>

    <select id="query">
        select
            material_meta.*,
            user.name as creator_name,
            user.avatar as creator_avatar,
            user.user_name as creator_username
        from material_meta left join user on material_meta.creator_id = user.id where
        material_meta.status = #{status}
        <if test="title !== undefined">
            and title like #{title}
        </if>
        <if test="business !== undefined">
            and business = #{business}
        </if>
        order by material_meta.create_time asc
        limit #{pageSize} offset #{offset}
    </select>

    <select id="queryListTotal">
        select count(*) as total from material_meta where
        status = #{status}
        <if test="title !== undefined">
            and title like #{title}
        </if>
        <if test="business !== undefined">
            and business = #{business}
        </if>
    </select>

    <select id="queryByBusiness">
        select * from material_meta where
        status = 1
        <if test="business !== undefined">
            and business = #{business}
        </if>
        order by create_time desc
    </select>

    <select id="queryByIds">
        select * from material_meta where
        status = 1
        AND id in
        <foreach collection="ids" item="id" open="( " separator="," close=" )" index="index">
            #{id}
        </foreach>
    </select>
</mapper>