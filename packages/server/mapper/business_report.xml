<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="business_report">
    <select id="create">
        insert into business_report
        (
        id,
        business,
        type,
        value,
        code_type,
        relation_key,
        start_time,
        end_time,
        update_time,
        status
        )
        <foreach collection="reports" item="item" open="values " separator="," close="" index="index">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.business},
                #{item.type},
                #{item.value},
                #{item.code_type},
                #{item.relation_key},
                #{item.start_time},
                #{item.end_time},
                #{item.update_time},
                #{item.status}
            </trim>
        </foreach>
    </select>

    <select id="query">
        SELECT
            *
        FROM
            business_report
        WHERE
            status = 1
            <if test="start_time">
                AND start_time = #{start_time}
            </if>
            <if test="end_time">
                AND end_time = #{end_time}
            </if>
            <if test="code_type">
                AND code_type = #{code_type}
            </if>
            <if test="business != undefined">
                AND business = #{business}
            </if>
            <if test="type">
                AND type = #{type}
            </if>
            <if test="relation_key != undefined">
                AND relation_key = #{relation_key}
            </if>
        ORDER BY id DESC
    </select>

    <update id="deleteReportByType_Business_Time">
        update business_report set
        status = -1
        where business = #{business}
        <if test="type">
            AND type = #{type}
        </if>
        AND code_type = #{code_type}
        AND start_time = #{start_time}
        AND end_time = #{end_time}
    </update>

    <update id="deleteReportByType_RelationKey_Business_Time">
        update business_report set
        status = -1
        where business = #{business}
        AND type = #{type}
        AND code_type = #{code_type}
        AND relation_key = #{relation_key}
        AND start_time = #{start_time}
        AND end_time = #{end_time}
    </update>
</mapper>