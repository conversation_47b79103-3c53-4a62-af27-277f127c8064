<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_tag">
    <insert id="create">
        insert into material_tag (
        id,
        title,
        value,
        \`order\`,
        creator_id,
        create_time,
        status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{title},
            #{value},
            #{order},
            #{creator_id},
            #{create_time},
            #{status}
        </trim>
    </insert>

    <update id="delete">
        update material_tag set status = -1 where id = #{id}
    </update>

    <update id="update">
        update material_tag set
        <if test="title != undefined">title = #{title},</if>
        <if test="order != undefined">\`order\` = #{order},</if>
        <if test="status != undefined">status = #{status},</if>
        <if test="value != undefined">value = #{value},</if>
        id = #{id}
        where id = #{id}
    </update>

    <select id="detail">
        SELECT * FROM material_tag WHERE id = #{id}
    </select>

    <select id="query">
        select
            material_tag.*,
            user.name as creator_name,
            user.avatar as creator_avatar,
            user.user_name as creator_username
        from material_tag left join user on material_tag.creator_id = user.id where
        material_tag.status = #{status}
        <if test="title !== undefined">
            and title like #{title}
        </if>
        order by material_tag.order desc
        limit #{pageSize} offset #{offset}
    </select>

    <select id="queryByValue">
        select * from material_tag where
        status = 1
        and value = #{value}
    </select>

    <select id="queryByValues">
        select * from material_tag where
        status = 1
        and value in
        <foreach collection="values" item="value" open="(" separator="," close=")" index="index">
            #{value}
        </foreach>
    </select>

    <select id="queryListTotal">
        select count(*) as total from material_tag where
        status = #{status}
        <if test="title !== undefined">
            and title like #{title}
        </if>
    </select>
</mapper>
