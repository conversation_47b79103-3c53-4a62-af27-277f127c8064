<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="analysis_new_code_lines">
    <insert id="insert">
        insert into analysis_new_code_lines (
            id,
            business,
            repo_project_id,
            start_commit_hash,
            end_commit_hash,
            new_code_lines,
            scan_id,
            status,
            create_time
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{business},
            #{repo_project_id},
            #{start_commit_hash},
            #{end_commit_hash},
            #{new_code_lines},
            #{scan_id},
            #{status},
            #{create_time}
        </trim>
    </insert>

    <update id="deleteByScanId">
        update analysis_new_code_lines set
        status=-1
        where scan_id = #{scanId} and status=1
    </update>

    <select id="queryByScanId">
        select
            id,
            business,
            repo_project_id,
            start_commit_hash,
            end_commit_hash,
            new_code_lines,
            scan_id,
            status,
            create_time
        from analysis_new_code_lines
        where scan_id = #{scanId} and status = 1
    </select>

    <update id="deleteByScanIdAndBusinessList">
        update analysis_new_code_lines set
        status = -1
        where scan_id = #{scanId} and status = 1 and business in
        <foreach collection="businessList" item="business" open="(" separator="," close=")" index="index">
            #{business}
        </foreach>
    </update>
</mapper>