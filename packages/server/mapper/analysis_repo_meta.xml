<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="analysis_repo_meta">
    <insert id="insert">
        insert into analysis_repo_meta (
            id,
            business,
            clone_url,
            project_id,
            name,
            status,
            create_time
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{business},
            #{clone_url},
            #{project_id},
            #{name},
            #{status},
            #{create_time}
        </trim>
    </insert>

    <select id="queryByUrls">
        select
            *
        from analysis_repo_meta
        where
            status = 1
            and clone_url in
        <foreach collection="urls" item="url" open="( " separator="," close=" )" index="index">
            #{url}
        </foreach>
    </select>

    <select id="queryByUrl">
        select
            *
        from analysis_repo_meta
        where
            status = 1
            and clone_url = #{url}
    </select>

    <select id="queryByBusiness">
        select
            *
        from analysis_repo_meta
        where
            status = 1
            and business = #{business}
    </select>
</mapper>