<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_pub">
    <insert id="create">
        insert into material_pub (
            id,
            material_id,
            version,
            preview_img,
            readme,
            content,
            \`schema\`,
            creator_id,
            create_time,
            status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{material_id},
            #{version},
            #{preview_img},
            #{readme},
            #{content},
            #{schema},
            #{creator_id},
            #{create_time},
            #{status}
        </trim>
    </insert>

    <update id="delete">
        update material_pub set status = -1 where id = #{id}
    </update>

    <update id="update">
        update material_pub set
        <if test="material_id !== undefined">
            material_id = #{material_id},
        </if>
        <if test="version !== undefined">
            version = #{version},
        </if>
        <if test="preview_img !== undefined">
            preview_img = #{preview_img},
        </if>
        <if test="readme !== undefined">
            readme = #{readme},
        </if>
        <if test="content !== undefined">
            content = #{content},
        </if>
        <if test="schema !== undefined">
            \`schema\` = #{schema},
        </if>
        <if test="status !== undefined">
            status = #{status},
        </if>
        id = #{id}
        where id = #{id}
    </update>

    <select id="detail">
        select
            material_pub.*,
            user.name as creator_name,
            user.avatar as creator_avatar,
            user.user_name as creator_username
        from
            material_pub,
            user
        where
            material_pub.id = #{id} and material_pub.creator_id = user.id
    </select>

    <select id="detailByNamespaceVersion">
        select
            material_pub.*
        from
            material_pub, material
        where
            material_pub.material_id = material.id
            and material.namespace = #{namespace}
            and material_pub.version = #{version}
            and material_pub.status = 1
            and material.status = 1
    </select>

    <select id="query">
        select
            material_pub.*,
            user.name as creator_name,
            user.avatar as creator_avatar,
            user.user_name as creator_username
        from material_pub left join user on material_pub.creator_id = user.id where
        1 = 1
        <if test="typeof material_id === 'number' || material_id">
            and material_id = #{material_id}
        </if>
        <if test="status != undefined">
            and material_pub.status = #{status}
        </if>
        <if test="branch == 'main'">
            and material_pub.version REGEXP '^([0-9]+)\\.([0-9]+)\\.([0-9]+)$'
        </if>
        <if test="version !== undefined">
            and material_pub.version = #{version}
        </if>
        and material_pub.status = 1
        order by material_pub.create_time desc
        <if test="pageSize">
            limit #{pageSize}
        </if>
        <if test="offset">
            offset #{offset}
        </if>
    </select>

    <select id="queryListTotal">
        select count(*) as total from material_pub, user where
        material_pub.creator_id = user.id
        <if test="typeof material_id === 'number' || material_id">
            and material_id = #{material_id}
        </if>
        <if test="status != undefined">
            and material_pub.status = #{status}
        </if>
        <if test="version !== undefined">
            and version = #{version}
        </if>
    </select>

    <select id="queryMaterialVersionsByMaterialIds" >
        SELECT id, material_id, version, preview_img
        <if test="needSchema">
            ,material_pub.schema
        </if>
        FROM material_pub
        WHERE material_id IN
        <foreach item="materialId" collection="materialIds" open="(" separator="," close=")" index="index">
            #{materialId}
        </foreach>
        <if test="branch == 'main'">
            and version REGEXP '^([0-9]+)\\.([0-9]+)\\.([0-9]+)$'
        </if>
        AND status = 1
        ORDER BY material_pub.id
    </select>

    <select id="queryLatestVersionByMaterialId" >
        SELECT *
        FROM material_pub
        WHERE material_id = #{id}
        AND status = 1
        ORDER BY material_id
        LIMIT 1
    </select>

</mapper>
