<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_tag_relation">
    <update id="deleteByMaterialId">
        update material_tag_relation set status = -1 where material_id = #{id}
    </update>

    <insert id="create">
        insert into material_tag_relation (
        id,
        material_id,
        tag_id,
        creator_id,
        create_time,
        status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{material_id},
            #{tag_id},
            #{creator_id},
            #{create_time},
            #{status}
        </trim>
    </insert>

    <select id="queryByMaterialIds">
        SELECT
        material_tag.id,
        material_tag.title,
        material_tag_relation.material_id
        FROM
        material_tag,
        material_tag_relation
        WHERE
        material_tag.id = material_tag_relation.tag_id
        AND material_tag_relation.status = 1
        AND material_tag_relation.material_id in
        <foreach collection="materialIds" item="materialId" open="( " separator="," close=" )" index="index">
            #{materialId}
        </foreach>
    </select>
</mapper>