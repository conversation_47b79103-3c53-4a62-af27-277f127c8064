<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material">
    <insert id="create">
        insert into material (
            id,
            namespace,
            version,
            title,
            description,
            type,
            business,
            domain,
            git_url,
            platform,
            creator_id,
            create_time,
            updater_id,
            update_time,
            meta,
            status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{namespace},
            #{version},
            #{title},
            #{description},
            #{type},
            #{business},
            #{domain},
            #{git_url},
            #{platform},
            #{creator_id},
            #{create_time},
            #{updater_id},
            #{update_time},
            #{meta},
            #{status}
        </trim>
    </insert>

    <update id="delete">
        update material set status = -1 where id = #{id}
    </update>

    <update id="update">
        update material set
            <if test="version != undefined">
                version = #{version},
            </if>
            <if test="title != undefined">
                title = #{title},
            </if>
            <if test="description != undefined">
                description = #{description},
            </if>
            <if test="business != undefined">
                business = #{business},
            </if>
            <if test="git_url != undefined">
                git_url = #{git_url},
            </if>
            <if test="platform != undefined">
                platform = #{platform},
            </if>
            <if test="namespace != undefined">
                namespace = #{namespace},
            </if>
            <if test="meta != undefined">
                meta = #{meta},
            </if>
            <if test="status != undefined">
              status = #{status},
            </if>
            <if test="domain !== undefined">
                domain = #{domain},
            </if>
            updater_id = #{updater_id},
            update_time = #{update_time}
        where id = #{id}
    </update>

    <select id="detail">
        SELECT * FROM material WHERE id = #{id}
    </select>

    <select id="query">
        SELECT
            material.*,
            user.name as creator_name,
            user.avatar as creator_avatar,
            user.user_name as creator_username,
            material_pub.preview_img,
            material_pub.readme,
            material_pub.updater_name,
            material_pub.updater_avatar,
            material_pub.updater_username
        <if test="tagId">
            , material_tag.title as tag_title
            , material_tag.id as tag_id
        </if>
        <if test="needSchema">
            , material_pub.schema
        </if>
        FROM
            material,
            user,
            (
                select
                    material_pub.*,
                    user.name as updater_name,
                    user.avatar as updater_avatar,
                    user.user_name as updater_username
                from
                    material_pub,
                    user
                where
                    material_pub.status = 1
                    and material_pub.creator_id = user.id
                order by material_pub.create_time desc
            ) as material_pub
        <if test="tagId">
            , material_tag
            , material_tag_relation
        </if>
        WHERE
            material.creator_id = user.id
            AND material.id = material_pub.material_id
            AND material.version = material_pub.version
            AND material.status = #{status}
        <if test="type">
            AND type = #{type}
        </if>
        <if test="platform !== undefined">
            AND platform REGEXP #{platform}
        </if>
        <if test="business">
            AND business = #{business}
        </if>
        <if test="tagId">
            AND material_tag.id = #{tagId}
            AND material_tag.id = material_tag_relation.tag_id
            AND material_tag_relation.material_id = material.id
        </if>
        <if test="keyword">
            AND (material.title LIKE #{keyword} OR material.description LIKE #{keyword} OR material.namespace LIKE #{keyword})
        </if>
        ORDER BY material.update_time DESC
        LIMIT #{pageSize} OFFSET #{offset};
    </select>

    <select id="queryListTotal">
        SELECT
            COUNT(*) as total
        FROM
        material,
        user,
        (
            select
                material_pub.*,
                user.name as updater_name,
                user.avatar as updater_avatar
            from
                material_pub,
                user
            where
                material_pub.status = 1
                and material_pub.creator_id = user.id
            order by material_pub.create_time desc
        ) as material_pub
        <if test="tagId">
            , material_tag
            , material_tag_relation
        </if>
        WHERE
            material.creator_id = user.id
            AND material.id = material_pub.material_id
            AND material.version = material_pub.version
            AND material.status = #{status}
        <if test="type">
            AND type = #{type}
        </if>
        <if test="platform">
            AND platform REGEXP #{platform}
        </if>
        <if test="business">
            AND business = #{business}
        </if>
        <if test="tagId">
            AND material_tag.id = #{tagId}
            AND material_tag.id = material_tag_relation.tag_id
            AND material_tag_relation.material_id = material.id
        </if>
        <if test="keyword">
            AND (material.title LIKE #{keyword} OR material.description LIKE #{keyword} OR material.namespace LIKE #{keyword})
        </if>
    </select>

    <select id="queryByNamespaces">
        SELECT
        material.*,
        user.name as creator_name,
        user.avatar as creator_avatar,
        user.user_name as creator_username
        FROM
        material,
        user
        WHERE
        material.creator_id = user.id
        AND material.namespace in
        <foreach collection="namespaces" item="namespace" open="( " separator="," close=" )" index="index">
            #{namespace}
        </foreach>
        AND material.status = 1
    </select>

    <select id="queryByIds">
        SELECT
        material.*,
        user.name as creator_name,
        user.avatar as creator_avatar,
        user.user_name as creator_username
        FROM
        material,
        user
        WHERE
        material.creator_id = user.id
        AND material.id in
        <foreach collection="ids" item="id" open="( " separator="," close=" )" index="index">
            #{id}
        </foreach>
        AND material.status = #{status}
    </select>

    <select id="queryByNamespaceAndVersion">
        SELECT
        material.*
        FROM
        material,
        material_pub
        WHERE
        material.namespace = #{namespace}
        AND material_pub.material_id = material.id
        AND material_pub.version = #{version}
    </select>

     <select id="queryByNamespaceAndVersionList">
        SELECT
            m.*,
            pub.version
        FROM material m
        JOIN material_pub pub on m.id = pub.material_id
        WHERE (m.namespace, pub.version) IN
         <foreach item="item" index="index" collection="components" open="(" separator="," close=")">
          (#{item.namespace},#{item.version})
         </foreach>
    </select>

    <select id="queryByConvertTag">
        select * from material where status = 1 and meta like #{convert_tag}
    </select>

    <select id="queryUserInfoByMaterialId">
        SELECT
            user.id AS user_id,
            user.name AS user_name,
            user.user_name AS user_username,
            user.department AS user_department
        FROM
            user
        JOIN
            material ON material.creator_id = user.id
        WHERE
            material.id = #{material_id}
    </select>
    <select id="queryMaterialCount">
      SELECT
        COUNT(*) as total
      FROM
        material
      WHERE
        status = 1
        <if test="business !== undefined">
          AND business = #{business}
        </if>
        <if test="type !== undefined">
          AND type = #{type}
        </if>
    </select>

    <select id="queryCountGroupByDate">
      SELECT
          DATE(FROM_UNIXTIME(create_time / 1000)) as create_date,
          COUNT(*) as count
      FROM
          material
      WHERE
          status = 1
      GROUP BY
          create_date
    </select>
</mapper>
