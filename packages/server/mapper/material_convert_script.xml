<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_convert_script">
  <insert id="create">
    insert into material_convert_script (
      id,
      business,
      script,
      creator_id,
      create_time,
      status
    )
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id},
      #{business},
      #{script},
      #{creator_id},
      #{create_time},
      #{status}
    </trim>
  </insert>

  <update id="delete">
    update material_convert_script set status = -1 where id = #{id}
  </update>

  <update id="update">
    update material_convert_script set
    <if test="business != undefined">business = #{business},</if>
    <if test="script != undefined">script = #{script},</if>
    id = #{id}
    where id = #{id}
  </update>

  <select id="detail">
    SELECT * FROM material_convert_script WHERE id = #{id}
  </select>

  <select id="query">
    select
    material_convert_script.*,
    user.name as creator_name,
    user.avatar as creator_avatar,
    user.user_name as creator_username
    from material_convert_script left join user on material_convert_script.creator_id = user.id where 1 = 1
    <if test="status != undefined">
      and material_convert_script.status = #{status}
    </if>
    <if test="business !== undefined">
      and business = #{business}
    </if>
    order by material_convert_script.create_time desc
  </select>

  <select id="queryListTotal">
    select count(*) as total from material_convert_script where 1 = 1
    <if test="status != undefined">
      and status = #{status}
    </if>
    <if test="business !== undefined">
      and business = #{business}
    </if>
  </select>
</mapper>