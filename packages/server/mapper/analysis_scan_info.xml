<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="analysis_scan_info">
    <insert id="insert">
        insert into analysis_scan_info (
            id,
            scan_range,
            type,
            start_time,
            end_time,
            display_txt,
            status,
            create_time
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{scan_range},
            #{type},
            #{start_time},
            #{end_time},
            #{display_txt},
            #{status},
            #{create_time}
        </trim>
    </insert>

    <update id="deleteByScanId">
        update analysis_scan_info set
        status = -1
        where id = #{scanId} and status=1
    </update>

    <select id="queryPreScanById">
        select
            analysis_scan_info.*
        from
            analysis_scan_info,
            (select * from analysis_scan_info where id = #{scanId} and status = 1) as current_scan_info
        where
            analysis_scan_info.status = 1
            and analysis_scan_info.type = current_scan_info.type
            and current_scan_info.start_time > analysis_scan_info.start_time
        order by analysis_scan_info.start_time desc
        limit 1
    </select>

    <select id="queryById">
        select * from analysis_scan_info where id = #{scanId} and status = 1
    </select>

    <select id="query">
        select * from analysis_scan_info where type = #{scanType} and start_time = #{startTime} and end_time = #{endTime} and status = 1
    </select>

    <update id="update">
        update analysis_scan_info set
        scan_range = #{scanRange}
        where id = #{scanId} and status=1
    </update>
</mapper>