<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="analysis_route_file_usage">
    <insert id="insert">
        insert into analysis_route_file_usage (
            id,
            scan_id,
            business,
            repo_project_id,
            package,
            route_path,
            file_path,
            file_create_time,
            material_info,
            filter_material_info,
            status,
            create_time
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{scan_id},
            #{business},
            #{repo_project_id},
            #{package},
            #{route_path},
            #{file_path},
            #{file_create_time},
            #{material_info},
            #{filter_material_info},
            #{status},
            #{create_time}
        </trim>
    </insert>

    <update id="deleteByScanId">
        update analysis_route_file_usage set
        status=-1
        where scan_id = #{scanId} and status=1
    </update>

    <select id="query">
        SELECT
            *
        FROM
            analysis_route_file_usage
        WHERE
            status = 1
            AND scan_id = #{scanId}
            AND business = #{business}
        ORDER BY id DESC
    </select>

    <select id="queryByScanId">
        select
            *
        from analysis_route_file_usage
        where scan_id = #{scanId} and status = 1
    </select>

    <update id="deleteByScanIdAndBusinessList">
        update analysis_route_file_usage set
        status = -1
        where scan_id = #{scanId} and status = 1 and business in
        <foreach collection="businessList" item="business" open="(" separator="," close=")" index="index">
            #{business}
        </foreach>
    </update>
</mapper>