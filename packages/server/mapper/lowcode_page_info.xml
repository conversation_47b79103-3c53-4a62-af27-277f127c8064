<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="lowcode_page_info">
    <select id="create">
        insert into lowcode_page_info
        (
        id,
        business,
        url,
        version,
        lowcode_page_info.schema,
        content,
        update_time,
        status
        )
        <foreach collection="pages" item="item" open="values " separator="," close="" index="index">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.business},
                #{item.url},
                #{item.version},
                #{item.schema},
                #{item.content},
                #{item.update_time},
                #{item.status}
            </trim>
        </foreach>
    </select>

    <select id="query">
        SELECT
            *
        FROM
            lowcode_page_info
        WHERE
            status = 1
            AND business = #{business}
            <if test="url">
                AND url = #{url}
            </if>
            <if test="version">
                AND version = #{version}
            </if>
        ORDER BY id DESC
    </select>

    <update id="update">
        update lowcode_page_info set
        <if test="content != undefined">content = #{content},</if>
        <if test="schema != undefined">lowcode_page_info.schema = #{schema},</if>
        id = #{id}
        where id = #{id}
    </update>

    <select id="queryComplexityReport"> 
        SELECT
            CAST(CONCAT('L', CAST(value AS CHAR)) AS CHAR) AS level,
            total AS count,
            CASE
                WHEN business = 'fangzhou' THEN '方舟'
                WHEN business = 'kael' THEN '千象'
                ELSE business
            END AS platform
        FROM(
            select
                lowcode_build_report.type,
                lowcode_build_report.value,
                count(lowcode_build_report.value) as total,
                lowcode_page_info.business
            from
                lowcode_page_info,
                lowcode_build_report
            where
                lowcode_page_info.id in (
                    select
                    max(id)
                    from
                    lowcode_page_info
                    where
                    status = 1
                    group by
                    url
                )
                and lowcode_build_report.page_id = lowcode_page_info.id
                and type = 'level'
                and lowcode_page_info.status = 1
                and lowcode_build_report.status = 1
            group by
                lowcode_page_info.business,
                lowcode_build_report.value
        ) AS sql_view_query
        GROUP BY
            level,
            count,
            platform
        LIMIT
            100000 OFFSET 0
    </select>

    <select id="queryKaelPageInfo">
        SELECT
            id,
            url,
            version
        FROM
            lowcode_page_info
        WHERE
            status = 1
            AND business = 'kael'
        ORDER BY id DESC
    </select>

    <select id="findByURLAndVersion">
        SELECT
            *
        FROM
            lowcode_page_info
        WHERE
            status = 1
            AND url = #{url}
            AND version = #{version}
    </select>

    <select id="queryDailyPageCount">
        select 
            * 
        from 
            shared
        where 
            shared.key = #{key}
            and shared.status = 1
    </select>
</mapper>