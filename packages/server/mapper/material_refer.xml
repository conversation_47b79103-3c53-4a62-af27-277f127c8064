<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_refer">
    <select id="query">
        SELECT 
            mr.*,
            u.name AS creator_name
        FROM
            material_refer AS mr
        LEFT JOIN
            user u ON mr.creator_id = u.id
        WHERE 
            mr.namespace = #{namespace}
            AND mr.status = 1
            <choose>
                <when test="business == 'kael'">
                    AND mr.ref_business = 'kael'
                </when>
                <otherwise>
                    AND mr.ref_business != 'kael'
                </otherwise>
            </choose>
            AND mr.create_time = (
                SELECT MAX(inner_mr.create_time)
                FROM material_refer AS inner_mr
                WHERE
                    inner_mr.namespace = mr.namespace
                    AND inner_mr.ref_page = mr.ref_page
                    AND inner_mr.status = 1
                    <choose>
                        <when test="business == 'kael'">
                            AND inner_mr.ref_business = 'kael'
                        </when>
                        <otherwise>
                            AND inner_mr.ref_business != 'kael'
                        </otherwise>
                    </choose>
            )
        ORDER BY
            mr.create_time DESC
        LIMIT
            #{pageSize} OFFSET #{offset}
    </select>

    <select id="queryTotalCount">
        SELECT
            COUNT(*) AS total_count
        FROM
            material_refer mr
        WHERE 
            mr.namespace = #{namespace}
            AND mr.status = 1
            <choose>
                <when test="business == 'kael'">
                    AND mr.ref_business = 'kael'
                </when>
                <otherwise>
                    AND mr.ref_business != 'kael'
                </otherwise>
            </choose>
            AND mr.create_time = (
                SELECT MAX(inner_mr.create_time)
                FROM material_refer AS inner_mr
                WHERE
                    inner_mr.namespace = mr.namespace
                    AND inner_mr.ref_page = mr.ref_page
                    AND inner_mr.status = 1
                    <choose>
                        <when test="business == 'kael'">
                            AND inner_mr.ref_business = 'kael'
                        </when>
                        <otherwise>
                            AND inner_mr.ref_business != 'kael'
                        </otherwise>
                    </choose>
            )
    </select>

    <select id="queryDailyMaterialPageCount">
        SELECT
            DATE(FROM_UNIXTIME(mr.create_time / 1000)) AS create_date,
            COUNT(DISTINCT mr.ref_page) AS material_page_count
        FROM material_refer mr
        JOIN (
            SELECT ref_page, MAX(create_time) AS max_create_time
            FROM material_refer
            WHERE namespace = #{namespace} AND status = 1 
            <choose>
                <when test="business == 'kael'">
                    AND ref_business = 'kael'
                </when>
                <otherwise>
                    AND ref_business != 'kael'
                </otherwise>
            </choose>
            GROUP BY ref_page
        ) m ON mr.ref_page = m.ref_page AND mr.create_time = m.max_create_time
        WHERE mr.namespace = #{namespace} AND mr.status = 1 
        <choose>
            <when test="business == 'kael'">
                AND mr.ref_business = 'kael'
            </when>
            <otherwise>
                AND mr.ref_business != 'kael'
            </otherwise>
        </choose>
        GROUP BY
            create_date
        ORDER BY
            create_date
    </select>

    <select id="queryAll">
        SELECT
            *
        FROM
            material_refer
        WHERE
            status = 1
    </select>

    <select id="queryByBusiness_Time">
        SELECT
            material_refer.*
        FROM
            material_refer
        WHERE
            material_refer.ref_business = #{business}
            AND #{end_time} >= material_refer.create_time
            AND material_refer.create_time >= #{start_time}
            AND material_refer.status = 1
    </select>

    <select id="queryTotalByBusiness">
        SELECT
            count(distinct ref_page) as total
        FROM
            material_refer
        WHERE
            material_refer.ref_business = #{business}
            AND material_refer.status = 1
    </select>

    <select id="queryReferCountByBusiness">
        SELECT
            sum(material_refer.refer_count) as total
        FROM
            material_refer
        WHERE
            material_refer.ref_business = #{business}
            AND material_refer.status = 1
    </select>

    <select id="queryTotalByNamespace">
        SELECT
            count(distinct ref_page) as total
        FROM
            material_refer
        WHERE
            material_refer.namespace = #{namespace}
            AND material_refer.status = 1
    </select>

    <select id="queryReferCountByNamespace">
        SELECT
            sum(material_refer.refer_count) as total
        FROM
            material_refer
        WHERE
            material_refer.namespace = #{namespace}
            AND material_refer.status = 1
    </select>

    <update id="deleteByReferAndNamespace">
        update
            material_refer
        set
            status = -1
        where
            ref_page = #{refer_page}
            AND namespace = #{namespace}
            <if test="start_time">
                AND material_refer.create_time >= #{start_time}
            </if>
            <if test="end_time">
                AND #{end_time} >= material_refer.create_time
            </if>
    </update>

    <update id="deleteByGitAndNamespace">
        update
            material_refer
        set
            status = -1
        where
            ref_page like #{refer_page}
            <if test="start_time">
                AND material_refer.create_time >= #{start_time}
            </if>
            <if test="end_time">
                AND #{end_time} >= material_refer.create_time
            </if>
    </update>

    <update id="update">
        update
            material_refer
        set
            namespace = #{namespace}
        where
            id = #{id}
    </update>

    <insert id="create">
        insert into material_refer (
        id,
        material_id,
        namespace,
        version,
        ref_page,
        ref_business,
        type,
        code_type,
        refer_count,
        creator_id,
        create_time,
        status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            0,
            #{namespace},
            #{version},
            #{ref_page},
            #{ref_business},
            #{type},
            #{code_type},
            #{refer_count},
            #{creator_id},
            #{create_time},
            #{status}
        </trim>
    </insert>
</mapper>