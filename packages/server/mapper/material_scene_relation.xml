<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_scene_relation">
    <update id="deleteByMaterialId">
        update material_scene_relation set status = -1 where material_id = #{id}
    </update>

    <insert id="create">
        insert into material_scene_relation (
        id,
        material_id,
        scene_id,
        creator_id,
        create_time,
        status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{material_id},
            #{scene_id},
            #{creator_id},
            #{create_time},
            #{status}
        </trim>
    </insert>

    <select id="queryByMaterialIds">
        SELECT
        material_scene.id,
        material_scene.title,
        material_scene_relation.material_id
        FROM
        material_scene,
        material_scene_relation
        WHERE
        material_scene.id = material_scene_relation.scene_id
        AND material_scene_relation.status = 1
        AND material_scene_relation.material_id in
        <foreach collection="materialIds" item="materialId" open="( " separator="," close=" )" index="index">
            #{materialId}
        </foreach>
    </select>
</mapper>