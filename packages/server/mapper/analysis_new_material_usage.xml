<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="analysis_new_material_usage">
    <insert id="insert">
        insert into analysis_new_material_usage (
            id,
            business,
            start_scan_id,
            end_scan_id,
            repo_project_id,
            package,
            route_path,
            file_path,
            material_info,
            status,
            create_time
        )
        <foreach collection="materials" item="item" open="values " separator="," close="" index="index">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.business},
                #{item.start_scan_id},
                #{item.end_scan_id},
                #{item.repo_project_id},
                #{item.package},
                #{item.route_path},
                #{item.file_path},
                #{item.material_info},
                #{item.status},
                #{item.create_time}
            </trim>
        </foreach>
    </insert>

    <update id="deleteByScanId">
        update analysis_new_material_usage set
        status = -1
        where end_scan_id = #{scanId} and status = 1
    </update>

    <select id="query">
        SELECT
            *
        FROM
            analysis_new_material_usage
        WHERE
            status = 1
            AND end_scan_id = #{scanId}
            AND business = #{business}
        ORDER BY id DESC
    </select>

    <select id="queryByScanId">
        select
            id,
            business,
            start_scan_id,
            end_scan_id,
            repo_project_id,
            package,
            route_path,
            file_path,
            material_info,
            status,
            create_time
        from analysis_new_material_usage
        where end_scan_id = #{scanId} and status = 1
    </select>

    <update id="deleteByScanIdAndBusinessList">
        update analysis_new_material_usage set
        status = -1
        where end_scan_id = #{scanId} and status = 1 and business in
        <foreach collection="businessList" item="business" open="(" separator="," close=")" index="index">
            #{business}
        </foreach>
    </update>
</mapper>