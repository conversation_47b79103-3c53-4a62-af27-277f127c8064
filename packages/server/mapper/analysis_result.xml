<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="analysis_result">
    <insert id="insert">
        insert into analysis_result (
            id,
            business,
            scan_id,
            type,
            value,
            content,
            status,
            create_time
        )
        <foreach collection="results" item="item" open="values " separator="," close="" index="index">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.business},
                #{item.scan_id},
                #{item.type},
                #{item.value},
                #{item.content},
                #{item.status},
                #{item.create_time}
            </trim>
        </foreach>
    </insert>

    <update id="deleteByScanId">
        update analysis_result set
        status = -1
        where scan_id = #{scanId} and status=1
    </update>

    <select id="query">
        SELECT
            *
        FROM
            analysis_result
        WHERE
            status = 1
            AND scan_id = #{scanId}
            AND business = #{business}
        ORDER BY id DESC
    </select>

    <update id="deleteByScanIdAndBusinessList">
        update analysis_result set
        status = -1
        where scan_id = #{scanId} and status = 1 and business in
        <foreach collection="businessList" item="business" open="(" separator="," close=")" index="index">
            #{business}
        </foreach>
    </update>
</mapper>