<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="business_project">
    <select id="create">
        insert into business_project
        (
        id,
        business,
        title,
        git_url,
        content,
        type,
        start_time,
        end_time,
        update_time,
        status
        )
        <foreach collection="reports" item="item" open="values " separator="," close="" index="index">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.business},
                #{item.title},
                #{item.git_url},
                #{item.content},
                #{item.type},
                #{item.start_time},
                #{item.end_time},
                #{item.update_time},
                #{item.status}
            </trim>
        </foreach>
    </select>

    <select id="query">
        SELECT
            *
        FROM
        business_project
        WHERE
            status = 1
        AND id in (
            SELECT
                max(id)
            FROM
                business_project
            WHERE
                status = 1
                <if test="start_time">
                    AND start_time = #{start_time}
                </if>
                <if test="end_time">
                    AND end_time = #{end_time}
                </if>
                <if test="git_url">
                    AND git_url = #{git_url}
                </if>
                <if test="type">
                    AND type = #{type}
                </if>
                <if test="business">
                    AND business = #{business}
                </if>
            GROUP BY git_url
        )
        ORDER BY business_project.id DESC
    </select>

    <update id="update">
        update business_project set
        <if test="content">content = #{content},</if>
        <if test="update_time">update_time = #{update_time},</if>
        id = #{id}
        where id = #{id}
    </update>

    <update id="deleteProjectByGit_Time">
        update business_project set
        status = -1
        where git_url = #{git_url}
        AND start_time = #{start_time}
        AND end_time = #{end_time}
        AND type = #{type}
    </update>
</mapper>