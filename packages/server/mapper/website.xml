<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="website">
    <select id="detail">
        SELECT
            material.*,
            user.name as creator_name,
            user.avatar as creator_avatar,
            user.user_name as creator_username
        FROM material, user WHERE
        material.creator_id = user.id
        <if test="id">
            AND material.id = #{id}
        </if>
        <if test="namespace">
            AND material.namespace = #{namespace}
        </if>
    </select>

    <select id="queryMaterialList">
        SELECT
            material.*,
            material_integral.integral
        <if test="tagId">
            , material_tag.title AS tag_title
            , material_tag.id AS tag_id
        </if>
        FROM
            material
        LEFT JOIN material_integral ON material.id = material_integral.material_id AND material_integral.status = 1
        <choose>
            <when test="integralType == 'refer_count'">
                AND material_integral.integral_type = 'REFER_COUNT'
            </when>
            <otherwise>
                AND material_integral.integral_type = 'MASS_SCORE'
            </otherwise>
        </choose>
        <if test="tagId">
            LEFT JOIN material_tag_relation ON material_tag_relation.material_id = material.id AND material_tag_relation.status = 1
            LEFT JOIN material_tag ON material_tag.id = material_tag_relation.tag_id AND material_tag.status = 1
        </if>
        WHERE
            material.status = #{status}
        <if test="domain !== 0">
            AND FIND_IN_SET(#{domain}, domain)
        </if>
        <if test="type">
            AND type = #{type}
        </if>
        <if test="platform">
            AND platform REGEXP #{platform}
        </if>
        <if test="business">
            AND business = #{business}
        </if>
        <if test="tagId">
            AND material_tag.id = #{tagId}
        </if>
        <if test="keyword">
            AND (material.title LIKE #{keyword} OR material.description LIKE #{keyword} OR material.namespace LIKE #{keyword})
        </if>
        <if test="meta_value_ids">
            AND (
            <foreach collection="meta_value_ids" item="id" open="(" separator=") AND (" close=")" index="index">
                material.id IN (SELECT material_id FROM material_meta_value_relation WHERE meta_value_id = #{id})
            </foreach>
            )
        </if>
        <choose>
            <when test="sortingType">
                ORDER BY
                CASE
                    WHEN #{sortingType} = 'integral' THEN integral
                    WHEN #{sortingType} = 'update_time' THEN material.update_time
                END DESC
            </when>
            <otherwise>
                ORDER BY material.update_time DESC
            </otherwise>
        </choose>
        LIMIT #{pageSize} OFFSET #{offset};
    </select>

    <select id="queryMaterialListTotal">
        SELECT
            COUNT(*) as total
        FROM
            material
        <if test="tagId">
            LEFT JOIN material_tag_relation ON material_tag_relation.material_id = material.id AND material_tag_relation.status = 1
            LEFT JOIN material_tag ON material_tag.id = material_tag_relation.tag_id AND material_tag.status = 1
        </if>
        WHERE
            material.status = #{status}
        <if test="type">
            AND type = #{type}
        </if>
        <if test="platform">
            AND platform REGEXP #{platform}
        </if>
        <if test="business">
            AND business = #{business}
        </if>
        <if test="domain !== 0">
            AND FIND_IN_SET(#{domain}, domain)
        </if>
        <if test="tagId">
            AND material_tag.id = #{tagId}
        </if>
        <if test="keyword">
            AND (material.title LIKE #{keyword} OR material.description LIKE #{keyword} OR material.namespace LIKE #{keyword})
        </if>
        <if test="meta_value_ids">
            AND (
            <foreach collection="meta_value_ids" item="id" open="(" separator=") AND (" close=")" index="index">
                material.id IN (SELECT material_id FROM material_meta_value_relation WHERE meta_value_id = #{id})
            </foreach>
            )
        </if>
    </select>

    <select id="queryCreatorInfoByMaterialCreatorId">
        SELECT
            user.name as creator_name
        FROM
            user
        WHERE
            id = #{creatorId} AND status != -1
    </select>

    <select id="queryMaterialPubInfoByMaterialIdAndVersion">
        SELECT
            preview_img
        FROM
            material_pub
        WHERE
            material_id = #{materialId} AND version = #{version} AND status != -1
    </select>

    <select id="query">
        SELECT
            material.*,
            user.name as creator_name,
            user.avatar as creator_avatar,
            user.user_name as creator_username,
            material_pub.preview_img,
            <if test="needSchema">
                material_pub.schema,
            </if>
            material_pub.version as version,
            material_pub.readme,
            material_pub.updater_name,
            material_pub.updater_avatar,
            material_pub.updater_username
        <if test="tagId">
            , material_tag.title as tag_title
            , material_tag.id as tag_id
        </if>
        FROM
            material,
            user,
            (
                select
                    material_pub.*,
                    user.name as updater_name,
                    user.avatar as updater_avatar,
                    user.user_name as updater_username
                from
                    material_pub,
                    user
                where
                    material_pub.status = 1
                    and material_pub.creator_id = user.id
                order by material_pub.create_time desc
            ) as material_pub
        <if test="tagId">
            , material_tag
            , material_tag_relation
        </if>
        WHERE
            material.creator_id = user.id
            AND material.id = material_pub.material_id
            <if test="exclude_tianhe_basic_materials">
              AND material.namespace NOT LIKE '@es/tianhe%'
            </if>
            <if test="ids">
                AND material.id in
                <foreach collection="ids" item="id" open="( " separator="," close=" )" index="index">
                    #{id}
                </foreach>
            </if>
            <choose>
                <when test="branch == 'main'">
                    AND material_pub.id in (select max(id) from material_pub where version REGEXP '^([0-9]+)\\.([0-9]+)\\.([0-9]+)$' group by material_id)
                </when>
                <otherwise>
                    AND material.version = material_pub.version
                </otherwise>
            </choose>
            <if test="status !== undefined">
              AND material.status = #{status}
            </if>
        <if test="type">
            AND type = #{type}
        </if>
        <if test="platform">
            AND platform REGEXP #{platform}
        </if>
        <if test="business">
            AND business = #{business}
        </if>
        <if test="tagId">
            AND material_tag.id = #{tagId}
            AND material_tag.id = material_tag_relation.tag_id
            AND material_tag_relation.material_id = material.id
        </if>
        <if test="keyword">
            AND (material.title LIKE #{keyword} OR material.description LIKE #{keyword} OR material.namespace LIKE #{keyword})
        </if>
        <if test="meta_value_ids">
            AND (
            <foreach collection="meta_value_ids" item="id" open="(" separator=") AND (" close=")" index="index">
                material.id in (select material_id from material_meta_value_relation where meta_value_id = #{id})
            </foreach>
            )
        </if>
        ORDER BY material.update_time DESC
        LIMIT #{pageSize} OFFSET #{offset};
    </select>

    <select id="queryListTotal">
        SELECT
            COUNT(*) as total

        FROM
            material,
            user,
            (
                select
                    material_pub.*,
                    user.name as updater_name,
                    user.avatar as updater_avatar,
                    user.user_name as updater_username
                from
                    material_pub,
                    user
                where
                    material_pub.status = 1
                    and material_pub.creator_id = user.id
                order by material_pub.create_time desc
            ) as material_pub
        <if test="tagId">
            , material_tag
            , material_tag_relation
        </if>
        WHERE
            material.creator_id = user.id
            AND material.id = material_pub.material_id
            <if test="exclude_tianhe_basic_materials">
              AND material.namespace NOT LIKE '@es/tianhe%'
            </if>
            <if test="ids">
                AND material.id in
                <foreach collection="ids" item="id" open="( " separator="," close=" )" index="index">
                    #{id}
                </foreach>
            </if>
            <choose>
                <when test="branch == 'main'">
                    AND material_pub.version REGEXP '^([0-9]+)\\.([0-9]+)\\.([0-9]+)$'
                </when>
                <otherwise>
                    AND material.version = material_pub.version
                </otherwise>
            </choose>
            <if test="status !== undefined">
              AND material.status = #{status}
            </if>
        <if test="type">
            AND type = #{type}
        </if>
        <if test="platform">
            AND platform REGEXP #{platform}
        </if>
        <if test="business">
            AND business = #{business}
        </if>
        <if test="tagId">
            AND material_tag.id = #{tagId}
            AND material_tag.id = material_tag_relation.tag_id
            AND material_tag_relation.material_id = material.id
        </if>
        <if test="keyword">
            AND (material.title LIKE #{keyword} OR material.description LIKE #{keyword} OR material.namespace LIKE #{keyword})
        </if>
        <if test="meta_value_ids">
            AND (
            <foreach collection="meta_value_ids" item="id" open="(" separator=") AND (" close=")" index="index">
                material.id in (select material_id from material_meta_value_relation where meta_value_id = #{id})
            </foreach>
            )
        </if>
    </select>

    <select id="queryByPkgNameAndVersion">
        SELECT
            material.*,
            user.name as creator_name,
            user.avatar as creator_avatar,
            user.user_name as creator_username,
            material_pub.preview_img,
            material_pub.schema,
            <if test="needContent">
                material_pub.content,
            </if>
            material_pub.readme,
            material_pub.version as version,
            material_pub.updater_name,
            material_pub.updater_avatar,
            material_pub.updater_username
        FROM
            material,
            user,
            (
                select
                    material_pub.*,
                    user.name as updater_name,
                    user.avatar as updater_avatar,
                    user.user_name as updater_username
                from
                    material_pub,
                    user
                where
                    material_pub.status = 1
                    and material_pub.creator_id = user.id
                order by material_pub.create_time desc
            ) as material_pub
        WHERE
            material.creator_id = user.id
            AND material.id = material_pub.material_id
            <choose>
                <when test="version">
                    AND material_pub.version = #{version}
                </when>
                <otherwise>
                    AND material.version = material_pub.version
                </otherwise>
            </choose>
            AND material.status = 1
            AND type = 'component'
            AND (material.namespace LIKE #{keyword} OR material.namespace = #{name})
        ORDER BY material.update_time DESC
    </select>

    <select id="queryByNamespace">
        SELECT
            *
        FROM
            material
        WHERE
            material.namespace = #{namespace}
            and status != -1
    </select>

    <select id="queryByNamespaceAndVersion">
        SELECT
            material.*,
            material_pub.id as pub_id
        FROM
            material,
            material_pub
        WHERE
            material.namespace = #{namespace}
            AND material_pub.material_id = material.id
            AND material_pub.version = #{version}
            AND material.status != -1
            AND material_pub.status = 1
    </select>

    <select id="queryByNamespaceAndVersionList">
        SELECT
            material.*,
            material_pub.version
        FROM
            material,
            material_pub
        WHERE
            material_pub.material_id = material.id
            <foreach collection="components" item="component" open="AND (" separator=" OR " close=")" index="index">
                (material.namespace = #{component.namespace} AND material_pub.version = #{component.version})
            </foreach>
            AND material.status != -1
            AND material_pub.status = 1
    </select>

    <select id="create">
        insert into material
        (
        id,
        namespace,
        version,
        title,
        description,
        type,
        business,
        domain,
        git_url,
        platform,
        creator_id,
        create_time,
        updater_id,
        update_time,
        meta,
        status
        )
        <foreach collection="materials" item="item" open="values " separator="," close="" index="index">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.namespace},
                #{item.version},
                #{item.title},
                #{item.description},
                #{item.type},
                #{item.business},
                #{item.domain},
                #{item.git_url},
                #{item.platform},
                #{item.creator_id},
                #{item.create_time},
                #{item.updater_id},
                #{item.update_time},
                <choose>
                    <when test="item.meta">
                        #{item.meta},
                    </when>
                    <otherwise>
                        '',
                    </otherwise>
                </choose>
                #{item.status}
            </trim>
        </foreach>
    </select>

    <select id="queryByNamespaces">
        SELECT
        material.*,
        user.name as creator_name,
        user.avatar as creator_avatar,
        user.user_name as creator_username
        <if test="needPub">
            ,material_pub.preview_img,
            material_pub.readme,
            material_pub.updater_name,
            material_pub.updater_avatar,
            material_pub.updater_username
        </if>
        FROM
            material,
            user
        <if test="needPub">
            , (
                select
                    material_pub.*,
                    user.name as updater_name,
                    user.avatar as updater_avatar,
                    user.user_name as updater_username
                from
                    material_pub,
                    user
                where
                    material_pub.status = 1
                    and material_pub.creator_id = user.id
                order by material_pub.create_time desc
            ) as material_pub
        </if>
        WHERE
            material.creator_id = user.id
            <if test="needPub">
                AND material.id = material_pub.material_id
                AND material.version = material_pub.version
            </if>
            AND material.status = #{status}
            AND material.namespace in
        <foreach collection="namespaces" item="namespace" open="( " separator="," close=" )" index="index">
            #{namespace}
        </foreach>
    </select>
</mapper>
