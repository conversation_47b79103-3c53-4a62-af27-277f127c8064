<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="user">
    <select id="queryByUserName">
        SELECT
            *
        FROM
            user
        WHERE
            user_name = #{username}
            AND status = 1
    </select>

    <select id="queryByEmail">
        SELECT
            *
        FROM
            user
        WHERE
            email = #{email}
            AND status = 1
    </select>

    <select id="queryById">
        SELECT
            *
        FROM
            user
        WHERE
            id = #{id}
            AND status = 1
    </select>

    <insert id="create">
        insert into user (
        id,
        user_id,
        name,
        user_name,
        avatar,
        department,
        email,
        create_time,
        status
        )
        SELECT #{id}, #{user_id}, #{name}, #{user_name}, #{avatar}, #{department}, #{email}, #{create_time}, #{status}
        WHERE NOT EXISTS (
            SELECT 1 FROM user WHERE email = #{email} and status = 1
        );
    </insert>
</mapper>