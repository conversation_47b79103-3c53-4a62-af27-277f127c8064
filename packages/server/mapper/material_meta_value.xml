<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_meta_value">
    <insert id="create">
        insert into material_meta_value (
        id,
        title,
        value,
        meta_id,
        \`order\`,
        creator_id,
        create_time,
        status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{title},
            #{value},
            #{meta_id},
            #{order},
            #{creator_id},
            #{create_time},
            #{status}
        </trim>
    </insert>

    <update id="delete">
        update material_meta_value set status = -1 where id = #{id}
    </update>

    <update id="update">
        update material_meta_value set
        <if test="title">title = #{title},</if>
        <if test="value">value = #{value},</if>
        <if test="order != undefined">\`order\` = #{order},</if>
        <if test="status != undefined">status = #{status},</if>
        id = #{id}
        where id = #{id}
    </update>

    <select id="detail">
        SELECT * FROM material_meta_value WHERE id = #{id}
    </select>

    <select id="queryByMetaIds">
        select * from material_meta_value where
            status = 1
        AND meta_id in
        <foreach collection="meta_ids" item="meta_id" open="( " separator="," close=" )" index="index">
            #{meta_id}
        </foreach>
    </select>

    <select id="queryByIds">
        select * from material_meta_value where
            status = 1
        AND id in
        <foreach collection="ids" item="id" open="( " separator="," close=" )" index="index">
            #{id}
        </foreach>
    </select>

    <select id="queryByMetaIdAndValue">
        select * from material_meta_value where
            status = 1
        AND meta_id = #{meta_id}
        AND value = #{value}
    </select>

    <select id="query">
        select
            material_meta_value.*,
            user.name as creator_name,
            user.avatar as creator_avatar,
            user.user_name as creator_username
        from material_meta_value left join user on material_meta_value.creator_id = user.id where
        material_meta_value.status = #{status}
        <if test="title !== undefined">
            and title like #{title}
        </if>
        <if test="meta_id !== undefined">
            and meta_id = #{meta_id}
        </if>
        order by \`order\` desc
        limit #{pageSize} offset #{offset}
    </select>

    <select id="queryListTotal">
        select count(*) as total from material_meta_value where
        status = #{status}
        <if test="title !== undefined">
            and title like #{title}
        </if>
        <if test="meta_id !== undefined">
            and meta_id = #{meta_id}
        </if>
    </select>
</mapper>