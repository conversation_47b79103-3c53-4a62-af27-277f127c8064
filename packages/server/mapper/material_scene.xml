<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="material_scene">
  <insert id="create">
    insert into material_scene (
      id,
      title,
      \`order\`,
      creator_id,
      create_time,
      status
    )
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id},
      #{title},
      #{order},
      #{creator_id},
      #{create_time},
      #{status}
    </trim>
  </insert>

  <update id="deleteRelationByMaterialId">
    update material_scene_relation set status = -1 where material_id = #{id}
  </update>

  <update id="delete">
    update material_scene set status = -1 where id = #{id}
  </update>

  <update id="update">
    update material_scene set
    <if test="title != undefined">title = #{title},</if>
    <if test="order != undefined">\`order\` = #{order},</if>
    <if test="status != undefined">status = #{status},</if>
    id = #{id}
    where id = #{id}
  </update>

  <select id="detail">
    SELECT * FROM material_scene WHERE id = #{id}
  </select>

  <select id="query">
    select * from material_scene where
    status = #{status}
    <if test="title !== undefined">
      and title like #{title}
    </if>
    order by \`order\` desc
    limit #{pageSize} offset #{offset}
  </select>

  <select id="queryListTotal">
    select count(*) as total from material_scene where
    status = #{status}
    <if test="title !== undefined">
      and title like #{title}
    </if>
  </select>
</mapper>