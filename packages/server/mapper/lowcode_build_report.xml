<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="lowcode_build_report">
    <select id="create"> insert into lowcode_build_report ( id, page_id, type, value, update_time,
        status ) <foreach collection="reports" item="item" open="values " separator="," close=""
            index="index">
            <trim prefix="(" suffix=")" suffixOverrides=","> #{item.id}, #{item.page_id},
        #{item.type}, #{item.value}, #{item.update_time}, #{item.status} </trim>
        </foreach>
    </select>

    <select id="query"> SELECT * FROM lowcode_build_report WHERE status = 1 <if test="type"> AND
        type = #{type} </if>
            <if test="page_id"> AND page_id = #{page_id} </if> ORDER BY id DESC </select>

    <select id="queryUserTimeReport">
        SELECT
            CAST(CONCAT('L', CAST(value AS CHAR)) AS CHAR) AS level,
            SUM(CAST(ROUND(time / 1000, 2) AS DECIMAL) / 3600) AS hours,
            CASE
                WHEN business = 'fangzhou' THEN '方舟'
                WHEN business = 'kael' THEN '千象'
                ELSE business
            END AS platform
        FROM(
            select
                custom_page_info.value,
                lowcode_page_info.business,
                avg(lowcode_build_report.value) as time
            from
                (
                    select
                    min(page_id) as id,
                    lowcode_build_report.value
                    from
                    lowcode_page_info,
                    lowcode_build_report
                    where
                    lowcode_build_report.page_id = lowcode_page_info.id
                    and type = 'level'
                    and lowcode_page_info.status = 1
                    and lowcode_build_report.status = 1
                    group by
                    url,
                    lowcode_build_report.value
                ) as custom_page_info,
                lowcode_page_info,
                lowcode_build_report
            where
                lowcode_build_report.page_id = lowcode_page_info.id
                and lowcode_build_report.page_id = custom_page_info.id
                and lowcode_page_info.id = custom_page_info.id
                and lowcode_page_info.status = 1
                and lowcode_build_report.status = 1
                and type = 'build_time'
                and lowcode_build_report.value > 0
                and custom_page_info.value > 0
            group by
                lowcode_page_info.business,
                custom_page_info.value
            ) AS sql_view_query
        GROUP BY
            level,
            platform
        LIMIT
            100000 OFFSET 0
    </select>
</mapper>