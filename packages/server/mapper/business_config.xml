<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="business_config">
    <insert id="create">
        insert into business_config (
        id,
        meta,
        business,
        updater_id,
        update_time,
        status
        )
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{meta},
            #{business},
            #{updater_id},
            #{update_time},
            #{status}
        </trim>
    </insert>

    <update id="delete">
        update business_config set status = -1 where id = #{id}
    </update>

    <update id="update">
        update business_config set
        <if test="meta">meta = #{meta},</if>
        <if test="status != undefined">status = #{status},</if>
        id = #{id}
        where id = #{id}
    </update>

    <select id="detail">
        SELECT * FROM business_config WHERE id = #{id}
    </select>

    <select id="query">
        select
            business_config.*,
            user.name as creator_name,
            user.avatar as creator_avatar,
            user.user_name as creator_username
        from business_config left join user on business_config.updater_id = user.id where
        business_config.status = #{status}
        <if test="business !== undefined">
            and business = #{business}
        </if>
        order by business_config.update_time desc
    </select>
</mapper>