import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { MaterialModule } from "./module/material/material.module";
import { WebsiteModule } from "./module/website/website.module";
import { MaterialPubModule } from "./module/material-pub/material-pub.module";
import { MaterialSceneModule } from "./module/material-scene/material-scene.module";
import { MaterialTagModule } from "./module/material-tag/material-tag.module";
import { MaterialConvertScriptModule } from "./module/material-convert-script/material-convert-script.module";
import { MaterialConvertModule } from "./module/material-convert/material-convert.module";
import { MaterialReferModule } from "./module/material-refer/material-refer.module";
import { UserModule } from "./module/user/user.module";
import CDNModule from "./module/cdn/cdn.module";
import { MaterialMetaModule } from "./module/material-meta/material-meta.module";
import { MaterialMetaValueModule } from "./module/material-meta-value/material-meta-value.module";
import { MaterialMetaValueRelationModule } from "./module/material-meta-value-relation/material-meta-value-relation.module";
import { BusinessConfigModule } from "./module/business-config/business-config.module";
import { ReportModule } from "./module/report/report.module";
import { ProxyModule } from "./module/proxy/proxy.module";
import { HttpModule } from "@nestjs/axios";
import { ProxyV2Middleware } from "./middleware/proxy.v2.middleware";
import { RequestLoggerMiddleware } from "./middleware/request.middleware";

@Module({
  imports: [
    MaterialModule,
    WebsiteModule,
    MaterialPubModule,
    MaterialSceneModule,
    MaterialTagModule,
    MaterialConvertScriptModule,
    MaterialConvertModule,
    MaterialReferModule,
    UserModule,
    CDNModule,
    MaterialMetaModule,
    MaterialMetaValueModule,
    MaterialMetaValueRelationModule,
    BusinessConfigModule,
    ReportModule,
    ProxyModule,
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    }),
  ],
  providers: [ProxyV2Middleware],
})
export default class IndexModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggerMiddleware).forRoutes("*");
    consumer.apply(ProxyV2Middleware).forRoutes("/api/v2/*");
  }
}
