import { LoggerService } from '@nestjs/common';
import { Logger } from '@mybricks/rocker-commons';
import { AnyType } from '../../common/types';

export default class CustomLogger implements LoggerService {
	log(message: AnyType) {
		console.log(message);
		Logger.info(message);
	}

	error(message: AnyType) {
		Logger.error(message);
	}

	warn(message: AnyType) {
		Logger.warn(message);
	}

	debug?(message: AnyType) {
		Logger.debug(message);
	}

	verbose?(message: AnyType) {
		Logger.trace(message);
	}
}
