import { Logger } from '@mybricks/rocker-commons';
import { start as startDB } from '@mybricks/rocker-dao';
import { infraLogger } from './common/util';
import { AnyType } from './common/types';
import { MYSQL_PASSWORD } from './common/const';

export async function globalLogic() {
	startDB([
    {
    	...MYSQL_PASSWORD,
    	sqlPath: '../../mapper',
    } as AnyType,
	]);

	process.on('unhandledRejection', (e: AnyType) => {
		Logger.info(`[global error]: [unhandledRejection]: ${e?.message}`);
		Logger.info(
			`[global error]: [unhandledRejection]: 错误详情是 ${e?.stack?.toString()}`,
		);
		infraLogger.perf({
			subtag: 'FZ_MATERIAL_MIDDLE_OFFICE_ERROR',
			extra1: 'unhandledRejection',
			extra2: '',
			extra3: '',
			extra4: e?.message || 'unhandledRejection',
		});
	});
	process.on('uncaughtException', (e) => {
		Logger.info(`[global error]: [uncaughtException]: ${e?.message}`);
		Logger.info(
			`[global error]: [uncaughtException]: 错误详情是 ${e?.stack?.toString()}`,
		);
		infraLogger.perf({
			subtag: 'FZ_MATERIAL_MIDDLE_OFFICE_ERROR',
			extra1: 'uncaughtException',
			extra2: '',
			extra3: '',
			extra4: e?.message || 'uncaughtException',
		});
	});
	process.on('exit', (code) => {
		Logger.info(`[global error]: [exit]: ${code}`);
		infraLogger.perf({
			subtag: 'FZ_MATERIAL_MIDDLE_OFFICE_ERROR',
			extra1: 'exit',
			extra2: '',
			extra3: '',
			extra4: code.toString(),
		});
	});
}
