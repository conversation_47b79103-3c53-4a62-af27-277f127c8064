import { Request } from 'express';
import UserDao from '../module/user/user.dao';

const userDao = new UserDao();

/** 新用户注册 */
export async function registerMiddleware(request: Request, response, next) {
	const isDev: boolean = process.env.NODE_ENV === 'development';
	/** 本地开发默认admin */
	const userName: string = isDev
		? 'dev'
		: request.get('username') || request.cookies['userName'];

	if (userName) {
		/** 根据userId查询用户表 */
		const [user] = await userDao.queryByUserNameNotFill(userName);
		if (!user) {
			/** 头像 */
			const avatar: string = isDev
				? '/public/default_avatar.png'
				: request.get('avatar');
			/** 中文名 */
			const displayName: string = isDev ? '开发者' : request.get('displayname');

			try {
				await userDao.create({
					name: decodeURIComponent(displayName),
					email: `${userName}@kuaishou.com`,
					avatar,
					user_name: userName,
				});
			} catch (error) {
				console.log('注册用户失败，原因是' + error.message);
			}
		}
	}

	await next();
}
