import { Request, Response, NextFunction } from 'express';
import path from 'path';
import serveStatic from 'serve-static';

export function staticMiddleware(
	req: Request,
	res: Response,
	next: NextFunction,
) {
	if (!req.url.startsWith('/api')) {
		if (!req.url.startsWith('/assets')) {
			req.url = '/index.html';
		}

		return serveStatic(path.join(__dirname, '../../assets'), {
			index: ['index.html'],
			etag: true,
			lastModified: true,
		})(req, res, next);
	}

	next();
}
