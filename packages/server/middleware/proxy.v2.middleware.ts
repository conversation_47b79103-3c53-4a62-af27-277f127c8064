import { Injectable, NestMiddleware } from "@nestjs/common";
import { Request, Response } from "express";
import { existsSync, readJsonSync } from "fs-extra";
import { resolve } from "path";
import axios from "axios";
import pm2 from 'pm2';

@Injectable()
export class ProxyV2Middleware implements NestMiddleware {
  static _v2Port: number;
  static get v2Port(): number {
    const portsFile = resolve(__dirname, "../../../ports.json");
    if (!ProxyV2Middleware._v2Port && existsSync(portsFile)) {
      const port = readJsonSync(portsFile)?.v2;

      if (port) {
        ProxyV2Middleware._v2Port = +port;
      }
    }
    return ProxyV2Middleware._v2Port;
  }

  static get v2Url() {
    return `http://localhost:${ProxyV2Middleware.v2Port}`;
  }

  static async v2SwaggerJSON() {
    try {
      return (await axios.get(`${ProxyV2Middleware.v2Url}/api/v2/docs-json`)).data;
    } catch (error) {
      return {
        paths: {},
        definitions: {},
      }
    }
  }

  static async request(method: string, path: string) {
    return axios.request({
      method: method,
      url: `http://localhost:${ProxyV2Middleware.v2Port}${path}`,
    });
  }

  async use(req: Request, res: Response, next: Function) {
    if (!req.baseUrl.startsWith("/api/v2")) {
      return next();
    }

    const targetUrl = `http://localhost:${ProxyV2Middleware.v2Port}${req.baseUrl}`;

    // 移除一些不需要转发的头部
    const headers = req.rawHeaders.reduce((acc, cur, index, arr) => {
      if (
        index % 2 === 0 &&
        ![
          "host",
          "content-length",
          "connection",
          "transfer-encoding",
          "proxy-authorization",
          "upgrade",
          "via",
          "x-forwarded-for",
          "x-forwarded-host",
          "x-forwarded-proto",
        ].includes(cur.toLocaleLowerCase())
      ) {
        acc[cur] = arr[index + 1];
      }
      return acc;
    }, {});

    // headers['Host'] = `localhost:${ProxyV2Middleware.v2Port}`;
    // if (req.headers["content-type"]) {
    //   headers["Content-Type"] = req.headers["content-type"];
    // }

    try {
      const response = await axios.request({
        method: req.method,
        url: targetUrl,
        data: req.body,
        headers: headers,
        params: req.query,
        maxRedirects: 5,
        timeout: 100 * 60 * 1000, // 10 分钟超时
        validateStatus: (status) => true, // 允许所有状态码
      });

      // 转发响应头
      Object.entries(response.headers).forEach(([key, value]) => {
        if (
          [
            "transfer-encoding",
            "content-length",
            "connection",
            "keep-alive",
            "proxy-authenticate",
            "set-cookie",
          ].includes(key.toLocaleLowerCase())
        ) {
          return;
        }
        res.setHeader(key, value);
      });

      // 设置状态码和响应体
      res.status(response.status);
      // 根据内容类型处理响应
      const contentType = response.headers["content-type"];
      if (contentType && contentType.includes("application/json")) {
        res.json(response.data);
      } else {
        res.send(response.data);
      }
    } catch (error) {
      // 错误处理
      if (error.response) {
        // 服务器返回的错误
        console.error(error.response.status, error.response.data);
        res.status(error.response.status).send(error.response.data);
      } else if (error.request) {
        console.error("No response received", error.request._currentUrl);
        // 请求未得到响应
        res.status(504).json({ message: "Gateway Timeout" });
      } else {
        // 发送请求时发生的其他错误
        res.status(500).json({ message: "Internal Server Error", error: error.message });
      }
    }
  }
}
