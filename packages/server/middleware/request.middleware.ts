// request-logger.middleware.ts
import { Injectable, NestMiddleware } from '@nestjs/common';
import { infraLogger } from '../common/util';
import dayjs from 'dayjs';
import { Request, Response, NextFunction } from 'express';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  private readonly logFolder: string;

  constructor() {
    this.logFolder = path.join(process.cwd(), '../../logs/request-logs');
    this.ensureLogDirectoryExists();
  }

  private ensureLogDirectoryExists(): void {
    const logDir = this.logFolder;
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  private getOrigin(req: Request): string {
    // 优先获取 Origin 头，其次是 Referer
    return req.headers.origin || req.headers.referer || 'unknown';
  }

  private getUsername(req: Request): string {
    return req.cookies.userName || 'anonymous';
  }

  private appendLog(logData: any): void {
    const logEntry = JSON.stringify(logData) + '\n';
    const fileNameWithDate = dayjs().format('YYYY-MM-DD') + '.jsonl';
    fs.appendFileSync(path.join(this.logFolder, fileNameWithDate), logEntry);
  }

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const user = this.getUsername(req);

    res.on('finish', () => {
      const wasteTime = Date.now() - startTime;
      const path = req.baseUrl || req.path;
      const origin = this.getOrigin(req);
      const logData = {
        timestamp: new Date().toISOString(),
        method: req.method,
        path,
        origin,
        sourceIp: req.ip,
        user: user,
        userAgent: req.get('user-agent'),
        statusCode: res.statusCode,
        responseTime: wasteTime,
        query: req.query,
        headers: {
          ...req.headers,
          // 可能需要过滤掉一些敏感的头部信息
          authorization: req.headers.authorization ? '[FILTERED]' : undefined,
        }
      };

      this.appendLog(logData);
      infraLogger.perf({
        subtag: 'MATERIAL_PLATFORM_REQUEST',
        millis: wasteTime,
        extra1: path,
        extra2: req.method,
        extra3: res.statusCode + '',
        extra4: user,
        extra5: origin,
      })
    });

    next();
  }
}
