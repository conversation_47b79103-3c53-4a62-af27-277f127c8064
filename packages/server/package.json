{"private": true, "name": "@global-material-middleoffice/server", "main": "index.js", "module": "index.js", "types": "./module/module.d.ts", "scripts": {"dev": "pnpm run start:watch", "start:prod": "NODE_ENV=production node ./index.js", "start:watch": "NODE_ENV=development nodemon --ext js,ts --exec 'node ./index.js'", "gen-mui-sql": "tsx ./mui-script/index.js"}, "dependencies": {"@global-material-middleoffice/server-v2": "workspace:*", "@infra-node/kconf": "^1.1.17", "@infra-node/logger": "^1.1.13", "@kael/complexity": "1.0.27-beta.0", "@kael/schema-utils": "1.0.0-rc.186", "@ks-material-middleoffice/measure-sdk": "^0.7.0-beta.5", "@mybricks/rocker-commons": "^0.0.2", "@mybricks/rocker-dao": "0.0.16", "@nestjs/axios": "^3.1.2", "@nestjs/common": "^8.0.0", "@nestjs/core": "^8.0.0", "@nestjs/platform-express": "^8.0.0", "@nestjs/swagger": "^7.4.2", "@rockerjs/midlog": "^1.0.1", "@yingpengsha/swagger-stats": "0.99.8-beta.1", "async-mutex": "^0.5.0", "axios": "^1.7.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "dayjs": "^1.11.10", "express": "^4.19.2", "express-request-hook": "^0.0.1", "form-data": "^4.0.0", "fs-extra": "^11.2.0", "gen-uniqueid": "0.0.2", "http-proxy-middleware": "^3.0.3", "json5": "^2.2.3", "lodash": "^4.17.21", "nanoid": "^5.0.8", "node-schedule": "^2.1.1", "number-precision": "^1.6.0", "proper-lockfile": "^4.1.2", "reflect-metadata": "^0.1.13", "request": "^2.88.2", "rxjs": "^7.6.0", "semver": "^7.6.2", "serve-static": "^1.15.0", "swagger-stats": "^0.99.7", "tsx": "^4.16.0"}, "devDependencies": {"@types/cache-manager": "^3.4.2", "@types/cookie-parser": "^1.4.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.13", "@types/fs-extra": "^11.0.4", "@types/json5": "^2.2.0", "@types/lodash": "^4.14.202", "@types/node": "^16.0.0", "@types/request": "^2.48.12", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "eslint": "8.22.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.30.1", "nodemon": "^3.0.1", "pm2": "^5.2.2", "ts-node": "10.9.1", "typescript": "^4.9.3"}}