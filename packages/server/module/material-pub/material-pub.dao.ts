import { DOBase } from "@mybricks/rocker-dao";
import { compare } from "semver";
import { genMainIndexOfDB } from "../../common/util";
import { EffectStatus, PAGE_NUM, PAGE_SIZE } from "../../common/const";
import { computeOffset } from "../../common/util/paging";

export default class MaterialPubDao extends DOBase {
  public async create(params: Module.Material.CreateMaterialPubParams) {
    return (
      await this.exe<{ insertId: number }>("material_pub:create", {
        ...params,
        id: genMainIndexOfDB(),
        create_time: Date.now(),
        readme: params.readme ?? "",
        preview_img: params.preview_img ?? "",
        status: EffectStatus.EFFECT,
      })
    ).insertId;
  }

  public async delete(id: number) {
    return this.exe("material_pub:delete", { id });
  }

  public async update(params: Module.Material.UpdateMaterialPubParams) {
    return this.exe("material_pub:update", params);
  }

  public async detail(id: number) {
    return (
      await this.exe("material_pub:detail", { id })
    )?.[0] as Module.Material.Pub;
  }

  public async detailByNamespaceVersion(namespace: string, version: string) {
    return (
      await this.exe("material_pub:detailByNamespaceVersion", {
        namespace,
        version,
      })
    )?.[0] as Module.Material.Pub;
  }

  public async query(
    params: Partial<Module.Material.GetMaterialPubListParams> & {
      offset?: number;
    },
  ) {
    const result = await this.exe<Module.Material.QueryPub[]>(
      "material_pub:query",
      {
        ...params,
        branch: params.branch || "all",
        pageNum: params.pageNum || PAGE_NUM,
        pageSize: Number(params.pageSize || 200),
        offset: computeOffset(
          params.pageNum || PAGE_NUM,
          params.pageSize || 200,
        ),
      },
    );
    try {
      return result.sort(
        (a, b) =>
          compare(b.version, a.version) || b.create_time - a.create_time,
      );
    } catch {
      return result;
    }
  }

  public async queryListTotal(
    params: Omit<
      Module.Material.GetMaterialPubListParams,
      "pageNum" | "pageSize"
    >,
  ) {
    const result = await this.exe<Array<{ total: number }>>(
      "material_pub:queryListTotal",
      params,
    );
    return result[0]?.total ?? 0;
  }

  public async queryVersionsByMaterialIds(params: {
    materialIds: number[];
    needSchema?: boolean;
    branch?: "all" | "main";
  }) {
    if (params.materialIds.length === 0) {
      return [];
    }
    return (
      await this.exe<Module.Material.QueryPub[]>(
        "material_pub:queryMaterialVersionsByMaterialIds",
        params,
      )
    ).sort((a, b) => compare(b.version, a.version));
  }

  public async queryLatestVersionByMaterialId(id: number) {
    return await this.exe<Module.Material.QueryPub[]>(
      "material_pub:queryLatestVersionByMaterialId",
      { id },
    );
  }
}
