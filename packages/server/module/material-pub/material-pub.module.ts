import { Module, forwardRef } from '@nestjs/common';
import MaterialPubController from './material-pub.controller';
import MaterialPubService from './material-pub.service';
import MaterialPubDao from './material-pub.dao';
import { MaterialModule } from '../material/material.module';
import UserDao from '../user/user.dao';
import BusinessConfigDao from '../business-config/business-config.dao';
import NoticeService from '../notice/notice.service';

@Module({
	imports: [forwardRef(() => MaterialModule)],
	controllers: [MaterialPubController],
	providers: [MaterialPubService, MaterialPubDao, UserDao, BusinessConfigDao, NoticeService],
	exports: [MaterialPubService, MaterialPubDao],
})
export class MaterialPubModule {}
