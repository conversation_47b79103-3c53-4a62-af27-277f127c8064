import { Body, Controller, Get, Inject, Post, Query } from '@nestjs/common';
import MaterialPubService from './material-pub.service';

@Controller('api/manage/version')
export default class MaterialPubController {
  @Inject(MaterialPubService)
  	materialPubService: MaterialPubService;

  @Post('/create')
  async createMaterialPub(
    @Body() body: Module.Material.CreateMaterialPubParams,
  ) {
  	return { code: 1, data: await this.materialPubService.create(body) };
  }

  @Post('/delete')
  async delete(@Body('id') id: number) {
  	await this.materialPubService.delete(id);
  	return { code: 1 };
  }

  @Post('/update')
  async update(@Body() body: Module.Material.UpdateMaterialPubParams) {
  	await this.materialPubService.update(body);
  	return { code: 1 };
  }

  @Post('/updateSchemaProps')
  async updateSchema(@Body() body: { id: number; props: AnyType }) {
	  if (!body.id || !body.props) {
		  return { code: -1, message: '参数 id、props 不能为空' };
	  }

	  try {
  		await this.materialPubService.updateSchema(body);

		  return { code: 1, message: '物料协议编辑成功' };
	  } catch (error) {
		  return { code: -1, message: '物料协议编辑失败，错误原因是' + error.message };
	  }
  }

  @Get('/detail')
  async getMaterialPubDetail(
		@Query('id') id: number,
		@Query('namespace') namespace: string,
		@Query('version') version: string,
  ) {
	  if (!id && (!namespace || !version)) {
		  return { code: -1, message: '参数 id 或 namespace、version 必传' };
	  }

	  try {
		  return { code: 1, data: await this.materialPubService.detail({ id, namespace, version }) };
	  } catch (error) {
		  return { code: -1, message: '查询物料详情失败，错误原因是' + error.message };
	  }
  }

  @Get('/list')
  async getMaterialPubList(
    @Query() query: Module.Material.GetMaterialPubListParams,
  ) {
  	return { code: 1, data: await this.materialPubService.query(query) };
  }

  @Get('/getMaterialAllPub')
  async getMaterialAllPub(@Query('id') id: number) {
  	return {
  		code: 1,
  		data: await this.materialPubService.getMaterialAllPub(id),
  	};
  }

	@Post('/getVersionsByMaterialIds')
  async getVersionsByMaterialIds(@Body() body: { materialIds: number[] }) {
  	if (!body.materialIds || !body.materialIds?.length) {
  		return { code: -1, message: '参数 materialIds 不能为空' };
  	}

  	try {
  		const data = await this.materialPubService.getMaterialAllPubByMaterialIds(body);
  		let groupVersionData = { };
  		(data || []).forEach(item => {
  			let materialId = item.material_id;
  			if(groupVersionData[materialId]) {
  				groupVersionData[materialId].push(item);
  			}else {
  				groupVersionData[materialId] = [item];
  			}
  		});

  		return { code: 1, message: '获取物料版本列表成功', data: groupVersionData };
  	} catch (error) {
  		return { code: -1, message: '版本列表获取是吧，错误原因是' + error.message };
  	}
  }
}
