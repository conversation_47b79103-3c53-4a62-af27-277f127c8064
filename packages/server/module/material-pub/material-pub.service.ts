import { Inject, Injectable } from '@nestjs/common';
import MaterialPubDao from './material-pub.dao';
import MaterialService from '../material/material.service';
import { MaterialOperateAction, PAGE_NUM, PAGE_SIZE } from '../../common/const';
import { computeOffset } from '../../common/util/paging';
import NoticeService from '../notice/notice.service';
import { safeParse, safeStringify } from '../../common/util';

@Injectable()
export default class MaterialPubService {
  @Inject(MaterialPubDao)
  	materialPubDao: MaterialPubDao;
  @Inject(MaterialService)
  	materialService: MaterialService;
	@Inject(NoticeService)
		noticeService: NoticeService;

	async create(params: Module.Material.CreateMaterialPubParams) {
  	if (await this.materialService.exist(params.material_id)) {
  		return this.materialPubDao.create(params);
  	}
	}

	async delete(id: number) {
  	return this.materialPubDao.delete(id);
	}

	async update(params: Module.Material.UpdateMaterialPubParams) {
  	const version = await this.materialPubDao.detail(params.id);

	  if (!version) {
		  throw new Error('版本不存在');
	  }

	  await this.materialPubDao.update(params);
		await this.noticeService.notice(version.material_id, MaterialOperateAction.UPDATE, version.version);
	}

	async updateSchema(params: { id: number; props: AnyType }) {
		const version = await this.materialPubDao.detail(params.id);

		if (!version) {
			throw new Error('版本不存在');
		}

		const schema = safeParse(version.schema);
		schema.props = params.props;
		await this.materialPubDao.update({ id: version.id, schema: safeStringify(schema) });
		await this.noticeService.notice(version.material_id, MaterialOperateAction.UPDATE, version.version);
	}

	async detail(params: { id?: number; namespace?: string; version?: string }) {
  	return params.id ? this.materialPubDao.detail(params.id) : this.materialPubDao.detailByNamespaceVersion(params.namespace, params.version);
	}

	async query(params: Module.Material.GetMaterialPubListParams) {
  	const mergedParams = {
  		status: 1,
  		...params,
  		pageSize: Number(params.pageSize || PAGE_SIZE),
  		pageNum: Number(params.pageNum || PAGE_NUM),
  	};

  	const [dataSource, total] = await Promise.all([
  		this.materialPubDao.query({
  			...mergedParams,
  			offset: computeOffset(mergedParams.pageNum, mergedParams.pageSize),
  		}),
  		this.materialPubDao.queryListTotal(mergedParams),
  	]);

  	return {
  		pageSize: mergedParams.pageSize,
  		pageNum: mergedParams.pageNum,
		  dataSource: dataSource.map(v => {
			  return {
  				id: v.id,
				  preview_img: v.preview_img,
				  version: v.version,
				  creator_id: v.creator_id,
				  create_time: v.create_time,
				  readme: v.readme,
				  status: v.status
  			};
		  }),
  		total,
  	};
	}

	async getMaterialAllPub(id: number) {
  	return this.materialPubDao.query({ material_id: id });
	}

	async exist(id: number) {
  	return !!(await this.detail({ id }));
	}

	async getMaterialAllPubByMaterialIds(body: { materialIds: number[] }) {
		return this.materialPubDao.queryVersionsByMaterialIds({ materialIds: body.materialIds });
	}
}
