import { DOBase } from '@mybricks/rocker-dao';
import { EffectStatus, PAGE_SIZE, RefType } from '../../common/const';
import { computeOffset } from '../../common/util/paging';
import { genMainIndexOfDB } from '../../common/util';
import { CODE_TYPE } from '@global-material-middleoffice/server-v2/shared';

export default class MaterialReferDao extends DOBase {
	public async query(params: Omit<Module.Material.GetMaterialReferParams, 'id'> & { namespace: string }) {
		return await this.exe<Module.Material.Refer[]>('material_refer:query', {
			...params,
			pageSize: Number(params.pageSize || PAGE_SIZE),
			offset: computeOffset(params.pageNum, params.pageSize),
		});
	}

	public async queryTotalCount(params: Omit<Module.Material.GetMaterialReferParams, 'id'> & { namespace: string }) {
		return await this.exe<[{total_count: number}]>('material_refer:queryTotalCount', {
			...params,
		});
	}

	public async queryAll() {
		return await this.exe<Module.Material.Refer[]>('material_refer:queryAll');
	}

	public async queryDailyMaterialPageCount(params: { namespace: string, business: Business}) {
		return await this.exe<[{create_date:Date; material_page_count: number}]>('material_refer:queryDailyMaterialPageCount', {
			...params
		});
	}

	public async queryByBusiness_Time(params: { business: string; start_time: number; end_time: number }) {
		return await this.exe<Module.Material.Refer[]>('material_refer:queryByBusiness_Time', params);
	}

	public async queryTotalByBusiness(params: { business: string }) {
		return await this.exe<Array<{ total: number }>>('material_refer:queryTotalByBusiness', params);
	}

	public async queryReferCountByBusiness(params: { business: string }) {
		return await this.exe<Array<{ total: number }>>('material_refer:queryReferCountByBusiness', params);
	}

	public async queryTotalByNamespace(params: { namespace: string }) {
		return await this.exe<Array<{ total: number }>>('material_refer:queryTotalByNamespace', params);
	}

	public async queryReferCountByNamespace(params: { namespace: string }) {
		return await this.exe<Array<{ total: number }>>('material_refer:queryReferCountByNamespace', params);
	}

	public async deleteByReferAndNamespace(params: { refer_page: string; namespace: string; start_time?: number; end_time?: number }) {
		return await this.exe('material_refer:deleteByReferAndNamespace', params);
	}

	public async deleteByGitAndNamespace(params: { refer_page: string; start_time?: number; end_time?: number }) {
		return await this.exe('material_refer:deleteByGitAndNamespace', params);
	}

	public async update(params: { id: number; namespace: string; }) {
		return await this.exe('material_refer:update', params);
	}

	public async create(params: Module.Material.CreateMaterialReferItem & { creator_id: number }) {
		const result = await this.exe<{ insertId: number }>('material_refer:create', {
			...params,
			id: genMainIndexOfDB(),
			refer_count: params.refer_count || 1,
			type: params.type || RefType.REFER,
			create_time: params.create_time ?? Date.now(),
			status: EffectStatus.EFFECT,
			code_type: params.code_type || CODE_TYPE.PRO_CODE,
		});

		return result.insertId;
	}
}
