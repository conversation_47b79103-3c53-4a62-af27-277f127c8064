import axios from 'axios';
import { Body, Controller, Get, Inject, Post, Query } from '@nestjs/common';

import MaterialReferService from './material-refer.service';
import { isProd, safeParse } from '../../common/util';
import { PromiseAllFulfilled } from '../../common/util/Promise';
import { ProxyV2Middleware } from '../../middleware/proxy.v2.middleware';

@Controller('api/manage/refer')
export default class MaterialReferController {
  @Inject(MaterialReferService)
  materialReferService: MaterialReferService;

  @Post('/create')
  async createMaterialRefer(
    @Body() body: Module.Material.CreateMaterialReferParams,
  ) {
  	try {
  		return { code: 1, data: await this.materialReferService.create(body) };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '创建物料引用记录失败，错误原因是' + (error.message ?? error),
  		};
  	}
  }

  @Post('/create_for_kael')
  async createMaterialReferForKael(
    @Body() body: Module.Material.CreateMaterialReferParamsForKael,
  ) {
  	try {
  		if (!body.creator || !body.kael_schema || !body.ref_page) {
  			return {
  				code: -1,
  				message: '参数 creator、kael_schema、ref_page 必传',
  			};
  		}
  		try {
  			const result = await axios.post(`http://localhost:${ProxyV2Middleware.v2Port}/api/v2/low-code/report/tianhe/page/report`, {
  				ref_page: body.ref_page,
  				version: body.version,
  				kael_schema: body.kael_schema,
          creator: body.creator,
  			}, {
          timeout: 10 * 60 * 1000,
        });
  			if (result.data.code !== 1) {
  				console.error('请求计算服务失败');
  				throw new Error();
  			}
  			return result.data;
  		} catch (e) {
        console.log(e);
  			return {
  				code: 1,
  				data: await this.materialReferService.createForKael({
  					...body,
  					kael_schema:
              typeof body.kael_schema === 'string'
              	? safeParse(body.kael_schema)
              	: body.kael_schema,
  				}),
  			};
  		}


  	} catch (error) {
  		return {
  			code: -1,
  			message: '创建物料引用记录失败，错误原因是' + (error.message ?? error),
  		};
  	}
  }

  @Post('/refresh-kael-from-tianhe-page-path')
  async refreshKaelFromTianhePagePath(
    @Body('pages') pages: { path: string; version: string }[],
  ) {
  	try {
  		return {
  			code: 1,
  			data: await PromiseAllFulfilled(
  				pages.map((item) =>
  					this.materialReferService.refreshKaelFromTianhePagePath(
  						item.path,
  						item.version,
  					),
  				),
  			),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '刷新 Kael 数据集失败，错误原因是' + error.message,
  		};
  	}
  }

  @Post('/refresh_kael_dataset')
  async refreshKaelDataset() {
  	try {
  		return this.materialReferService.refreshKaelDataset();
  	} catch (error) {
  		return {
  			code: -1,
  			message: '刷新 Kael 数据集失败，错误原因是' + error.message,
  		};
  	}
  }

  @Post('/create_for_fangzhou')
  async createMaterialReferForFangZhou(
    @Body() body: Module.Material.CreateMaterialReferParamsForFangZhou,
  ) {
  	try {
  		if (
  			!body.creator ||
        !body.schema ||
        !body.ref_page ||
        !body.refers?.length
  		) {
  			return {
  				code: -1,
  				message: '参数 creator、schema、ref_page、refers 必传',
  			};
  		}
  		return {
  			code: 1,
  			data: await this.materialReferService.createForFangZhou(body),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '创建物料引用记录失败，错误原因是' + (error.message ?? error),
  		};
  	}
  }

  @Get('/list')
  async getMaterialRefers(
    @Query() query: Module.Material.GetMaterialReferParams,
  ) {
  	if (!query.id) {
  		return { code: -1, message: '参数 id 必传' };
  	}

  	try {
  		return {
  			code: 1,
  			data: await this.materialReferService.getMaterialRefers(query),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询物料引用记录失败，错误原因是' + error.message,
  		};
  	}
  }

  @Get('/rate')
  async getDailyMaterialReferRate(
    @Query() query: Module.Material.GetMaterialReferParams,
  ) {
  	if (!query.id) {
  		return { code: -1, message: '参数 id 必传' };
  	}

  	try {
  		return {
  			code: 1,
  			data: await this.materialReferService.getDailyMaterialReferRate(query),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询物料引用记录失败，错误原因是' + error.message,
  		};
  	}
  }

  @Get('/query-low-code-page-complexity-report')
  async queryLowCodePageComplexityReport() {
  	try {
  		return {
  			code: 1,
  			data: await this.materialReferService.queryLowCodePageComplexityReport(),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询页面复杂度报告失败，错误原因是' + error.message,
  		};
  	}
  }

  @Get('/query-low-code-platform-user-time-report')
  async queryLowCodePlatformUserTimeReport() {
  	try {
  		return {
  			code: 1,
  			data: await this.materialReferService.queryLowCodePlatformUserTimeReport(),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询低代码平台用户时间报告失败，错误原因是' + error.message,
  		};
  	}
  }
}
