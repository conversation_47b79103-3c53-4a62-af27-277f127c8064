import { DOBase } from '@mybricks/rocker-dao';
import { Business, EffectStatus } from '../../common/const';
import { genMainIndexOfDB } from '../../common/util';

export default class LowCodePageDao extends DOBase {
	public async create(params: Module.Report.CreateLowCodePageItem) {
		const result = await this.exe<{ insertId: number }>(
			'lowcode_page_info:create',
			{
				pages: [
					{
						...params,
						id: genMainIndexOfDB(),
						update_time: Date.now(),
						status: EffectStatus.EFFECT,
					},
				],
			},
		);

		return result.insertId;
	}

	public async update(params: Module.Report.UpdateLowCodePageItem) {
		await this.exe<{ insertId: number }>('lowcode_page_info:update', params);
	}

	public async queryComplexityReport() {
		return await this.exe('lowcode_page_info:queryComplexityReport');
	}

	public async queryUserTimeReport() {
		return await this.exe('lowcode_build_report:queryUserTimeReport');
	}

	public async queryKaelPageInfo() {
		return await this.exe<
      {
        id: number;
        url: string;
        version: string;
      }[]
    >('lowcode_page_info:queryKaelPageInfo');
	}

	public async findByURLAndVersion(url: string, version: string) {
		return (
			await this.exe<{ id: number; url: string; schema: string }[]>(
				'lowcode_page_info:findByURLAndVersion',
				{ url, version },
			)
		)?.[0];
	}

	public async queryDailyPageCount({ key }:{ key?: string }) {
		return await this.exe<[{value: Array<{time: number, count: number}>}]>('lowcode_page_info:queryDailyPageCount', {
			key
		});
	}
}
