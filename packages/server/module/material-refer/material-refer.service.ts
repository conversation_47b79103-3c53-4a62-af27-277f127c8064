import { Inject, Injectable } from "@nestjs/common";
import {
  decodeComponentType,
  genComponentChildrenIncludeProps,
} from "@kael/schema-utils";
import { getPageLevelFromSchema } from "@kael/complexity";
import MaterialReferDao from "./material-refer.dao";
import UserDao from "../user/user.dao";
import MaterialDao from "../material/material.dao";
import {
  Business,
  LEVEL_MAP,
  LowCodeBuildReportType,
  RefType,
} from "../../common/const";
import LowCodePageDao from "./low-code-page.dao";
import ReportDao from "../report/report.dao";
import { generateTimestamps, safeStringify } from "../../common/util";
import axios from "axios";
import get from "lodash/get";
import { CODE_TYPE } from "@global-material-middleoffice/server-v2/shared";

const FANG_ZHOU_BLOCK_COMPONENTS = [
  "mybricks.core-comlib.fn",
  "mybricks.core-comlib.var",
  "mybricks.core-comlib.type-change",
  "mybricks.core-comlib.connector",
  "mybricks.core-comlib.frame-input",
  "mybricks.core-comlib.frame-output",
  "mybricks.core-comlib.scenes",
  "mybricks.core-comlib.defined-com",
  "mybricks.core-comlib.module",
  "mybricks.core-comlib.group",
  "mybricks.core-comlib.selection",
];

@Injectable()
export default class MaterialReferService {
  @Inject(MaterialReferDao)
  materialReferDao: MaterialReferDao;
  @Inject(UserDao)
  userDao: UserDao;
  @Inject(MaterialDao)
  materialDao: MaterialDao;
  @Inject(LowCodePageDao)
  lowCodePageDao: LowCodePageDao;
  @Inject(ReportDao)
  reportDao: ReportDao;

  async create(params: Module.Material.CreateMaterialReferParams) {
    const [user] = await this.userDao.queryByUserName(params.creator);

    if (!user) {
      throw new Error("用户不存在");
    }
    /** 方舟组件的 namespace 需要转换 */
    if (params.business === Business.FANG_ZHOU) {
      params.refers = params.refers.filter((refer) => {
        return !FANG_ZHOU_BLOCK_COMPONENTS.includes(refer.namespace);
      });
      params.refers.forEach((refer) => {
        refer.namespace = `@fangzhou/component-${refer.namespace.replace(
          /(\w*)[-\\.](\w*)/g,
          ($1, $2, $3) => {
            return $2 + $3[0].toUpperCase() + $3.slice(1);
          },
        )}`;
      });
    }

    for (const refer of params.refers) {
      if (refer.needClear) {
        await this.materialReferDao.deleteByReferAndNamespace({
          refer_page: refer.ref_page,
          namespace: refer.namespace,
        });
      }

      await this.materialReferDao.create({
        ...refer,
        namespace: refer.namespace,
        creator_id: user.id,
        code_type:
          params.code_type ||
          ([Business.KAEL, Business.FANG_ZHOU].includes(params.business)
            ? CODE_TYPE.LOW_CODE
            : CODE_TYPE.PRO_CODE),
      });
    }
  }

  async createForKael(
    params: Module.Material.CreateMaterialReferParamsForKael,
  ) {
    const [user] = await this.userDao.queryByUserName(params.creator);

    if (!user) {
      throw new Error("用户不存在");
    }

    let result = {};
    let level = [params.kael_schema?.view] || [];
    while (level.length) {
      const nextLevel: typeof level = [];
      for (const component of level) {
        if (component) {
          const [libType, compType] = decodeComponentType(component.type);
          if (!result[libType]) {
            result[libType] = {};
          }
          if (result[libType]?.[compType] === undefined) {
            result[libType][compType] = { count: 1 };
          } else {
            let preCount = get(result, [libType, compType])?.count || 0;
            result[libType][compType] = {
              ...(result[libType][compType] || {}),
              count: preCount + 1,
            };
          }

          for (const child of genComponentChildrenIncludeProps(component)) {
            nextLevel.push(child);
          }
        }
      }
      level = nextLevel;
    }

    const refers = [];
    Object.keys(result).forEach((key) => {
      const lib = params.kael_schema.materialLibs.find(
        (lib) => lib.name === key,
      );

      Object.keys(result[key]).forEach((materialKey) => {
        const component = lib?.schemaJson?.components?.find?.(
          (com) => com.moduleCode === materialKey,
        );

        refers.push({
          namespace: component?.componentName ?? `${key}/${materialKey}`,
          version: component?.moduleVersion ?? "0.0.0",
          ref_page: params.ref_page,
          ref_business: Business.KAEL,
          type: RefType.REFER,
          refer_count: result[key][materialKey]?.count ?? 1,
          code_type: CODE_TYPE.LOW_CODE,
          needClear: true,
          create_time: Date.now(),
        });
      });
    });

    try {
      let pageId: number;
      const alreadyPage = await this.lowCodePageDao.findByURLAndVersion(
        params.ref_page,
        params.version,
      );
      if (alreadyPage) {
        pageId = alreadyPage.id;
        await this.lowCodePageDao.update({
          id: pageId,
          schema: safeStringify(params.kael_schema),
        });
      } else {
        pageId = await this.lowCodePageDao.create({
          business: Business.KAEL,
          url: params.ref_page,
          version: params.version,
          schema: safeStringify(params.kael_schema),
          content: JSON.stringify({}),
        });
      }

      await this.reportDao.createLowCodeReport({
        /** 低码页面信息记录ID */
        page_id: pageId,
        /** 报表指标类型 */
        type: LowCodeBuildReportType.BUILD_TIME,
        /** 指标值 */
        value: get(
          params.kael_schema,
          ["designerState", "editTime", "duration"],
          get(params.kael_schema, ["editTime"], 0),
        ),
      });

      let count = 0;
      let level = "L1";
      try {
        const res = getPageLevelFromSchema(params.kael_schema);
        count = res.count;
        level = res.level;
      } catch (e) {
        const urlParts = params.ref_page.split("/").filter((part) => part);
        const pageCode = urlParts[urlParts.length - 1];
        const appKey = urlParts[urlParts.length - 3];
        const blobStoreURL = `https://bs3-hb1.corp.kuaishou.com/image-tianhe/%22${[appKey, pageCode, params.version].join("_")}%22`;
        console.log(
          "千象页面指标录入错误",
          blobStoreURL,
          params.ref_page,
          params.version,
          e,
        );
      }
      await this.reportDao.createLowCodeReport({
        /** 低码页面信息记录ID */
        page_id: pageId,
        /** 报表指标类型 */
        type: LowCodeBuildReportType.LEVEL,
        /** 指标值 */
        value: LEVEL_MAP[level] ?? 0,
      });
      console.log("录入成功", pageId, "level:", LEVEL_MAP[level]);
      await this.lowCodePageDao.update({
        id: pageId,
        content: JSON.stringify({
          report: {
            complex_value: count ?? 0,
            level: level ?? 0,
            build_time: get(
              params.kael_schema,
              ["designerState", "editTime", "duration"],
              get(params.kael_schema, ["editTime"], 0),
            ),
          },
        }),
      });
    } catch (e) {
      console.log("指标录入错误", e);
    }

    await this.create({
      refers,
      business: Business.KAEL,
      code_type: CODE_TYPE.LOW_CODE,
      creator: params.creator,
    });
  }

  async refreshKaelDataset() {
    const errors = [];
    // 1. 所有 kael 的 page info 列表（只需要基本信息即可）
    const kaelPageInfos = await this.lowCodePageDao.queryKaelPageInfo();
    // 2. 遍历 page info 列表
    for (let pageIdx = 0; pageIdx < kaelPageInfos.length; pageIdx++) {
      const { url, version } = kaelPageInfos[pageIdx];
      // 3. 拆解 page info 里的 url 信息，并拼接成完整的 blob store url
      const urlParts = url.split("/").filter((part) => part);
      const pageCode = urlParts[urlParts.length - 1];
      const appKey = urlParts[urlParts.length - 3];
      const blobStoreURL = `https://bs3-hb1.corp.kuaishou.com/image-tianhe/%22${[appKey, pageCode, version].join("_")}%22`;

      // 4. 通过 blob store url 获取到 page 的数据，注意转换成 JSON 格式
      try {
        const { data } = await axios.get(blobStoreURL);
        const storeSchema = typeof data === "string" ? JSON.parse(data) : data;
        // 5. 将数据集信息写入到数据库中
        this.createForKael({
          creator: "yingpengsha",
          kael_schema: storeSchema,
          ref_page: url,
          version,
        });
      } catch (error) {
        if (error.response && error.response.status === 404) {
          console.log("404 not found", blobStoreURL);
        } else {
          errors.push({
            page: url,
            version,
            blobStoreURL,
            error: error,
          });
        }
      }
    }
    console.log(errors.length, errors);
    return errors;
  }

  async refreshKaelFromTianhePagePath(tianhePath: string, version: string) {
    const errors = [];

    const tianheURL = `https://tianhe.corp.kuaishou.com/applications/${tianhePath}`;

    const urlParts = tianhePath.split("/").filter((part) => part);
    const pageCode = urlParts[urlParts.length - 1];
    const appKey = urlParts[urlParts.length - 3];
    const blobStoreURL = `https://bs3-hb1.corp.kuaishou.com/image-tianhe/%22${[appKey, pageCode, version].join("_")}%22`;

    try {
      const { data } = await axios.get(blobStoreURL);
      const storeSchema = typeof data === "string" ? JSON.parse(data) : data;
      // 5. 将数据集信息写入到数据库中
      this.createForKael({
        creator: "yingpengsha",
        kael_schema: storeSchema,
        ref_page: tianheURL,
        version,
      });
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log("404 not found", blobStoreURL);
      } else {
        errors.push({
          page: tianheURL,
          version,
          blobStoreURL,
          error: error,
        });
      }
    }
  }

  async createForFangZhou(
    params: Module.Material.CreateMaterialReferParamsForFangZhou,
  ) {
    const [user] = await this.userDao.queryByUserName(params.creator);

    if (!user) {
      throw new Error("用户不存在");
    }

    try {
      const pageId = await this.lowCodePageDao.create({
        business: Business.FANG_ZHOU,
        url: params.ref_page,
        version: params.version,
        schema: safeStringify(params.schema),
        content: JSON.stringify({ report: params.summary }),
      });
      let buildTime: AnyType = params.summary?.costTime ?? 0;

      if (buildTime) {
        if (buildTime?.includes("分钟")) {
          buildTime = Number(buildTime?.replace("分钟", "")) * 60 * 1000;
        } else if (buildTime?.includes("小时")) {
          buildTime = Number(buildTime?.replace("小时", "")) * 60 * 60 * 1000;
        }
      }

      await this.reportDao.createLowCodeReport({
        /** 低码页面信息记录ID */
        page_id: pageId,
        /** 报表指标类型 */
        type: LowCodeBuildReportType.BUILD_TIME,
        /** 指标值 */
        value: buildTime ?? 0,
      });

      await this.reportDao.createLowCodeReport({
        /** 低码页面信息记录ID */
        page_id: pageId,
        /** 报表指标类型 */
        type: LowCodeBuildReportType.LEVEL,
        /** 指标值 */
        value: LEVEL_MAP[params.summary?.level as string] ?? 0,
      });
    } catch (e) {
      console.log("指标录入错误", e);
    }

    await this.create({
      refers: params.refers,
      business: Business.FANG_ZHOU,
      code_type: CODE_TYPE.LOW_CODE,
      creator: params.creator,
    });
  }

  async getMaterialRefers(query: Module.Material.GetMaterialReferParams) {
    const material = await this.materialDao.detail(query.id);

    if (!material) {
      throw Error("物料不存在");
    }

    return await this.materialReferDao.query({
      ...query,
      namespace: material.namespace,
    });
  }

  async queryLowCodePageComplexityReport() {
    return await this.lowCodePageDao.queryComplexityReport();
  }

  async queryLowCodePlatformUserTimeReport() {
    return await this.lowCodePageDao.queryUserTimeReport();
  }

  async getDailyMaterialReferRate(
    query: Module.Material.GetMaterialReferParams,
  ) {
    const material = await this.materialDao.detail(query.id);

    if (!material) {
      throw Error("物料不存在");
    }
    const dailyMaterialPageCount =
      await this.materialReferDao.queryDailyMaterialPageCount({
        namespace: material.namespace.replace(/(\([^()]*\))+$/g, ""),
        business: query.business,
      });
    const [{ value: dailyPageCount }] =
      await this.lowCodePageDao.queryDailyPageCount({
        key:
          material.business === "fangzhou"
            ? "FANGZHOU_PAGE_COUNT_GROWTH"
            : "TIANHE_PAGE_COUNT_GROWTH",
      });
    dailyPageCount.sort((a, b) => a.time - b.time);
    const accumulatedDailyMaterialPageCount = dailyMaterialPageCount.reduce(
      (acc, item) => {
        const cutoffDate = new Date("2024-06-03T00:00:00+08:00").getTime();

        if (item.create_date.getTime() >= cutoffDate) {
          const count = (acc.totalCount || 0) + item.material_page_count;
          acc.totalCount = count;
          acc.result.push({ time: item.create_date, count });
        }
        return acc;
      },
      { totalCount: 0, result: [] },
    ).result;

    const accumulatedDailyPageCount = dailyPageCount.reduce(
      (acc, item) => {
        const cutoffDate = new Date("2024-06-03T00:00:00+08:00").getTime();

        if (item.time >= cutoffDate) {
          const count = (acc.totalCount || 0) + item.count;
          acc.totalCount = count;
          acc.result.push({ time: item.time, count });
        }
        return acc;
      },
      { totalCount: 0, result: [] },
    ).result;

    const timestampsArray = generateTimestamps(
      "2024-06-03",
      Date.now() - 86400000,
    );
    let preMaterialPageCount = null;
    let prePageCount = accumulatedDailyPageCount[0];
    return timestampsArray.map((timestamp) => {
      const materialPageCount = accumulatedDailyMaterialPageCount.find(
        (item) => timestamp === item.time.getTime(),
      );
      const pageCount = accumulatedDailyPageCount.find(
        (item) => timestamp === item.time,
      );
      if (materialPageCount) {
        preMaterialPageCount = materialPageCount;
      }
      if (pageCount) {
        prePageCount = pageCount;
      }
      return {
        create_time: timestamp,
        rate: (
          (materialPageCount?.count ?? preMaterialPageCount?.count ?? 0) /
          (pageCount?.count ?? prePageCount?.count)
        ).toFixed(4),
      };
    });
  }
}
