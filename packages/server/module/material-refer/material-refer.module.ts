import { Module } from '@nestjs/common';
import MaterialReferController from './material-refer.controller';
import MaterialReferService from './material-refer.service';
import MaterialReferDao from './material-refer.dao';
import UserDao from '../user/user.dao';
import MaterialDao from '../material/material.dao';
import LowCodePageDao from './low-code-page.dao';
import ReportDao from '../report/report.dao';

@Module({
	imports: [],
	controllers: [MaterialReferController],
	providers: [MaterialReferService, MaterialReferDao, MaterialDao, UserDao, LowCodePageDao, ReportDao],
	exports: [MaterialReferService, MaterialReferDao, LowCodePageDao],
})
export class MaterialReferModule {}
