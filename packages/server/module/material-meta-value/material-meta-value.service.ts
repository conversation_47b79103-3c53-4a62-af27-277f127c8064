import { Inject, Injectable } from '@nestjs/common';
import MaterialMetaValueDao from './material-meta-value.dao';
import { EffectStatus, PAGE_NUM, PAGE_SIZE } from '../../common/const';
import { computeOffset } from '../../common/util/paging';

@Injectable()
export default class MaterialMetaValueService {
  @Inject(MaterialMetaValueDao)
  	materialMetaValueDao: MaterialMetaValueDao;

  async create(params: Module.Meta.CreateMetaValueParams) {
  	const [value] = await this.materialMetaValueDao.queryByMetaIdAndValue({ meta_id: params.meta_id, value: params.value });
		
  	if (value) {
  		throw new Error('元信息扩充数据值已存在');
  	}
		
  	return this.materialMetaValueDao.create(params);
  }

  async delete(id: number) {
  	return this.materialMetaValueDao.delete(id);
  }

  async update(params: Module.Meta.UpdateMetaValueParams) {
  	if (await this.exist(params.id)) {
  		return this.materialMetaValueDao.update(params);
  	} else {
  		throw new Error('元信息扩充数据值不存在');
  	}
  }

  async detail(id: number) {
  	return this.materialMetaValueDao.detail(id);
  }

  async query(params: Module.Meta.GetMetaValueListParams) {
  	const mergedParams = {
  		...params,
		  title: params.title ? `%${params.title}%` : undefined,
		  pageSize: Number(params.pageSize || PAGE_SIZE),
		  status: params.status ?? EffectStatus.EFFECT,
		  pageNum: Number(params.pageNum || PAGE_NUM),
  	};
  	const [dataSource, total] = await Promise.all([
  		this.materialMetaValueDao.query({
  			...mergedParams,
  			offset: computeOffset(mergedParams.pageNum, mergedParams.pageSize),
  		}),
  		this.materialMetaValueDao.queryListTotal(mergedParams),
  	]);

  	return {
  		pageSize: mergedParams.pageSize,
  		pageNum: mergedParams.pageNum,
  		dataSource,
  		total,
  	};
  }

  async exist(id: number) {
  	return !!(await this.detail(id));
  }
}
