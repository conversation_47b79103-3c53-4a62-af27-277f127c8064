import { Module } from '@nestjs/common';
import MaterialMetaValueController from './material-meta-value.controller';
import MaterialMetaValueService from './material-meta-value.service';
import MaterialMetaValueDao from './material-meta-value.dao';

@Module({
	imports: [],
	controllers: [MaterialMetaValueController],
	providers: [MaterialMetaValueService, MaterialMetaValueDao],
	exports: [MaterialMetaValueService, MaterialMetaValueDao],
})
export class MaterialMetaValueModule {}
