import { DOBase } from '@mybricks/rocker-dao';
import { genMainIndexOfDB } from '../../common/util';
import { EffectStatus } from '../../common/const';

export default class MaterialMetaValueDao extends DOBase {
	public async create(params: Module.Meta.CreateMetaValueParams) {
		const result = await this.exe<{ insertId: number }>('material_meta_value:create', {
			...params,
			id: genMainIndexOfDB(),
			order: params.order ?? 0,
			create_time: Date.now(),
			status: EffectStatus.EFFECT
		});

		return result.insertId;
	}

	public async delete(id: number) {
		return await this.exe('material_meta_value:delete', { id });
	}

	public async update(params: Module.Meta.UpdateMetaValueParams) {
		return await this.exe('material_meta_value:update', params);
	}

	public async detail(id: number) {
		return (await this.exe<Module.Meta.MetaValue[]>('material_meta_value:detail', { id }))?.[0];
	}

	public async query(params: Module.Meta.GetMetaValueListParams & { offset: number }) {
		return await this.exe<Module.Meta.MetaValue[]>('material_meta_value:query', params);
	}

	public async queryByMetaIds(params: { meta_ids: number[] }) {
		if (!params.meta_ids?.length) {
			return [];
		}

		return await this.exe<Module.Meta.MetaValue[]>('material_meta_value:queryByMetaIds', params);
	}

	public async queryByIds(params: { ids: number[] }) {
		if (!params.ids?.length) {
			return [];
		}

		return await this.exe<Module.Meta.MetaValue[]>('material_meta_value:queryByIds', params);
	}

	public async queryByMetaIdAndValue(params: { meta_id: number; value: string }) {
		return await this.exe<Module.Meta.MetaValue[]>('material_meta_value:queryByMetaIdAndValue', params);
	}

	public async queryListTotal(params: Omit<Module.Meta.GetMetaValueListParams, 'pageNum' | 'pageSize'>) {
		const result = await this.exe<Array<{ total: number }>>('material_meta_value:queryListTotal', params);
		return result[0]?.total;
	}
}
