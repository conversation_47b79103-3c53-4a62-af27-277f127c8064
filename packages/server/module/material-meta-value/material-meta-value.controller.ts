import { Body, Controller, Get, Inject, Post, Query } from '@nestjs/common';
import { isNil } from 'lodash';
import MaterialMetaValueService from './material-meta-value.service';

@Controller('api/manage/meta/value')
export default class MaterialMetaController {
  @Inject(MaterialMetaValueService)
  	materialMetaValueService: MaterialMetaValueService;

  @Post('/create')
  async create(@Body() body: Module.Meta.CreateMetaValueParams) {
	  if (!body.title || !body.value  || !body.meta_id || !body.creator_id) {
		  return { code: -1, message: '参数 title、value、meta_id、creator_id 不能为空' };
	  }

	  try {
		  return { code: 1, data: await this.materialMetaValueService.create(body), message: '元信息扩充数据值新建成功' };
	  } catch (error) {
		  return { code: -1, message: '元信息扩充数据值新建失败，错误原因是' + error.message };
	  }
  }

  @Post('/delete')
  async delete(@Body('id') id: number) {
	  if (!id) {
		  return { code: -1, message: '参数 id 不能为空' };
	  }

  	await this.materialMetaValueService.delete(id);

  	return { code: 1, message: '元信息扩充数据值删除成功' };
  }

  @Post('/update')
  async updated(@Body() body: Module.Meta.UpdateMetaValueParams) {
	  if (!body.id || (!body.title && isNil(body.status) && !body.value)) {
		  return { code: -1, message: '参数 id 不能为空，且参数 title、status、value 不能同时为空' };
	  }

  	try {
  		await this.materialMetaValueService.update(body);

  		return { code: 1, message: '元信息扩充数据值更新成功' };
  	} catch (error) {
  		return { code: -1, message: '元信息扩充数据值更新失败，错误原因是' + error.message };
  	}
  }

  @Get('/list')
  async getList(@Query() params: Module.Meta.GetMetaValueListParams) {
  	try {
  		return { code: 1, data: await this.materialMetaValueService.query(params) };
  	} catch (error) {
  		return { code: -1, message: '查询元信息扩充数据值列表失败' };
  	}
  }
}
