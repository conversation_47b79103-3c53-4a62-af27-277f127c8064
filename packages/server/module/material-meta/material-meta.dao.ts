import { DOBase } from '@mybricks/rocker-dao';
import { genMainIndexOfDB } from '../../common/util';
import { EffectStatus } from '../../common/const';

export default class MaterialMetaDao extends DOBase {
	public async create(params: Module.Meta.CreateMetaParams) {
		const result = await this.exe<{ insertId: number }>('material_meta:create', {
			...params,
			id: genMainIndexOfDB(),
			meta: params.meta ?? '{}',
			create_time: Date.now(),
			status: EffectStatus.EFFECT
		});

		return result.insertId;
	}

	public async delete(id: number) {
		return await this.exe('material_meta:delete', { id });
	}

	public async update(params: Module.Meta.UpdateMetaParams) {
		return await this.exe('material_meta:update', params);
	}

	public async detail(id: number) {
		return (await this.exe<Module.Meta.Meta[]>('material_meta:detail', { id }))?.[0];
	}

	public async query(params: Module.Meta.GetMetaListParams & { offset: number }) {
		return await this.exe<Module.Meta.Meta[]>('material_meta:query', params);
	}

	public async queryByIds(params: { ids: number[] }) {
		if (!params.ids?.length) {
			return [];
		}

		return await this.exe<Module.Meta.Meta[]>('material_meta:queryByIds', params);
	}

	public async queryListTotal(params: Omit<Module.Meta.GetMetaListParams, 'pageNum' | 'pageSize'>) {
		const result = await this.exe<Array<{ total: number }>>('material_meta:queryListTotal', params);
		return result[0]?.total;
	}

	public async queryByBusiness(business: string) {
		return await this.exe<Module.Meta.Meta[]>('material_meta:queryByBusiness', { business });
	}
}
