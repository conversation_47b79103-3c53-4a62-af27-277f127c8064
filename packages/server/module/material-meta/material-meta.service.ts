import { Inject, Injectable } from '@nestjs/common';
import MaterialMetaDao from './material-meta.dao';
import { EffectStatus, PAGE_NUM, PAGE_SIZE } from '../../common/const';
import { computeOffset } from '../../common/util/paging';
import MaterialDao from '../material/material.dao';
import MaterialMetaValueDao from '../material-meta-value/material-meta-value.dao';
import MaterialMetaValueRelationDao from '../material-meta-value-relation/material-meta-value-relation.dao';
import MaterialPubDao from '../material-pub/material-pub.dao';
import { safeParse, safeStringify } from '../../common/util';
@Injectable()
export default class MaterialMetaService {
  @Inject(MaterialMetaDao)
  	materialMetaDao: MaterialMetaDao;
  @Inject(MaterialDao)
  	materialDao: MaterialDao;
  @Inject(MaterialMetaValueDao)
  	materialMetaValueDao: MaterialMetaValueDao;
  @Inject(MaterialMetaValueRelationDao)
  	materialMetaValueRelationDao: MaterialMetaValueRelationDao;
	@Inject(MaterialPubDao)
		materialPubDao: MaterialPubDao;

	async create(params: Module.Meta.CreateMetaParams) {
  	const metas = await this.materialMetaDao.query({
  		business: params.business,
  		status: EffectStatus.EFFECT,
  		offset: 0,
  		pageSize: 100
  	});
  	const find = metas.find(item => item.value === params.value);
		
  	if (find) {
  		throw new Error('元信息已存在');
  	}

  	return this.materialMetaDao.create(params);
	}

	async delete(id: number) {
  	return this.materialMetaDao.delete(id);
	}

	async update(params: Module.Meta.UpdateMetaParams) {
  	if (await this.exist(params.id)) {
  		return this.materialMetaDao.update(params);
  	} else {
  		throw new Error('元信息不存在');
  	}
	}

	async detail(id: number) {
  	return this.materialMetaDao.detail(id);
	}

	async query(params: Module.Meta.GetMetaListParams) {
  	const mergedParams = {
  		...params,
		  title: params.title ? `%${params.title}%` : undefined,
		  pageSize: Number(params.pageSize || PAGE_SIZE),
		  status: params.status ?? EffectStatus.EFFECT,
		  pageNum: Number(params.pageNum || PAGE_NUM),
  	};

  	const [dataSource, total] = await Promise.all([
  		this.materialMetaDao.query({
  			...mergedParams,
  			offset: computeOffset(mergedParams.pageNum, mergedParams.pageSize),
  		}),
  		this.materialMetaDao.queryListTotal(mergedParams),
  	]);

  	return {
  		pageSize: mergedParams.pageSize,
  		pageNum: mergedParams.pageNum,
  		dataSource,
  		total,
  	};
	}

	async exist(id: number) {
  	return !!(await this.detail(id));
	}

	async getMetasByMaterialId(id: number) {
  	const material = await this.materialDao.detail(id);
  	if (!material) {
  		throw new Error('物料不存在');
  	}
		
  	const metas = await this.materialMetaDao.query({ business: material.business, pageSize: 100, offset: 0, status: EffectStatus.EFFECT });
  	const metaValues = await this.materialMetaValueDao.queryByMetaIds({ meta_ids: metas.map(m => m.id), });
  	const relations = await this.materialMetaValueRelationDao.query({ material_id: id, status: EffectStatus.EFFECT });

  	return {
  		metas: metas.map(meta => {
  			const items = metaValues.filter(v => v.meta_id === meta.id);

  			return { ...meta, items };
  		}),
		  value: metas.reduce((pre, meta) => {
  			const metaValueIds = metaValues.filter(v => v.meta_id === meta.id).map(v => v.id);
  			const relationMetaValueIds = relations.map(v => v.meta_value_id);

  			return {
  				...pre,
  				[meta.value]: metaValueIds.filter(id => relationMetaValueIds.includes(id)),
  			};
		  }, {}),
	  };
	}

	async updateMetaRelationByMaterialId(params: Module.Meta.UpdateMetaRelationByMaterialIdParams) {
	  const material = await this.materialDao.detail(params.id);
	  if (!material) {
		  throw new Error('物料不存在');
	  }

	  const metas = await this.materialMetaDao.query({ business: material.business, pageSize: 100, offset: 0, status: EffectStatus.EFFECT });

  	Object.keys(params.value).forEach((key) => {
		  const meta = metas.find(m => m.value === key);
			
		  if (!meta) {
			  throw new Error('元信息不存在');
		  }
  	});

	  await this.materialMetaValueRelationDao.deleteByMaterialId(params.id);

	  for (const key of Object.keys(params.value)) {
  		for (const id of params.value[key]) {
			  await this.materialMetaValueRelationDao.create({ material_id: params.id, meta_value_id: id, creator_id: params.creator_id, });
  		}
	  }

		const materialPub = await this.materialPubDao.detailByNamespaceVersion(
			material.namespace,
			material.version,
		);
		if (materialPub) {
			const schema = safeParse(materialPub.schema);
			if (!schema.tags) {
				schema.tags = {
					meta: {},
				};
			}
			for (const [key, value] of Object.entries(params.value)) {
				if(!schema.tags.meta) {
					schema.tags.meta = {};
				}
				schema.tags.meta[key] = (await this.materialMetaValueDao.queryByIds({
					ids: value,
				})).map(({ value }) => value);
			}
			await this.materialPubDao.update({
				id: materialPub.id,
				schema: safeStringify(schema),
			});
		}
	}
}
