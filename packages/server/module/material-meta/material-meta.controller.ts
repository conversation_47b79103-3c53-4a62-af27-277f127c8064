import { Body, Controller, Get, Inject, Post, Query } from '@nestjs/common';
import { isNil } from 'lodash';
import MaterialMetaService from './material-meta.service';

@Controller('api/manage/meta')
export default class MaterialMetaController {
  @Inject(MaterialMetaService)
  	materialMetaService: MaterialMetaService;

  @Post('/create')
  async create(@Body() body: Module.Meta.CreateMetaParams) {
	  if (!body.title || !body.business || !body.creator_id || !body.value) {
		  return { code: -1, message: '参数 title、value、business、creator_id 不能为空' };
	  }

	  try {
		  return { code: 1, data: await this.materialMetaService.create(body), message: '元信息新建成功' };
	  } catch (error) {
		  return { code: -1, message: '元信息新建失败，错误原因是' + error.message };
	  }
  }

  @Post('/delete')
  async delete(@Body('id') id: number) {
	  if (!id) {
		  return { code: -1, message: '参数 id 不能为空' };
	  }

  	await this.materialMetaService.delete(id);

  	return { code: 1, message: '元信息删除成功' };
  }

  @Post('/update')
  async update(@Body() body: Module.Meta.UpdateMetaParams) {
	  if (!body.id || (!body.title && isNil(body.status) && !body.value)) {
		  return { code: -1, message: '参数 id 不能为空，且参数 title、status、value 不能同时为空' };
	  }

  	try {
  		await this.materialMetaService.update(body);

  		return { code: 1, message: '元信息更新成功' };
  	} catch (error) {
  		return { code: -1, message: '元信息更新失败，错误原因是' + error.message };
  	}
  }

  @Get('/list')
  async getList(@Query() params: Module.Meta.GetMetaListParams) {
  	try {
  		return { code: 1, data: await this.materialMetaService.query(params) };
  	} catch (error) {
  		return { code: -1, message: '查询元信息列表失败' };
  	}
  }


	@Get('/list/material_id')
  async getMetasByMaterialId(@Query('id') id: number) {
  	if (!id) {
  		return { code: 1, message: '参数 id 不能为空' };
  	}

  	try {
  		return { code: 1, data: await this.materialMetaService.getMetasByMaterialId(id) };
  	} catch (error) {
  		return { code: -1, message: '查询扩展元信息失败，错误原因是' + error.message };
  	}
  }

	@Post('/update/material_id')
	async updateMetaRelationByMaterialId(@Body() body: Module.Meta.UpdateMetaRelationByMaterialIdParams) {
		if (!body.id || !body.value) {
			return { code: -1, message: '参数 id、value 不能为空' };
		}

		try {
			await this.materialMetaService.updateMetaRelationByMaterialId(body);

			return { code: 1, message: '元信息关系更新成功' };
		} catch (error) {
			return { code: -1, message: '元信息关系更新失败，错误原因是' + error.message };
		}
	}
}
