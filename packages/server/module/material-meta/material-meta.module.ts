import { Module } from '@nestjs/common';
import MaterialMetaController from './material-meta.controller';
import MaterialMetaService from './material-meta.service';
import MaterialMetaDao from './material-meta.dao';
import MaterialDao from '../material/material.dao';
import MaterialMetaValueDao from '../material-meta-value/material-meta-value.dao';
import MaterialMetaValueRelationDao from '../material-meta-value-relation/material-meta-value-relation.dao';
import MaterialPubDao from '../material-pub/material-pub.dao'

@Module({
	imports: [],
	controllers: [MaterialMetaController],
	providers: [MaterialMetaService, MaterialMetaDao, MaterialDao, MaterialMetaValueDao, MaterialMetaValueRelationDao, MaterialPubDao],
	exports: [MaterialMetaService, MaterialMetaDao],
})
export class MaterialMetaModule {}
