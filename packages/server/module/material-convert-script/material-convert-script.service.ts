import { Inject, Injectable } from '@nestjs/common';
import MaterialConvertScriptDao from './material-convert-script.dao';
import { EffectStatus, PAGE_NUM, PAGE_SIZE } from '../../common/const';

@Injectable()
export default class MaterialConvertScriptService {
  @Inject(MaterialConvertScriptDao)
  	materialConvertScriptDao: MaterialConvertScriptDao;

  async create(params: Module.ConvertScript.CreateConvertScriptParams) {
  	const [script] = await this.materialConvertScriptDao.query({ business: params.business, status: EffectStatus.EFFECT });
  	if (script) {
  		throw new Error('该业务下已经存在脚本');
  	}

  	return this.materialConvertScriptDao.create(params);
  }

  async delete(id: number) {
  	return this.materialConvertScriptDao.delete(id);
  }

  async update(params: Module.ConvertScript.UpdateConvertScriptParams) {
  	if (await this.exist(params.id)) {
  		return this.materialConvertScriptDao.update(params);
  	} else {
  		throw new Error('脚本不存在');
  	}
  }

  async detail(id: number) {
  	return this.materialConvertScriptDao.detail(id);
  }

  async query(params: Module.ConvertScript.GetConvertScriptListParams) {
  	const mergedParams = { ...params, status: params.status ?? EffectStatus.EFFECT };
  	const [dataSource, total] = await Promise.all([
  		this.materialConvertScriptDao.query(mergedParams),
  		this.materialConvertScriptDao.queryListTotal(mergedParams),
  	]);

  	return {
  		pageSize: PAGE_SIZE,
  		pageNum: PAGE_NUM,
  		dataSource,
  		total,
  	};
  }

  async exist(id: number) {
  	return !!(await this.detail(id));
  }
}
