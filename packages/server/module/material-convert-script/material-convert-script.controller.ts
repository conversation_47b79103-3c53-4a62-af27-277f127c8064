import { Body, Controller, Get, Inject, Post, Query } from '@nestjs/common';
import MaterialConvertScriptService from './material-convert-script.service';

@Controller('api/manage/script')
export default class MaterialConvertScriptController {
  @Inject(MaterialConvertScriptService)
  	materialConvertScriptService: MaterialConvertScriptService;

  @Post('/create')
  async create(@Body() body: Module.ConvertScript.CreateConvertScriptParams) {
	  if (!body.business || !body.script || !body.creator_id) {
		  return { code: -1, message: '参数 business、script、creator_id 不能为空' };
	  }

	  try {
		  return { code: 1, data: await this.materialConvertScriptService.create(body), message: '脚本新建成功' };
	  } catch (error) {
		  return { code: -1, message: '脚本新建失败，错误原因是' + error.message };
	  }
  }

  @Post('/delete')
  async delete(@Body('id') id: number) {
  	await this.materialConvertScriptService.delete(id);

  	return { code: 1, message: '脚本删除成功' };
  }

  @Post('/update')
  async updated(@Body() body: Module.ConvertScript.UpdateConvertScriptParams) {
	  if (!body.id) {
		  return { code: -1, message: '参数 id 不能为空' };
	  }

  	try {
  		await this.materialConvertScriptService.update(body);

  		return { code: 1, message: '脚本更新成功' };
  	} catch (error) {
  		return { code: -1, message: '脚本更新失败，错误原因是' + error.message };
  	}
  }

  @Get('/list')
  async getList(@Query() params: Module.ConvertScript.GetConvertScriptListParams) {
  	try {
  		return { code: 1, data: await this.materialConvertScriptService.query(params) };
  	} catch (error) {
  		return { code: -1, message: '查询脚本列表失败' };
  	}
  }
}
