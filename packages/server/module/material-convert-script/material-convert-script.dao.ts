import { DOBase } from '@mybricks/rocker-dao';
import { EffectStatus } from '../../common/const';
import { genMainIndexOfDB } from '../../common/util';

export default class MaterialConvertScriptDao extends DOBase {
	public async create(params: Module.ConvertScript.CreateConvertScriptParams) {
		const result = await this.exe<{ insertId: number }>('material_convert_script:create', {
			...params,
			id: genMainIndexOfDB(),
			create_time: Date.now(),
			status: EffectStatus.EFFECT
		});

		return result.insertId;
	}

	public async delete(id: number) {
		return await this.exe('material_convert_script:delete', { id });
	}

	public async update(params: Module.ConvertScript.UpdateConvertScriptParams) {
		return await this.exe('material_convert_script:update', params);
	}

	public async detail(id: number) {
		return (await this.exe<Module.ConvertScript.ConvertScript[]>('material_convert_script:detail', { id }))?.[0];
	}

	public async query(params: Module.ConvertScript.GetConvertScriptListParams) {
		return await this.exe<Module.ConvertScript.ConvertScript[]>('material_convert_script:query', params);
	}

	public async queryListTotal(
		params: Omit<Module.ConvertScript.GetConvertScriptListParams, 'pageNum' | 'pageSize'>,
	) {
		const result = await this.exe<Array<{ total: number }>>('material_convert_script:queryListTotal', params);
		return result[0]?.total;
	}
}
