import request from 'request';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ForwardService {
  constructor() {}

  async forwardRequest(
    method: string,
    hostname: string,
    path: string,
    headers: any,
    body: any,
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const options = {
        method: method,
        url: hostname + path,
        headers,
        body: JSON.stringify(body),
      };
      console.log(options);
      request(options, function (error, response) {
        if (error) {
          reject(error);
        } else {
          resolve(response.body);
        }
      });
    });
  }
}
