import {
  Controller,
  Req,
  Body,
  Post,
} from '@nestjs/common';
import { Request } from 'express';
import { ForwardService } from './forward.service';

@Controller('/api/proxy')
export class ProxyController {
  constructor(private readonly forwardService: ForwardService) {}

  @Post('/kwaipilot/*')
  async proxy(
    @Req() req: Request,
    @Body('method') method: string,
    @Body('headers') headers: any,
    @Body('body') body: any,
  ) {
    try {
      const result = await this.forwardService.forwardRequest(
        method,
				// 'https://ai-gateway.corp.kuaishou.com',
        'http://ai-gateway.internal',
        req.url.split('/api/proxy/kwaipilot').pop(),
        headers,
        body,
      );
      return result;
    } catch (error) {
      return error;
    }
  }
}
