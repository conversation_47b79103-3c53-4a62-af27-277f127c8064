import { Injectable } from '@nestjs/common';
import { AnalysisService } from './analysis.service';
import { Business } from '../../common/const';

@Injectable()
export class NewCoverageRateService {
	constructor(private analysisService: AnalysisService) {}
	async startNewCoverageRateCompute(scanId: number) {
		// 1. 重新计算新增的物料使用情况
		this.analysisService.diffRouteFileAndGenRawDetails(scanId, [
			Business.BIZ,
			Business.ES,
			Business.LOCAL_LIFE,
			Business.LOCAL_LIFE_CLIENT,
		]);

		// 2. 计算在本次扫描之前的物料使用情况，得到物料-个数-行数-总使用行数的映射

		// 3. 开始计算本次新增物料的代码行数

		// 4. 计算新增物料的覆盖率

		// 5. 落库
	}
}
