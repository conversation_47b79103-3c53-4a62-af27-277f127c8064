import { Body, Controller, Post } from '@nestjs/common';
import { AnalysisService } from './analysis.service';
import { queryRepoInfoByUrl } from '../../common/util/gitlab';
import { Business } from '../../common/const';
import { MaterialUsage } from '@ks-material-middleoffice/measure-sdk';

@Controller('api/manage/analysis')
export default class AnalysisController {
  analysisService = new AnalysisService();

  @Post('/startAnalysis')
  async startAnalysis(
    @Body('scanRange') scanRange: AnyType,
    @Body('startTime') startTime: number,
    @Body('endTime') endTime: number,
    @Body('scanType') scanType: string,
    @Body('repoRange') repoRange?: ('profession' | 'material')[],
  ) {
  	try {
  		this.analysisService.startAnalysis({
  			scanType,
  			startTime,
  			scanRange,
  			endTime,
  			repoRange:
          !repoRange || !Array.isArray(repoRange) || !repoRange.length
          	? ['material', 'profession']
          	: repoRange,
  		});
  		return { code: 1, data: null };
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/adjustMUIMaterialMeta')
  async adjustMUIMaterialMeta(@Body('scanId') scanId: number) {
  	try {
  		this.analysisService.adjustMUIMaterialMeta(scanId);
  		return { code: 1, data: null };
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/clone-material-mate-to-export-all-from-another-package')
  async cloneMaterialMateToExportAllFromAnotherPackage(
    @Body('scanId') scanId: number,
  ) {
  	try {
  		this.analysisService.cloneMaterialMateToExportAllFromAnotherPackage(
  			scanId,
  		);
  		return { code: 1, data: null };
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/serial-execution-analysis')
  async serialExecutionAnalysis(
    @Body('scan-configs')
    scanConfigs: {
      scanRange: AnyType;
      startTime: number;
      endTime: number;
      scanType: string;
      repoRange?: ('profession' | 'material')[];
    }[],
  ) {
  	try {
  		this.analysisService.serialExecutionAnalysis(scanConfigs);
  		return { code: 1, data: null };
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/queryMaterial')
  async queryMaterial() {
  	try {
  		const res = await this.analysisService.queryMaterialPackageNameByBusiness(
  			Business.ES,
  		);
  		return {
  			code: 1,
  			data: res,
  		};
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/queryRepoInfoByUrl')
  async queryRepoInfoByUrl(@Body('url') url: string) {
  	try {
  		const res = await queryRepoInfoByUrl(url);
  		return {
  			code: 1,
  			data: res,
  		};
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/queryRepoInfoByUrls')
  async queryRepoInfoByUrls(@Body('urls') urls: string[]) {
  	try {
  		const reqs = urls?.map((url) => {
  			return queryRepoInfoByUrl(url);
  		});
  		const res = await Promise.allSettled(reqs);
  		const success = {};
  		const failed = {};
  		res?.forEach((i: AnyType, index: number) => {
  			if (i.status === 'fulfilled') {
  				success[urls[index]] = i.value;
  			} else if (i.status === 'rejected') {
  				failed[urls[index]] = {
  					msg: i.reason?.stack,
  				};
  			}
  		});
  		return {
  			code: 1,
  			data: {
  				success,
  				failed,
  			},
  		};
  	} catch (e) {
  		console.log('queryRepoInfoByUrls:', e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/merge-third-material-usage')
  async mergeThirdMaterialUsage(
    @Body('scanId') scanId: number,
    @Body('materialUsage') materialUsage: MaterialUsage,
  ) {
  	try {
  		const res = await this.analysisService.mergeThirdMaterialUsage(
  			scanId,
  			materialUsage,
  		);
  		return {
  			code: 1,
  			data: res,
  		};
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/diffRouteFileAndGenRawDetails')
  async diffRouteFileAndGenRawDetails(
    @Body('scanId') scanId: number,
    @Body('businessList') businessList: Business[],
  ) {
  	try {
  		if (!scanId) {
  			throw new Error('scanId is required');
  		}
  		await this.analysisService.diffRouteFileAndGenRawDetails(
  			scanId,
  			businessList,
  		);
  		return {
  			code: 1,
  			data: null,
  		};
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/transferRawDataToProcessMaterialDetail')
  async transferRawDataToProcessMaterialDetail(@Body('scanId') scanId: number) {
  	try {
  		if (!scanId) {
  			throw new Error('scanId is required');
  		}
  		const { start_time, end_time, scan_range } =
        await this.analysisService.getScanInfo(scanId);
  		await this.analysisService.transferRawDataToProcessMaterialDetail(
  			{
  				scanId,
  				startTime: start_time,
  				endTime: end_time,
  			},
  			await this.analysisService.getAllMaterialFromCenter(),
  			JSON.parse(scan_range),
  			[],
  		);
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/indexesCalculation')
  async indexesCalculation(@Body('scanId') scanId: number) {
  	try {
  		if (!scanId) {
  			throw new Error('scanId is required');
  		}
  		await this.analysisService.indexesCalculation(scanId);
  		return {
  			code: 1,
  			data: null,
  		};
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }

  @Post('/getLibUsage')
  async getLibUsage(@Body('scanIds') scanIds: number[]) {
  	try {
  		if (!Array.isArray(scanIds) || !scanIds.length) {
  			throw new Error('scanIds is required');
  		}
  		await this.analysisService.getLibUsage(scanIds);
  		return { code: 1, data: null };
  	} catch (e) {
  		console.log(e);
  		return { code: -1, msg: e };
  	}
  }

  @Post('/testResult')
  async testResult(
    @Body('scanIds') scanIds: number[],
    @Body('coefficient') coefficient: number,
  ) {
  	try {
  		if (!Array.isArray(scanIds) || !scanIds.length) {
  			throw new Error('scanIds is required');
  		}
  		await this.analysisService.testResult(scanIds, coefficient);
  		return { code: 1, data: null };
  	} catch (e) {
  		console.log(e);
  		return { code: -1, msg: e };
  	}
  }

  @Post('/computeMaterialCount')
  async computeMaterialCount(@Body('scanId') scanId: number) {
  	try {
  		if (!scanId) {
  			throw new Error('scanId is required');
  		}
  		await this.analysisService.computeCurrentScanningMaterialCount(scanId);
  		return { code: 1, data: null };
  	} catch (e) {
  		console.log(e);
  		return { code: -1, msg: e };
  	}
  }

  @Post('/queryUserInfoByMaterialId')
  async queryUserInfoByMaterialId(@Body('materialId') materialId: number) {
  	try {
  		const res =
        await this.analysisService.queryUserInfoByMaterialId(materialId);
  		return {
  			code: 1,
  			data: res,
  		};
  	} catch (e) {
  		console.log(e);
  		return {
  			code: -1,
  			msg: e,
  		};
  	}
  }
}
