import { Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import MaterialDao from '../material/material.dao';
import BusinessConfigDao from '../business-config/business-config.dao';
import { chunk, cloneDeep, isNumber, omit, uniq, uniqBy } from 'lodash';
import CDNService from '../cdn/cdn.service';
import AnalysisRawDataDao, {
	CreateRawScanResultParams,
} from './dao/rawData.dao';
import {
	cloneProject,
	extractGitRepoPath,
	formatTime,
	genMainIndexOfDB,
	getNamespaceFromGitUrl,
	runScanWorker,
	safeParse,
	safeStringify,
	ScanWorkerParams,
} from '../../common/util';

import {
	Business,
	EffectStatus,
	MaterialType,
	NewReportType,
	PROJECT_PATH,
} from '../../common/const';
import {
	APPModeReport,
	LibraryModeReport,
	MaterialUsage,
} from '@ks-material-middleoffice/measure-sdk';
import { getBizProjects, getEsProjects } from '../../common/util/project';
import { queryRepoInfoByUrl } from '../../common/util/gitlab';
import AnalysisRepoMetaDao from './dao/repoMeta.dao';
import RouteFileUsageDao, {
	IRouteFileUsageDAO,
} from './dao/routeFileUsage.dao';
import NewCodeLinesDao from './dao/newCodeLines.dao';
import CodeLinesDao from './dao/codeLines.dao';
import MaterialMetaDao, { IMaterialMetaDO } from './dao/materialMeta.dao';
import ScanInfoDao from './dao/scanInfo.dao';
import NewMaterialUsageDao from './dao/newMaterialUsage.dao';
import NP from 'number-precision';
import ResultDao, { IResultDO } from './dao/result.dao';
import MUIMaterialCodeLines from '../../mui-script/MUICodeLines.json';

@Injectable()
export class AnalysisService {
	cdnService: CDNService;
	businessConfigDao: AnyType;
	materialDao: MaterialDao;

	analysisRepoMetaDao: AnalysisRepoMetaDao;
	rawDataDao: AnalysisRawDataDao;
	routeFileUsageDao: RouteFileUsageDao;
	newCodeLinesDao: NewCodeLinesDao;
	codeLinesDao: CodeLinesDao;
	materialMetaDao: MaterialMetaDao;
	scanInfoDao: ScanInfoDao;
	newMaterialUsageDao: NewMaterialUsageDao;
	resultDao: ResultDao;

	constructor() {
		this.cdnService = new CDNService();
		this.businessConfigDao = new BusinessConfigDao();
		this.materialDao = new MaterialDao();
		this.rawDataDao = new AnalysisRawDataDao();
		this.newCodeLinesDao = new NewCodeLinesDao();
		this.codeLinesDao = new CodeLinesDao();
		this.analysisRepoMetaDao = new AnalysisRepoMetaDao();
		this.routeFileUsageDao = new RouteFileUsageDao();
		this.materialMetaDao = new MaterialMetaDao();
		this.scanInfoDao = new ScanInfoDao();
		this.newMaterialUsageDao = new NewMaterialUsageDao();
		this.resultDao = new ResultDao();
	}

	async startAnalysis(params: {
    startTime: number;
    endTime: number;
    scanRange: Business[];
    scanType: string;
    repoRange: ('profession' | 'material')[];
  }) {
		const professionFailedProjects = [];
		const materialFailedProjects = [];
		const transferErrors = [];
		const diffErrors = [];
		const calcFinalResultErrors = [];
		const { startTime, endTime, scanRange, scanType } = params;
		const [scan] = await this.scanInfoDao.query(params);
		let scan_id,
			needClearBusinessList: Business[] = [];
		const errorStack = [];

		if (scan) {
			const preScanRange = safeParse(scan.scan_range, []);
			const scanRange = uniq([...params.scanRange, ...preScanRange]);
			await this.scanInfoDao.update({
				scanId: scan.id,
				scanRange: safeStringify(scanRange),
			});
			/** 判断需要清除数据的 BU */
			needClearBusinessList = preScanRange.filter((bu) =>
				params.scanRange.includes(bu),
			);

			scan_id = scan.id;
		} else {
			// 本次scan_id入库
			scan_id = await this.createOrSelectScanRecord({
				scanRange,
				startTime,
				endTime,
				scanType: scanType || 'test',
			});
		}

		console.info('[analysis] scan_id:', scan_id);

		// 0. 清空当次scan_id下的所有历史记录
		await this.cleanHistoryByScanId({
			scanId: scan_id,
			businessList: needClearBusinessList,
			repoRange: params.repoRange,
		});

		// 1. 根据scanId查询的range需要的仓库列表
		const BUProjects = await this.queryAndSyncProjects(
			scanRange,
			params.repoRange,
		);

		// 2. 根据仓库列表入库rawData
		for (const BUName in BUProjects) {
			const allTypeProjects = BUProjects[BUName];
			for (const [type, projects] of Object.entries(allTypeProjects)) {
				for (let i = 0; i < projects.length; i++) {
					const project = projects[i];
					try {
						const content = await this.scanProject(
              type as 'profession' | 'material',
              {
              	git_url: project.git_url,
              	black_list: project.black_list,
              	router_entry: project.router_entry,
              	pkg_filter: project.pkg_filter,
              	branch: project.branch,
              	end_time: endTime,
              	start_time: startTime,
              },
						);
						await this.insertRawData({
							scan_id: scan_id,
							type: type as 'profession' | 'material',
							gitUrl: project.git_url,
							business: BUName,
							content: JSON.stringify(content),
						});
						console.info('[analysis] 创建底表成功:', {
							business: BUName,
							gitUrl: project.git_url,
							type,
						});
					} catch (e) {
						if (type === 'profession') {
							professionFailedProjects.push({
								...project,
								error: `拉取错误原因是：${typeof e === 'string' ? e : e.message}`,
							});
						} else {
							materialFailedProjects.push({
								...project,
								error: `拉取项目错误原因是：${typeof e === 'string' ? e : e.message}`,
							});
						}
						errorStack.push(e);
					}
				}
			}
		}

		// 3. 根据rawData生成明细表
		const allMaterials = await this.getAllMaterialFromCenter();
		await this.transferRawDataToProcessMaterialDetail(
			{
				scanId: scan_id,
				startTime,
				endTime,
			},
			allMaterials,
      Object.keys(BUProjects) as Business[],
      transferErrors,
		);

		// 特调 MUI 的行数
		try {
			await this.adjustMUIMaterialMeta(scan_id);
		} catch (e) {
			console.log('adjustMUIMaterialMeta:', e);
		}

		// 4. 根据明细计算新增组件类型以及数量（两次diff）
		try {
			await this.diffRouteFileAndGenRawDetails(
				scan_id,
        Object.keys(BUProjects) as Business[],
			);
		} catch (e) {
			diffErrors.push({
				scan_id,
				BUProjects,
				error: `diffRouteFileAndGenRawDetails错误原因是：${typeof e === 'string' ? e : e.message}`,
			});
			console.log(e);
		}

		// 5. 根据明细计算+第四部算出的结果，最终for展示的结果
		try {
			await this.indexesCalculation(scan_id);
		} catch (e) {
			calcFinalResultErrors.push({
				scan_id,
				error: `最终计算出错原因是：${typeof e === 'string' ? e : e.message}`,
			});
			console.log(e);
		}

		try {
			await this.cloneMaterialMateToExportAllFromAnotherPackage(scan_id);
		} catch (e) {
			calcFinalResultErrors.push({
				scan_id,
				error: `cloneMaterialMateToExportAllFromAnotherPackage错误原因是：${typeof e === 'string' ? e : e.message}`,
			});
			console.log('cloneMaterialMateToExportAllFromAnotherPackage:', e);
		}

		try {
			await this.computeCurrentScanningMaterialCount(scan_id);
		} catch (error) {
			calcFinalResultErrors.push({
				scan_id,
				error: `computeCurrentScanningMaterialCount错误原因是：${typeof error === 'string' ? error : error.message}`,
			});
		}

		// final: 最终将本次计算错误结果上传
		try {
			await this.cdnService.uploadToCDN({
				filename: `scan-failed-${dayjs().format('YYYY-MM-DD_HH:mm:ss')}.json`,
				str: JSON.stringify(
					{
						scan: { materialFailedProjects, professionFailedProjects },
						transfer: { transferErrors },
						diff: { diffErrors },
						calcFinal: { calcFinalResultErrors },
					},
					null,
					2,
				),
				path: `/scan-logs/${scan_id}`,
			});
		} catch (e) {
			console.log(
				`日志文件上传失败，错误原因是${typeof e === 'string' ? e : e.message}`,
			);
		}
	}

	async serialExecutionAnalysis(
		configs: {
      startTime: number;
      endTime: number;
      scanRange: Business[];
      scanType: string;
      repoRange?: ('profession' | 'material')[];
    }[],
	) {
		if (!Array.isArray(configs) || !configs.length) {
			throw new Error('scanConfigs is required');
		}
		const waitingForScanConfigs = configs.slice();
		const scan = async () => {
			if (waitingForScanConfigs.length) {
				try {
					const scanConfig = waitingForScanConfigs.shift();
					await this.startAnalysis({
						scanType: scanConfig.scanType,
						startTime: scanConfig.startTime,
						scanRange: scanConfig.scanRange,
						endTime: scanConfig.endTime,
						repoRange:
              !scanConfig.repoRange ||
              !Array.isArray(scanConfig.repoRange) ||
              !scanConfig.repoRange.length
              	? ['material', 'profession']
              	: scanConfig.repoRange,
					});
					await scan();
				} catch (error) {
					this.cdnService.uploadToCDN({
						filename: `scan-failed-${dayjs().format('YYYY-MM-DD_HH:mm:ss')}.json`,
						str: JSON.stringify({ error: error.message }, null, 2),
						path: '/serial-scan-logs',
					});
					await scan();
				}
			}
		};
		scan();
	}

	async queryUserInfoByMaterialId(materialId: number) {
		return this.materialDao.queryUserInfoByMaterialId(materialId);
	}

	async adjustMUIMaterialMeta(scanId: number) {
		const allMaterialMeta =
      await this.materialMetaDao.queryMaterialMeta(scanId);
		const muiMaterialMeta = allMaterialMeta.filter(
			(item) => item.package === '@m-ui/react',
		);
		for (let i = 0; i < muiMaterialMeta.length; i++) {
			const item = muiMaterialMeta[i];
			const practiceMUIMaterial = MUIMaterialCodeLines.find(
				(source) => source.materialName === item.material_name,
			);
			if (practiceMUIMaterial) {
				item.code_lines = practiceMUIMaterial.codeLines;
				await this.materialMetaDao.updateMaterialCodeLines({
					materialMetaId: item.id as number,
					codeLines: practiceMUIMaterial.codeLines,
				});
			} else {
				console.log('找不到对应的 MUI material');
			}
		}
	}

	public async cloneMaterialMateToExportAllFromAnotherPackage(
		scanId: number | string,
	) {
		const currentScanAllMaterialMeta =
      await this.materialMetaDao.queryMaterialMeta(scanId);
		const exportAllFromAnotherPackage = currentScanAllMaterialMeta.filter(
			(item) => item.material_name === '__ALL_FROM_THIRD_PARTY__',
		);
		const newMaterialMeta: IMaterialMetaDO[] = [];
		for (let i = 0; i < exportAllFromAnotherPackage.length; i++) {
			const { file_path } = exportAllFromAnotherPackage[i];
			const targetPackageMaterialMeta = currentScanAllMaterialMeta.filter(
				(item) =>
					item.package === file_path &&
          item.material_name !== '__ALL_FROM_THIRD_PARTY__',
			);
			for (let j = 0; j < targetPackageMaterialMeta.length; j++) {
				const item = targetPackageMaterialMeta[j];
				const newMaterialMetaItem = {
					...(omit(item, ['id', 'create_time', 'status']) as IMaterialMetaDO),
					project_id: exportAllFromAnotherPackage[i].project_id,
					type: exportAllFromAnotherPackage[i].type,
					package: file_path,
				};
				await this.materialMetaDao.create(newMaterialMetaItem);
				newMaterialMeta.push(newMaterialMetaItem);
			}
		}
		return newMaterialMeta;
	}

	public async createOrSelectScanRecord(param: {
    startTime: number;
    endTime: number;
    scanRange: Business[];
    scanType: string;
  }) {
		return await this.scanInfoDao.create({
			start_time: param.startTime,
			end_time: param.endTime,
			scan_range: safeStringify(param.scanRange || []),
			display_txt: `${formatTime(param.startTime)}-${formatTime(param.endTime)}`,
			type: param.scanType as AnyType,
		});
	}

	async queryMaterialPackageNameByBusiness(
		business: Business,
	): Promise<string[]> {
		const materials = await this.queryMaterialsByBusiness(business);
		return uniq(
			materials.map((m) => {
				// console.log(m.schema);
				return m.schema?.packageName;
			}),
		);
	}

	async queryMaterialsByBusiness(business: Business): Promise<AnyType[]> {
		let materials = await this.materialDao.query({
			business,
			status: EffectStatus.EFFECT,
			offset: 0,
			pageSize: 10000,
			type: MaterialType.COMPONENT,
			needSchema: true,
		});
		materials = materials
			.map((m) => ({ ...m, schema: safeParse(m.schema as AnyType) }))
			.filter((m) => m.schema?.packageName);
		return materials;
	}

	async queryAndSyncProjects(
		businessList: Business[],
		repoRange: ('profession' | 'material')[],
	): Promise<{
    [key: string]: {
      profession: Module.Scan.ScanProject[];
      material: Module.Scan.ScanProject[];
    };
  }> {
		let res = {};

		if (businessList.includes(Business.TEST)) {
			return {
				[Business.TEST]: {
					profession: [
						{
							title: 'nearby-rn',
							branch: 'master',
							git_url:
                '*************************:locallife/client/bizPage/nearby-rn.git',
						},
					],
					material: [
						{
							title: 'krn-log',
							router_entry: ['src/**/*.{ts,tsx}'],
							git_url:
                'https://git.corp.kuaishou.com/locallife/client/library/react-native/log.git',
						},
						{
							title: 'biz-request',
							router_entry: ['src/**/*.{ts,tsx}'],
							git_url:
                'https://git.corp.kuaishou.com/locallife/client/library/react-native/biz-request.git',
						},
					],
				},
			};
		}

		for (const business of businessList) {
			const [businessConfig] = await this.businessConfigDao.query({
				business,
				status: EffectStatus.EFFECT,
			});
			const meta = safeParse(businessConfig?.meta);

			const professionProjects = uniqBy(
				(meta?.report?.projects ?? []).concat(
					business === Business.ES
						? await getEsProjects()
						: business === Business.BIZ
							? await getBizProjects()
							: [],
				),
				(item) => {
					return item.git_url
						?.trim()
						.replace(/\/+$/g, '')
						.replace(/[?|#].*$/, '')
						.replace(/\.git$/, '')
						.replace(
							'*************************:',
							'https://git.corp.kuaishou.com/',
						);
				},
			) as AnyType[];

			let materials = await this.materialDao.query({
				business,
				status: EffectStatus.EFFECT,
				offset: 0,
				pageSize: 10000,
				type: MaterialType.COMPONENT,
				needSchema: true,
			});
			materials = materials
				.map((m) => ({ ...m, schema: safeParse(m.schema as AnyType) }))
				.filter((m) => m.schema?.packageName);
			const materialProjects = uniqBy(
				(meta?.material?.projects ?? []).concat(
					materials
						.map((m) => m.schema.gitUrl)
						.filter(Boolean)
						.filter((m) => m.includes('git.corp.kuaishou.com'))
						.map((url) => {
							const namespace = getNamespaceFromGitUrl(url);
							return {
								title: namespace,
								namespace,
								git_url: url,
								router_entry: undefined,
								component_folder_map: {},
							};
						}),
				),
				(item) => {
					return item.git_url
						?.trim()
						.replace(/\/+$/g, '')
						.replace(/[?|#].*$/, '')
						.replace(/\.git$/, '')
						.replace(
							'*************************:',
							'https://git.corp.kuaishou.com/',
						);
				},
			) as AnyType[];

			const repoMetas = await this.analysisRepoMetaDao.queryByUrls([
				...professionProjects.map((p) => p.git_url),
				...materialProjects.map((p) => p.git_url),
			]);

			const needCheckProjects = [
				...professionProjects,
				...materialProjects,
			].filter((p) => !repoMetas.find((r) => r.clone_url === p.git_url));
			const chunks = chunk(needCheckProjects, 10);
			const stack = [];
			const notFoundUrlList: string[] = [];

			for (const list of chunks) {
				await Promise.all(
					list.map(async (item: AnyType) => {
						try {
							const repoInfo: AnyType = await queryRepoInfoByUrl(item.git_url);

							if (!repoInfo) {
								notFoundUrlList.push(item.git_url);
								console.log(
									'[Warning] 获取 git 详细信息 404，地址是',
									item.git_url,
								);
								return;
							}
							await this.analysisRepoMetaDao.create({
								business,
								clone_url: item.git_url,
								name: repoInfo.name,
								project_id: repoInfo.id,
							});
						} catch (error) {
							console.log('获取 git 详细信息失败，错误原因是', error.message);
							console.log(
								'失败的 URL 列表是',
								JSON.stringify(list.map((i) => i.git_url)),
							);
							console.log(error);
							stack.push('获取 git 详细信息失败，错误原因是' + error.message);
						}
					}),
				);
			}

			if (notFoundUrlList.length) {
				console.log(
					'[Warning] 所有获取 git 详细信息 404，地址集合是',
					JSON.stringify(notFoundUrlList),
				);
			}

			if (stack.length) {
				throw new Error(stack.join('\n'));
			}

			res[business] = {
				profession: repoRange.includes('profession')
					? professionProjects
						.filter((p) => !notFoundUrlList.includes(p.git_url))
						.filter((p) => Boolean(p.git_url))
						.map((p) => ({ ...p, git_url: p.git_url.trim() }))
					: [],
				material: repoRange.includes('material')
					? materialProjects
						.filter((p) => !notFoundUrlList.includes(p.git_url))
						.filter((p) => Boolean(p.git_url))
						.map((p) => ({ ...p, git_url: p.git_url.trim() }))
					: [],
			};
		}

		return res;
	}

	async cleanHistoryByScanId(param: {
    scanId: number;
    businessList: Business[];
    repoRange: ('profession' | 'material')[];
  }) {
		try {
			/** 存在列表，按 BU + scanId 的范围清除数据 */
			if (param.businessList.length) {
				await this.rawDataDao.deleteByScanIdAndBusinessList(param);
				console.log(
					`analysis_raw_data清理完毕：${param.scanId} ${JSON.stringify(param.businessList)}`,
				);

				if (param.repoRange.includes('profession')) {
					await this.routeFileUsageDao.deleteByScanIdAndBusinessList(param);
					console.log(
						`analysis_route_file_usage清理完毕：${param.scanId} ${JSON.stringify(param.businessList)}`,
					);

					await this.newCodeLinesDao.deleteByScanIdAndBusinessList(param);
					console.log(
						`analysis_new_code_lines清理完毕：${param.scanId} ${JSON.stringify(param.businessList)}`,
					);

					await this.codeLinesDao.deleteByScanIdAndBusinessList(param);
					console.log(
						`analysis_code_lines清理完毕：${param.scanId} ${JSON.stringify(param.businessList)}`,
					);

					await this.newMaterialUsageDao.deleteByScanIdAndBusinessList(param);
					console.log(
						`analysis_new_material_usage清理完毕：${param.scanId} ${JSON.stringify(param.businessList)}`,
					);
				}

				if (param.repoRange.includes('material')) {
					await this.materialMetaDao.deleteByScanIdAndBusinessList(param);
					console.log(
						`analysis_material_meta清理完毕：${param.scanId} ${JSON.stringify(param.businessList)}`,
					);
				}

				await this.resultDao.deleteByScanIdAndBusinessList(param);
				console.log(
					`analysis_result清理完毕：${param.scanId} ${JSON.stringify(param.businessList)}`,
				);
			}
		} catch (e) {
			console.log('cleanHistoryByScanId:', e);
		}
	}

	// 从物料中台拉取全量的物料到内存中
	async getAllMaterialFromCenter() {
		const businesses = [
			Business.LOCAL_LIFE,
			Business.ES,
			Business.LOCAL_LIFE_CLIENT,
			Business.BIZ,
		];
		/**
		 *
		 	'@ks-material/template-filter-table': {
				packageName: '@ks-material/template-filter-table',
				componentBundleType: 'SLSC',
				components: [ 'FilterTable' ]
			}
		 */
		let materials: Record<
      string,
      {
        materialId: number;
        packageName: string;
        business: Business;
        componentBundleType: 'SLSC' | 'SLMC';
        components: string[];
      }
    > = {};
		for (let bs of businesses) {
			const list = await this.materialDao.query({
				business: bs,
				status: EffectStatus.EFFECT,
				offset: 0,
				pageSize: 10000,
				type: MaterialType.COMPONENT,
				needSchema: true,
			});
			for (let item of list) {
				const obj = safeParse(item.schema as AnyType);
				if (!materials[obj.packageName]) {
					materials[obj.packageName] = {
						materialId: item.id,
						packageName: obj.packageName,
						business: item.business,
						componentBundleType: obj.componentBundleType,
					} as AnyType;
				}
				const componentBundleType =
          materials[obj.packageName]?.componentBundleType;
				if (componentBundleType === 'SLMC') {
					if (materials[obj.packageName]['components']?.length) {
						materials[obj.packageName]['components'].push(obj.componentName);
					} else {
						materials[obj.packageName]['components'] = [obj.componentName];
					}
				} else if (componentBundleType === 'SLSC') {
					materials[obj.packageName]['components'] = [obj.componentName];
				}
			}
		}
		// console.log('materials: ', materials);
		return materials;
	}

	async transferRawDataToProcessMaterialDetail(
		param: {
      scanId: number;
      startTime: number;
      endTime: number;
    },
		allMaterialsInPlatform,
		businessList: Business[],
		transferErrors,
	) {
		try {
			const list = await this.rawDataDao.queryByScanIdAndBusinessList({
				scanId: param.scanId,
				businessList,
			});
			const professionProjects = list
				.filter((item) => item.type === 'profession')
				.filter((item) => !!item.content);
			const materialProjects = list
				.filter((item) => item.type === 'material')
				.filter((item) => !!item.content);

			for (let i = 0, l = materialProjects.length; i < l; i++) {
				const item = materialProjects[i];
				const rawObj = safeParse(item.content);
				if (Object.keys(rawObj || {}).length === 0) {
					// 收集日志
					transferErrors.push({
						...item,
						error: '物料仓库的底表content字段为空，请确认！',
					});
					continue;
				}

				// 过程指标物料信息明细表
				const packages = rawObj.packages;
				for (let j = 0; j < packages.length; j++) {
					const pkg = packages[j];
					if (pkg) {
						const componentBundleType =
              allMaterialsInPlatform[pkg.name]?.componentBundleType;
						if (componentBundleType === 'SLMC') {
							// 单包多组件
							for (let m = 0; m < pkg?.materials.length; m++) {
								const material = pkg.materials[m];
								// 非类型才入库
								if (material.isType === false) {
									const filteredThirdMaterialUsage =
                    await this.filterUsefulMaterialUsage(
                    	material.thirdMaterialUsage,
                    	allMaterialsInPlatform,
                    );
									await this.materialMetaDao.create({
										business: item.business,
										scan_id: param.scanId,
										project_id: item.repo_project_id,
										package: pkg.name,
										type: 'SLMC',
										code_lines: material.codeLines,
										file_path: material.path,
										third_usage: JSON.stringify(material.thirdMaterialUsage),
										filter_third_usage: JSON.stringify(
											filteredThirdMaterialUsage,
										),
										material_name: material.name,
										variable_name: material.variableName,
									});
									console.info('[analysis] 创建 material meta 成功:', {
										projectId: item.repo_project_id,
										package: pkg.name,
										materialName: material.name,
									});
								}
							}
						} else if (componentBundleType === 'SLSC') {
							// 单包单组件
							const names = pkg?.materials?.map((i) => i.name);
							if (names.includes('default')) {
								// 找到了，放进去，用default这个component的代码行
								const index = names.indexOf('default');
								const material = pkg.materials[index];
								// 非类型才入库
								if (material.isType === false) {
									const filteredThirdMaterialUsage =
                    await this.filterUsefulMaterialUsage(
                    	material.thirdMaterialUsage,
                    	allMaterialsInPlatform,
                    );
									await this.materialMetaDao.create({
										business: item.business,
										scan_id: param.scanId,
										project_id: item.repo_project_id,
										package: pkg.name,
										type: 'SLSC',
										code_lines: material.codeLines,
										file_path: material.path,
										third_usage: JSON.stringify(material.thirdMaterialUsage),
										filter_third_usage: JSON.stringify(
											filteredThirdMaterialUsage,
										),
										material_name: material.name,
										variable_name: material.variableName,
									});
									console.info('[analysis] 创建 material meta 成功:', {
										projectId: item.repo_project_id,
										package: pkg.name,
										materialName: material.name,
									});
								}
							} else {
								// 没找到，强插一条，代码行用仓库的代码行
								await this.materialMetaDao.create({
									business: item.business,
									scan_id: param.scanId,
									project_id: item.repo_project_id,
									package: pkg.name,
									type: 'detail',
									code_lines: pkg.codeLines,
									file_path: '.',
									third_usage: '{}',
									filter_third_usage: '{}',
									material_name: 'default',
									variable_name: 'default',
								});
								console.info('[analysis] 创建 material meta 成功:', {
									projectId: item.repo_project_id,
									package: pkg.name,
									materialName: 'default',
								});
							}
						}
					}
				}
			}

			for (let i = 0, l = professionProjects.length; i < l; i++) {
				const item = list[i];
				const rawObj = safeParse(item.content);
				if (Object.keys(rawObj || {}).length === 0) {
					transferErrors.push({
						...item,
						error: '业务仓库的底表content字段为空，请确认！',
					});
					continue;
				}
				// 过程指标物料信息明细表
				const packages = rawObj.packages;

				for (let j = 0; j < packages.length; j++) {
					const pkg = packages[j];
					if (pkg) {
						const pages = Array.isArray(pkg.pages) ? pkg.pages : [];
						for (let m = 0; m < pages?.length; m++) {
							const page = pages[m];
							const fileLength = page.files.length;
							const routerPath = page.browserPath + '::' + genMainIndexOfDB();
							for (let n = 0; n < fileLength; n++) {
								const file = page.files[n];
								const filterLibraryMaterialUsage = cloneDeep(
									file.libraryMaterialUsage,
								);
								const libraryNames = Object.keys(filterLibraryMaterialUsage);

								// 根据查出来的物料中台的数据过滤非物料中台的组件
								for (let libraryName of libraryNames) {
									// 使用的包在物料中台中
									if (allMaterialsInPlatform[libraryName]) {
										// 接下来判断组件库类型然后计算里面的子组件是否在物料中台上
										if (
											allMaterialsInPlatform[libraryName]
												?.componentBundleType === 'SLSC'
										) {
											// 里面数据结构一定是{default}
											if (
												!allMaterialsInPlatform[
													libraryName
												]?.components?.includes('default')
											) {
												// 如果物料没录入到物料中台，直接删除即可
												delete filterLibraryMaterialUsage[libraryName];
											}
										} else if (
											allMaterialsInPlatform[libraryName]
												?.componentBundleType === 'SLMC'
										) {
											// 如果子组件没有录入到物料中台
											// console.log('物料中台组件是', libraryName);
											const usedLibrary =
                        filterLibraryMaterialUsage[libraryName];
											for (let componentName in usedLibrary) {
												if (
													!allMaterialsInPlatform[
														libraryName
													]?.components?.includes(componentName)
												) {
													// console.log('即将删除', libraryName, componentName);
													delete filterLibraryMaterialUsage[libraryName][
														componentName
													];
												}
											}
										}
									} else {
										// 当前页面使用的组件不在物料中台中
										delete filterLibraryMaterialUsage[libraryName];
									}
								}
								// { "@na":{}, "pk2": {} } => {}
								for (let key in filterLibraryMaterialUsage) {
									let item = filterLibraryMaterialUsage[key];
									if (Object.keys(item)?.length === 0) {
										delete filterLibraryMaterialUsage[key];
									}
								}

								await this.routeFileUsageDao.create({
									scan_id: param.scanId,
									business: item.business,
									repo_project_id: item.repo_project_id,
									package: pkg.name,
									route_path: routerPath,
									file_path: file.path,
									file_create_time: page.createTime,
									material_info: JSON.stringify(file.libraryMaterialUsage),
									filter_material_info: JSON.stringify(
										filterLibraryMaterialUsage,
									),
									start_time: param.startTime,
									end_time: param.endTime,
								});

								console.info('[analysis] 创建 route file page 成功:', {
									projectId: item.repo_project_id,
									route: routerPath,
									file: file.path,
								});
							}
						}
					}
				}
				// 插入北极星指标仓库信息表
				await this.newCodeLinesDao.create({
					business: item.business,
					repo_project_id: item.repo_project_id,
					start_commit_hash: rawObj.startCommitHash,
					end_commit_hash: rawObj.endCommitHash,
					new_code_lines: rawObj.newCodeLines,
					scan_id: param.scanId,
				});
				console.info('[analysis] 创建 new code lines 成功:', {
					projectId: item.repo_project_id,
					newCodeLines: rawObj.newCodeLines,
				});
				// 过程指标仓库信息
				await this.codeLinesDao.create({
					business: item.business,
					repo_project_id: item.repo_project_id,
					scan_id: param.scanId,
					code_lines: rawObj.codeLines,
				});
				console.info('[analysis] 创建 code lines 成功:', {
					projectId: item.repo_project_id,
					codeLines: rawObj.codeLines,
				});
			}
		} catch (e) {
			transferErrors.push({
				error: `transferRawDataToProcessMaterialDetail错误原因是：${typeof e === 'string' ? e : e.message}`,
			});
			console.log(e);
		}
	}

	async scanProject<Type extends 'profession' | 'material'>(
		type: 'profession' | 'material',
		project: ScanWorkerParams & { git_url: string },
	):
    | never
    | Promise<Type extends 'profession' ? APPModeReport : LibraryModeReport> {
		const throwError = (msg: string) => {
			console.log(
				`扫描项目工程失败，仓库配置：${JSON.stringify(project, null, 2)}`,
			);
			console.log(msg);
			throw new Error(
				JSON.stringify({
					...project,
					error: msg,
				}),
			);
		};

		// 1. clone
		try {
			cloneProject({
				path: PROJECT_PATH,
				gitUrl: project.git_url,
			});
		} catch (e) {
			throwError(`拉取错误原因是：${typeof e === 'string' ? e : e.message}`);
		}

		// 2. call SDK
		try {
			const { projectNameList, filteredProject, ...result } =
        await runScanWorker<Type>(type as Type, {
        	...project,
        	pkg_filter: project.pkg_filter
        		? escape(project.pkg_filter)
        		: undefined,
        });

			if (projectNameList.length === filteredProject.length) {
				throwError(
					`${filteredProject.join(', ')} 项目框架为 vue 或被 pkgFilter 过滤，将被忽略扫描`,
				);
			}
			return result as unknown as Type extends 'profession'
        ? APPModeReport
        : LibraryModeReport;
		} catch (e) {
			throwError(`扫描错误原因是：${typeof e === 'string' ? e : e.message}`);
		}
	}

	async filterUsefulMaterialUsage(
		currentMaterialUsage: MaterialUsage,
		allMaterialsInPlatform: any,
	): Promise<MaterialUsage> {
		try {
			const targetMaterialUsage = cloneDeep(currentMaterialUsage);
			const libraryNames = Object.keys(targetMaterialUsage);

			// 根据查出来的物料中台的数据过滤非物料中台的组件
			for (let libraryName of libraryNames) {
				// 使用的包在物料中台中
				if (allMaterialsInPlatform[libraryName]) {
					// 接下来判断组件库类型然后计算里面的子组件是否在物料中台上
					if (
						allMaterialsInPlatform[libraryName]?.componentBundleType === 'SLSC'
					) {
						// 里面数据结构一定是{default}
						if (
							!allMaterialsInPlatform[libraryName]?.components?.includes(
								'default',
							)
						) {
							// 如果物料没录入到物料中台，直接删除即可
							delete targetMaterialUsage[libraryName];
						}
					} else if (
						allMaterialsInPlatform[libraryName]?.componentBundleType === 'SLMC'
					) {
						// 如果子组件没有录入到物料中台
						// console.log('物料中台组件是', libraryName);
						const usedLibrary = targetMaterialUsage[libraryName];
						for (let componentName in usedLibrary) {
							if (
								!allMaterialsInPlatform[libraryName]?.components?.includes(
									componentName,
								)
							) {
								// console.log('即将删除', libraryName, componentName);
								delete targetMaterialUsage[libraryName][componentName];
							}
						}
					}
				} else {
					// 当前页面使用的组件不在物料中台中
					delete targetMaterialUsage[libraryName];
				}
			}
			// { "@na":{}, "pk2": {} } => {}
			for (let key in targetMaterialUsage) {
				let item = targetMaterialUsage[key];
				if (Object.keys(item)?.length === 0) {
					delete targetMaterialUsage[key];
				}
			}
			return targetMaterialUsage || {};
		} catch (error) {
			return {};
		}
	}

	async mergeThirdMaterialUsage(
		scanId: string | number,
		filterLibraryMaterialUsage: MaterialUsage,
	): Promise<MaterialUsage> {
		// 查找第三方依赖
		const materialsMetas = await this.materialMetaDao.queryMaterialMeta(scanId);
    type LibraryUsage = {
      library: string;
      materials: { name: string; count: number }[];
    };
    const objectMaterialUsage2Array = (obj: MaterialUsage): LibraryUsage[] => {
    	return Object.entries(obj).map(([library, usage]) => ({
    		library,
    		materials: Object.entries(usage).map(([name, count]) => ({
    			name,
    			count,
    		})),
    	}));
    };

    const arrayMaterialUsage2Object = (arr: LibraryUsage[]): MaterialUsage => {
    	const obj = {};
    	for (let i = 0; i < arr.length; i++) {
    		const { library, materials } = arr[i];
    		obj[library] = {};
    		for (let j = 0; j < materials.length; j++) {
    			const { name, count } = materials[j];
    			obj[library][name] = count;
    		}
    	}
    	return obj;
    };

    const pushToStack = (origin: LibraryUsage[], target: LibraryUsage) => {
    	const alreadyHas = origin.find((item) => item.library === target.library);
    	if (alreadyHas) {
    		const originMaterials = alreadyHas.materials;
    		const targetMaterials = target.materials;
    		for (let i = 0; i < targetMaterials.length; i++) {
    			const target = targetMaterials[i];
    			const alreadyHas = originMaterials.find(
    				(item) => item.name === target.name,
    			);
    			if (alreadyHas) {
    				alreadyHas.count += target.count;
    			} else {
    				originMaterials.push(target);
    			}
    		}
    	} else {
    		origin.push(target);
    	}
    };
    const result: LibraryUsage[] = [];
    const waitingForSearchMaterials: LibraryUsage[] = objectMaterialUsage2Array(
    	filterLibraryMaterialUsage,
    );

    for (let i = 0; i < waitingForSearchMaterials.length; i++) {
    	const { library, materials } = waitingForSearchMaterials[i];
    	const materialMeta = materialsMetas.filter(
    		(item) => (item.package = library),
    	);

    	for (let i = 0; i < materials.length; i++) {
    		const { name, count } = materials[i];
    		materialMeta.find((item) => {
    			if (item.material_name === name) {
    				const thirdUsage = safeParse(item.filter_third_usage);
    				const thirdUsageArray = objectMaterialUsage2Array(thirdUsage);
    				for (let j = 0; j < thirdUsageArray.length; j++) {
    					for (let z = 0; z < count; z++) {
    						pushToStack(result, thirdUsageArray[j]);
    					}
    				}
    			}
    		});
    	}
    	pushToStack(result, { library, materials });
    }

    return arrayMaterialUsage2Object(result);
	}

	async insertRawData(
		params: Omit<
      CreateRawScanResultParams,
      'repo_project_id' | 'start_time' | 'end_time'
    > & {
      scanId?: string;
      gitUrl: string;
    },
	) {
		const result = await this.analysisRepoMetaDao.queryByUrls([params.gitUrl]);
		const [repo_project_id] = result;
		return await this.rawDataDao.createRawData({
			...params,
			repo_project_id: repo_project_id.project_id,
		});
	}

	async diffRouteFileAndGenRawDetails(
		scanId: number,
		businessList: Business[],
	) {
		const preScan = await this.scanInfoDao.queryPreScanById(scanId);
		await this.newMaterialUsageDao.deleteByScanIdAndBusinessList({
			scanId,
			businessList,
		})
		for (const business of businessList) {
			/** 按 project_id、package、file_path 去重 */
			const routeFileList = uniqBy(
				await this.routeFileUsageDao.query({
					scanId,
					business,
				}),
				(item) => `${item.repo_project_id}-${item.package}-${item.file_path}`,
			);
			const preRouteFileList = preScan
				? await this.routeFileUsageDao.query({ scanId: preScan.id, business })
				: [];

			for (const routeFile of routeFileList) {
				const preRouteFile = preRouteFileList.find(
					(item) =>
						item.repo_project_id === routeFile.repo_project_id &&
            item.file_path === routeFile.file_path &&
            item.package === routeFile.package,
				);
				if (!preScan || !preRouteFile) {
					if (Object.keys(safeParse(routeFile.filter_material_info)).length) {
						await this.newMaterialUsageDao.create([
							{
								business,
								start_scan_id: preScan?.id ?? 0,
								end_scan_id: scanId,
								repo_project_id: routeFile.repo_project_id,
								package: routeFile.package,
								route_path: routeFile.route_path,
								file_path: routeFile.file_path,
								material_info: routeFile.filter_material_info,
							},
						]);
						console.info('[analysis] 创建 new material usage 成功:', {
							projectId: routeFile.repo_project_id,
							package: routeFile.package,
							route: routeFile.route_path,
							file: routeFile.file_path,
						});
					}
				} else {
					const materialInfo = JSON.parse(routeFile.filter_material_info);
					const preMaterialInfo = JSON.parse(preRouteFile.filter_material_info);

					Object.keys(materialInfo).forEach((key) => {
						if (preMaterialInfo[key]) {
							Object.keys(materialInfo[key]).forEach((materialKey) => {
								if (preMaterialInfo[key][materialKey]) {
									const count =
                    materialInfo[key][materialKey] -
                    preMaterialInfo[key][materialKey];

									if (count <= 0) {
										delete materialInfo[key][materialKey];
									} else {
										materialInfo[key][materialKey] = count;
									}
								}
							});

							if (!Object.keys(materialInfo[key]).length) {
								delete materialInfo[key];
							}
						}
					});

					if (Object.keys(materialInfo).length) {
						await this.newMaterialUsageDao.create([
							{
								business,
								start_scan_id: preScan?.id ?? 0,
								end_scan_id: scanId,
								repo_project_id: routeFile.repo_project_id,
								package: routeFile.package,
								route_path: routeFile.route_path,
								file_path: routeFile.file_path,
								material_info: safeStringify(materialInfo),
							},
						]);

						console.info('[analysis] 创建 new material usage 成功:', {
							projectId: routeFile.repo_project_id,
							package: routeFile.package,
							route: routeFile.route_path,
							file: routeFile.file_path,
						});
					}
				}
			}
		}
	}

	async getScanInfo(scanId: number) {
		return await this.scanInfoDao.queryById(scanId);
	}

	async indexesCalculation(scanId: number) {
		this.resultDao.deleteByScanId({ scanId });

		// 0. 拿到本次扫描的基础信息
		const scanInfo = await this.scanInfoDao.queryById(scanId);
		const businesses = JSON.parse(scanInfo.scan_range) as Business[];
		const startTime = scanInfo.start_time;

		// 1 通过 scanId 拿到所有明细表
		// 1.1 拿到新增代码行
		const projectNewCodeLines =
      await this.newCodeLinesDao.queryByScanId(scanId);
		// 1.2 拿到代码行
		const projectCodeLines = await this.codeLinesDao.queryByScanId(scanId);
		// 1.3 拿到页面明细
		const routeFileMaterialUsage =
      await this.routeFileUsageDao.queryByScanId(scanId);
		// 1.4 拿到新增组件使用明细
		const newMaterialUsage =
      await this.newMaterialUsageDao.queryByScanId(scanId);
		// 1.5 拿到物料扫描情况
		const materialMeta = await this.materialMetaDao.queryMaterialMeta(scanId);

		// 2.0 展开所有事业部
		for (const business of businesses) {
			// 2.1.0 展开所有项目
			const projects = uniq(
				routeFileMaterialUsage
					.filter((item) => item.business === business)
					.map((item) => item.repo_project_id),
			);
			const projectsIndexesMeta: {
        projectId: number;
        pages: string[];
        usedMaterialPages: string[];
        newPages: string[];
        usedMaterialNewPages: string[];
        codeLines: number;
        newCodeLines: number;
        totalMaterialCodeLines: number;
        newTotalMaterialCodeLines: number;
      }[] = [];

			for (const projectId of projects) {
				const rawPageRecords = routeFileMaterialUsage.filter(
					(item) => item.repo_project_id === projectId,
				);
				// 2.1.1.0 项目的页面数量
				const pages = uniq(rawPageRecords.map((item) => item.route_path));

				// 2.1.1.1 使用了物料的页面数量
				const usedMaterialPages = pages.filter((page) => {
					const fileRecords = rawPageRecords.filter(
						(item) => item.route_path === page,
					);
					return fileRecords.some((record) => {
						return Object.keys(safeParse(record.filter_material_info)).length;
					});
				});

				// 2.1.2.0 项目的新增页面数量
				const newPages = pages.filter((page) => {
					const fileRecords = rawPageRecords.filter(
						(item) => item.route_path === page,
					);
					const min = Math.min(
						...fileRecords.map((file) => +file.file_create_time),
					);
					return min >= startTime;
				});

				// 2.1.2.1 使用了物料的新增页面数量
				const usedMaterialNewPages = newPages.filter((page) => {
					return usedMaterialPages.includes(page);
				});

				// 2.1.3.0 计算使用了的物料及次数
				const usedMaterialUsageRecords = uniqBy(
					rawPageRecords.filter(
						(item) => Object.keys(safeParse(item.filter_material_info)).length,
					),
					(item) => item.file_path,
				);
				const totalUsage = usedMaterialUsageRecords.reduce((n, c) => {
					const filteredMaterialInfo = safeParse(c.filter_material_info);
					for (const [libraryName, usage] of Object.entries(
						filteredMaterialInfo,
					)) {
						for (const [materialName, count] of Object.entries(usage)) {
							if (!n[libraryName]) {
								n[libraryName] = {};
							}
							if (!n[libraryName][materialName]) {
								n[libraryName][materialName] = 0;
							}
							n[libraryName][materialName] += count as number;
						}
					}
					return n;
				}, {} as MaterialUsage);

				// 2.1.3.1 计算使用了的物料的对应行数
				const totalMaterialCodeLines = Object.entries(totalUsage).reduce(
					(n, [libraryName, usage]) => {
						for (const [materialName, count] of Object.entries(usage)) {
							const material = materialMeta.find(
								(item) =>
									item.package === libraryName &&
                  item.material_name === materialName,
							);
							if (material) {
								n += material.code_lines * count;
							}
						}
						return n;
					},
					0,
				);

				// 2.1.4.0 计算新增使用物料及次数
				const newMaterialUsageRecords = uniqBy(
					newMaterialUsage.filter(
						(item) =>
							item.repo_project_id === projectId &&
              Object.keys(safeParse(item.material_info)).length,
					),
					(item) => item.file_path,
				);
				const newTotalUsage = newMaterialUsageRecords.reduce((n, c) => {
					const filteredMaterialInfo = safeParse(c.material_info);
					for (const [libraryName, usage] of Object.entries(
						filteredMaterialInfo,
					)) {
						if (libraryName === '@m-ui/react') continue
						for (const [materialName, count] of Object.entries(usage)) {
							if (!n[libraryName]) {
								n[libraryName] = {};
							}
							if (!n[libraryName][materialName]) {
								n[libraryName][materialName] = 0;
							}
							n[libraryName][materialName] += count as number;
						}
					}
					return n;
				}, {});

				// 2.1.4.1 计算新增使用物料的对应行数
				const newTotalMaterialCodeLines = Object.entries(newTotalUsage).reduce(
					(n, [libraryName, usage]) => {
						for (const [materialName, count] of Object.entries(usage)) {
							const material = materialMeta.find(
								(item) =>
									item.package === libraryName &&
                  item.material_name === materialName,
							);
							if (material) {
								n += material.code_lines * count;
							}
						}
						return n;
					},
					0,
				);

				// 2.1.5 计算项目的代码行数
				const codeLines = projectCodeLines.find(
					(item) => item.repo_project_id === projectId,
				).code_lines;

				// 2.1.6 计算代码的新增行数
				const newCodeLines = projectNewCodeLines.find(
					(item) => item.repo_project_id === projectId,
				).new_code_lines;

				projectsIndexesMeta.push({
					projectId,
					pages,
					usedMaterialPages,
					newPages,
					usedMaterialNewPages,
					codeLines,
					newCodeLines,
					totalMaterialCodeLines,
					newTotalMaterialCodeLines,
				});
			}
			// 2.2.0 合并事业部数据
			const businessPageCount = projectsIndexesMeta.reduce(
				(n, c) => n + c.pages.length,
				0,
			);
			const businessUsedMaterialPageCount = projectsIndexesMeta.reduce(
				(n, c) => n + c.usedMaterialPages.length,
				0,
			);
			const businessNewPageCount = projectsIndexesMeta.reduce(
				(n, c) => n + c.newPages.length,
				0,
			);
			const businessUsedMaterialNewPageCount = projectsIndexesMeta.reduce(
				(n, c) => n + c.usedMaterialNewPages.length,
				0,
			);
			const businessCodeLines = projectsIndexesMeta.reduce(
				(n, c) => n + c.codeLines,
				0,
			);
			const businessNewCodeLines = projectsIndexesMeta.reduce(
				(n, c) => n + c.newCodeLines,
				0,
			);
			const businessTotalMaterialCodeLines = projectsIndexesMeta.reduce(
				(n, c) => n + c.totalMaterialCodeLines,
				0,
			);
			const businessNewTotalMaterialCodeLines = projectsIndexesMeta.reduce(
				(n, c) => n + c.newTotalMaterialCodeLines,
				0,
			);

			const createParams: Array<
        Omit<IResultDO, 'id' | 'status' | 'create_time'>
      > = [];
			// 2.2.1 计算页面覆盖率
			const businessUsedMaterialPageCoverage = NP.round(
				NP.divide(businessUsedMaterialPageCount, businessPageCount),
				4,
			);
			createParams.push({
				business,
				scan_id: scanId.toString(),
				type: NewReportType.PAGE_REUSE_RATE,
				value: businessUsedMaterialPageCoverage,
				content: JSON.stringify({
					pageCount: businessPageCount,
					usedMaterialPageCount: businessUsedMaterialPageCount,
					// metadata: projectsIndexesMeta,
				}),
			});

			// 2.2.2 计算新增页面覆盖率
			const businessUsedMaterialNewPageCoverage = NP.round(
				NP.divide(businessUsedMaterialNewPageCount, businessNewPageCount),
				4,
			);
			createParams.push({
				business,
				scan_id: scanId.toString(),
				type: NewReportType.NEW_PAGE_REUSE_RATE,
				value: businessUsedMaterialNewPageCoverage,
				content: JSON.stringify({
					newPageCount: businessNewPageCount,
					usedMaterialNewPageCount: businessUsedMaterialNewPageCount,
					// metadata: projectsIndexesMeta,
				}),
			});

			// 2.2.3 计算项目覆盖率
			const businessProjectCoverage = NP.round(
				NP.divide(
					projectsIndexesMeta.filter(
						(project) => project.usedMaterialPages.length,
					).length,
					projects.length,
				),
				4,
			);
			createParams.push({
				business,
				scan_id: scanId.toString(),
				type: NewReportType.PROJECT_REUSE_RATE,
				value: businessProjectCoverage,
				content: JSON.stringify({
					projectCount: projects.length,
					usedMaterialProjectCount: projectsIndexesMeta.filter(
						(project) => project.usedMaterialPages.length,
					).length,
					// metadata: projectsIndexesMeta,
				}),
			});

			// 2.2.4 计算代码复用率
			const businessCodeReuseRate = NP.round(
				NP.divide(
					businessTotalMaterialCodeLines,
					NP.plus(businessCodeLines, businessTotalMaterialCodeLines),
				),
				4,
			);
			createParams.push({
				business,
				scan_id: scanId.toString(),
				type: NewReportType.CODE_REUSE_RATE,
				value: businessCodeReuseRate,
				content: JSON.stringify({
					codeLines: businessCodeLines,
					totalMaterialCodeLines: businessTotalMaterialCodeLines,
					// metadata: projectsIndexesMeta,
				}),
			});

			// 2.2.5 计算新增代码复用率
			const businessNewCodeReuseRate = NP.round(
				NP.divide(
					businessNewTotalMaterialCodeLines,
					NP.plus(businessNewCodeLines, businessNewTotalMaterialCodeLines),
				),
				4,
			);
			createParams.push({
				business,
				scan_id: scanId.toString(),
				type: NewReportType.NEW_CODE_REUSE_RATE,
				value: businessNewCodeReuseRate,
				content: JSON.stringify({
					newCodeLines: businessNewCodeLines,
					newTotalMaterialCodeLines: businessNewTotalMaterialCodeLines,
					// metadata: projectsIndexesMeta,
				}),
			});

			await this.resultDao.create(createParams);
		}
	}

	async getLibUsage(scanIds: number[]) {
		const scanResultInfo = {};
		for (let scanId of scanIds) {
			const scanInfo = await this.scanInfoDao.queryById(scanId);
			const businesses = JSON.parse(scanInfo.scan_range) as Business[];
			// 1.3 拿到页面明细
			const routeFileMaterialUsage =
        await this.routeFileUsageDao.queryByScanId(scanId);
			// 1.4 拿到新增组件使用明细
			const newMaterialUsage =
        await this.newMaterialUsageDao.queryByScanId(scanId);
			const allResult = {};

			for (let business of businesses) {
				const routeFile = {};
				const newMaterial = {};
				const projects = uniq(
					routeFileMaterialUsage
						.filter((item) => item.business === business)
						.map((item) => item.repo_project_id),
				);
				for (const projectId of projects) {
					const rawPageRecords = routeFileMaterialUsage.filter(
						(item) => item.repo_project_id === projectId,
					);

					const usedMaterialUsageRecords = uniqBy(
						rawPageRecords.filter(
							(item) =>
								Object.keys(safeParse(item.filter_material_info)).length,
						),
						(item) => item.file_path,
					);

					usedMaterialUsageRecords.forEach((item) => {
						const filteredMaterialInfo = safeParse(item.filter_material_info);
						for (const [libraryName, usage] of Object.entries(
							filteredMaterialInfo,
						)) {
							for (const [materialName, count] of Object.entries(usage)) {
								if (!routeFile[libraryName]) {
									routeFile[libraryName] = {};
								}
								if (!routeFile[libraryName][materialName]) {
									routeFile[libraryName][materialName] = 0;
								}
								routeFile[libraryName][materialName] += count as number;
							}
						}
					});

					const newMaterialUsageRecords = uniqBy(
						newMaterialUsage.filter(
							(item) =>
								item.repo_project_id === projectId &&
                Object.keys(safeParse(item.material_info)).length,
						),
						(item) => item.file_path,
					);
					newMaterialUsageRecords.forEach((item) => {
						const filteredMaterialInfo = safeParse(item.material_info);
						for (const [libraryName, usage] of Object.entries(
							filteredMaterialInfo,
						)) {
							for (const [materialName, count] of Object.entries(usage)) {
								if (!newMaterial[libraryName]) {
									newMaterial[libraryName] = {};
								}
								if (!newMaterial[libraryName][materialName]) {
									newMaterial[libraryName][materialName] = 0;
								}
								newMaterial[libraryName][materialName] += count as number;
							}
						}
					});
				}

				allResult[business] = {
					route_file: routeFile,
					new_material: newMaterial,
				};
			}

			scanResultInfo[scanId] = allResult;
		}

		try {
			await this.cdnService.uploadToCDN({
				filename: `material-usage-${dayjs().format('YYYY-MM-DD_HH:mm:ss')}.json`,
				str: JSON.stringify(scanResultInfo, null, 2),
				path: '/scan-material-usage',
			});
		} catch (e) {
			console.log(
				`日志文件上传失败，错误原因是${typeof e === 'string' ? e : e.message}`,
			);
		}
	}

	async computeCurrentScanningMaterialCount(scanId: number) {
		// 0. 拿到本次扫描的基础信息
		const { start_time, end_time, display_txt } =
      await this.scanInfoDao.queryById(scanId);
		// 1. 找到所有在物料中台的物料
		const allMaterials = await this.getAllMaterialFromCenter();

		// 2. 找到本次 scan 算出来的物料行数和物料引用情况
		const currentMaterialMeta =
      await this.materialMetaDao.queryMaterialMeta(scanId);

		// 3. 通过 1 的数据过滤 2 的结果，并计算出物料的行数，生成一个空的物料使用情况
		const resultMaterialUsage = {
			id: scanId,
			start_time,
			end_time,
			display_txt,
			usage: [] as Array<{
        library: string;
        business: Business;
        materials: Array<{
          materialId: number;
          name: string;
          codeLines: number;
          count: number;
          directUseCount: number;
          indirectUseCount: number;
          thirdPartyUsage: MaterialUsage | null;
        }>;
      }>,
		};

		for (const [, componentInfo] of Object.entries(allMaterials)) {
			const { materialId, packageName, components, componentBundleType } =
        componentInfo;
			const initMaterials: Array<{
        materialId: number;
        name: string;
        codeLines: number;
        count: number;
        directUseCount: number;
        indirectUseCount: number;
        thirdPartyUsage: MaterialUsage | null;
				department: string;
      }> = [];
			const user = await this.materialDao.queryUserInfoByMaterialId(materialId);
			if (componentBundleType === 'SLMC') {
				components.forEach((component) => {
					initMaterials.push({
						materialId,
						name: component,
						codeLines: 0,
						count: 0,
						directUseCount: 0,
						indirectUseCount: 0,
						thirdPartyUsage: {},
						department: user.user_department,
					});
				});
			} else if (componentBundleType === 'SLSC') {
				initMaterials.push({
					materialId,
					name: 'default',
					codeLines: 0,
					count: 0,
					directUseCount: 0,
					indirectUseCount: 0,
					thirdPartyUsage: null,
					department: user.user_department,
				});
			}
			resultMaterialUsage.usage.push({
				library: packageName,
				business: componentInfo.business as Business,
				materials: initMaterials,
			});
		}

		// 初始化行数及第三方使用情况
		for (let i = 0; i < currentMaterialMeta.length; i++) {
			const materialMeta = currentMaterialMeta[i];
			const {
				code_lines,
				filter_third_usage,
				package: packageName,
				material_name,
			} = materialMeta;

			// 3.2 插入物料使用情况
			const libMaterialUsage =
        resultMaterialUsage.usage.find(
        	(lib) =>
        		lib.library === packageName &&
            lib.business === materialMeta.business,
        ) ||
        resultMaterialUsage.usage.find((lib) => lib.library === packageName);
			if (!libMaterialUsage) continue;
			// FIXME: 如果有重复的物料名，会导致数据错误
			const material = libMaterialUsage.materials.find(
				(mat) => mat.name === material_name,
			);
			if (material) {
				material.codeLines = code_lines;
				material.thirdPartyUsage = safeParse(filter_third_usage);
			}
		}

		// 4. 找到本次 scan 所有页面的物料使用情况
		const routeFileMaterialUsage: IRouteFileUsageDAO[] =
      await this.routeFileUsageDao.queryByScanId(scanId);
		const noRepeatFileUsage: IRouteFileUsageDAO[] = [];
		// 4.1 先对页面文件去重
		for (let i = 0; i < routeFileMaterialUsage.length; i++) {
			const file = routeFileMaterialUsage[i];
			if (
				!noRepeatFileUsage.find(
					(item) =>
						item.repo_project_id === file.repo_project_id &&
            item.file_path === file.file_path,
				)
			) {
				noRepeatFileUsage.push(file);
			}
		}

		// 4.2 计算直接使用物料的情况
		for (let i = 0; i < noRepeatFileUsage.length; i++) {
			const fileUsage = noRepeatFileUsage[i];
			const { filter_material_info } = fileUsage;
			const parsedMaterialInfo = safeParse(filter_material_info);
			for (const [fileUsedLibraryName, fileUsedMaterialCount] of Object.entries(
				parsedMaterialInfo,
			)) {
				// 优先同部门的物料
				const libUsage =
          resultMaterialUsage.usage.find(
          	(lib) =>
          		lib.library === fileUsedLibraryName &&
              lib.business === fileUsage.business,
          ) ||
          resultMaterialUsage.usage.find(
          	(lib) => lib.library === fileUsedLibraryName,
          );
				if (!libUsage) continue;
				for (const [materialName, count] of Object.entries(
					fileUsedMaterialCount,
				)) {
					const material = libUsage.materials.find(
						(mat) => mat.name === materialName,
					);
					if (material && isNumber(+count) && isNaN(+count) === false) {
						material.count += +count;
						material.directUseCount += +count;
					}
				}
			}
		}
		// 4.3 计算间接使用物料的情况
		for (let j = 0; j < resultMaterialUsage.usage.length; j++) {
			const currentWalkLibUsage = resultMaterialUsage.usage[j];
			for (let k = 0; k < currentWalkLibUsage.materials.length; k++) {
				const currentWalkMaterial = currentWalkLibUsage.materials[k];
				const currentWalkMaterialThirdUsage =
          currentWalkMaterial.thirdPartyUsage;
				if (!currentWalkMaterialThirdUsage) continue;
				for (const [thirdLibraryName, thirdMaterials] of Object.entries(
					currentWalkMaterialThirdUsage,
				)) {
					// 优先同部门的物料
					const usedThirdLib =
            resultMaterialUsage.usage.find(
            	(lib) =>
            		lib.library === thirdLibraryName &&
                lib.business === currentWalkLibUsage.business,
            ) ||
            resultMaterialUsage.usage.find(
            	(lib) => lib.library === thirdLibraryName,
            );
					if (!usedThirdLib) continue;
					for (const [indirectUsedMaterialName, count] of Object.entries(
						thirdMaterials,
					)) {
						const indirectUsedMaterial = usedThirdLib.materials.find(
							(mat) => mat.name === indirectUsedMaterialName,
						);
						if (
							indirectUsedMaterial &&
              isNumber(+count) &&
              isNaN(+count) === false
						) {
							indirectUsedMaterial.count += +count * currentWalkMaterial.count;
							indirectUsedMaterial.indirectUseCount +=
                +count * currentWalkMaterial.count;
						}
					}
				}
			}
		}

		try {
			await this.cdnService.uploadToCDN({
				filename: `material-usage-${scanId}-${display_txt.split(' ').join('-')}.json`,
				str: JSON.stringify(resultMaterialUsage, null, 2),
				path: '/material-count-and-codeLines',
			});
		} catch (error) {
			console.log('添加物料使用情况失败', error);
		}

		return resultMaterialUsage;
	}

	async testResult(scanIds: number[], coefficient = 1) {
		const scanResultInfo = {};
		for (let scanId of scanIds) {
			// 0. 拿到本次扫描的基础信息
			const scanInfo = await this.scanInfoDao.queryById(scanId);
			const businesses = JSON.parse(scanInfo.scan_range) as Business[];

			// 1 通过 scanId 拿到所有明细表
			// 1.1 拿到新增代码行
			const projectNewCodeLines =
        await this.newCodeLinesDao.queryByScanId(scanId);
			// 1.2 拿到代码行
			const projectCodeLines = await this.codeLinesDao.queryByScanId(scanId);
			// 1.3 拿到页面明细
			const routeFileMaterialUsage =
        await this.routeFileUsageDao.queryByScanId(scanId);
			// 1.4 拿到新增组件使用明细
			const newMaterialUsage =
        await this.newMaterialUsageDao.queryByScanId(scanId);
			// 1.5 拿到物料扫描情况
			const materialMeta = await this.materialMetaDao.queryMaterialMeta(scanId);
			scanResultInfo[scanId] = {};

			// 2.0 展开所有事业部
			for (const business of businesses) {
				// 2.1.0 展开所有项目
				const projects = uniq(
					routeFileMaterialUsage
						.filter((item) => item.business === business)
						.map((item) => item.repo_project_id),
				);
				const projectsIndexesMeta: {
          codeLines: number;
          newCodeLines: number;
          totalMaterialCodeLines: number;
          newTotalMaterialCodeLines: number;
        }[] = [];

				for (const projectId of projects) {
					const rawPageRecords = routeFileMaterialUsage.filter(
						(item) => item.repo_project_id === projectId,
					);

					// 2.1.3.0 计算使用了的物料及次数
					const usedMaterialUsageRecords = uniqBy(
						rawPageRecords.filter(
							(item) =>
								Object.keys(safeParse(item.filter_material_info)).length,
						),
						(item) => item.file_path,
					);
					const totalUsage = usedMaterialUsageRecords.reduce((n, c) => {
						const filteredMaterialInfo = safeParse(c.filter_material_info);
						for (const [libraryName, usage] of Object.entries(
							filteredMaterialInfo,
						)) {
							for (const [materialName, count] of Object.entries(usage)) {
								if (!n[libraryName]) {
									n[libraryName] = {};
								}
								if (!n[libraryName][materialName]) {
									n[libraryName][materialName] = 0;
								}
								n[libraryName][materialName] += count as number;
							}
						}
						return n;
					}, {} as MaterialUsage);

					// 2.1.3.1 计算使用了的物料的对应行数
					const totalMaterialCodeLines = Object.entries(totalUsage).reduce(
						(n, [libraryName, usage]) => {
							for (const [materialName, count] of Object.entries(usage)) {
								const material = materialMeta.find(
									(item) =>
										item.package === libraryName &&
                    item.material_name === materialName,
								);
								if (material) {
									n += NP.times(
										material.code_lines,
										count,
										libraryName === '@m-ui/react' ? coefficient : 1,
									);
								}
							}
							return n;
						},
						0,
					);

					// 2.1.4.0 计算新增使用物料及次数
					const newMaterialUsageRecords = uniqBy(
						newMaterialUsage.filter(
							(item) =>
								item.repo_project_id === projectId &&
                Object.keys(safeParse(item.material_info)).length,
						),
						(item) => item.file_path,
					);
					const newTotalUsage = newMaterialUsageRecords.reduce((n, c) => {
						const filteredMaterialInfo = safeParse(c.material_info);
						for (const [libraryName, usage] of Object.entries(
							filteredMaterialInfo,
						)) {
							for (const [materialName, count] of Object.entries(usage)) {
								if (!n[libraryName]) {
									n[libraryName] = {};
								}
								if (!n[libraryName][materialName]) {
									n[libraryName][materialName] = 0;
								}
								n[libraryName][materialName] += count as number;
							}
						}
						return n;
					}, {});

					// 2.1.4.1 计算新增使用物料的对应行数
					const newTotalMaterialCodeLines = Object.entries(
						newTotalUsage,
					).reduce((n, [libraryName, usage]) => {
						for (const [materialName, count] of Object.entries(usage)) {
							const material = materialMeta.find(
								(item) =>
									item.package === libraryName &&
                  item.material_name === materialName,
							);
							if (material) {
								n += NP.times(
									material.code_lines,
									count,
									libraryName === '@m-ui/react' ? coefficient : 1,
								);
							}
						}
						return n;
					}, 0);

					// 2.1.5 计算项目的代码行数
					const codeLines = projectCodeLines.find(
						(item) => item.repo_project_id === projectId,
					).code_lines;

					// 2.1.6 计算代码的新增行数
					const newCodeLines = projectNewCodeLines.find(
						(item) => item.repo_project_id === projectId,
					).new_code_lines;

					projectsIndexesMeta.push({
						codeLines,
						newCodeLines,
						totalMaterialCodeLines,
						newTotalMaterialCodeLines,
					});
				}
				const businessCodeLines = projectsIndexesMeta.reduce(
					(n, c) => n + c.codeLines,
					0,
				);
				const businessNewCodeLines = projectsIndexesMeta.reduce(
					(n, c) => n + c.newCodeLines,
					0,
				);
				const businessTotalMaterialCodeLines = projectsIndexesMeta.reduce(
					(n, c) => n + c.totalMaterialCodeLines,
					0,
				);
				const businessNewTotalMaterialCodeLines = projectsIndexesMeta.reduce(
					(n, c) => n + c.newTotalMaterialCodeLines,
					0,
				);

				const createParams: Array<AnyType> = [];
				// 2.2.4 计算代码复用率
				const businessCodeReuseRate = NP.round(
					NP.divide(
						businessTotalMaterialCodeLines,
						NP.plus(businessCodeLines, businessTotalMaterialCodeLines),
					),
					4,
				);
				createParams.push({
					business,
					scan_id: scanId.toString(),
					type: NewReportType.CODE_REUSE_RATE,
					value: businessCodeReuseRate,
					codeLines: businessCodeLines,
					totalMaterialCodeLines: businessTotalMaterialCodeLines,
				});

				// 2.2.5 计算新增代码复用率
				const businessNewCodeReuseRate = NP.round(
					NP.divide(
						businessNewTotalMaterialCodeLines,
						NP.plus(businessNewCodeLines, businessNewTotalMaterialCodeLines),
					),
					4,
				);
				createParams.push({
					business,
					scan_id: scanId.toString(),
					type: NewReportType.NEW_CODE_REUSE_RATE,
					value: businessNewCodeReuseRate,
					newCodeLines: businessNewCodeLines,
					newTotalMaterialCodeLines: businessNewTotalMaterialCodeLines,
				});

				scanResultInfo[scanId][business] = createParams;
			}
		}

		try {
			await this.cdnService.uploadToCDN({
				filename: `result-${coefficient}-${dayjs().format('YYYY-MM-DD_HH:mm:ss')}.json`,
				str: JSON.stringify(scanResultInfo, null, 2),
				path: '/scan-test-result',
			});
		} catch (e) {
			console.log(
				`日志文件上传失败，错误原因是${typeof e === 'string' ? e : e.message}`,
			);
		}
	}
}
