import { DOBase } from '@mybricks/rocker-dao';
import { genMainIndexOfDB } from '../../common/util';
import { EffectStatus } from '../../common/const';

export default class MaterialTagDao extends DOBase {
	public async create(params: Module.Material.CreateTagParams) {
		const result = await this.exe<{ insertId: number }>('material_tag:create', {
			...params,
			id: genMainIndexOfDB(),
			order: params.order ?? 0,
			create_time: Date.now(),
			status: EffectStatus.EFFECT
		});

		return result.insertId;
	}

	public async delete(id: number) {
		return await this.exe('material_tag:delete', { id });
	}

	public async update(params: Module.Material.UpdateTagParams) {
		return await this.exe('material_tag:update', params);
	}

	public async detail(id: number) {
		return (await this.exe<Module.Material.Tag[]>('material_tag:detail', { id }))?.[0];
	}

	public async query(params: Module.Material.GetTagListParams & { offset: number }) {
		return await this.exe<Module.Material.Tag[]>('material_tag:query', params);
	}

	public async queryByValue(params: { value: string; }) {
		return await this.exe<Module.Material.Tag[]>('material_tag:queryByValue', params);
	}

	public async queryByValues(params: { values: string[]; }) {
		if (!params.values.length) {
			return [];
		}
		return await this.exe<Module.Material.Tag[]>('material_tag:queryByValues', params);
	}

	public async queryListTotal(params: Omit<Module.Material.GetTagListParams, 'pageNum' | 'pageSize'>) {
		const result = await this.exe<Array<{ total: number }>>('material_tag:queryListTotal', params);
		return result[0]?.total;
	}
}
