import { Inject, Injectable } from '@nestjs/common';
import MaterialTagDao from './material-tag.dao';
import { EffectStatus, PAGE_NUM, PAGE_SIZE } from '../../common/const';
import { computeOffset } from '../../common/util/paging';

@Injectable()
export default class MaterialTagService {
  @Inject(MaterialTagDao)
  	materialTagDao: MaterialTagDao;

  async create(params: Module.Material.CreateTagParams) {
  	const [tag] = await this.materialTagDao.queryByValue(params);

  	if (tag) {
  		throw new Error('标签已存在');
  	}

  	return this.materialTagDao.create(params);
  }

  async delete(id: number) {
  	return this.materialTagDao.delete(id);
  }

  async update(params: Module.Material.UpdateTagParams) {
  	if (await this.exist(params.id)) {
  		return this.materialTagDao.update(params);
  	} else {
  		throw new Error('标签不存在');
  	}
  }

  async detail(id: number) {
  	return this.materialTagDao.detail(id);
  }

  async query(params: Module.Material.GetTagListParams) {
  	const mergedParams = {
  		...params,
		  title: params.title ? `%${params.title}%` : undefined,
		  pageSize: Number(params.pageSize || PAGE_SIZE),
		  status: params.status ?? EffectStatus.EFFECT,
		  pageNum: Number(params.pageNum || PAGE_NUM),
  	};
  	const [dataSource, total] = await Promise.all([
  		this.materialTagDao.query({
  			...mergedParams,
  			offset: computeOffset(mergedParams.pageNum, mergedParams.pageSize),
  		}),
  		this.materialTagDao.queryListTotal(mergedParams),
  	]);

  	return {
  		pageSize: mergedParams.pageSize,
  		pageNum: mergedParams.pageNum,
  		dataSource,
  		total,
  	};
  }

  async exist(id: number) {
  	return !!(await this.detail(id));
  }
}
