import { Body, Controller, Get, Inject, Post, Query } from "@nestjs/common";
import MaterialTagService from "./material-tag.service";

@Controller("api/manage/tag")
export default class MaterialTagController {
  @Inject(MaterialTagService)
  materialTagService: MaterialTagService;

  @Post("/create")
  async create(@Body() body: Module.Material.CreateTagParams) {
    if (!body.title || !body.value) {
      return { code: -1, message: "参数 title、value、creator_id 不能为空" };
    }

    try {
      return {
        code: 1,
        data: await this.materialTagService.create(body),
        message: "标签新建成功",
      };
    } catch (error) {
      return { code: -1, message: "标签新建失败，错误原因是" + error.message };
    }
  }

  @Post("/delete")
  async delete(@Body("id") id: number) {
    await this.materialTagService.delete(id);

    return { code: 1, message: "标签删除成功" };
  }

  @Post("/update")
  async updated(@Body() body: Module.Material.UpdateTagParams) {
    if (!body.id) {
      return { code: -1, message: "参数 id 不能为空" };
    }

    try {
      await this.materialTagService.update(body);

      return { code: 1, message: "标签更新成功" };
    } catch (error) {
      return { code: -1, message: "标签更新失败，错误原因是" + error.message };
    }
  }

  @Get("/list")
  async getList(@Query() params: Module.Material.GetTagListParams) {
    try {
      return { code: 1, data: await this.materialTagService.query(params) };
    } catch (error) {
      return {
        code: -1,
        message: "查询标签列表失败，错误原因是" + error.message,
      };
    }
  }
}
