import { DOBase } from '@mybricks/rocker-dao';
import { genMainIndexOfDB } from '../../common/util';
import { EffectStatus } from '../../common/const';

export default class MaterialTagRelationDao extends DOBase {
	public async create(params: Module.Material.CreateTagRelationParams) {
		const result = await this.exe<{ insertId: number }>('material_tag_relation:create', {
			...params,
			id: genMainIndexOfDB(),
			create_time: Date.now(),
			status: EffectStatus.EFFECT
		});

		return result.insertId;
	}

	public async deleteByMaterialId(id: number) {
		return await this.exe('material_tag_relation:deleteByMaterialId', { id });
	}

	public async queryByMaterialIds(materialIds: number[]) {
		if(!materialIds.length) {
			return [];
		}

		return await this.exe<Module.Material.QueryMaterialTag[]>('material_tag_relation:queryByMaterialIds', { materialIds });
	}
}
