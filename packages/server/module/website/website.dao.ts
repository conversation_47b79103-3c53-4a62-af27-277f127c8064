import { DOBase } from "@mybricks/rocker-dao";
import { genMainIndexOfDB } from "../../common/util";
import { EffectStatus } from "../../common/const";

export default class WebsiteDao extends DOBase {
  public async query(
    params: Omit<
      Module.Material.GetMaterialListParams,
      "needSchema" | "needExtraMeta"
    > & { offset: number; needSchema?: boolean; exclude_tianhe_basic_materials?: boolean },
  ) {
    return await this.exe<Module.Material.QueryMaterial[]>(
      "website:query",
      params,
    );
  }

  public async queryMaterialList(
    params: Omit<
      Module.Material.GetMaterialListParams,
      "needSchema" | "needExtraMeta"
    > & { offset: number; needSchema?: boolean },
  ) {
    return await this.exe<Module.Material.QueryMaterial[]>(
      "website:queryMaterialList",
      {
        ...params,
        domain: isNaN(+params.domain) ? 0 : +params.domain,
      },
    );
  }

  public async queryMaterialListTotal(
    params: Omit<
      Module.Material.GetMaterialListParams,
      "needSchema" | "needExtraMeta"
    >,
  ) {
    return await this.exe<[{ total: number }]>(
      "website:queryMaterialListTotal",
      {
        ...params,
        domain: isNaN(+params.domain) ? 0 : +params.domain,
      },
    );
  }

  public async queryCreatorInfoByMaterialCreatorId(params: {
    creatorId: number;
  }) {
    return await this.exe<Module.Material.QueryMaterial[]>(
      "website:queryCreatorInfoByMaterialCreatorId",
      params,
    );
  }

  public async queryMaterialPubInfoByMaterialIdAndVersion(params: {
    materialId: number;
    version: string;
  }) {
    return await this.exe<Module.Material.QueryMaterial[]>(
      "website:queryMaterialPubInfoByMaterialIdAndVersion",
      params,
    );
  }

  public async queryByPkgNameAndVersion(params: {
    keyword: string;
    version?: string;
    name: string;
    needContent: boolean;
  }) {
    return await this.exe<Module.Material.QueryMaterial[]>(
      "website:queryByPkgNameAndVersion",
      params,
    );
  }

  public async queryByNamespaces(params: {
    namespaces: string[];
    needPub?: boolean;
    status: EffectStatus;
  }) {
    if (!params.namespaces.length) {
      return [];
    }

    return await this.exe<Module.Material.QueryMaterial[]>(
      "website:queryByNamespaces",
      params,
    );
  }

  public async queryListTotal(
    params: Omit<
      Module.Material.GetMaterialListParams,
      "pageNum" | "pageSize" | "needSchema" | "needExtraMeta"
    > & { exclude_tianhe_basic_materials?: boolean },
  ) {
    return await this.exe("website:queryListTotal", params);
  }

  public async queryByNamespace(namespace: string) {
    return await this.exe<Module.Material.Material[]>(
      "website:queryByNamespace",
      { namespace },
    );
  }

  public async queryByNamespaceAndVersion(namespace: string, version: string) {
    return await this.exe<(Module.Material.Material & { pub_id: number })[]>(
      "website:queryByNamespaceAndVersion",
      { namespace, version },
    );
  }

  public async queryByNamespaceAndVersionList(
    components: Array<{ namespace: string; version: string }>,
  ) {
    if (!components?.length) {
      return [];
    }
    return await this.exe<Module.Material.Material[]>(
      "website:queryByNamespaceAndVersionList",
      { components },
    );
  }

  public async create(
    materials: Array<
      Module.Material.CreateMaterialParamsForWebsiteDao & { creator_id: number }
    >,
  ) {
    const materialRecords = materials.map((item) => {
      return {
        ...item,
        id: genMainIndexOfDB(),
        create_time: Date.now(),
        update_time: Date.now(),
        updater_id: item.creator_id,
        status: item.hidden ? EffectStatus.DISABLED : EffectStatus.EFFECT,
        meta: item.meta ?? "",
      };
    });
    console.log(materialRecords);
    const result = await this.exe<{ insertId: number }>("website:create", {
      materials: materialRecords,
    });
    return result?.insertId;
  }

  public async queryDetail(params: { id?: number; namespace?: string }) {
    if (!params.id && !params.namespace) {
      throw Error("物料 id 或 namespace 不能都缺失");
    }

    return await this.exe<Module.Material.QueryMaterial[]>(
      "website:detail",
      params,
    );
  }
}
