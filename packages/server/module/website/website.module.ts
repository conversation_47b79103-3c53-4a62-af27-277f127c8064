import { Module } from '@nestjs/common';
import WebsiteController from './website.controller';
import WebsiteService from './website.service';
import WebsiteDao from './website.dao';
import UserDao from '../user/user.dao';
import MaterialPubDao from '../material-pub/material-pub.dao';
import MaterialDao from '../material/material.dao';
import MaterialTagDao from '../material-tag/material-tag.dao';
import MaterialSceneDao from '../material-scene/material-scene.dao';
import MaterialReferDao from '../material-refer/material-refer.dao';
import MaterialTagRelationDao from '../material-tag/material-tag-relation.dao';
import MaterialSceneRelationDao from '../material-scene/material-scene-relation.dao';
import MaterialMetaDao from '../material-meta/material-meta.dao';
import MaterialMetaValueDao from '../material-meta-value/material-meta-value.dao';
import NoticeService from '../notice/notice.service';
import BusinessConfigDao from '../business-config/business-config.dao';
import CDNService from '../cdn/cdn.service';
import MaterialService from '../material/material.service';
import MaterialMetaValueRelationDao from '../material-meta-value-relation/material-meta-value-relation.dao';
import ReportDao from '../report/report.dao';

@Module({
	imports: [],
	controllers: [WebsiteController],
	providers: [
		WebsiteService,
		WebsiteDao,
		UserDao,
		MaterialPubDao,
		MaterialDao,
		MaterialTagDao,
		MaterialSceneDao,
		MaterialReferDao,
		MaterialTagRelationDao,
		MaterialSceneRelationDao,
		MaterialMetaDao,
		MaterialMetaValueDao,
		BusinessConfigDao,
		NoticeService,
		CDNService,
		MaterialService,
		MaterialMetaValueRelationDao,
		ReportDao
	],
	exports: [],
})
export class WebsiteModule {}
