import { Body, Controller, Get, Inject, Post, Query } from "@nestjs/common";
import { BUNDLE_TYPES } from '@global-material-middleoffice/server-v2/shared';

import WebsiteService from "./website.service";
import { PromiseAllFulfilled } from "../../common/util/Promise";

@Controller("api/website/material")
export default class WebsiteController {
  @Inject(WebsiteService)
  websiteService: WebsiteService;

  @Get("/list")
  async getMaterialList(@Query() query: Module.Material.GetMaterialListParams) {
    try {
      const keys = Object.keys(query).filter((key) => key.startsWith("meta."));

      return {
        code: 1,
        data: await this.websiteService.query({
          ...query,
          meta: keys.length
            ? keys.reduce(
                (pre, key) => ({
                  ...pre,
                  [key.replace("meta.", "")]: query[key],
                }),
                {},
              )
            : undefined,
        }),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询物料列表失败，错误原因是" + error.message,
      };
    }
  }

  @Get("/list_for_tianhe")
  async getMaterialListForTianhe(@Query() query: Module.Material.GetMaterialListParams) {
    try {
      const keys = Object.keys(query).filter((key) => key.startsWith("meta."));

      return {
        code: 1,
        data: await this.websiteService.query({
          ...query,
          meta: keys.length
            ? keys.reduce(
                (pre, key) => ({
                  ...pre,
                  [key.replace("meta.", "")]: query[key],
                }),
                {},
              )
            : undefined,
          exclude_tianhe_basic_materials: true,
        }),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询物料列表失败，错误原因是" + error.message,
      };
    }
  }

  @Get("/list/ids")
  async getMaterialListByIds(
    @Query()
    queries: {
      ids: string;
      needSchema?: number;
      needExtraMeta?: number;
      branch?: "main" | "all";
    },
  ) {
    try {
      return {
        code: 1,
        data: await this.websiteService.query({
          ids: queries.ids.split(',').map((id) => +id),
          needSchema: queries.needSchema,
          needExtraMeta: queries.needExtraMeta,
          branch: queries.branch,
          status: undefined
        }),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询物料列表失败，错误原因是" + error.message,
      };
    }
  }

  @Get("/list_optimized")
  async queryMaterialList(
    @Query() query: Module.Material.GetMaterialListParams,
  ) {
    try {
      const keys = Object.keys(query).filter((key) => key.startsWith("meta."));

      return {
        code: 1,
        data: await this.websiteService.queryMaterialList({
          ...query,
          meta: keys.length
            ? keys.reduce(
                (pre, key) => ({
                  ...pre,
                  [key.replace("meta.", "")]: query[key],
                }),
                {},
              )
            : undefined,
        }),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询物料列表失败，错误原因是" + error.message,
      };
    }
  }

  /** 电商前端专用接口 */
  @Get("/list_for_es")
  async getMaterialListForES(
    @Query() query: Module.Material.GetMaterialListParams,
  ) {
    try {
      const keys = Object.keys(query).filter((key) => key.startsWith("meta."));

      return {
        code: 1,
        data: await this.websiteService.getMaterialListForES({
          ...query,
          meta: keys.length
            ? keys.reduce(
                (pre, key) => ({
                  ...pre,
                  [key.replace("meta.", "")]: query[key],
                }),
                {},
              )
            : undefined,
        }),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询物料列表失败，错误原因是" + error.message,
      };
    }
  }

  /** 根据包名查询所有子组件 */
  @Get("/list_by_pkg_name")
  async getMaterialListByPackageName(
    @Query() query: { name: string; version?: string; needContent?: string },
  ) {
    try {
      if (!query.name) {
        return { code: -1, message: "参数 name 必传" };
      }

      return {
        code: 1,
        data: await this.websiteService.getMaterialListByPackageName(query),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询子组件列表失败，错误原因是" + error.message,
      };
    }
  }

  @Get("/list_by_pkg_name_for_tianhe")
  async getMaterialListByPackageNameForTianhe(
    @Query() query: { name: string; version?: string; needContent?: string },
  ) {
    try {
      if (!query.name) {
        return { code: -1, message: "参数 name 必传" };
      }

      return {
        code: 1,
        data: await this.websiteService.getMaterialListByPackageNameForTianhe(
          query,
        ),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询子组件列表失败，错误原因是" + error.message,
      };
    }
  }

  @Post("/create_for_lingzhu")
  async createMaterialForLingzhu(
    @Body() body: Module.Material.CreateMaterialParamsForLingZhu,
  ) {
    return this.commonCreateMaterial({
      ...body,
      schema: Object.assign(
        {},
        {
          componentBundleType: BUNDLE_TYPES.SLMC,
          componentName: "default",
          libraryName: `${body.schema.packageName}/default`,
          instructionUrl: "#",
          publishTime: Date.now(),
          gitUrl: "#",
          updateTime: Date.now(),
        },
        body.schema,
        {
          tags: Object.assign(
            {},
            {
              category: ["common"],
            },
            body.schema.tags,
          ),
        },
      ),
    });
  }

  @Post("/create")
  async createMaterial(
    @Body() body: Module.Material.CreateMaterialParamsForWebsite,
  ) {
    return this.commonCreateMaterial(body);
  }

  @Post("/batch-create")
  async batchCreateMaterial(
    @Body() body: Module.Material.CreateMaterialParamsForWebsite[],
  ) {
    try {
      if (Array.isArray(body) && body.length) {
        return {
          code: 1,
          data: await PromiseAllFulfilled(
            body.map(async (item) => {
              const materialIdentifier = `${item.schema.packageName}/${item.schema.componentName}@${item.schema.version}`;
              const materialNamespace = `${item.schema.packageName}/${item.schema.componentName}`;
              try {
                const result = await this.commonCreateMaterial(item);
                if (result.code === -1) {
                  return {
                    success: false,
                    material: materialIdentifier,
                    packageName: item.schema.packageName,
                    componentName: item.schema.componentName,
                    materialNamespace,
                    message: result.message,
                  };
                } else {
                  return {
                    success: true,
                    material: materialIdentifier,
                    packageName: item.schema.packageName,
                    componentName: item.schema.componentName,
                    materialNamespace,
                    materialId: result.data.materialId,
                    materialVersion: item.schema.version,
                  };
                }
              } catch (error) {
                return {
                  success: false,
                  material: materialIdentifier,
                  packageName: item.schema.packageName,
                  componentName: item.schema.componentName,
                  materialNamespace,
                  message: "创建物料失败，错误原因是" + error.message,
                };
              }
            }),
          ),
        };
      }
    } catch (error) {
      return {
        code: -1,
        message: "批量创建物料失败，错误原因是" + error.message,
      };
    }
  }

  async commonCreateMaterial(
    body: Module.Material.CreateMaterialParamsForWebsite,
  ) {
    try {
      if (!body.schema) {
        return { code: -1, message: "参数 schema 必传" };
      }

      const {
        packageName,
        componentName,
        componentBundleType,
        libraryName,
        componentChineseName,
        author,
        assetDownloadType,
        assetDownloadUrl,
        thumbnailUrl,
        tags,
        version,
      } = body.schema;

      if (
        !packageName ||
        !componentName ||
        !componentBundleType ||
        !libraryName ||
        !componentChineseName ||
        !author ||
        !assetDownloadType ||
        !assetDownloadUrl ||
        !thumbnailUrl ||
        !tags ||
        !version
      ) {
        return {
          code: -1,
          message:
            "参数 packageName、componentName、componentBundleType、libraryName、componentChineseName、author、assetDownloadType、assetDownloadUrl、thumbnailUrl、tags、version 必传",
        };
      }
      if (
        (body.schema.tags.domain && !Array.isArray(body.schema.tags.domain)) ||
        (Array.isArray(body.schema.tags.domain) &&
          !body.schema.tags.domain.every((item) => typeof item === "number"))
      ) {
        return { code: -1, message: "参数 tags.domain 必须为数字数组" };
      }
      const data = await this.websiteService.create(body);
      if (data?.isRepeat) {
        return {
          code: -1,
          message: "物料已存在，不可重复创建",
          data,
        }
      } else {
        return { code: 1, data };
      }
    } catch (error) {
      return {
        code: -1,
        message: "创建物料失败，错误原因是" + (error.message ?? error),
      };
    }
  }

  @Post("/com_lib/create")
  async createComLibMaterial(
    @Body() body: Module.Material.CreateMaterialParamsForWebsite,
  ) {
    try {
      if (!body.schema) {
        return { code: -1, message: "参数 schema 必传" };
      }

      const {
        packageName,
        componentBundleType,
        componentChineseName,
        author,
        thumbnailUrl,
        version,
        components,
      } = body.schema;
      if (
        !packageName ||
        !componentBundleType ||
        !componentChineseName ||
        !author ||
        !thumbnailUrl ||
        !version ||
        !components?.length
      ) {
        return {
          code: -1,
          message:
            "参数 packageName、componentBundleType、componentChineseName、author、thumbnailUrl、version、components 必传",
        };
      }

      if (componentBundleType !== BUNDLE_TYPES.SL) {
        return {
          code: -1,
          message: "组件库物料 componentBundleType 必须为 SL",
        };
      }

      return { code: 1, data: await this.websiteService.createComLib(body) };
    } catch (error) {
      console.log(error, typeof error);
      return {
        code: -1,
        message: "创建组件库物料失败，错误原因是" + (error.message ?? error),
      };
    }
  }

  @Post("/update")
  async updateMaterial(
    @Body() body: Module.Material.UpdateMaterialParamsForWebsite,
  ) {
    try {
      if (!body.updater || !body.namespace) {
        return { code: -1, message: "参数 updater、namespace 必传" };
      }

      return { code: 1, data: await this.websiteService.update(body) };
    } catch (error) {
      return {
        code: -1,
        message: "更新物料失败，错误原因是" + (error.message ?? error),
      };
    }
  }

  @Get("/detail")
  async getMaterialDetail(
    @Query() query: Module.Website.GetMaterialDetailParams,
  ) {
    if (!query.id && !query.namespace) {
      return { code: -1, message: "参数 id 或 namespace 必传" };
    }

    try {
      return {
        code: 1,
        data: await this.websiteService.getMaterialDetail(query),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询物料详情失败，错误原因是" + error.message,
      };
    }
  }

  @Get("/comlib/component/list")
  async getComponentsFromComLib(
    @Query() query: Module.Website.GetMaterialDetailParams,
  ) {
    if (!query.id && !query.namespace) {
      return { code: -1, message: "参数 id 或 namespace 必传" };
    }

    try {
      return {
        code: 1,
        data: await this.websiteService.getComponentsFromComLib(query),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询组件库物料列表失败，错误原因是" + error.message,
      };
    }
  }

  @Get("/version/list")
  async getMaterialVersions(
    @Query() query: Module.Website.GetMaterialDetailParams,
  ) {
    if (!query.id && !query.namespace) {
      return { code: -1, message: "参数 id 或 namespace 必传" };
    }

    try {
      return {
        code: 1,
        result: 1,
        data: await this.websiteService.getMaterialVersions(query),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询物料版本列表失败，错误原因是" + error.message,
      };
    }
  }

  @Get("/refer/list")
  async getMaterialRefers(
    @Query() query: Module.Website.GetMaterialDetailParams,
  ) {
    if (!query.id && !query.namespace) {
      return { code: -1, message: "参数 id 或 namespace 必传" };
    }

    try {
      return {
        code: 1,
        data: await this.websiteService.getMaterialRefers(query),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询物料引用记录失败，错误原因是" + error.message,
      };
    }
  }

  @Get("/refer/reuse_rate/list")
  async getMaterialReferRateList(
    @Query() query: Module.Website.GetMaterialDetailParams,
  ) {
    if (!query.id && !query.namespace) {
      return { code: -1, message: "参数 id 或 namespace 必传" };
    }

    try {
      return {
        code: 1,
        data: await this.websiteService.getMaterialReferRateList(query),
      };
    } catch (error) {
      return {
        code: -1,
        message: "查询物料复用率记录失败，错误原因是" + error.message,
      };
    }
  }

  @Get("/meta/list")
  async getMeta(@Query("business") business: Business) {
    if (!business) {
      return { code: 1, data: [] };
    }

    try {
      return { code: 1, data: await this.websiteService.getMetas(business) };
    } catch (error) {
      return {
        code: -1,
        message: "查询扩展元信息失败，错误原因是" + error.message,
      };
    }
  }

  @Post("/update/meta/material_id")
  async updateMetaRelationByMaterialId(
    @Body() body: Module.Meta.UpdateMetaRelationByMaterialIdParamsForWebsite,
  ) {
    if (!body.id || !body.value || !body.creator) {
      return { code: -1, message: "参数 id、value、creator 不能为空" };
    }

    try {
      await this.websiteService.updateMetaRelationByMaterialId(body);

      return { code: 1, message: "元信息关系更新成功" };
    } catch (error) {
      return {
        code: -1,
        message: "元信息关系更新失败，错误原因是" + error.message,
      };
    }
  }
}
