import { Inject, Injectable } from "@nestjs/common";
import { isArray, isObject, uniqBy } from "lodash";
import semver from "semver";
import { BUNDLE_TYPES, CODE_TYPE, PLATFORMS, flattenComponentField } from '@global-material-middleoffice/server-v2/shared';
import WebsiteDao from "./website.dao";
import {
  Business,
  EffectStatus,
  MaterialOperateAction,
  MaterialType,
  PAGE_NUM,
  PAGE_SIZE,
  ReportType,
} from "../../common/const";
import { computeOffset } from "../../common/util/paging";
import UserDao from "../user/user.dao";
import MaterialPubDao from "../material-pub/material-pub.dao";
import MaterialDao from "../material/material.dao";
import MaterialTagDao from "../material-tag/material-tag.dao";
import MaterialSceneDao from "../material-scene/material-scene.dao";
import { safeParse, safeStringify } from "../../common/util";
import { AnyType } from "../../common/types";
import MaterialReferDao from "../material-refer/material-refer.dao";
import MaterialTagRelationDao from "../material-tag/material-tag-relation.dao";
import MaterialSceneRelationDao from "../material-scene/material-scene-relation.dao";
import MaterialMetaDao from "../material-meta/material-meta.dao";
import MaterialMetaValueDao from "../material-meta-value/material-meta-value.dao";
import NoticeService from "../notice/notice.service";
import CDNService from "../cdn/cdn.service";
import MaterialService from "../material/material.service";
import MaterialMetaValueRelationDao from "../material-meta-value-relation/material-meta-value-relation.dao";
import ReportDao from "../report/report.dao";

@Injectable()
export default class WebsiteService {
  @Inject(WebsiteDao)
  websiteDao: WebsiteDao;
  @Inject(UserDao)
  userDao: UserDao;
  @Inject(MaterialPubDao)
  materialPubDao: MaterialPubDao;
  @Inject(MaterialDao)
  materialDao: MaterialDao;
  @Inject(MaterialTagDao)
  materialTagDao: MaterialTagDao;
  @Inject(ReportDao)
  reportDao: ReportDao;
  @Inject(MaterialTagRelationDao)
  materialTagRelationDao: MaterialTagRelationDao;
  @Inject(MaterialSceneDao)
  materialSceneDao: MaterialSceneDao;
  @Inject(MaterialSceneRelationDao)
  materialSceneRelationDao: MaterialSceneRelationDao;
  @Inject(MaterialReferDao)
  materialReferDao: MaterialReferDao;
  @Inject(MaterialMetaDao)
  materialMetaDao: MaterialMetaDao;
  @Inject(MaterialMetaValueDao)
  materialMetaValueDao: MaterialMetaValueDao;
  @Inject(MaterialMetaValueRelationDao)
  materialMetaValueRelationDao: MaterialMetaValueRelationDao;
  @Inject(NoticeService)
  noticeService: NoticeService;
  @Inject(CDNService)
  cdnService: CDNService;
  @Inject(MaterialService)
  materialService: MaterialService;

  async query(params: Module.Material.GetMaterialListParams) {
    const formatParams = {
      ...params,
      keyword: params.keyword ? `%${params.keyword}%` : undefined,
      platform: params.platform ? `,?${params.platform},?` : undefined,
      status: "status" in params ? params.status : EffectStatus.EFFECT,
      pageSize: params.ids
        ? params.ids.length
        : Number(params.pageSize || PAGE_SIZE),
      pageNum: params.ids ? 1 : Number(params.pageNum || PAGE_NUM),
      offset: params.ids ? 0 : computeOffset(params.pageNum, params.pageSize),
      meta_value_ids: undefined,
      needSchema: Boolean(Number(params.needSchema)),
      exclude_tianhe_basic_materials: params.exclude_tianhe_basic_materials || false,
    };
    const metaKeys = Object.keys(formatParams.meta ?? {});
    if (formatParams.business && metaKeys.length) {
      const metas = await this.materialMetaDao.query({
        business: params.business,
        offset: 0,
        pageSize: 100,
        status: EffectStatus.EFFECT,
      });
      for (const meta of metas.filter((m) => metaKeys.includes(m.value))) {
        const [metaValue] =
          await this.materialMetaValueDao.queryByMetaIdAndValue({
            meta_id: meta.id,
            value: formatParams.meta[meta.value],
          });
        if (!metaValue) {
          continue;
        }

        if (!formatParams.meta_value_ids) {
          formatParams.meta_value_ids = [];
        }

        formatParams.meta_value_ids.push(metaValue.id);
      }
    }
    const list = await this.websiteDao.query(formatParams);
    const tags = await this.materialTagRelationDao.queryByMaterialIds(
      list.map((i) => i.id),
    );
    list.forEach((material) => {
      material.tags = tags
        .filter((t) => t.material_id === material.id)
        .map((t) => ({ id: t.id, title: t.title }));
      // @ts-ignore
      material.platform = material.platform?.split(",");

      if (Number(params.needSchema)) {
        material.schema = safeParse(material.schema as unknown as string);
      }
    });

    if (Number(params.needExtraMeta)) {
      const allMetas = await this.materialMetaDao.query({
        business: formatParams.business,
        pageSize: 100,
        offset: 0,
        status: EffectStatus.EFFECT,
      });
      const allMetaValues = await this.materialMetaValueDao.queryByMetaIds({
        meta_ids: allMetas.map((m) => m.id),
      });
      const relations =
        await this.materialMetaValueRelationDao.queryByMaterialIds(
          list.map((i) => i.id),
        );

      list.forEach((material) => {
        // @ts-ignore
        material.extra_metas = allMetas.reduce((pre, meta) => {
          const metaValues = allMetaValues.filter((v) => v.meta_id === meta.id);
          const relationMetaValueIds = relations
            .filter((r) => r.material_id === material.id)
            .filter((r) =>
              metaValues.find((item) => item.id === r.meta_value_id),
            );

          return relationMetaValueIds.length
            ? [
                ...pre,
                {
                  id: meta.id,
                  title: meta.title,
                  value: meta.value,
                  items: metaValues
                    .filter((value) =>
                      relationMetaValueIds.find(
                        (r) => r.meta_value_id === value.id,
                      ),
                    )
                    .map((value) => {
                      return {
                        id: value.id,
                        title: value.title,
                        value: value.value,
                      };
                    }),
                },
              ]
            : pre;
        }, []);
      });
    }

    return {
      pageSize: formatParams.pageSize,
      pageNum: formatParams.pageNum,
      dataSource: uniqBy(list, "id"),
      total: (await this.websiteDao.queryListTotal(formatParams))[0]?.total,
    };
  }

  async queryMaterialList(params: Module.Material.GetMaterialListParams) {
    const formatParams = {
      ...params,
      keyword: params.keyword ? `%${params.keyword}%` : undefined,
      platform: params.platform ? `,?${params.platform},?` : undefined,
      status: EffectStatus.EFFECT,
      domain: params.domain ?? 0,
      pageSize: Number(params.pageSize || PAGE_SIZE),
      pageNum: Number(params.pageNum || PAGE_NUM),
      offset: computeOffset(params.pageNum, params.pageSize),
      meta_value_ids: undefined,
      needSchema: Boolean(Number(params.needSchema)),
    };
    const metaKeys = Object.keys(formatParams.meta ?? {});
    if (formatParams.business && metaKeys.length) {
      const metas = await this.materialMetaDao.query({
        business: params.business,
        offset: 0,
        pageSize: 100,
        status: EffectStatus.EFFECT,
      });
      for (const meta of metas.filter((m) => metaKeys.includes(m.value))) {
        const [metaValue] =
          await this.materialMetaValueDao.queryByMetaIdAndValue({
            meta_id: meta.id,
            value: formatParams.meta[meta.value],
          });
        if (!metaValue) {
          continue;
        }

        if (!formatParams.meta_value_ids) {
          formatParams.meta_value_ids = [];
        }

        formatParams.meta_value_ids.push(metaValue.id);
      }
    }
    const list = await this.websiteDao.queryMaterialList(formatParams);
    const tags = await this.materialTagRelationDao.queryByMaterialIds(
      list.map((i) => i.id),
    );
    const creators = await Promise.all(
      list.map((item) =>
        this.websiteDao.queryCreatorInfoByMaterialCreatorId({
          creatorId: item.creator_id,
        }),
      ),
    );
    const materialPubInfos = await Promise.all(
      list.map((item) =>
        this.websiteDao.queryMaterialPubInfoByMaterialIdAndVersion({
          materialId: item.id,
          version: item.version,
        }),
      ),
    );

    list.forEach((material, index) => {
      material.tags = tags
        .filter((t) => t.material_id === material.id)
        .map((t) => ({ id: t.id, title: t.title }));
      // @ts-ignore
      material.platform = material.platform?.split(",");

      if (Number(params.needSchema)) {
        material.schema = safeParse(material.schema as unknown as string);
      }
      material.creator_name = creators[index][0]?.creator_name;
      material.preview_img = materialPubInfos[index][0]?.preview_img;
    });

    if (Number(params.needExtraMeta)) {
      const allMetas = await this.materialMetaDao.query({
        business: formatParams.business,
        pageSize: 100,
        offset: 0,
        status: EffectStatus.EFFECT,
      });
      const allMetaValues = await this.materialMetaValueDao.queryByMetaIds({
        meta_ids: allMetas.map((m) => m.id),
      });
      const relations =
        await this.materialMetaValueRelationDao.queryByMaterialIds(
          list.map((i) => i.id),
        );

      list.forEach((material) => {
        // @ts-ignore
        material.extra_metas = allMetas.reduce((pre, meta) => {
          const metaValues = allMetaValues.filter((v) => v.meta_id === meta.id);
          const relationMetaValueIds = relations
            .filter((r) => r.material_id === material.id)
            .filter((r) =>
              metaValues.find((item) => item.id === r.meta_value_id),
            );

          return relationMetaValueIds.length
            ? [
                ...pre,
                {
                  id: meta.id,
                  title: meta.title,
                  value: meta.value,
                  items: metaValues
                    .filter((value) =>
                      relationMetaValueIds.find(
                        (r) => r.meta_value_id === value.id,
                      ),
                    )
                    .map((value) => {
                      return {
                        id: value.id,
                        title: value.title,
                        value: value.value,
                      };
                    }),
                },
              ]
            : pre;
        }, []);
      });
    }

    return {
      pageSize: Number(params.pageSize || PAGE_SIZE),
      pageNum: Number(params.pageNum || PAGE_NUM),
      dataSource: list,
      total: (await this.websiteDao.queryMaterialListTotal(formatParams))[0]
        ?.total,
    };
  }

  async getMaterialListForES(params: Module.Material.GetMaterialListParams) {
    const formatParams = {
      ...params,
      keyword: params.keyword ? `%${params.keyword}%` : undefined,
      platform: params.platform ? `,?${params.platform},?` : undefined,
      status: EffectStatus.EFFECT,
      pageSize: Number(params.pageSize || PAGE_SIZE),
      pageNum: Number(params.pageNum || PAGE_NUM),
      offset: computeOffset(params.pageNum, params.pageSize),
      meta_value_ids: undefined,
    };
    const metaKeys = Object.keys(formatParams.meta ?? {});
    if (formatParams.business && metaKeys.length) {
      const metas = await this.materialMetaDao.query({
        business: params.business,
        offset: 0,
        pageSize: 100,
        status: EffectStatus.EFFECT,
      });
      for (const meta of metas.filter((m) => metaKeys.includes(m.value))) {
        const [metaValue] =
          await this.materialMetaValueDao.queryByMetaIdAndValue({
            meta_id: meta.id,
            value: formatParams.meta[meta.value],
          });
        if (!metaValue) {
          continue;
        }

        if (!formatParams.meta_value_ids) {
          formatParams.meta_value_ids = [];
        }

        formatParams.meta_value_ids.push(metaValue.id);
      }
    }
    const list = await this.websiteDao.query({
      ...formatParams,
      needSchema: true,
    });
    const tags = await this.materialTagRelationDao.queryByMaterialIds(
      list.map((i) => i.id),
    );
    list.forEach((material) => {
      material.tags = tags
        .filter((t) => t.material_id === material.id)
        .map((t) => ({ id: t.id, title: t.title }));
      // @ts-ignore
      material.platform = material.platform?.split(",");
      const schema = safeParse(
        material.schema as AnyType,
      ) as Module.Schema.Schema;
      (material as AnyType).component_bundle_type = schema.componentBundleType;
      (material as AnyType).package_name = schema.packageName;
      (material as AnyType).library_name = schema.libraryName;
      delete material.schema;
    });

    if (Number(params.needExtraMeta)) {
      const allMetas = await this.materialMetaDao.query({
        business: formatParams.business,
        pageSize: 100,
        offset: 0,
        status: EffectStatus.EFFECT,
      });
      const allMetaValues = await this.materialMetaValueDao.queryByMetaIds({
        meta_ids: allMetas.map((m) => m.id),
      });
      const relations =
        await this.materialMetaValueRelationDao.queryByMaterialIds(
          list.map((i) => i.id),
        );

      list.forEach((material) => {
        // @ts-ignore
        material.extra_metas = allMetas.reduce((pre, meta) => {
          const metaValues = allMetaValues.filter((v) => v.meta_id === meta.id);
          const relationMetaValueIds = relations
            .filter((r) => r.material_id === material.id)
            .filter((r) =>
              metaValues.find((item) => item.id === r.meta_value_id),
            );

          return relationMetaValueIds.length
            ? [
                ...pre,
                {
                  id: meta.id,
                  title: meta.title,
                  value: meta.value,
                  items: metaValues
                    .filter((value) =>
                      relationMetaValueIds.find(
                        (r) => r.meta_value_id === value.id,
                      ),
                    )
                    .map((value) => {
                      return {
                        id: value.id,
                        title: value.title,
                        value: value.value,
                      };
                    }),
                },
              ]
            : pre;
        }, []);
      });
    }

    if (Number(params.needVersions)) {
      const versions = await this.materialPubDao.queryVersionsByMaterialIds({
        materialIds: list.map((i) => i.id),
        needSchema: true,
        branch: params.branch,
      });

      list.forEach((material) => {
        (material as AnyType).versions = uniqBy(
          versions.filter((v) => v.material_id === material.id),
          (v) => v.version,
        ).map((v) => {
          return {
            ...v,
            schema: safeParse(v.schema),
          };
        });
      });
    }

    return {
      pageSize: Number(params.pageSize || PAGE_SIZE),
      pageNum: Number(params.pageNum || PAGE_NUM),
      dataSource: list,
      total: (await this.websiteDao.queryListTotal(formatParams))[0]?.total,
    };
  }

  async getMaterialListByPackageName(params: {
    name: string;
    version?: string;
    needContent?: string;
  }) {
    const list = await this.websiteDao.queryByPkgNameAndVersion({
      keyword: `${params.name}%`,
      name: params.name,
      version: params.version,
      needContent: Number(params.needContent ?? "") === 1,
    });
    const tags = await this.materialTagRelationDao.queryByMaterialIds(
      list.map((i) => i.id),
    );

    list.forEach((material) => {
      material.tags = tags
        .filter((t) => t.material_id === material.id)
        .map((t) => ({ id: t.id, title: t.title }));
      // @ts-ignore
      material.platform = material.platform?.split(",");
      const schema = safeParse(
        material.schema as AnyType,
      ) as Module.Schema.Schema;
      (material as AnyType).component_bundle_type = schema.componentBundleType;
      (material as AnyType).package_name = schema.packageName;
      (material as AnyType).library_name = schema.libraryName;
      (material as AnyType).component_name = schema.componentName;

      (material as AnyType).content =
        Number(params.needContent ?? "") === 1
          ? (material as AnyType).content || "{}"
          : undefined;
    });

    return list;
  }

  async getMaterialListByPackageNameForTianhe(query: {
    name: string;
    version?: string;
    needContent?: string;
  }) {
    const list = await this.websiteDao.queryByPkgNameAndVersion({
      keyword: `${query.name}%`,
      name: query.name,
      version: query.version,
      needContent: Number(query.needContent ?? "") === 1,
    });
    const tags = await this.materialTagRelationDao.queryByMaterialIds(
      list.map((i) => i.id),
    );

    // 证明当前是单包多组件
    const hasPackageNameMaterialIdx = list.findIndex(
      (material) =>
        material.namespace.startsWith(`${query.name}(`) &&
        material.namespace.endsWith("(kael)"),
    );
    // 证明之前是单包单组件
    const hadPackageNameMaterial = (
      await this.materialDao.queryByNamespaces({
        namespaces: [`${query.name}`],
      })
    ).length;
    if (!hasPackageNameMaterialIdx && hadPackageNameMaterial) {
      const defaultMaterial = list.find(
        (material) =>
          material.namespace.startsWith(`${query.name}/default(`) &&
          material.namespace.endsWith("(kael)"),
      );
      if (!defaultMaterial) {
        list.push({
          ...defaultMaterial,
          namespace: `${query.name}(kael)`,
        });
      }
    }

    list.forEach((material) => {
      material.tags = tags
        .filter((t) => t.material_id === material.id)
        .map((t) => ({ id: t.id, title: t.title }));
      // @ts-ignore
      material.platform = material.platform?.split(",");
      const schema = safeParse(
        material.schema as AnyType,
      ) as Module.Schema.Schema;
      (material as AnyType).component_bundle_type = schema.componentBundleType;
      (material as AnyType).package_name = schema.packageName;
      (material as AnyType).library_name = schema.libraryName;
      (material as AnyType).component_name = schema.componentName;

      (material as AnyType).content =
        Number(query.needContent ?? "") === 1
          ? (material as AnyType).content || "{}"
          : undefined;
    });

    return list;
  }

  async create(body: Module.Material.CreateMaterialParamsForWebsite) {
    const { schema, meta, content, hidden } = body;
    const {
      packageName,
      componentName,
      componentBundleType,
      author: creator,
      version,
    } = schema;
    const namespace =
      componentBundleType === BUNDLE_TYPES.SLMC
        ? `${packageName}/${componentName}`
        : packageName;
    const [material] = await this.websiteDao.queryByNamespace(namespace);
    const [user] = await this.userDao.queryByUserName(creator);
    let materialId = material?.id;
    let action: MaterialOperateAction;
    const domain = isArray(schema.tags.domain)
      ? schema.tags.domain.join(",")
      : "0";

    if (!user) {
      throw Error("用户不存在");
    }

    if (material) {
      const [materialVersion] =
        await this.websiteDao.queryByNamespaceAndVersion(namespace, version);

      if (materialVersion) {
        throw new Error("当前物料版本已存在");
        // return {
        //   isRepeat: true,
        //   materialId: material.id,
        //   materialPubId: materialVersion.pub_id,
        // }
      }

      await this.materialDao.update({
        meta: safeStringify(meta),
        version: schema.version,
        description: schema.description,
        git_url: schema.gitUrl,
        id: materialId,
        updater_id: user.id,
        title: schema.componentChineseName ?? material.title,
        business: schema.tags.business ?? material.business,
        domain,
        type: MaterialType.COMPONENT,
        platform: ((Array.isArray(schema.tags.platform)
          ? schema.tags.platform.join(",")
          : schema.tags.platform) || material.platform) as PLATFORMS,
        hidden,
      });
      action = MaterialOperateAction.PUBLISH;
    } else {
      materialId = await this.websiteDao.create([
        {
          namespace,
          version,
          title: schema.componentChineseName,
          description: schema.description,
          creator_id: user.id,
          domain,
          business: schema.tags.business,
          type: MaterialType.COMPONENT,
          git_url: schema.gitUrl,
          platform: ((Array.isArray(schema.tags.platform)
            ? schema.tags.platform.join(",")
            : schema.tags.platform) || PLATFORMS.PC) as PLATFORMS,
          meta: safeStringify(meta),
          hidden,
        },
      ]);

      if (!materialId) {
        throw Error("物料创建失败");
      }
      action = MaterialOperateAction.CREATE;
    }

    const pubId = await this.materialPubDao.create({
      /** 对应物料 id */
      material_id: materialId,
      /** 当前物料版本  */
      version,
      /** 物料预览图 */
      preview_img: schema.thumbnailUrl,
      readme: schema.instructionUrl,
      /** 物料发布内容 */
      content: safeStringify(content),
      /** 物料标准 schema */
      schema: safeStringify(schema),
      /** 创建者 */
      creator_id: user.id,
    });

    await this.handleMaterialMeta(schema, materialId, user.id);
    await this.noticeService.notice(materialId, action);

    return { materialId, materialPubId: pubId };
  }

  async handleMaterialMeta(
    schema: Module.Schema.Schema,
    materialId: number,
    userId: number,
  ) {
    if (schema.tags.category) {
      const tags = await this.materialTagDao.queryByValues({
        values: Array.isArray(schema.tags.category)
          ? schema.tags.category
          : [schema.tags.category],
      });
      await this.materialTagRelationDao.deleteByMaterialId(materialId);

      if (tags.length) {
        for (const tag of tags) {
          await this.materialTagRelationDao.create({
            material_id: materialId,
            tag_id: tag.id,
            creator_id: userId,
          });
        }
      }
    }

    if (schema.tags.meta) {
      if (!isObject(schema.tags.meta)) throw Error("tags.meta类型错误");
      const metaList = await this.materialMetaDao.queryByBusiness(
        schema.tags.business,
      );
      const validMeta = Object.keys(schema.tags.meta)
        .map((value) => {
          const existMeta = metaList.find((meta) => meta.value === value);
          if (existMeta) {
            return { ...existMeta, values: schema.tags.meta[value] };
          }
        })
        .filter(Boolean);
      const metaValues = await this.materialMetaValueDao.queryByMetaIds({
        meta_ids: validMeta.map((it) => it.id),
      });
      const validValues = validMeta
        .map((meta) =>
          metaValues.filter(
            (it) => meta.values.includes(it.value) && it.meta_id === meta.id,
          ),
        )
        .flat();

      await this.materialMetaValueRelationDao.deleteByMaterialId(materialId);
      await Promise.all(
        validValues.map((it) =>
          this.materialMetaValueRelationDao.create({
            meta_value_id: it.id,
            material_id: materialId,
            creator_id: userId,
          }),
        ),
      );
    }
  }

  async createComLib(
    body: Module.Material.CreateComLibMaterialParamsForWebsite,
  ) {
    const { schema, meta, content } = body;
    const { packageName, author: creator, version } = schema;
    const namespace = packageName;
    const [material] = await this.websiteDao.queryByNamespace(namespace);
    const [user] = await this.userDao.queryByUserName(creator);
    let materialId = material?.id;
    let action: MaterialOperateAction;
    const domain = isArray(schema.tags.domain)
      ? schema.tags.domain.join(",")
      : "0";

    if (!user) {
      throw Error("用户不存在");
    }

    const components = flattenComponentField(schema.components);
    const componentMaterials =
      await this.websiteDao.queryByNamespaceAndVersionList(components);
    for (const component of components) {
      if (
        !componentMaterials.find(
          (item) =>
            item.namespace === component.namespace &&
            item.version === component.version,
        )
      ) {
        throw Error(
          `组件库中组件 ${component.namespace} 对应版本 ${component.version} 不存在`,
        );
      }
    }

    if (material) {
      const [materialVersion] =
        await this.websiteDao.queryByNamespaceAndVersion(namespace, version);

      if (materialVersion) {
        throw Error("当前物料库版本已存在");
      }

      await this.materialDao.update({
        meta: safeStringify(meta),
        version: schema.version,
        description: schema.description,
        git_url: schema.gitUrl,
        id: materialId,
        updater_id: user.id,
        title: schema.componentChineseName ?? material.title,
        business: schema?.tags?.business ?? material.business,
        type: MaterialType.COM_LIB,
        domain,
        platform: ((Array.isArray(schema?.tags?.platform)
          ? schema.tags.platform.join(",")
          : schema.tags.platform) || material.platform) as PLATFORMS,
      });
      action = MaterialOperateAction.PUBLISH;
    } else {
      materialId = await this.websiteDao.create([
        {
          namespace,
          version,
          title: schema.componentChineseName,
          description: schema.description,
          creator_id: user.id,
          business: schema?.tags?.business,
          type: MaterialType.COM_LIB,
          git_url: schema.gitUrl,
          domain,
          platform: ((Array.isArray(schema?.tags?.platform)
            ? schema.tags.platform.join(",")
            : schema.tags.platform) || PLATFORMS.PC) as PLATFORMS,
          meta: safeStringify(meta),
        },
      ]);

      if (!materialId) {
        throw Error("物料创建失败");
      }
      action = MaterialOperateAction.CREATE;
    }

    const pubId = await this.materialPubDao.create({
      /** 对应物料 id */
      material_id: materialId,
      /** 当前物料版本  */
      version,
      /** 物料预览图 */
      preview_img: schema.thumbnailUrl,
      readme: schema.instructionUrl,
      /** 物料发布内容 */
      content: safeStringify(content),
      /** 物料标准 schema */
      schema: safeStringify(schema),
      /** 创建者 */
      creator_id: user.id,
    });

    await this.handleMaterialMeta(schema, materialId, user.id);
    await this.noticeService.notice(materialId, action);

    return { materialId, materialPubId: pubId };
  }

  async update(body: Module.Material.UpdateMaterialParamsForWebsite) {
    const { updater, namespace, ...other } = body;

    const [user] = await this.userDao.queryByUserName(updater);
    if (!user) {
      throw Error("用户不存在");
    }

    const [material] = await this.websiteDao.queryByNamespace(namespace);
    if (!material) {
      throw Error("物料不存在");
    }

    await this.materialService.update({
      id: material.id,
      updater_id: user.id,
      ...other,
    });
  }

  async getMaterialDetail(query: Module.Website.GetMaterialDetailParams) {
    const [material] = await this.websiteDao.queryDetail(query);

    if (!material) {
      throw Error("物料不存在");
    }
    const [version] = await this.materialPubDao.query({
      material_id: material.id,
      version: query.version ?? material.version,
    });
    material.tags = (
      await this.materialTagRelationDao.queryByMaterialIds([material.id])
    ).map((t) => ({ id: t.id, title: t.title }));
    material.scenes = (
      await this.materialSceneRelationDao.queryByMaterialIds([material.id])
    ).map((s) => ({ id: s.id, title: s.title }));
    const metaValueRelation = await this.materialMetaValueRelationDao.query({
      material_id: material.id,
      status: EffectStatus.EFFECT,
    });
    const metaValues = await this.materialMetaValueDao.queryByIds({
      ids: metaValueRelation.map((r) => r.meta_value_id),
    });
    const metas = await this.materialMetaDao.queryByIds({
      ids: metaValues.map((r) => r.meta_id),
    });

    if (metas.length) {
      (material as AnyType).extra_metas = metas.map((meta) => {
        return {
          id: meta.id,
          title: meta.title,
          key: meta.value,
          values: metaValues
            .filter((v) => v.meta_id === meta.id)
            .map((v) => {
              return {
                id: v.id,
                title: v.title,
                key: v.value,
              };
            }),
        };
      });
    }

    return {
      ...material,
      material_pub_id: version?.id,
      preview_img: version?.preview_img,
      version: version?.version,
      updater_id: version?.creator_id,
      updater_name: version?.creator_name,
      updater_avatar: version?.creator_avatar,
      updater_username: version?.creator_username,
      update_time: version?.create_time,
      readme: version?.readme,
      content: version?.content,
      schema: version?.schema,
    };
  }

  async getComponentsFromComLib(query: Module.Website.GetMaterialDetailParams) {
    const [material] = await this.websiteDao.queryDetail(query);

    if (!material) {
      throw Error("物料不存在");
    }
    if (material.type !== MaterialType.COM_LIB) {
      throw Error("物料类型为非组件库");
    }
    let componentMaterials: AnyType[] = [];
    const [version] = await this.materialPubDao.query({
      material_id: material.id,
      version: query.version ?? material.version,
    });

    if (!version) {
      throw Error("物料版本不存在");
    }

    const schema = safeParse(version.schema);
    if (schema?.components) {
      componentMaterials = await this.websiteDao.queryByNamespaces({
        namespaces: flattenComponentField(schema.components).map(
          (com) => com.namespace,
        ),
        needPub: true,
        status: EffectStatus.EFFECT,
      });
      const tags = await this.materialTagRelationDao.queryByMaterialIds(
        componentMaterials.map((m) => m.id),
      );

      componentMaterials.forEach((material) => {
        material.tags = tags
          .filter((t) => t.material_id === material.id)
          .map((t) => ({ id: t.id, title: t.title }));
      });

      schema.components = schema.components
        .map((com) => {
          if (com.children) {
            com.children = com.children
              .map((child) => {
                const childComponent = componentMaterials.find(
                  (m) => m.namespace === child.namespace,
                );
                if (childComponent) {
                  return { ...child, ...childComponent };
                }
              })
              .filter(Boolean);
            return com;
          } else {
            const component = componentMaterials.find(
              (m) => m.namespace === com.namespace,
            );
            if (component) {
              return { ...com, ...component };
            }
          }
        })
        .filter(Boolean);
    }

    return schema.components;
  }

  async getMaterialVersions(query: Module.Website.GetMaterialDetailParams) {
    const [material] = await this.websiteDao.queryDetail(query);

    if (!material) {
      throw Error("物料不存在");
    }

    const versions = await this.materialPubDao.query({
      material_id: material.id,
      status: EffectStatus.EFFECT,
      pageNum: query.pageNum,
      pageSize: query.pageSize,
    });

    return versions
      .map((v) => {
        return {
          id: v.id,
          preview_img: v.preview_img,
          version: v.version,
          creator_id: v.creator_id,
          creator_name: v.creator_name,
          creator_avatar: v.creator_avatar,
          creator_username: v.creator_username,
          create_time: v.create_time,
          readme: v.readme,
          status: v.status,
        };
      })
      .sort((a, b) => {
        return (
          semver.compare(b.version, a.version) || b.create_time - a.create_time
        );
      });
  }

  async getMaterialRefers(
    query: Module.Website.GetMaterialDetailParams & Module.Common.Paging,
  ) {
    const [material] = await this.websiteDao.queryDetail(query);

    if (!material) {
      throw Error("物料不存在");
    }

    const referList = await this.materialReferDao.query({
      ...query,
      namespace: material.namespace.replace(/(\([^()]*\))+$/g, ""),
    });
    const [{ total_count }] = await this.materialReferDao.queryTotalCount({
      ...query,
      namespace: material.namespace.replace(/(\([^()]*\))+$/g, ""),
    });
    return referList.map((refer) => ({
      ...refer,
      reference_record_total_count: total_count,
    }));
  }

  async getMaterialReferRateList(
    query: Module.Website.GetMaterialDetailParams,
  ) {
    const [material] = await this.websiteDao.queryDetail(query);

    if (!material) {
      throw Error("物料不存在");
    }

    return await this.reportDao.query({
      business: material.business,
      code_type: CODE_TYPE.LOW_CODE,
      type: ReportType.REUSE_RATE,
      relation_key: material.id,
    });
  }

  async getMetas(business: Business) {
    const metas = await this.materialMetaDao.query({
      business,
      pageSize: 100,
      offset: 0,
      status: EffectStatus.EFFECT,
    });
    const metaValues = await this.materialMetaValueDao.queryByMetaIds({
      meta_ids: metas.map((m) => m.id),
    });

    return metas.map((meta) => {
      const items = metaValues.filter((v) => v.meta_id === meta.id);

      return { ...meta, items };
    });
  }

  async updateMetaRelationByMaterialId(
    params: Module.Meta.UpdateMetaRelationByMaterialIdParamsForWebsite,
  ) {
    const material = await this.materialDao.detail(params.id);
    if (!material) {
      throw new Error("物料不存在");
    }

    const [user] = await this.userDao.queryByUserName(params.creator);
    if (!user) {
      throw Error("用户不存在");
    }

    const allMetas = await this.materialMetaDao.query({
      business: material.business,
      pageSize: 100,
      offset: 0,
      status: EffectStatus.EFFECT,
    });
    const metas = allMetas.filter((m) =>
      Object.keys(params.value).includes(m.value),
    );

    Object.keys(params.value).forEach((key) => {
      const meta = metas.find((m) => m.value === key);

      if (!meta) {
        throw new Error(`元信息 ${key} 不存在`);
      }
      (meta as AnyType).valueKeys = params.value[key];
    });

    const allMetaValueIds: number[] = [];
    const metaValues = await this.materialMetaValueDao.queryByMetaIds({
      meta_ids: metas.map((m) => m.id),
    });
    for (const meta of metas) {
      for (const metaValueKey of (meta as AnyType).valueKeys) {
        const metaValue = metaValues.find(
          (m) => m.meta_id === meta.id && m.value === metaValueKey,
        );

        if (!metaValue) {
          throw new Error(`元信息 ${meta.value} 中 ${metaValueKey} 不存在`);
        }

        allMetaValueIds.push(metaValue.id);
      }
    }
    await this.materialMetaValueRelationDao.deleteByMaterialId(params.id);

    for (const id of allMetaValueIds) {
      await this.materialMetaValueRelationDao.create({
        material_id: params.id,
        meta_value_id: id,
        creator_id: user.id,
      });
    }

    const materialPub = await this.materialPubDao.detailByNamespaceVersion(
      material.namespace,
      material.version,
    );
    if (materialPub) {
      const schema = safeParse(materialPub.schema);
      if (!schema.tags) {
        schema.tags = {
          meta: {},
        };
      }
      for (const key of Object.keys(params.value)) {
        if (!schema.tags.meta) {
          schema.tags.meta = {};
        }
        schema.tags.meta[key] = params.value[key];
      }
      await this.materialPubDao.update({
        id: materialPub.id,
        schema: safeStringify(schema),
      });
    }
  }
}
