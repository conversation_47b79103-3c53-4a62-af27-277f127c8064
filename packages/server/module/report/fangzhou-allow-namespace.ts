import { kConfInstance } from '../../common/util';

export const getFZAllowNamespace = async () => {
	try {
		const config = await kConfInstance.getJSONValue('platecoDev.kwaishopPower.materialMiddleOfficeScanProjectConfig');

		if (typeof config === 'object') {
			return (config as AnyType).FZAllowNamespace ?? [];
		} else {
			throw Error('配置为非对象类型');
		}
	} catch (e) {
		console.log('获取方舟物料 namespace 配置失败，错误原因是' + typeof e === 'string' ? e : e.message);
		return [];
	}
};
