import { getNamespaceFromGitUrl, kConfInstance } from '../../common/util';

export const getEsProject = async () => {
	try {
		const config = await kConfInstance.getJSONValue('platecoDev.kwaishopPower.materialMiddleOfficeScanProjectConfig');

		if (typeof config === 'object') {
			return (config as AnyType).es?.filter(item => Boolean(item.gitUrl)).map(item => {
				return {
					title: item.desc,
					namespace: getNamespaceFromGitUrl(item.gitUrl),
					git_url: item.gitUrl,
				};
			}) ?? [];
		} else {
			throw Error('配置为非对象类型');
		}
	} catch (e) {
		console.log('获取电商前端项目配置失败，错误原因是' + typeof e === 'string' ? e : e.message);
		return [];
	}
};