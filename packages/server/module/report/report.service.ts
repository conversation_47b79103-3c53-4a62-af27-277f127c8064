import { Inject, Injectable } from "@nestjs/common";
import NP from "number-precision";
import dayjs from "dayjs";
import path from "path";
import fs from "fs";
import { uniq, uniqBy } from "lodash";
import { BUNDLE_TYPES, CODE_TYPE } from '@global-material-middleoffice/server-v2/shared';
import ReportDao from "./report.dao";
import MaterialDao from "../material/material.dao";
import {
  cloneProject,
  genMainIndexOfDB,
  getNamespaceFromGitUrl,
  getPreDayRange,
  getPreWeekRange,
  __old_runScanWorker__,
  safeParse,
  safeStringify,
  getOneDayRange,
} from "../../common/util";
import BusinessConfigDao from "../business-config/business-config.dao";
import {
  Business,
  EffectStatus,
  MaterialType,
  PROJECT_PATH,
  ReportType,
  SCAN_LOGS_PATH,
} from "../../common/const";
import MaterialReferDao from "../material-refer/material-refer.dao";
import { getEsProject } from "./es-project";
import { getBizProject } from "./biz-project";
import { getFZAllowNamespace } from "./fangzhou-allow-namespace";
import CDNService from "../cdn/cdn.service";

@Injectable()
export default class ReportService {
  @Inject(ReportDao)
  reportDao: ReportDao;
  @Inject(MaterialReferDao)
  materialReferDao: MaterialReferDao;
  @Inject(MaterialDao)
  materialDao: MaterialDao;
  @Inject(BusinessConfigDao)
  businessConfigDao: BusinessConfigDao;
  @Inject(CDNService)
  cdnService: CDNService;

  async create(body: Module.Report.CreateReportParams) {
    await this.reportDao.create(body);
  }

  async getReports(query: Module.Report.GetReportParams) {
    const reports = await this.reportDao.query({
      ...query,
      relation_key: query.code_type === CODE_TYPE.LOW_CODE ? 0 : undefined,
    });
    const result: Record<string, AnyType[]> = {};

    reports.forEach((item) => {
      if (!result[item.type]) {
        result[item.type] = [item];
      } else {
        result[item.type].push(item);
      }
    });

    return result;
  }

  async getReportOverview(query: Module.Report.GetReportOverviewParams) {
    const isProCode = query.code_type === CODE_TYPE.PRO_CODE;

    if (!query.start_time || !query.end_time) {
      const { start, end } = isProCode ? getPreWeekRange() : getPreDayRange();
      query.start_time = start;
      query.end_time = end;
    }

    const { start, end } = (isProCode ? getPreWeekRange : getPreDayRange)(
      Number(query.start_time),
    );
    const report = await this.reportDao.query({
      ...query,
      relation_key: query.code_type === CODE_TYPE.LOW_CODE ? 0 : undefined,
    });
    const preReport = await this.reportDao.query({
      ...query,
      start_time: start,
      end_time: end,
      relation_key: query.code_type === CODE_TYPE.LOW_CODE ? 0 : undefined,
    });

    report.forEach((item) => {
      const preItem = preReport.find(
        (p) => p.type === item.type && p.code_type === item.code_type,
      );
      (item as AnyType).loop_rate = preItem
        ? NP.round(
            NP.divide(NP.minus(item.value, preItem.value), preItem.value),
            3,
          )
        : 1;
    });

    return report;
  }

  async triggerScanTask(params: {
    business: Business;
    time?: number;
    not_reset?: boolean;
    material_not_reset?: boolean;
  }) {
    const { business, not_reset, time, material_not_reset } = params;
    const businesses = business
      ? [business]
      : [
          Business.LOCAL_LIFE,
          Business.ES,
          Business.LOCAL_LIFE_CLIENT,
          Business.BIZ,
        ];
    const { start, end } = getPreWeekRange(time);
    for (const business of businesses) {
      try {
        const professionFailedProjects = [];
        const materialFailedProjects = [];
        const [businessConfig] = await this.businessConfigDao.query({
          business,
          status: EffectStatus.EFFECT,
        });
        let materials = await this.materialDao.query({
          business,
          status: EffectStatus.EFFECT,
          offset: 0,
          pageSize: 10000,
          type: MaterialType.COMPONENT,
          needSchema: true,
        });
        materials = materials
          .map((m) => ({ ...m, schema: safeParse(m.schema as AnyType) }))
          .filter((m) => m.schema?.packageName);

        const meta = safeParse(businessConfig?.meta);
        const professionProjects = uniqBy(
          (meta?.report?.projects ?? []).concat(
            business === Business.ES
              ? await getEsProject()
              : business === Business.BIZ
                ? await getBizProject()
                : [],
          ),
          (item) => {
            return item.git_url
              ?.trim()
              .replace(/\.git$/, "")
              .replace(
                "*************************:",
                "https://git.corp.kuaishou.com/",
              );
          },
        ) as AnyType[];

        for (const project of professionProjects) {
          if (not_reset) {
            const [report] = await this.reportDao.queryProject({
              business,
              start_time: start,
              type: "profession",
              end_time: end,
              git_url: project.git_url,
            });

            if (report) {
              continue;
            }
          }
          try {
            cloneProject({ path: PROJECT_PATH, gitUrl: project.git_url });
          } catch (e) {
            console.log(
              `拉取项目工程失败，仓库配置：${JSON.stringify(project, null, 2)}`,
            );
            console.log(
              `拉取错误原因是：${typeof e === "string" ? e : e.message}`,
            );
            professionFailedProjects.push({
              ...project,
              error: `拉取错误原因是：${typeof e === "string" ? e : e.message}`,
            });
            continue;
          }

          try {
            const { filteredProject, projectNameList, ...res } =
              await __old_runScanWorker__(
                {
                  project,
                  materials: uniq(materials.map((m) => m.schema?.packageName)),
                  start,
                  end,
                  blacklist: meta?.report?.blacklist ?? [],
                },
                true,
              );

            await this.reportDao.deleteProjectByGit_Time({
              git_url: project.git_url,
              type: "profession",
              start_time: start,
              end_time: end,
            });

            if (projectNameList.length === filteredProject.length) {
              professionFailedProjects.push({
                ...project,
                filteredProject,
                projectNameList,
                error: `${filteredProject.join(", ")} 项目框架为 vue，将被忽略扫描`,
              });
              continue;
            }
            /** 本地生活客户端单独处理，多个 package 判断为多个项目 */
            if (business === Business.LOCAL_LIFE_CLIENT) {
              for (const pkg of res.packages) {
                if (!pkg.pages?.length) {
                  pkg.pages = [
                    {
                      browserPath: "*",
                      files: [
                        {
                          path: "*",
                          importPath: "*",
                          usedLibraryMaterial: pkg.usedLibraryMaterials,
                        } as AnyType,
                      ],
                    },
                  ];
                }

                const gitUrl = project.git_url + `?package=${pkg.name}`;
                await this.reportDao.createProject({
                  projects: [
                    {
                      title: project.title,
                      git_url: gitUrl,
                      content: safeStringify({
                        git_url: gitUrl,
                        code_lines: pkg.codeLines,
                        isDependedTargetLibraries:
                          pkg.isDependedTargetLibraries,
                        usedLibraryMaterials: pkg.usedLibraryMaterials,
                        packages: [pkg],
                        pages: pkg.pages,
                      }),
                      type: "profession",
                      business,
                      start_time: start,
                      end_time: end,
                    },
                  ],
                });
                await this.materialReferDao.deleteByGitAndNamespace({
                  refer_page: `${gitUrl}%`,
                  start_time: start,
                  end_time: end,
                });

                for (const material of materials) {
                  for (const page of pkg.pages) {
                    const usedLibraryMaterial =
                      page.files?.reduce((pre, file) => {
                        Object.keys(file.usedLibraryMaterial).forEach((key) => {
                          if (!pre[key]) {
                            pre[key] = file.usedLibraryMaterial[key];
                          } else {
                            pre[key] = uniq([
                              ...pre[key],
                              ...file.usedLibraryMaterial[key],
                            ]);
                          }
                        });

                        return pre;
                      }, []) ?? {};
                    if (
                      usedLibraryMaterial[material.schema.packageName]
                        ?.length &&
                      (material.schema.componentBundleType === BUNDLE_TYPES.SLSC
                        ? true
                        : usedLibraryMaterial[
                            material.schema.packageName
                          ].includes(material.schema.componentName))
                    ) {
                      const refPage = `${gitUrl}?page=${page.browserPath}`;
                      await this.materialReferDao.create({
                        namespace: material.namespace,
                        refer_count: 1,
                        ref_page: refPage,
                        version: "",
                        ref_business: business,
                        code_type: CODE_TYPE.PRO_CODE,
                        /** liulei11 user id */
                        creator_id: 541859450355781,
                        create_time: end,
                      } as AnyType);
                    }
                  }
                }
              }
            } else {
              /** 遍历所有 pkg 获取所有 page */
              res.pages = res.packages
                .reduce((pre, pkg) => {
                  return [...pre, ...pkg.pages];
                }, [])
                .map((p) => {
                  return {
                    ...p,
                    /** 确保路由唯一，度量 SDK 的 page 数据 path 可能会重复 */
                    browserPath: p.browserPath + `/${genMainIndexOfDB()}`,
                  };
                });

              if (!res.pages.length) {
                res.pages = [
                  {
                    browserPath: "*",
                    files: [
                      {
                        path: "*",
                        importPath: "*",
                        usedLibraryMaterial: res.usedLibraryMaterials,
                      } as AnyType,
                    ],
                  },
                ];
              }

              await this.reportDao.createProject({
                projects: [
                  {
                    title: project.title,
                    git_url: project.git_url,
                    content: safeStringify({
                      git_url: project.git_url,
                      code_lines: res.codeLines,
                      isDependedTargetLibraries: res.isDependedTargetLibraries,
                      usedLibraryMaterials: res.usedLibraryMaterials,
                      packages: res.packages,
                      pages: res.pages,
                    }),
                    type: "profession",
                    business,
                    start_time: start,
                    end_time: end,
                  },
                ],
              });

              await this.materialReferDao.deleteByGitAndNamespace({
                refer_page: `${project.git_url}%`,
                start_time: start,
                end_time: end,
              });
              for (const material of materials) {
                for (const index in res.pages) {
                  const page = res.pages[index];

                  const usedLibraryMaterial =
                    page.files?.reduce((pre, file) => {
                      Object.keys(file.usedLibraryMaterial).forEach((key) => {
                        if (!pre[key]) {
                          pre[key] = file.usedLibraryMaterial[key];
                        } else {
                          pre[key] = uniq([
                            ...pre[key],
                            ...file.usedLibraryMaterial[key],
                          ]);
                        }
                      });

                      return pre;
                    }, []) ?? {};
                  if (
                    usedLibraryMaterial[material.schema.packageName]?.length &&
                    (material.schema.componentBundleType === BUNDLE_TYPES.SLSC
                      ? true
                      : usedLibraryMaterial[
                          material.schema.packageName
                        ].includes(material.schema.componentName))
                  ) {
                    await this.materialReferDao.create({
                      namespace: material.namespace,
                      refer_count: 1,
                      ref_page: `${project.git_url}?page=${page.browserPath}`,
                      version: "",
                      ref_business: business,
                      code_type: CODE_TYPE.PRO_CODE,
                      /** liulei11 user id */
                      creator_id: 541859450355781,
                      create_time: end,
                    } as AnyType);
                  }
                }
              }
            }
          } catch (e) {
            console.log(
              `扫描项目工程失败，仓库配置：${JSON.stringify(project, null, 2)}`,
            );
            console.log("扫描错误原因是：", e);
            professionFailedProjects.push({
              ...project,
              error: `扫描错误原因是：${typeof e === "string" ? e : e.message}`,
            });
          }
        }

        const materialProjects = uniqBy(
          (meta?.material?.projects ?? []).concat(
            materials
              .map((m) => m.schema.gitUrl)
              .filter(Boolean)
              .map((url) => {
                const namespace = getNamespaceFromGitUrl(url);
                return {
                  title: namespace,
                  namespace,
                  git_url: url,
                  router_entry: undefined,
                  component_folder_map: {},
                };
              }),
          ),
          (item) => {
            return item.git_url
              ?.trim()
              .replace(/\.git$/, "")
              .replace(
                "*************************:",
                "https://git.corp.kuaishou.com/",
              );
          },
        ) as AnyType[];
        for (const project of materialProjects) {
          if (material_not_reset) {
            const [report] = await this.reportDao.queryProject({
              business,
              start_time: start,
              type: "material",
              end_time: end,
              git_url: project.git_url,
            });

            if (report) {
              continue;
            }
          }

          try {
            cloneProject({ path: PROJECT_PATH, gitUrl: project.git_url });
          } catch (e) {
            console.log(
              `拉取物料工程失败，仓库配置：${JSON.stringify(project, null, 2)}`,
            );
            console.log("拉取项目错误原因是：", e);
            materialFailedProjects.push({
              ...project,
              error: `拉取项目错误原因是：${typeof e === "string" ? e : e.message}`,
            });
            continue;
          }
          const folderMap = project.component_folder_map || {};

          try {
            const { filteredProject, projectNameList, ...res } =
              await __old_runScanWorker__(
                {
                  project,
                  start,
                  end,
                },
                false,
              );

            if (projectNameList.length === filteredProject.length) {
              materialFailedProjects.push({
                ...project,
                filteredProject,
                projectNameList,
                error: "物料项目框架为 vue，将被忽略扫描",
              });
              continue;
            }

            await this.reportDao.deleteProjectByGit_Time({
              git_url: project.git_url,
              type: "material",
              start_time: start,
              end_time: end,
            });
            await this.reportDao.createProject({
              projects: [
                {
                  title: project.title,
                  git_url: project.git_url,
                  content: safeStringify({
                    git_url: project.git_url,
                    packages: res.packages,
                    namespaceMaterials:
                      res.packages?.reduce((pre, pkg) => {
                        return [
                          ...pre,
                          ...[
                            {
                              name: "default",
                              path: "",
                              codeLines: pkg.codeLines,
                              namespace: pkg.name,
                            },
                            ...(pkg.materials ?? []).map((item) => {
                              const comName =
                                item.name === "default"
                                  ? item.variableName
                                  : item.name;
                              return {
                                ...item,
                                namespace:
                                  folderMap[comName] ||
                                  `${pkg.name}/${comName}`,
                              };
                            }),
                          ],
                        ];
                      }, []) ?? [],
                  }),
                  type: "material",
                  business,
                  start_time: start,
                  end_time: end,
                },
              ],
            });
          } catch (e) {
            console.log(
              `扫描物料工程失败，仓库配置：${JSON.stringify(project, null, 2)}`,
            );
            console.log(
              `扫描错误原因是：${typeof e === "string" ? e : e.message}`,
            );
            materialFailedProjects.push({
              ...project,
              error: `扫描错误原因是：${typeof e === "string" ? e : e.message}`,
            });
          }
        }

        /** 上一周的项目信息 */
        const allScanProject = (
          await this.reportDao.queryProject({
            business,
            type: "profession",
            start_time: start,
            end_time: end,
          })
        ).map((project) => ({
          ...project,
          content: safeParse(project.content),
        }));
        /** 上一周的物料项目信息 */
        const allMaterialProject = (
          await this.reportDao.queryProject({
            business,
            type: "material",
            start_time: start,
            end_time: end,
          })
        ).map((project) => ({
          ...project,
          content: safeParse(project.content),
        }));
        /** 上一周的引用物料的页面 */
        const allReferPage = await this.materialReferDao.queryByBusiness_Time({
          business,
          start_time: start,
          end_time: end,
        });

        let allProjectCoverageRate = 0;
        let allMaterialLine = 0;
        let allProjectLine = 0;
        let allReferPageNum = 0;
        let allPageNum = 0;
        let allNewReferPageNum = 0;
        let allNewPageNum = 0;

        for (const scanProject of allScanProject) {
          if (!scanProject.content?.pages) {
            console.log(`统计项目工程失败，仓库地址：${scanProject.git_url}`);
            console.log("统计错误原因是：数据 parse 错误，页面为空");
            professionFailedProjects.push({
              ...scanProject,
              content: "",
              error: "统计错误原因是：数据 parse 错误，页面为空",
            });
            continue;
          }
          const allReferPageForProject = allReferPage.filter((page) =>
            page.ref_page.startsWith(`${scanProject.git_url}?page=`),
          );
          /** 项目下所有的引用页面 */
          const referPages = [
            ...new Set(allReferPageForProject.map((p) => p.ref_page)),
          ];
          /** 上周相比上上周新增的页面 */
          const newPages = scanProject.content.pages.filter(
            (page) => page.createTime >= start && page.createTime <= end,
          );
          /** 新增页面中存在物料引用的页面 */
          const newReferPages = newPages.filter((page) =>
            referPages.find((p) => p?.endsWith(`?page=${page.browserPath}`)),
          );
          const materials = await this.materialDao.queryByNamespaces({
            namespaces: allReferPageForProject.map((p) => p.namespace),
          });
          /** 使用到物料的源代码行数 */
          const materialLine = materials.reduce((pre, material) => {
            let curLine = 0;
            for (let curMaterialProject of allMaterialProject) {
              const namespaceMaterial =
                curMaterialProject.content.namespaceMaterials?.find(
                  (m) => m.namespace === material.namespace,
                );

              if (namespaceMaterial) {
                curLine = namespaceMaterial.codeLines;
                break;
              }
            }

            return pre + curLine;
          }, 0);

          const pageCoverageRate = NP.round(
            NP.divide(
              referPages.length,
              scanProject.content.pages?.length || referPages.length || 1,
            ),
            3,
          );
          const projectCoverageRate = Number(!!referPages.length);
          const newPageCoverageRate = NP.round(
            NP.divide(newReferPages.length, newPages.length || 1),
            3,
          );
          const polarStar = NP.round(
            NP.divide(
              materialLine,
              materialLine + (scanProject.content.code_lines || 0),
            ),
            3,
          );
          await this.reportDao.updateProject({
            id: scanProject.id,
            content: safeStringify({
              ...scanProject.content,
              code_type: CODE_TYPE.PRO_CODE,
              report: {
                [ReportType.PAGE_COVERAGE_RATE]: pageCoverageRate,
                [ReportType.PROJECT_COVERAGE_RATE]: projectCoverageRate,
                [ReportType.NEW_PAGE_COVERAGE_RATE]: newPageCoverageRate,
                [ReportType.POLAR_STAR]: polarStar,
              },
            }),
          });

          allMaterialLine = NP.plus(allMaterialLine, materialLine);
          allProjectLine = NP.plus(
            allProjectLine,
            materialLine + (scanProject.content.code_lines || 0),
          );
          allReferPageNum = NP.plus(allReferPageNum, referPages.length);
          allPageNum = NP.plus(
            allPageNum,
            scanProject.content.pages?.length || referPages.length || 1,
          );
          allNewReferPageNum = NP.plus(
            allNewReferPageNum,
            newReferPages.length,
          );
          allNewPageNum = NP.plus(allNewPageNum, newPages.length || 0);
          allProjectCoverageRate = NP.plus(
            projectCoverageRate,
            allProjectCoverageRate,
          );
        }

        /** 删除已有数据 */
        await this.reportDao.deleteReportByType_Business_Time({
          business,
          code_type: CODE_TYPE.PRO_CODE,
          start_time: start,
          end_time: end,
        });

        await this.reportDao.create({
          reports: [
            {
              business,
              type: ReportType.PAGE_COVERAGE_RATE,
              code_type: CODE_TYPE.PRO_CODE,
              value: NP.round(NP.divide(allReferPageNum, allPageNum || 1), 3),
              start_time: start,
              end_time: end,
            },
            {
              business,
              type: ReportType.PROJECT_COVERAGE_RATE,
              code_type: CODE_TYPE.PRO_CODE,
              value: NP.round(
                NP.divide(allProjectCoverageRate, allScanProject.length || 1),
                3,
              ),
              start_time: start,
              end_time: end,
            },
            {
              business,
              type: ReportType.NEW_PAGE_COVERAGE_RATE,
              code_type: CODE_TYPE.PRO_CODE,
              value: NP.round(
                NP.divide(allNewReferPageNum, allNewPageNum || 1),
                3,
              ),
              start_time: start,
              end_time: end,
            },
            {
              business,
              type: ReportType.POLAR_STAR,
              code_type: CODE_TYPE.PRO_CODE,
              value: NP.round(
                NP.divide(allMaterialLine, allProjectLine || 1),
                3,
              ),
              start_time: start,
              end_time: end,
            },
          ],
        });

        try {
          await this.cdnService.uploadToCDN({
            filename: `scan-failed-${dayjs().format("YYYY-MM-DD_HH:mm:ss")}.json`,
            str: JSON.stringify(
              { materialFailedProjects, professionFailedProjects },
              null,
              2,
            ),
            path: `/scan-logs/${business}`,
          });
        } catch (e) {
          console.log(
            `日志文件上传失败，错误原因是${typeof e === "string" ? e : e.message}`,
          );
        }

        try {
          const folderPath = path.resolve(SCAN_LOGS_PATH, `./${business}`);
          if (!fs.existsSync(folderPath)) {
            fs.mkdirSync(folderPath);
          }
          fs.writeFileSync(
            path.resolve(
              folderPath,
              `./scan-failed-${dayjs().format("YYYY-MM-DD_HH:mm:ss")}.json`,
            ),
            JSON.stringify(
              { materialFailedProjects, professionFailedProjects },
              null,
              2,
            ),
          );
        } catch (e) {
          console.log(
            `日志文件写入失败，错误原因是${typeof e === "string" ? e : e.message}`,
          );
        }
      } catch (e) {
        console.log(
          `BU：${business} 扫描任务错误，错误原因是：${typeof e === "string" ? e : e.message}`,
        );
        console.log(e);
      }
    }

    const reportDataList = await Promise.all(
      [
        Business.LOCAL_LIFE,
        Business.LOCAL_LIFE_CLIENT,
        Business.ES,
        Business.BIZ,
      ].map((business) => {
        return this.reportDao.query({
          business,
          code_type: CODE_TYPE.PRO_CODE,
          start_time: start,
          end_time: end,
        });
      }),
    );
    const types = [
      ReportType.PAGE_COVERAGE_RATE,
      ReportType.PROJECT_COVERAGE_RATE,
      ReportType.NEW_PAGE_COVERAGE_RATE,
      ReportType.POLAR_STAR,
    ];
    const willInsertReportList = [];
    for (const type of types) {
      const allValues = reportDataList.reduce((pre, report) => {
        return NP.plus(pre, report.find((r) => r.type === type)?.value ?? 0);
      }, 0);

      willInsertReportList.push({
        business: "",
        type,
        code_type: CODE_TYPE.PRO_CODE,
        value: NP.round(NP.divide(allValues, reportDataList.length || 1), 3),
        start_time: start,
        end_time: end,
      });
    }

    /** 删除已有概览数据 */
    await this.reportDao.deleteReportByType_Business_Time({
      business: "" as Business,
      code_type: CODE_TYPE.PRO_CODE,
      start_time: start,
      end_time: end,
    });
    await this.reportDao.create({ reports: willInsertReportList });
  }

  async triggerLowCodeCalcTask(date?: string) {
    const businesses = [Business.FANG_ZHOU, Business.KAEL];
    const { start, end } = date ? getOneDayRange(date) : getPreDayRange();
    let allBusinessValue = 0;

    for (const business of businesses) {
      let materials = await this.materialDao.query({
        business,
        status: EffectStatus.EFFECT,
        offset: 0,
        pageSize: 10000,
        type: MaterialType.COMPONENT,
        needSchema: true,
      });

      if (business === Business.FANG_ZHOU) {
        const FZAllowNamespace = await getFZAllowNamespace();
        materials = FZAllowNamespace?.length
          ? materials.filter((m) => FZAllowNamespace.includes(m.namespace))
          : materials;
      }

      const total =
        (await this.materialReferDao.queryTotalByBusiness({ business }))[0]
          ?.total ?? 0;
      const totalReferCount =
        (await this.materialReferDao.queryReferCountByBusiness({ business }))[0]
          ?.total ?? 0;
      let allMaterialReferCount = 0;
      for (const material of materials) {
        const materialReferCount =
          (
            await this.materialReferDao.queryReferCountByNamespace({
              namespace: material.namespace,
            })
          )[0]?.total ?? 0;
        const materialReferTotal =
          (
            await this.materialReferDao.queryTotalByNamespace({
              namespace: material.namespace,
            })
          )[0]?.total ?? 0;

        await this.reportDao.deleteReportByType_RelationKey_Business_Time({
          business,
          type: ReportType.REUSE_RATE,
          code_type: CODE_TYPE.LOW_CODE,
          relation_key: material.id,
          start_time: start,
          end_time: end,
        });

        const value = NP.round(NP.divide(materialReferTotal, total || 1), 3);
        allMaterialReferCount = NP.plus(
          allMaterialReferCount,
          materialReferCount,
        );
        await this.reportDao.create({
          reports: [
            {
              business,
              type: ReportType.REUSE_RATE,
              code_type: CODE_TYPE.LOW_CODE,
              value,
              relation_key: material.id,
              start_time: start,
              end_time: end,
            },
          ],
        });
      }

      const value = NP.round(
        NP.divide(allMaterialReferCount, totalReferCount || 1),
        3,
      );
      await this.reportDao.deleteReportByType_RelationKey_Business_Time({
        business,
        type: ReportType.POLAR_STAR,
        code_type: CODE_TYPE.LOW_CODE,
        relation_key: 0,
        start_time: start,
        end_time: end,
      });
      await this.reportDao.create({
        reports: [
          {
            business,
            type: ReportType.POLAR_STAR,
            code_type: CODE_TYPE.LOW_CODE,
            relation_key: 0,
            value,
            start_time: start,
            end_time: end,
          },
        ],
      });
      allBusinessValue = NP.plus(allBusinessValue, value);
    }

    await this.reportDao.deleteReportByType_RelationKey_Business_Time({
      business: "" as Business,
      type: ReportType.POLAR_STAR,
      code_type: CODE_TYPE.LOW_CODE,
      relation_key: 0,
      start_time: start,
      end_time: end,
    });
    await this.reportDao.create({
      reports: [
        {
          business: "" as Business,
          relation_key: 0,
          type: ReportType.POLAR_STAR,
          code_type: CODE_TYPE.LOW_CODE,
          // value: NP.round(NP.divide(allBusinessValue, businesses.length || 1), 3),
          /** TODO: 暂时不需要除 2，因为千象数据一定为空 */
          value: NP.round(NP.divide(allBusinessValue, 1), 3),
          start_time: start,
          end_time: end,
        },
      ],
    });
  }
}
