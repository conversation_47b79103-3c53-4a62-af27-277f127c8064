import { Module } from '@nestjs/common';
import ReportController from './report.controller';
import ReportService from './report.service';
import ReportDao from './report.dao';
import MaterialDao from '../material/material.dao';
import BusinessConfigDao from '../business-config/business-config.dao';
import MaterialReferDao from '../material-refer/material-refer.dao';
import CDNService from '../cdn/cdn.service';

@Module({
	imports: [],
	controllers: [ReportController],
	providers: [ReportService, ReportDao, MaterialDao, BusinessConfigDao, MaterialReferDao, CDNService],
	exports: [ReportService, ReportDao],
})
export class ReportModule {}
