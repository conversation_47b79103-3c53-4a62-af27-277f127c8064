import { getNamespaceFromGitUrl, kConfInstance } from '../../common/util';

export const getBizProject = async () => {
	try {
		const config = await kConfInstance.getJSONValue('platecoDev.kwaishopPower.materialMiddleOfficeScanProjectConfig');
		
		if (typeof config === 'object') {
			return (config as AnyType).biz?.filter(item => Boolean(item.gitSSHUrl)).map(item => {
				return {
					title: item.name,
					namespace: getNamespaceFromGitUrl(item.gitSSHUrl),
					git_url: item.gitSSHUrl,
				};
			}) ?? [];
		} else {
			throw Error('配置为非对象类型');
		}
	} catch (e) {
		console.log('获取商业化项目配置失败，错误原因是' + typeof e === 'string' ? e : e.message);
		return [];
	}
};
