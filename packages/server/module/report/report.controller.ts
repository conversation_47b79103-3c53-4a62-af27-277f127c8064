import { Body, Controller, Get, Inject, Post, Query } from '@nestjs/common';
import { isNil } from 'lodash';
import ReportService from './report.service';
import { PromiseAllFulfilled } from '../../common/util/Promise';

// curl --location -X POST 'http://localhost:21894/api/manage/report/task/low_code/trigger'
@Controller('api/manage/report')
export default class ReportController {
  @Inject(ReportService)
  reportService: ReportService;

  @Post('/create')
  async createMaterialReport(@Body() body: Module.Report.CreateReportParams) {
  	try {
  		if (!body.reports?.length) {
  			for (const report of body.reports) {
  				if (
  					!report.business ||
            !report.type ||
            !report.value ||
            !report.code_type ||
            !report.start_time ||
            !report.end_time
  				) {
  					return {
  						code: -1,
  						message:
                '参数 reports 中每一项的属性 business、type、value、code_type、start_time、end_time 必传',
  					};
  				}
  			}
  		}

  		return { code: 1, data: await this.reportService.create(body) };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '创建报表记录失败，错误原因是' + (error.message ?? error),
  		};
  	}
  }

  @Get('/list')
  async getMaterialReports(@Query() query: Module.Report.GetReportParams) {
  	if (isNil(query.business) || !query.code_type) {
  		return { code: -1, message: '参数 business、code_type 必传' };
  	}

  	try {
  		return { code: 1, data: await this.reportService.getReports(query) };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询度量数据失败，错误原因是' + error.message,
  		};
  	}
  }

  @Get('/overview')
  async getMaterialReportOverview(
    @Query() query: Module.Report.GetReportOverviewParams,
  ) {
  	if (isNil(query.business)) {
  		return { code: -1, message: '参数 business 必传' };
  	}

  	try {
  		return {
  			code: 1,
  			data: await this.reportService.getReportOverview(query),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询度量数据失败，错误原因是' + error.message,
  		};
  	}
  }

  @Post('/task/trigger')
  async triggerScanTask(
    @Body()
    body: {
      business;
      not_reset?: boolean;
      material_not_reset?: boolean;
      time?: number;
    },
  ) {
  	try {
  		this.reportService.triggerScanTask(body);
  		return { code: 1, message: '扫描任务触发成功' };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '扫描任务触发失败，错误原因是' + (error.message ?? error),
  		};
  	}
  }

  @Post('/task/low_code/trigger')
  async triggerLowCodeCalcTask(@Body('dates') dates?: string | string[]) {
  	try {
  		if (dates) {
  			const targets = Array.isArray(dates) ? dates : [dates];
  			return {
  				code: 1,
  				data: await PromiseAllFulfilled(
  					targets.map((date) =>
  						this.reportService.triggerLowCodeCalcTask(date),
  					),
  				),
  			};
  		} else {
  			return {
  				code: 1,
  				data: await this.reportService.triggerLowCodeCalcTask(),
  			};
  		}
  	} catch (error) {
  		return {
  			code: -1,
  			message:
          '低代码指标计算任务触发失败，错误原因是' + (error.message ?? error),
  		};
  	}
  }
}
