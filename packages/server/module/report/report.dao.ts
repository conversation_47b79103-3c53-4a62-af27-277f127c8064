import { DOBase } from "@mybricks/rocker-dao";
import { genMainIndexOfDB } from "../../common/util";
import { EffectStatus } from "../../common/const";
import { CODE_TYPE } from "@global-material-middleoffice/server-v2/shared";

export default class Report<PERSON>ao extends DOBase {
  public async create(body: Module.Report.CreateReportParams) {
    const result = await this.exe<{ insertId: number }>(
      "business_report:create",
      {
        reports: body.reports.map((item) => {
          return {
            ...item,
            id: genMainIndexOfDB(),
            update_time: Date.now(),
            relation_key: item.relation_key ?? 0,
            status: EffectStatus.EFFECT,
          };
        }),
      }
    );
    return result?.insertId;
  }

  public async query(params: Partial<Module.Report.GetReportOverviewParams>) {
    return await this.exe<Module.Report.Report[]>(
      "business_report:query",
      params
    );
  }

  async deleteReportByType_Business_Time(params: {
    type?: ReportType;
    code_type: CODE_TYPE;
    business: Business;
    start_time: number;
    end_time: number;
  }) {
    await this.exe("business_report:deleteReportByType_Business_Time", params);
  }

  async deleteReportByType_RelationKey_Business_Time(params: {
    type: ReportType;
    code_type: CODE_TYPE;
    business: Business;
    relation_key: number;
    start_time: number;
    end_time: number;
  }) {
    await this.exe(
      "business_report:deleteReportByType_RelationKey_Business_Time",
      params
    );
  }

  public async createProject(params: Module.Report.CreateProjectParams) {
    const result = await this.exe<{ insertId: number }>(
      "business_project:create",
      {
        reports: params.projects.map((item) => {
          return {
            ...item,
            id: genMainIndexOfDB(),
            update_time: Date.now(),
            status: EffectStatus.EFFECT,
          };
        }),
      }
    );
    return result?.insertId;
  }

  async deleteProjectByGit_Time(params: {
    git_url: string;
    start_time: number;
    end_time: number;
    type: "material" | "profession";
  }) {
    await this.exe("business_project:deleteProjectByGit_Time", params);
  }

  public async queryProject(params: Module.Report.GetProjectParams) {
    return await this.exe<Module.Report.Project[]>(
      "business_project:query",
      params
    );
  }

  public async updateProject(params: Module.Report.UpdateProjectParams) {
    return await this.exe("business_project:update", {
      ...params,
      update_time: Date.now(),
    });
  }

  public async createLowCodeReport(
    params: Module.Report.CreateLowCodeBuildReportItem
  ) {
    return await this.exe("lowcode_build_report:create", {
      reports: [
        {
          ...params,
          id: genMainIndexOfDB(),
          update_time: Date.now(),
          status: EffectStatus.EFFECT,
        },
      ],
    });
  }
}
