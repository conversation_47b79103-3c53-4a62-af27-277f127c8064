import { Inject, Injectable } from '@nestjs/common';
import { isNil, isObject } from 'lodash';
import MaterialDao from './material.dao';
import MaterialTagDao from '../material-tag/material-tag.dao';
import {
	EffectStatus,
	MaterialOperateAction,
	MaterialType,
	PAGE_NUM,
	PAGE_SIZE,
} from '../../common/const';
import { BUNDLE_TYPES, flattenComponentField, PLATFORMS } from '@global-material-middleoffice/server-v2/shared';
import { computeOffset } from '../../common/util/paging';
import MaterialPubDao from '../material-pub/material-pub.dao';
import MaterialTagRelationDao from '../material-tag/material-tag-relation.dao';
import MaterialSceneRelationDao from '../material-scene/material-scene-relation.dao';
import {
	getNextVersion,
	safeParse,
	safeStringify,
} from '../../common/util';
import NoticeService from '../notice/notice.service';
import MaterialMetaDao from '../material-meta/material-meta.dao';
import MaterialMetaValueDao from '../material-meta-value/material-meta-value.dao';
import MaterialMetaValueRelationDao from '../material-meta-value-relation/material-meta-value-relation.dao';

@Injectable()
export default class MaterialService {
  @Inject(MaterialDao)
  materialDao: MaterialDao;
  @Inject(MaterialTagDao)
  materialTagDao: MaterialTagDao;
  @Inject(MaterialTagRelationDao)
  materialTagRelationDao: MaterialTagRelationDao;
  @Inject(MaterialSceneRelationDao)
  materialSceneRelationDao: MaterialSceneRelationDao;
  @Inject(MaterialPubDao)
  materialPubDao: MaterialPubDao;
  @Inject(NoticeService)
  noticeService: NoticeService;
  @Inject(MaterialMetaDao)
  materialMetaDao: MaterialMetaDao;
  @Inject(MaterialMetaValueDao)
  materialMetaValueDao: MaterialMetaValueDao;
  materialMetaValueRelationDao: MaterialMetaValueRelationDao;

  constructor() {
  	this.materialMetaValueRelationDao = new MaterialMetaValueRelationDao();
  }

  async create(params: Module.Material.CreateMaterialParams) {
  	const { schema, meta, content, creator_id } = params;
  	const { packageName, componentName, componentBundleType, version } = schema;
  	const namespace =
      componentBundleType === BUNDLE_TYPES.SLMC
      	? `${packageName}/${componentName}`
      	: packageName;
  	const [material] = await this.materialDao.queryByNamespaces({
  		namespaces: [namespace],
  	});
  	let materialId = material?.id;
  	let action: MaterialOperateAction;

  	if (material) {
  		const [materialVersion] =
        await this.materialDao.queryByNamespaceAndVersion(namespace, version);

  		if (materialVersion) {
  			throw Error('当前物料版本已存在');
  		}

  		await this.materialDao.update({
  			meta: safeStringify(meta),
  			version: schema.version,
  			description: schema.description,
  			git_url: schema.gitUrl,
  			id: materialId,
  			updater_id: creator_id,
  			title: schema.componentChineseName ?? material.title,
  			business: schema.tags.business ?? material.business,
  			type: MaterialType.COMPONENT,
  			platform: ((Array.isArray(schema.tags.platform)
  				? schema.tags.platform.join(',')
  				: schema.tags.platform) || material.platform) as PLATFORMS,
  		});
  		action = MaterialOperateAction.PUBLISH;
  	} else {
  		materialId = await this.materialDao.create({
  			namespace,
  			version,
  			title: schema.componentChineseName,
  			description: schema.description,
  			creator_id,
  			business: schema.tags.business,
  			type: MaterialType.COMPONENT,
  			git_url: schema.gitUrl,
  			platform: ((Array.isArray(schema.tags.platform)
  				? schema.tags.platform.join(',')
  				: schema.tags.platform) || PLATFORMS.PC) as PLATFORMS,
  			meta: safeStringify(meta),
  		});

  		if (!materialId) {
  			throw Error('物料创建失败');
  		}
  		action = MaterialOperateAction.CREATE;
  	}

  	const pubId = await this.materialPubDao.create({
  		/** 对应物料 id */
  		material_id: materialId,
  		/** 当前物料版本  */
  		version,
  		/** 物料预览图 */
  		preview_img: schema.thumbnailUrl,
  		readme: schema.instructionUrl,
  		/** 物料发布内容 */
  		content: safeStringify(content),
  		/** 物料标准 schema */
  		schema: safeStringify(schema),
  		/** 创建者 */
  		creator_id,
  	});

  	if (schema.tags.category) {
  		const tags = await this.materialTagDao.queryByValues({
  			values: Array.isArray(schema.tags.category)
  				? schema.tags.category
  				: [schema.tags.category],
  		});
  		await this.materialTagRelationDao.deleteByMaterialId(materialId);

  		if (tags.length) {
  			for (const tag of tags) {
  				await this.materialTagRelationDao.create({
  					material_id: materialId,
  					tag_id: tag.id,
  					creator_id,
  				});
  			}
  		}
  	}

  	if (schema.tags.meta) {
  		if (!isObject(schema.tags.meta)) throw Error('tags.meta类型错误');
  		const metaList = await this.materialMetaDao.queryByBusiness(
  			schema.tags.business,
  		);
  		const validMeta = Object.keys(schema.tags.meta)
  			.map((value) => {
  				const existMeta = metaList.find((meta) => meta.value === value);
  				if (existMeta) {
  					return { ...existMeta, values: schema.tags.meta[value] };
  				}
  			})
  			.filter(Boolean);
  		const metaValues = await this.materialMetaValueDao.queryByMetaIds({
  			meta_ids: validMeta.map((it) => it.id),
  		});
  		const validValues = validMeta
  			.map((meta) => {
  				return metaValues.filter(
  					(it) => meta.values.includes(it.value) && it.meta_id === meta.id,
  				);
  			})
  			.flat();
  		if (material) {
  			await this.materialMetaValueRelationDao.deleteByMaterialId(materialId);
  		}
  		await Promise.all(
  			validValues.map((it) =>
  				this.materialMetaValueRelationDao.create({
  					meta_value_id: it.id,
  					material_id: materialId,
  					creator_id,
  				}),
  			),
  		);
  	}

  	await this.noticeService.notice(materialId, action);

  	return { materialId, materialPubId: pubId };
  }

  async delete(id: number) {
  	await this.materialDao.delete(id);
  	await this.noticeService.notice(id, MaterialOperateAction.DELETE);
  }

  async update(params: Module.Material.UpdateMaterialParams) {
  	const material = await this.materialDao.detail(params.id);

  	if (material) {
  		await this.materialDao.update({
  			...params,
  			platform: (Array.isArray(params.platform)
  				? params.platform.join(',')
  				: params.platform) as PLATFORMS,
  		});
  		const [version] = await this.materialPubDao.query({
  			material_id: params.id,
  			version: params.version ?? material.version,
  		});

  		if (version) {
  			const schema = safeParse(version.schema);
  			const updatePub: Record<string, unknown> = {};

  			if (!isNil(params.title)) {
  				schema.componentChineseName = params.title;
  			}

  			if (!isNil(params.description)) {
  				schema.description = params.description;
  			}

  			if (!isNil(params.git_url)) {
  				schema.gitUrl = params.git_url;
  			}

  			if (!isNil(params.readme)) {
  				schema.instructionUrl = params.readme;
  				updatePub.readme = params.readme;
  			}

  			if (!isNil(params.preview_img)) {
  				schema.thumbnailUrl = params.preview_img;
  				updatePub.preview_img = params.preview_img;
  			}
  			if (!isNil(params?.schema)) {
  				if (!isNil(params.schema?.props)) {
  					schema.props = params.schema.props;
  				}

  				if (!isNil(params.schema?.components)) {
  					schema.components = params.schema.components;
  				}
  			}

  			if (!isNil(params.platform)) {
  				if (!schema.tags) {
  					schema.tags = {};
  				}
  				schema.tags.platform = params.platform;
  			}

        console.log(schema.props)

  			await this.materialPubDao.update({
  				...updatePub,
  				id: version.id,
  				schema: safeStringify(schema),
  			});
  		} else {
  			throw Error('当前物料对应版本不存在');
  		}

  		await this.noticeService.notice(params.id, MaterialOperateAction.UPDATE);
  	} else {
  		throw new Error('物料不存在');
  	}
  }

  async enable(params: { id: number; updater_id: number }) {
  	await this.materialDao.update({ ...params, status: EffectStatus.EFFECT });
  }

  async detail(id: number) {
  	return this.materialDao.detail(id);
  }

  async virtualMaterialLibDetail(query: { id?: number; version: string }) {
  	const { id, version } = query;
  	const material = await this.materialDao.detail(id);

  	if (!material) {
  		throw Error('物料不存在');
  	}
  	if (material.type !== MaterialType.COM_LIB) {
  		throw Error('物料类型不是组件库');
  	}
  	const [latestVersion] = await this.materialPubDao.query({
  		material_id: material.id,
  		version,
  	});
  	if (latestVersion) {
  		const schema = safeParse(latestVersion.schema, {});

  		if (schema?.components) {
  			let namespaceAndVersions = [];
  			schema.components.forEach((item) => {
  				if (item.title && item.children?.length > 0) {
  					item.children.forEach((ichild) => {
  						namespaceAndVersions.push({
  							namespace: ichild.namespace,
  							version: ichild.version,
  						});
  					});
  				} else if (item.namespace) {
  					namespaceAndVersions.push({
  						namespace: item.namespace,
  						version: item.version,
  					});
  				}
  			});
  			try {
  				if (namespaceAndVersions.length) {
  					const componentMaterials =
              await this.materialDao.queryByNamespaceAndVersionList(
              	namespaceAndVersions,
              );
  					schema.components = schema.components.map((item) => {
  						if (item.children) {
  							item.children = item.children.map((ichild) => {
  								let childComponent = componentMaterials.find(
  									(iMaterial) => iMaterial.namespace === ichild.namespace,
  								);
  								return {
  									...ichild,
  									title: childComponent.title,
  									id: childComponent.id,
  									...childComponent,
  								};
  							});
  							return item;
  						} else if (item.namespace) {
  							let component = componentMaterials.find(
  								(iMaterial) => iMaterial.namespace === item.namespace,
  							);
  							return { ...item, id: component.id, title: component.title };
  						}
  					});
  				}
  			} catch (error) {}
  			(material as AnyType).components = schema?.components || [];
  		}
  	}
  	return material;
  }

  async publishVirtualLibMaterial(
  	params: Module.Material.UpdateVirtualComponentParams,
  ) {
  	const { components, creator_id, material_id } = params;
  	const material = await this.materialDao.detail(material_id);
  	if (!material) {
  		throw Error('物料不存在');
  	}
  	if (material.type !== MaterialType.COM_LIB) {
  		throw Error('物料类型不是组件库');
  	}
  	const newComponents = flattenComponentField(components);

  	if (!components?.length) {
  		throw Error('组件库中组件不能为空');
  	}
  	const componentMaterials =
      await this.materialDao.queryByNamespaceAndVersionList(newComponents);
  	for (const component of newComponents) {
  		if (
  			!componentMaterials.find(
  				(item) =>
  					item.namespace === component.namespace &&
            item.version === component.version,
  			)
  		) {
  			throw Error(
  				`组件库中组件 ${component.namespace} 对应版本 ${component.version} 不存在`,
  			);
  		}
  	}
  	const [version] = await this.materialPubDao.query({
  		material_id,
  		version: material.version,
  	});
  	const nextVersion = getNextVersion(version.version);
  	const schema = safeParse(version.schema);
  	schema.components = components;
  	schema.version = nextVersion;
  	if (material) {
  		await this.materialDao.update({
  			id: material_id,
  			updater_id: creator_id,
  			version: nextVersion,
  		});
  	}

  	const pubId = await this.materialPubDao.create({
  		/** 对应物料 id */
  		material_id: material_id,
  		/** 当前物料版本  */
  		version: nextVersion,
  		/** 物料预览图 */
  		preview_img: schema.thumbnailUrl,
  		readme: schema.instructionUrl,
  		/** 物料发布内容TODO:暂时发布内容写死 */
  		content: safeStringify({
  			label: `升级虚拟库${material_id} -- ${version}`,
  		}),
  		/** 物料标准 schema */
  		schema: safeStringify(schema),
  		/** 创建者 */
  		creator_id: creator_id,
  	});

  	await this.noticeService.notice(material_id, MaterialOperateAction.PUBLISH);
  	return { materialId: material_id, materialPubId: pubId };
  }

  async query(params: Module.Material.GetMaterialListParams) {
  	const formatParams = {
  		...params,
  		keyword: params.keyword ? `%${params.keyword}%` : undefined,
  		platform: params.platform ? `,?${params.platform},?` : undefined,
  		pageNum: Number(params.pageNum || PAGE_NUM),
  		status: params.status || EffectStatus.EFFECT,
  		pageSize: Number(params.pageSize || PAGE_SIZE),
  		offset: computeOffset(params.pageNum, params.pageSize),
  		needSchema: false,
  	};
  	const list = await this.materialDao.query(formatParams);
  	const tags = await this.materialTagRelationDao.queryByMaterialIds(
  		list.map((i) => i.id),
  	);
  	list.forEach((material) => {
  		material.tags = tags
  			.filter((t) => t.material_id === material.id)
  			.map((t) => ({ id: t.id, title: t.title }));
  		// @ts-ignore
  		material.platform = material.platform?.split(',');
  	});

  	return {
  		pageSize: Number(params.pageSize || PAGE_SIZE),
  		pageNum: Number(params.pageNum || PAGE_NUM),
  		dataSource: list,
  		total: (await this.materialDao.queryListTotal(formatParams))[0]?.total,
  	};
  }

  async exist(id: number) {
  	return !!(await this.detail(id));
  }

  async getMaterialVersions(id: number, branch: 'all' | 'main' = 'all') {
  	const material = await this.materialDao.detail(id);

  	if (!material) {
  		throw Error('物料不存在');
  	}

  	const versions = await this.materialPubDao.query({
  		material_id: material.id,
  		status: EffectStatus.EFFECT,
  		branch,
  	});

  	return versions.map((v) => {
  		return {
  			id: v.id,
  			preview_img: v.preview_img,
  			version: v.version,
  			creator_id: v.creator_id,
  			creator_name: v.creator_name,
  			creator_avatar: v.creator_avatar,
  			creator_username: v.creator_username,
  			create_time: v.create_time,
  			readme: v.readme,
  			status: v.status,
  		};
  	});
  }

  async updateVersionStatus(params: { id: number; status: EffectStatus }) {
  	const curVersion = await this.materialPubDao.detail(params.id);

  	if (curVersion) {
  		await this.materialPubDao.update(params);
  		const material = await this.materialDao.detail(curVersion.material_id);

  		/** 废弃当前版本，自动更新为下一个版本 */
  		if (
  			material?.version === curVersion.version &&
        (params.status === EffectStatus.DISABLED ||
          params.status === EffectStatus.DELETE)
  		) {
  			const [latestVersion] =
          await this.materialPubDao.queryLatestVersionByMaterialId(
          	curVersion.material_id,
          );

  			if (latestVersion) {
  				await this.materialDao.update({
  					id: material.id,
  					version: latestVersion.version,
  					updater_id: material?.updater_id ?? latestVersion.creator_id,
  				});
  			}
  		}
  	}
  }

  async updateTags(params: Module.Material.UpdateMaterialTagRelationParams) {
  	const material = await this.materialDao.detail(params.id);

  	if (!material) {
  		throw Error('物料不存在');
  	}

  	await this.materialTagRelationDao.deleteByMaterialId(material.id);

  	await Promise.all(
  		params.tags.map((tag) => {
  			return this.materialTagRelationDao.create({
  				material_id: params.id,
  				creator_id: params.creator_id,
  				tag_id: tag,
  			});
  		}),
  	);

  	const tags = await this.materialTagDao.query({
  		pageSize: 1000,
  		offset: 0,
  		status: EffectStatus.EFFECT,
  	});
  	const filterTags = tags.filter((t) => params.tags.includes(t.id));
  	const [version] = await this.materialPubDao.query({
  		material_id: params.id,
  		version: material.version,
  	});

  	if (version) {
  		const schema = safeParse(version.schema);
  		if (!schema.tags) {
  			schema.tags = {};
  		}
  		schema.tags.category = filterTags.map((t) => t.value);

  		await this.materialPubDao.update({
  			id: version.id,
  			schema: safeStringify(schema),
  		});
  	}
  }

  async updateScenes(
  	params: Module.Material.UpdateMaterialSceneRelationParams,
  ) {
  	const material = await this.materialDao.detail(params.id);

  	if (!material) {
  		throw Error('物料不存在');
  	}

  	await this.materialSceneRelationDao.deleteByMaterialId(material.id);

  	await Promise.all(
  		params.scenes.map((scene) => {
  			return this.materialSceneRelationDao.create({
  				material_id: params.id,
  				creator_id: params.creator_id,
  				scene_id: scene,
  			});
  		}),
  	);
  }

  async openAutoConvert(params: {
    id: number;
    updater_id: number;
    business: Business[];
  }) {
  	const material = await this.materialDao.detail(params.id);

  	if (!material) {
  		throw Error('物料不存在');
  	}

  	const meta = safeParse(material.meta);
  	await this.materialDao.update({
  		id: params.id,
  		updater_id: params.updater_id,
  		meta: safeStringify({ ...meta, autoConvert: params.business }),
  	});
  }

  async closeAutoConvert(params: { id: number; updater_id: number }) {
  	const material = await this.materialDao.detail(params.id);

  	if (!material) {
  		throw Error('物料不存在');
  	}

  	const meta = safeParse(material.meta);
  	delete meta.autoConvert;

  	await this.materialDao.update({
  		id: params.id,
  		updater_id: params.updater_id,
  		meta: safeStringify(meta),
  	});
  }

  async queryMaterialCount(params: {
    business?: Business;
    type?: 'component' | 'com_lib';
  }) {
  	return this.materialDao.queryMaterialCount(params);
  }

  async queryMaterialGrowth() {
  	const countGroupByDate = await this.materialDao.queryCountGroupByDate();
  	return countGroupByDate;
  }
}
