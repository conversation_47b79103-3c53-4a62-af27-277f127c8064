import { Module } from '@nestjs/common';
import MaterialController from './material.controller';
import MaterialService from './material.service';
import MaterialDao from './material.dao';
import MaterialTagDao from '../material-tag/material-tag.dao';
import MaterialTagRelationDao from '../material-tag/material-tag-relation.dao';
import MaterialSceneRelationDao from '../material-scene/material-scene-relation.dao';
import NoticeService from '../notice/notice.service';
import MaterialPubDao from '../material-pub/material-pub.dao';
import BusinessConfigDao from '../business-config/business-config.dao';
import UserDao from '../user/user.dao';
import MaterialMetaDao from '../material-meta/material-meta.dao';
import MaterialMetaValueDao from '../material-meta-value/material-meta-value.dao';
import MaterialMetaValueRelationDao from '../material-meta-value-relation/material-meta-value-relation.dao';
import MaterialPubService from '../material-pub/material-pub.service';

@Module({
	imports: [],
	controllers: [MaterialController],
	providers: [
	MaterialService,
	MaterialDao,
	MaterialTagDao,
	MaterialTagRelationDao,
	MaterialSceneRelationDao,
	NoticeService,
	MaterialPubDao,
	BusinessConfigDao,
	UserDao,
	MaterialMetaDao,
	MaterialMetaValueDao,
	MaterialMetaValueRelationDao,
	MaterialPubService
	],
	exports: [MaterialService, MaterialDao],
	})
export class MaterialModule {}
