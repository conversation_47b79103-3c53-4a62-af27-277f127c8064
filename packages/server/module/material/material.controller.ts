import { Body, Controller, Get, Inject, Post, Query } from '@nestjs/common';
import { isNil } from 'lodash';
import MaterialService from './material.service';
import MaterialPubService from '../material-pub/material-pub.service';
import { Business, EffectStatus } from '../../common/const';

@Controller('api/manage/material')
export default class MaterialController {
  @Inject(MaterialService)
  materialService: MaterialService;
  @Inject(MaterialPubService)
  materialPubService: MaterialPubService;

  @Get('/list')
  async getMaterialList(
    @Query() params: Module.Material.GetMaterialListParams,
  ) {
  	try {
  		return { code: 1, data: await this.materialService.query(params) };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询物料列表失败，错误原因是' + error.message,
  		};
  	}
  }

  @Post('/create')
  async createMaterial(@Body() body: Module.Material.CreateMaterialParams) {
  	try {
  		if (!body.schema || !body.creator_id) {
  			return { code: -1, message: '参数 schema、creator_id 必传' };
  		}

  		const {
  			packageName,
  			componentName,
  			componentBundleType,
  			libraryName,
  			componentChineseName,
  			author,
  			assetDownloadType,
  			assetDownloadUrl,
  			thumbnailUrl,
  			tags,
  			version,
  		} = body.schema;

  		if (
  			!packageName ||
        !componentName ||
        !componentBundleType ||
        !libraryName ||
        !componentChineseName ||
        !author ||
        !assetDownloadType ||
        !assetDownloadUrl ||
        !thumbnailUrl ||
        !tags ||
        !version
  		) {
  			return {
  				code: -1,
  				message:
            '参数 packageName、componentName、componentBundleType、libraryName、componentChineseName、author、assetDownloadType、assetDownloadUrl、thumbnailUrl、tags、version 必传',
  			};
  		}

  		return { code: 1, data: await this.materialService.create(body) };
  	} catch (error) {
  		return { code: -1, message: '物料创建失败，错误原因是' + error.message };
  	}
  }

  @Post('/delete')
  async delete(@Body('id') id: number) {
  	await this.materialService.delete(id);

  	return { code: 1, message: '物料删除成功' };
  }

  @Post('/update')
  async update(@Body() body: Module.Material.UpdateMaterialParams) {
  	if (!body.id || !body.updater_id) {
  		return { code: -1, message: '参数 id、updater_id 不能为空' };
  	}

  	try {
  		await this.materialService.update(body);

  		return { code: 1, message: '物料编辑成功' };
  	} catch (error) {
  		return { code: -1, message: '物料编辑失败，错误原因是' + error.message };
  	}
  }

  @Post('/enable')
  async enable(@Body() body: { id: number; updater_id: number }) {
  	if (!body.id || !body.updater_id) {
  		return { code: -1, message: '参数 id、updater_id 不能为空' };
  	}

  	try {
  		await this.materialService.enable(body);

  		return { code: 1, message: '物料启用成功' };
  	} catch (error) {
  		return { code: -1, message: '物料启用失败，错误原因是' + error.message };
  	}
  }

  @Get('/detail')
  async getMaterialDetail(@Query('id') id: number) {
  	return { code: 1, data: await this.materialService.detail(id) };
  }

  @Get('/virtual_version_detail')
  async queryVirtualMaterialDetailUsingGET(
    @Query('id') id: number,
    @Query('version') version: string,
  ) {
  	if (!id) {
  		return { code: -1, message: '参数 id 必传' };
  	}
  	try {
  		return {
  			code: 1,
  			data: await this.materialService.virtualMaterialLibDetail({
  				id,
  				version,
  			}),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询物料详情失败，错误原因是' + error.message,
  		};
  	}
  }

  @Post('/publish_virtual_lib_components')
  async publishVirtualLibComponents(
    @Body() body: Module.Material.UpdateVirtualComponentParams,
  ) {
  	if (!body.material_id || !body.creator_id || !body.components.length) {
  		return {
  			code: -1,
  			message: '参数 material_id、creator_id、components 不能为空',
  		};
  	}

  	try {
  		await this.materialService.publishVirtualLibMaterial(body);

  		return { code: 1, message: '虚拟组件库发布成功' };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '发布虚拟组件库失败，错误原因是' + error.message,
  		};
  	}
  }

  @Get('/version/list')
  async getMaterialVersions(@Query('id') id: number) {
  	if (!id) {
  		return { code: -1, message: '参数 id 必传' };
  	}

  	try {
  		return {
  			code: 1,
  			data: await this.materialService.getMaterialVersions(id),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询物料版本列表失败，错误原因是' + error.message,
  		};
  	}
  }

  @Post('/version/update')
  async updateVersionStatus(
    @Body() body: { id: number; status: EffectStatus },
  ) {
  	if (!body.id || isNil(body.status)) {
  		return { code: -1, message: '参数 id、status 不能为空' };
  	}

  	try {
  		await this.materialService.updateVersionStatus(body);

  		return { code: 1, message: '物料版本状态编辑成功' };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '物料版本状态编辑失败，错误原因是' + error.message,
  		};
  	}
  }

  @Post('/updateTags')
  async updateTags(
    @Body() body: Module.Material.UpdateMaterialTagRelationParams,
  ) {
  	if (!body.id || isNil(body.tags) || !body.creator_id) {
  		return { code: -1, message: '参数 id、tags、creator_id 不能为空' };
  	}

  	try {
  		await this.materialService.updateTags(body);

  		return { code: 1, message: '物料标签编辑成功' };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '物料标签编辑失败，错误原因是' + error.message,
  		};
  	}
  }

  @Post('/updateScenes')
  async updateScenes(
    @Body() body: Module.Material.UpdateMaterialSceneRelationParams,
  ) {
  	if (!body.id || !body.scenes?.length || !body.creator_id) {
  		return { code: -1, message: '参数 id、tags、creator_id 不能为空' };
  	}

  	try {
  		await this.materialService.updateScenes(body);

  		return { code: 1, message: '物料场景编辑成功' };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '物料场景编辑失败，错误原因是' + error.message,
  		};
  	}
  }

  @Post('/convert/auto/open')
  async openAutoConvert(
    @Body() body: { id: number; updater_id: number; business: Business[] },
  ) {
  	if (!body.id || !body.updater_id || !body.business?.length) {
  		return { code: -1, message: '参数 id、updater_id、business 不能为空' };
  	}

  	try {
  		await this.materialService.openAutoConvert(body);

  		return { code: 1, message: '物料转换配置编辑成功' };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '物料转换配置编辑失败，错误原因是' + error.message,
  		};
  	}
  }

  @Post('/convert/auto/close')
  async closeAutoConvert(@Body() body: { id: number; updater_id: number }) {
  	if (!body.id || !body.updater_id) {
  		return { code: -1, message: '参数 id、updater_id 不能为空' };
  	}

  	try {
  		await this.materialService.closeAutoConvert(body);

  		return { code: 1, message: '物料转换配置编辑成功' };
  	} catch (error) {
  		return {
  			code: -1,
  			message: '物料转换配置编辑失败，错误原因是' + error.message,
  		};
  	}
  }

  @Get('/count')
  async queryMaterialCount(
    @Query() queries: { business?: Business; type?: 'component' | 'com_lib' },
  ) {
  	try {
  		return {
  			code: 1,
  			data: await this.materialService.queryMaterialCount(queries),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询物料数量失败，错误原因是' + error.message,
  		};
  	}
  }

  @Get('/growth')
  async queryCountGroupByDate() {
  	try {
  		return {
  			code: 1,
  			data: await this.materialService.queryMaterialGrowth(),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '查询物料数量失败，错误原因是' + error.message,
  		};
  	}
  }
}
