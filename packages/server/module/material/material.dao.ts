import { DOBase } from '@mybricks/rocker-dao';
import { genMainIndexOfDB } from '../../common/util';
import { EffectStatus } from '../../common/const';

export default class MaterialDao extends DOBase {
	public async create(params: Partial<Module.Material.Material>) {
		const result = await this.exe<{ insertId: number }>('material:create', {
			...params,
			id: genMainIndexOfDB(),
			create_time: Date.now(),
			update_time: Date.now(),
			updater_id: params.creator_id,
			status: EffectStatus.EFFECT,
			description: params.description ?? '',
			git_url: params.git_url ?? '',
      domain: params.domain ?? '0',
			meta: params.meta ?? '',
		});

		return result.insertId;
	}

	async queryUserInfoByMaterialId(material_id: number) {
		return (
			await this.exe<{
        user_id: number;
        user_name: string;
        user_username: string;
        user_department: string;
      }>('material:queryUserInfoByMaterialId', { material_id })
		)?.[0];
	}

	public async delete(id: number) {
		return await this.exe('material:delete', { id });
	}

	public async update(params: Module.Material.UpdateMaterialParams) {
		return await this.exe('material:update', {
			...params,
			update_time: Date.now(),
      status: params.hidden ? EffectStatus.DISABLED : params.status,
		});
	}

	public async detail(id: number) {
		return (
			await this.exe<Module.Material.Material[]>('material:detail', { id })
		)?.[0];
	}

	public async query(
		params: Omit<Module.Material.GetMaterialListParams, 'needSchema'> & {
      offset: number;
      needSchema?: boolean;
    },
	) {
		return await this.exe<Module.Material.QueryMaterial[]>(
			'material:query',
			params,
		);
	}

	public async queryByConvertTag(params: { convert_tag: string }) {
		return await this.exe<Module.Material.QueryMaterial[]>(
			'material:queryByConvertTag',
			params,
		);
	}

	public async queryListTotal(
		params: Omit<
      Module.Material.GetMaterialListParams,
      'pageNum' | 'pageSize' | 'needSchema'
    >,
	) {
		return await this.exe('material:queryListTotal', params);
	}

	public async queryByNamespaces(params: { namespaces: string[] }) {
		if (!params.namespaces.length) {
			return [];
		}

		return await this.exe<Module.Material.QueryMaterial[]>(
			'material:queryByNamespaces',
			params,
		);
	}

	public async queryByIds(params: { ids: number[]; status: EffectStatus }) {
		if (!params.ids.length) {
			return [];
		}

		return await this.exe<Module.Material.QueryMaterial[]>(
			'material:queryByIds',
			params,
		);
	}

	public async queryByNamespaceAndVersion(namespace: string, version: string) {
		return await this.exe<Module.Material.Material[]>(
			'material:queryByNamespaceAndVersion',
			{ namespace, version },
		);
	}

	public async queryByNamespaceAndVersionList(
		components: Array<{ namespace: string; version: string }>,
	) {
		if (!components.length) {
			return [];
		}
		return await this.exe<Module.Material.Material[]>(
			'material:queryByNamespaceAndVersionList',
			{ components },
		);
	}

	public async queryMaterialCount(params: {
    business?: Business;
    type?: 'component' | 'com_lib';
  }) {
		return await this.exe('material:queryMaterialCount', params);
	}

	public async queryCountGroupByDate() {
		return await this.exe<{ create_date: number; count: number }[]>(
			'material:queryCountGroupByDate',
		);
	}
}
