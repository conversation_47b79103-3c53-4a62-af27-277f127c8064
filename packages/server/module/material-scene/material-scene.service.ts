import { Inject, Injectable } from '@nestjs/common';
import MaterialSceneDao from './material-scene.dao';
import { EffectStatus, PAGE_NUM, PAGE_SIZE } from '../../common/const';
import { computeOffset } from '../../common/util/paging';

@Injectable()
export default class MaterialSceneService {
  @Inject(MaterialSceneDao)
  	materialSceneDao: MaterialSceneDao;

  async create(params: Module.Material.CreateSceneParams) {
  	return this.materialSceneDao.create(params);
  }

  async delete(id: number) {
  	return this.materialSceneDao.delete(id);
  }

  async update(params: Module.Material.UpdateSceneParams) {
  	if (await this.exist(params.id)) {
  		return this.materialSceneDao.update(params);
  	} else {
  		throw new Error('场景不存在');
  	}
  }

  async detail(id: number) {
  	return this.materialSceneDao.detail(id);
  }

  async query(params: Module.Material.GetSceneListParams) {
  	const mergedParams = {
  		...params,
		  title: params.title ? `%${params.title}%` : undefined,
		  pageSize: Number(params.pageSize || PAGE_SIZE),
		  status: params.status ?? EffectStatus.EFFECT,
		  pageNum: Number(params.pageNum || PAGE_NUM),
  	};
  	const [dataSource, total] = await Promise.all([
  		this.materialSceneDao.query({
  			...mergedParams,
  			offset: computeOffset(mergedParams.pageNum, mergedParams.pageSize),
  		}),
  		this.materialSceneDao.queryListTotal(mergedParams),
  	]);

  	return {
  		pageSize: mergedParams.pageSize,
  		pageNum: mergedParams.pageNum,
  		dataSource,
  		total,
  	};
  }

  async exist(id: number) {
  	return !!(await this.detail(id));
  }
}
