import { Body, Controller, Get, Inject, Post, Query } from '@nestjs/common';
import MaterialSceneService from './material-scene.service';

@Controller('api/manage/scene')
export default class MaterialSceneController {
  @Inject(MaterialSceneService)
  	materialSceneService: MaterialSceneService;

  @Post('/create')
  async create(@Body() body: Module.Material.CreateSceneParams) {
	  if (!body.title || !body.creator_id) {
		  return { code: -1, message: '参数 title、creator_id 不能为空' };
	  }

	  try {
		  return { code: 1, data: await this.materialSceneService.create(body), message: '场景新建成功' };
	  } catch (error) {
		  return { code: -1, message: '场景新建失败，错误原因是' + error.message };
	  }
  }

  @Post('/delete')
  async delete(@Body('id') id: number) {
  	await this.materialSceneService.delete(id);

  	return { code: 1, message: '场景删除成功' };
  }

  @Post('/update')
  async updated(@Body() body: Module.Material.UpdateSceneParams) {
	  if (!body.id) {
		  return { code: -1, message: '参数 id 不能为空' };
	  }

  	try {
  		await this.materialSceneService.update(body);

  		return { code: 1, message: '场景更新成功' };
  	} catch (error) {
  		return { code: -1, message: '场景更新失败，错误原因是' + error.message };
  	}
  }

  @Get('/list')
  async getList(@Query() params: Module.Material.GetSceneListParams) {
  	try {
  		return { code: 1, data: await this.materialSceneService.query(params) };
  	} catch (error) {
  		return { code: -1, message: '查询场景列表失败' };
  	}
  }
}
