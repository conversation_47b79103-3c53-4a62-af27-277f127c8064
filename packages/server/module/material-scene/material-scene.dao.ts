import { DOBase } from '@mybricks/rocker-dao';
import { EffectStatus } from '../../common/const';
import { genMainIndexOfDB } from '../../common/util';

export default class MaterialSceneDao extends DOBase {
	public async create(params: Module.Material.CreateSceneParams) {
		const result = await this.exe<{ insertId: number }>('material_scene:create', {
			...params,
			id: genMainIndexOfDB(),
			order: params.order ?? 0,
			create_time: Date.now(),
			status: EffectStatus.EFFECT
		});

		return result.insertId;
	}

	public async delete(id: number) {
		return await this.exe('material_scene:delete', { id });
	}

	public async update(params: Module.Material.UpdateSceneParams) {
		return await this.exe('material_scene:update', params);
	}

	public async detail(id: number) {
		return (await this.exe<Module.Material.Scene[]>('material_scene:detail', { id }))?.[0];
	}

	public async query(params: Module.Material.GetSceneListParams & { offset: number }) {
		return await this.exe<Module.Material.Scene[]>('material_scene:query', params);
	}

	public async queryListTotal(
		params: Omit<Module.Material.GetSceneListParams, 'pageNum' | 'pageSize'>,
	) {
		const result = await this.exe<Array<{ total: number }>>('material_scene:queryListTotal', params);
		return result[0]?.total;
	}
}
