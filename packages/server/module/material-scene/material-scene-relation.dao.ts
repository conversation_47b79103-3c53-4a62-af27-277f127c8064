import { DOBase } from '@mybricks/rocker-dao';
import { genMainIndexOfDB } from '../../common/util';
import { EffectStatus } from '../../common/const';

export default class MaterialSceneRelationDao extends DOBase {
	public async create(params: Module.Material.CreateSceneRelationParams) {
		const result = await this.exe<{ insertId: number }>('material_scene_relation:create', {
			...params,
			id: genMainIndexOfDB(),
			create_time: Date.now(),
			status: EffectStatus.EFFECT
		});

		return result.insertId;
	}

	public async deleteByMaterialId(id: number) {
		return await this.exe('material_scene_relation:deleteByMaterialId', { id });
	}

	public async queryByMaterialIds(materialIds: number[]) {
		if(!materialIds.length) {
			return [];
		}

		return await this.exe<Module.Material.QueryMaterialScene[]>('material_scene_relation:queryByMaterialIds', { materialIds });
	}
}
