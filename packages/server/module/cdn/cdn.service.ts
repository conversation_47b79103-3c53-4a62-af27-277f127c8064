import { Injectable } from "@nestjs/common";
import FormData from "form-data";
import axios from "axios";
import URL from "url";
import { CDN_CONFIG } from "../../common/const";

let cdnConfig: Record<string, unknown> = CDN_CONFIG;

@Injectable()
export default class CDNService {
  async uploadToCDN({ str, filename, path }: Module.CDN.UploadParams) {
    let KCDN_ORIGIN = cdnConfig.origin;
    let KCDN_TOKEN = cdnConfig.token;
    let KCDN_PID = cdnConfig.pid;

    if (!path) {
      throw Error("文件路径不能为空");
    }

    const form = new FormData();
    form.append("pid", KCDN_PID);
    form.append("dir", `fangzhou/material-middleoffice${path}`);
    form.append("allowRewrite", "true");
    form.append("allowHash", "false");
    form.append("files[]", Buffer.isBuffer(str) ? str : new Buffer(str), {
      filename: encodeUR<PERSON>(filename),
    });
    const url = `${KCDN_ORIGIN}/api/kcdn/v1/service/npmUpload/multiple?token=${KCDN_TOKEN}`;

    return await axios
      .post(url, form, {
        headers: form.getHeaders(),
        timeout: 60 * 1000,
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      })
      .then((e) => {
        try {
          if (
            e.status === 200 &&
            e.data &&
            e.data.result === 1 &&
            e.data.data.success
          ) {
            let rawUrl = e.data.data.fileResults?.[0]?.cdnUrl;

            return `https://f2.eckwai.com${URL.parse(rawUrl).path}`;
          } else {
            console.log(JSON.stringify(e.data.data, null, 2));
            return Promise.reject("上传失败");
          }
        } catch (error) {
          return Promise.reject("上传失败，错误原因是" + error.message);
        }
      });
  }
}
