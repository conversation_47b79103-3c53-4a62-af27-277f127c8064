import {
  Body,
  Controller,
  Inject,
  Post,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common";
import CDNService from "./cdn.service";
import { FileInterceptor } from "@nestjs/platform-express";

@Controller("api/cdn")
export default class CDNController {
  @Inject(CDNService)
  cdnService: CDNService;

  @Post("/upload")
  async uploadToCDN(
    @Body("content") content: string,
    @Body("filename") filename: string,
    @Body("path") path: string,
  ) {
    if (!content || !filename || !path) {
      return { code: -1, message: "参数 content、filename、path 不能为空" };
    }

    try {
      const url = await this.cdnService.uploadToCDN({
        str: content,
        filename,
        path,
      });
      return { code: 1, data: { url }, message: "上传成功" };
    } catch (error) {
      return { code: -1, message: "上传失败，错误原因是" + error.message };
    }
  }

  @Post("/uploadFile")
  @UseInterceptors(FileInterceptor("file"))
  async uploadFilesToCDN(@Body() body, @UploadedFile() file) {
    if (!file || !body.path) {
      return { code: -1, message: "参数 file、path 不能为空" };
    }

    try {
      const url = await this.cdnService.uploadToCDN({
        str: file.buffer,
        filename: body.hash
          ? file.originalname?.replace(
              /\.[^\\.]+$/,
              ($0) => "-" + Date.now() + $0,
            )
          : file.originalname,
        path: body.path,
      });

      return { data: { url }, code: 1 };
    } catch (error) {
      return { code: -1, message: "上传失败，错误原因是" + error.message };
    }
  }
}
