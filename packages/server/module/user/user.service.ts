import { Inject, Injectable } from '@nestjs/common';
import UserDao from '../user/user.dao';

@Injectable()
export default class UserService {
  @Inject(UserDao)
  	userDao: UserDao;

  async register(body: Module.User.CreateUserParams) {
  	const { user_name } = body;
  	const [user] = await this.userDao.queryByUserName(user_name);

  	if (user) {
  		throw Error('用户已存在');
  	}

  	return { userId: await this.userDao.create(body) };
  }
	
  async getUserInfo(params: Module.User.GetUserInfoParams) {
	  const [user] = await (
  		params.user_name
  			? this.userDao.queryByUserName(params.user_name)
  			: this.userDao.queryByEmail(params.user_name)
	  );

	  if (!user) {
		  throw Error('用户不存在');
	  }

  	return user;
  }
}
