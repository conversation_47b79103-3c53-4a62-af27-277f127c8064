import { Body, Controller, Get, Inject, Post, Req } from '@nestjs/common';
import { Request } from 'express';
import UserService from './user.service';

@Controller('api/website/user')
export default class UserController {
  @Inject(UserService)
  userService: UserService;

  @Post('/register')
  async registerUser(@Body() body: Module.User.CreateUserParams) {
  	try {
  		const { user_name, email, name } = body;

  		if (!user_name || !email || !name) {
  			return { code: -1, message: '参数 user_name, email, name 必传' };
  		}

  		return { code: 1, data: await this.userService.register(body) };
  	} catch (error) {
  		return { code: -1, message: '注册用户失败，错误原因是' + error.message };
  	}
  }

  @Get('/info')
  async getUserInfo(@Req() request: Request) {
  	try {
  		const isDev: boolean = process.env.NODE_ENV === 'development';
  		return {
  			code: 1,
  			data: await this.userService.getUserInfo({
  				user_name: isDev ? 'dev' : request.get('username') || request.cookies['userName'],
  			}),
  		};
  	} catch (error) {
  		return {
  			code: -1,
  			message: '获取用户信息失败，错误原因是' + error.message,
  		};
  	}
  }
}
