import { DOBase } from '@mybricks/rocker-dao';
import { EffectStatus } from '../../common/const';
import { genMainIndexOfDB } from '../../common/util';
import { getUserByAPI } from '../../common/util/user';

export default class UserDao extends DOBase {
	/** 直接从数据库查找，无需从远程拉取 */
	public async queryByUserNameNotFill(username: string) {
		return await this.exe<Module.User.User[]>('user:queryByUserName', { username });
	}
	public async queryByUserName(username: string) {
		const users = await this.exe<Module.User.User[]>('user:queryByUserName', { username });

		if (!users.length) {
			const user = await this.fillUserByAPI(username);

			return (user ? [user] : []) as AnyType as Module.User.User[];
		}

		return users;
	}

	public async fillUserByAPI(username: string) {
		const user = await getUser<PERSON><PERSON><PERSON><PERSON>(username);

		if (user) {
			await this.create({
				department: user.orgDisplayName,
				name: user.name,
				user_name: user.username,
				avatar: user.avatarUrl,
				email: user.email,
			});

			const users = await this.exe<Module.User.User[]>('user:queryByUserName', { username });

			return users[0];
		}
	}

	public async queryById(id: number) {
		return await this.exe<Module.User.User[]>('user:queryById', { id });
	}

	public async queryByEmail(email: string) {
		return await this.exe<Module.User.User[]>('user:queryByEmail', { email });
	}

	public async create(params: Module.User.CreateUserParams) {
		return await this.exe<{ insertId: number }>('user:create', {
			...params,
			id: genMainIndexOfDB(),
			user_id: params.user_id ?? '',
			avatar: params.avatar ?? '',
			department: params.department ?? '',
			create_time: Date.now(),
			status: EffectStatus.EFFECT
		});
	}
}
