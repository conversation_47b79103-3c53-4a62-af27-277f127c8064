import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import axios from 'axios';
import UserDao from '../user/user.dao';
import MaterialDao from '../material/material.dao';
import MaterialPubDao from '../material-pub/material-pub.dao';
import BusinessConfigDao from '../business-config/business-config.dao';
import { EffectStatus, MaterialOperateAction, MaterialType } from '../../common/const';
import { safeParse } from '../../common/util';
import MaterialConvertService from '../material-convert/material-convert.service';

@Injectable()
export default class NoticeService implements OnModuleInit {
  @Inject(UserDao)
  	userDao: UserDao;
  @Inject(MaterialDao)
  	materialDao: MaterialDao;
  @Inject(MaterialPubDao)
  	materialPubDao: MaterialPubDao;
  @Inject(BusinessConfigDao)
  	businessConfigDao: BusinessConfigDao;
  materialConvertService: MaterialConvertService;
  constructor(private moduleRef: ModuleRef) {}

  onModuleInit() {
  	this.materialConvertService = this.moduleRef.get(MaterialConvertService, { strict: false });
  }

  async notice(materialId: number, action: MaterialOperateAction, version?: string) {
  	const material = await this.materialDao.detail(materialId);

  	if (!material) {
  		throw Error('物料不存在');
	  }
  	if (material.business) {
		  const [config] = await this.businessConfigDao.query({ business: material.business, status: EffectStatus.EFFECT });
  		const configMeta = safeParse(config?.meta);

		  if (configMeta.subscription && configMeta.subscription.path) {
  			const [materialVersion] = await this.materialPubDao.query({
				  material_id: materialId,
				  version: version ?? material.version,
				  status: EffectStatus.EFFECT,
			  });
			  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  			const { creator_id: updater_id, creator_name: updater_name, creator_avatar: updater_avatar, creator_username: updater_username, id, status, ...pub } = materialVersion;

			  console.log(`物料变更(${action})通知开始，物料名：${material.title}，Namespace：${material.namespace}，版本：${materialVersion.version}`);
  			axios.post(
  				configMeta.subscription.path,
				  {
  					action,
					  material: {
  						...material,
						  updater_id,
						  updater_name,
						  updater_avatar,
						  updater_username,
						  ...pub,
						  content: safeParse(pub.content),
						  schema: safeParse(pub.schema),
					  }
  				}
  			).catch(e => {
  				console.log('物料变更通知失败，错误原因是', e.message);
  			});
  		}

  		if ([MaterialOperateAction.PUBLISH, MaterialOperateAction.CREATE].includes(action)) {
  			if (material.type === MaterialType.COMPONENT) {
				  this.materialConvertService.triggerAutoConvert({
					  material_id: materialId,
					  version: version ?? material.version,
				  });
			  } else if (material.type === MaterialType.COM_LIB) {
				  this.materialConvertService.triggerAutoConvertForComLib({
					  material_id: materialId,
					  version: version ?? material.version,
				  });
			  }
  		}
	  }
  }
}
