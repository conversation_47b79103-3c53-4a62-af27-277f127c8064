import { Inject, Injectable } from '@nestjs/common';
import BusinessConfigDao from './business-config.dao';
import { EffectStatus } from '../../common/const';
import { safeParse } from '../../common/util';

@Injectable()
export default class BusinessConfigService {
  @Inject(BusinessConfigDao)
  	businessConfigDao: BusinessConfigDao;

  async create(params: Module.BusinessConfig.CreateConfigParams) {
  	const [config] = await this.businessConfigDao.query({
  		business: params.business,
  		status: EffectStatus.EFFECT,
  	});
		
  	let configId = config?.id;
  	if (!config) {
		  configId = await this.businessConfigDao.create(params);
  	} else {
  		const parseMeta = safeParse(config.meta);
		  await this.businessConfigDao.update({
			  id: configId,
			  meta: JSON.stringify({
			  	...parseMeta,
				  ...(params.meta ?? {}),
  			}),
		  });
	  }

  	return { id: configId };
  }

  async delete(body: { id: number; key: string }) {
	  const [config] = await this.businessConfigDao.detail(body.id);

	  if (config) {
		  const parseMeta = safeParse(config.meta);
  		delete parseMeta[body.key];
			
		  await this.businessConfigDao.update({
			  id: config.id,
			  meta: JSON.stringify(parseMeta),
		  });
	  }
  }

  async update(params: Module.BusinessConfig.UpdateConfigParams) {
  	const [config] = await this.businessConfigDao.detail(params.id);
		
  	if (config) {
		  const parseMeta = safeParse(config.meta);
		  await this.businessConfigDao.update({
			  id: config.id,
			  status: params.status ?? config.status,
			  meta: JSON.stringify({
				  ...parseMeta,
				  ...(params.meta ?? {}),
			  }),
		  });
  	} else {
  		throw new Error('配置信息不存在');
  	}
  }

  async query(params: Module.BusinessConfig.GetConfigListParams) {
  	const dataSource = await this.businessConfigDao.query({
  		...params,
  		status: params.status ?? EffectStatus.EFFECT
  	});

  	return { dataSource: dataSource.map(d => ({ ...d, meta: safeParse(d.meta) })) };
  }
}
