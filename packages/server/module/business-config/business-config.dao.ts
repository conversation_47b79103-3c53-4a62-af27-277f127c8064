import { DOBase } from '@mybricks/rocker-dao';
import { genMainIndexOfDB } from '../../common/util';
import { EffectStatus } from '../../common/const';

export default class BusinessConfigDao extends DOBase {
	public async create(params: Module.BusinessConfig.CreateConfigParams) {
		const result = await this.exe<{ insertId: number }>('business_config:create', {
			...params,
			id: genMainIndexOfDB(),
			meta: typeof params.meta === 'object' ? JSON.stringify(params.meta) : params.meta,
			update_time: Date.now(),
			status: EffectStatus.EFFECT
		});

		return result.insertId;
	}

	public async delete(id: number) {
		return await this.exe('business_config:delete', { id });
	}

	public async update(params: Omit<Module.BusinessConfig.UpdateConfigParams, 'meta'> & { meta: string }) {
		return await this.exe('business_config:update', params);
	}

	public async detail(id: number) {
		return await this.exe<Module.BusinessConfig.Config[]>('business_config:detail', { id });
	}

	public async query(params: Module.BusinessConfig.GetConfigListParams) {
		return await this.exe<Module.BusinessConfig.Config[]>('business_config:query', params);
	}
}
