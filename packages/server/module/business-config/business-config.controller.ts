import { Body, Controller, Get, Inject, Post, Query } from '@nestjs/common';
import { isNil } from 'lodash';
import BusinessConfigService from './business-config.service';

@Controller('api/manage/business')
export default class BusinessConfigController {
  @Inject(BusinessConfigService)
  	businessConfigService: BusinessConfigService;

  @Post('/create')
  async create(@Body() body: Module.BusinessConfig.CreateConfigParams) {
	  if (!body.meta || !body.business || !body.updater_id) {
		  return { code: -1, message: '参数 meta、business、updater_id 不能为空' };
	  }

	  try {
		  return { code: 1, data: await this.businessConfigService.create(body), message: '配置信息新建成功' };
	  } catch (error) {
		  return { code: -1, message: '配置信息新建失败，错误原因是' + error.message };
	  }
  }

  @Post('/delete')
  async delete(@Body() body: { id: number; key: string }) {
	  if (!body.id || !body.key) {
		  return { code: -1, message: '参数 id、key 不能为空' };
	  }

  	await this.businessConfigService.delete(body);

  	return { code: 1, message: '配置信息删除成功' };
  }

  @Post('/update')
  async update(@Body() body: Module.BusinessConfig.UpdateConfigParams) {
	  if (!body.id || (!body.meta && isNil(body.status))) {
		  return { code: -1, message: '参数 id 不能为空，且参数 meta、status 不能同时为空' };
	  }

  	try {
  		await this.businessConfigService.update(body);

  		return { code: 1, message: '配置信息更新成功' };
  	} catch (error) {
  		return { code: -1, message: '配置信息更新失败，错误原因是' + error.message };
  	}
  }

  @Get('/list')
  async getList(@Query() params: Module.BusinessConfig.GetConfigListParams) {
  	try {
  		return { code: 1, data: await this.businessConfigService.query(params) };
  	} catch (error) {
  		return { code: -1, message: '查询配置信息列表失败' };
  	}
  }
}
