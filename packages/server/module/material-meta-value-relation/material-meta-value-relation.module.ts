import { Module } from '@nestjs/common';
import MaterialMetaValueRelationController from './material-meta-value-relation.controller';
import MaterialMetaValueRelationService from './material-meta-value-relation.service';
import MaterialMetaValueRelationDao from './material-meta-value-relation.dao';

@Module({
	imports: [],
	controllers: [MaterialMetaValueRelationController],
	providers: [MaterialMetaValueRelationService, MaterialMetaValueRelationDao],
	exports: [MaterialMetaValueRelationService, MaterialMetaValueRelationDao],
})
export class MaterialMetaValueRelationModule {}
