import { Body, Controller, Inject, Post } from '@nestjs/common';
import MaterialMetaValueRelationService from './material-meta-value-relation.service';

@Controller('api/manage/meta/relation')
export default class MaterialMetaController {
  @Inject(MaterialMetaValueRelationService)
  	materialMetaValueRelationService: MaterialMetaValueRelationService;

  @Post('/create')
  async create(@Body() body: Module.Meta.CreateMetaValueRelationParams) {
	  if (!body.meta_value_id || !body.material_id || !body.creator_id) {
		  return { code: -1, message: '参数 meta_value_id、material_id、creator_id 不能为空' };
	  }

	  try {
		  return { code: 1, data: await this.materialMetaValueRelationService.create(body), message: '元信息关系新建成功' };
	  } catch (error) {
		  return { code: -1, message: '元信息关系新建失败，错误原因是' + error.message };
	  }
  }

  @Post('/delete')
  async delete(@Body('id') id: number) {
	  if (!id) {
		  return { code: -1, message: '参数 id 不能为空' };
	  }

  	await this.materialMetaValueRelationService.delete(id);

  	return { code: 1, message: '元信息关系删除成功' };
  }
}
