import { DOBase } from '@mybricks/rocker-dao';
import { genMainIndexOfDB } from '../../common/util';
import { EffectStatus } from '../../common/const';

export default class MaterialMetaValueRelationDao extends DOBase {
	public async create(params: Module.Meta.CreateMetaValueRelationParams) {
		const result = await this.exe<{ insertId: number }>('material_meta_value_relation:create', {
			...params,
			id: genMainIndexOfDB(),
			create_time: Date.now(),
			status: EffectStatus.EFFECT
		});

		return result.insertId;
	}

	public async delete(id: number) {
		return await this.exe<Module.Meta.MetaValueRelation[]>('material_meta_value_relation:delete', { id });
	}

	public async deleteByMaterialId(id: number) {
		return await this.exe<Module.Meta.MetaValueRelation[]>('material_meta_value_relation:deleteByMaterialId', { id });
	}

	public async query(params: Module.Meta.GetMetaValueRelationListParams) {
		return await this.exe<Module.Meta.MetaValueRelation[]>('material_meta_value_relation:query', params);
	}

	public async queryByMaterialIds(ids: number[]) {
    if (ids.length === 0) {
      return [];
    }
		return await this.exe<Module.Meta.MetaValueRelation[]>('material_meta_value_relation:queryByMaterialIds', { ids });
	}
}
