import { Inject, Injectable } from '@nestjs/common';
import MaterialMetaValueRelationDao from './material-meta-value-relation.dao';

@Injectable()
export default class MaterialMetaValueRelationService {
  @Inject(MaterialMetaValueRelationDao)
  	materialMetaValueRelationDao: MaterialMetaValueRelationDao;

  async create(params: Module.Meta.CreateMetaValueRelationParams) {
  	return this.materialMetaValueRelationDao.create(params);
  }

  async delete(id: number) {
  	return this.materialMetaValueRelationDao.delete(id);
  }
}
