type Business = import('../common/const').Business;
type MaterialType = import('../common/const').MaterialType;
type EffectStatus = import('../common/const').EffectStatus;
type PLATFORMS = import('@global-material-middleoffice/server-v2/src/shared').PLATFORMS;
type ConvertStatus = import('@global-material-middleoffice/server-v2/src/shared').CONVERT_STATUS;
type CodeType = import('@global-material-middleoffice/server-v2/src/shared').CodeType;
type RefType = import('../common/const').RefType;
type AnyType = import('../common/types').AnyType;
type ReportType = import('../common/const').ReportType;

declare namespace Module.Common {
  export interface Paging {
    pageNum?: number;
    pageSize?: number;
  }

  export interface Creator {
    /** 创建者 */
    creator_id: number;
    /** 创建时间 */
    create_time: number;
  }

  export interface Updater {
    /** 更新者ID */
    updater_id: number;
    /** 更新时间 */
    update_time: number;
  }

  export interface Status {
    /** 生效状态，-1-删除，0-待上线，1-正常 */
    status: EffectStatus;
  }
}
/** 协议参考：https://components.corp.kuaishou.com/schema */
declare namespace Module.Schema {
  export interface Schema {
    packageName: string;
    componentName: string;
    componentBundleType: string;
    libraryName: string;
    componentChineseName: string;
    description: string;
    instructionUrl: string;
    assetDownloadType: 'cdn' | 'npm';
    assetDownloadUrl: string;
    gitUrl: string;
    author: string;
    thumbnailUrl: string;
    version: string;
    publishTime: number;
    updateTime: number;
    tags: {
      platform: PLATFORMS;
      business: Business;
      category: string;
      domain?: number[];
      meta: Record<string, string[]>;
    };
    props?: Array<AnyType>;
    dependencies?: Record<string, AnyType>;
    devDependencies?: Record<string, AnyType>;
    peerDependencies?: Record<string, AnyType>;
    components?: Array<
      | { namespace: string; version: string }
      | { title: string; children: { namespace: string; version: string }[] }
    >;
    extraConfig: {
      [key: Business]: Record<string, AnyType>;
    };
  }
}

declare namespace Module.Material {
  export interface Material
    extends Module.Common.Creator,
      Module.Common.Updater,
      Module.Common.Status {
    /** 物料ID */
    id: number;
    /** 命名空间 */
    namespace: string;
    /** 当前版本号 */
    version: string;
    /** 物料名 */
    title: string;
    /** 描述 */
    description?: string;
    /** 类型：组件库/组件/模板 */
    type: MaterialType;
    domain: string;
    /** 物料生产方 */
    business: Business;
    /** Git URL */
    git_url?: string;
    /** 适用端：PC、H5、RN、TK */
    platform?: PLATFORMS;
    /** 物料额外信息 */
    meta: string;
  }
  export type QueryMaterial = Material & {
    creator_name: string;
    creator_avatar: string;
    tag_title?: string;
    tag_id?: number;
    scene_title?: string;
    scene_id?: number;
    tags?: Pick<Tag, 'id' | 'title'>[];
    scenes?: Pick<Scene, 'id' | 'title'>[];
    /** 组件库类型返回值 */
    components?: QueryMaterial[];
    schema?: Module.Schema.Schema;
  };
  export type CreateMaterialParams = {
    meta?: Record<string, unknown>;
    /** 物料发布内容 */
    content?: Record<string, unknown>;
    /**
     *  物料标准协议
     *  https://components.corp.kuaishou.com/schema
     */
    schema: Module.Schema.Schema;
    creator_id: number;
  };
  export type UpdateMaterialParams = Partial<Material> & { id: number } & {
    readme?: string;
    preview_img?: string;
    schema?: Partial<Pick<Module.Schema.Schema, 'props' | 'components'>>;
    hidden?: boolean;
  };
  export type UpdateMaterialTagRelationParams = {
    id: number;
    tags: number[];
    creator_id: number;
  };
  export type CreateTagRelationParams = {
    material_id: number;
    tag_id: number;
    creator_id: number;
  };
  export type UpdateMaterialSceneRelationParams = {
    id: number;
    scenes: number[];
    creator_id: number;
  };
  export type CreateSceneRelationParams = {
    material_id: number;
    scene_id: number;
    creator_id: number;
  };
  export type UpdateVirtualComponentParams = {
    material_id: number;
    components: Module.Schema.Schema['components'];
    creator_id: number;
  };
  export interface GetMaterialListParams extends Module.Common.Paging {
    ids?: number[];
    tagId?: number;
    platform?: string | string[];
    creatorId?: number;
    keyword?: string;
    type?: string;
    status?: EffectStatus;
    business?: Business;
    domain?: number;
    meta?: Record<string, string>;
    meta_value_ids?: number[];
    needSchema?: number;
    needExtraMeta?: number;
    branch?: 'main' | 'all';
    needVersions?: number;

    exclude_tianhe_basic_materials?: boolean;
  }

  export interface Pub extends Module.Common.Creator, Module.Common.Status {
    /** 发布 ID */
    id: number;
    /** 对应物料 id */
    material_id: number;
    /** 当前物料版本  */
    version: string;
    /** 使用文档 */
    readme: string;
    /** 物料预览图 */
    preview_img: string;
    /** 物料发布内容 */
    content: string;
    /** 物料标准 schema */
    schema: string;
  }
  export type QueryPub = Pub & {
    creator_avatar: string;
    creator_name: string;
    creator_username: string;
  };
  export type CreateMaterialPubParams = Partial<Omit<Pub, 'id'>>;
  export type UpdateMaterialPubParams = Partial<Pub> & { id: number };
  export interface GetMaterialPubListParams extends Module.Common.Paging {
    material_id?: number;
    version?: string;
    status?: number;
    branch?: 'main' | 'all';
  }

  export interface Scene extends Module.Common.Creator, Module.Common.Status {
    /** 物料场景 id */
    id: number;
    /** 场景标题 */
    title: string;
    /** 展示顺序 */
    order: number;
  }
  export type CreateSceneParams = Partial<Omit<Scene, 'id'>>;
  export type UpdateSceneParams = Partial<Scene> & { id: number };
  export interface GetSceneListParams
    extends Partial<Pick<Scene, 'title' | 'status'>>,
      Module.Common.Paging {}
  export type QueryMaterialScene = Scene & { material_id: number };

  export interface Tag extends Module.Common.Creator, Module.Common.Status {
    id: number;
    order: number;
    title: string;
    value: string;
  }
  export type CreateTagParams = Omit<Tag, 'id'>;
  export type QueryMaterialTag = Tag & { material_id: number };
  export type UpdateTagParams = Partial<Tag> & { id: number };
  export interface GetTagListParams
    extends Partial<Pick<Tag, 'title' | 'status'>>,
      Module.Common.Paging {}

  export interface Refer extends Module.Common.Creator, Module.Common.Status {
    id: number;
    material_id: number;
    namespace: string;
    version: string;
    /** 引用的页面 URL */
    ref_page: string;
    /** 引用页对应的业务域 */
    ref_business: Business;
    /** 引用数、下载数 */
    type: RefType;
    /** 被当前页面引用的次数 */
    refer_count: number;
  }
  export interface GetMaterialReferParams extends Module.Common.Paging {
    id: number;
  }
  export type CreateMaterialReferItem = {
    namespace: string;
    version: string;
    ref_page: string;
    ref_business: Business;
    type: RefType;
    refer_count: number;
    code_type?: CodeType;
    /** 是否清空 refer_page & namespace 对应的记录 */
    needClear?: boolean;
    create_time?: number;
  };
  export type CreateMaterialReferParams = {
    refers: CreateMaterialReferItem[];
    business: Business;
    code_type?: CodeType;
    creator: string;
  };
  export type CreateMaterialReferParamsForKael = {
    ref_page: string;
    kael_schema: AnyType;
    creator: string;
    version: string;
  };
  export type CreateMaterialReferParamsForFangZhou = {
    ref_page: string;
    refers: CreateMaterialReferItem[];
    schema: AnyType;
    summary: Record<string, unknown>;
    creator: string;
    version: string;
  };

  export type CreateMaterialParamsForWebsite = {
    hidden?: boolean;
    meta?: Record<string, unknown>;
    /** 物料发布内容 */
    content?: Record<string, unknown>;
    /**
     *  物料标准协议
     *  https://components.corp.kuaishou.com/schema
     */
    schema: Module.Schema.Schema;
  };

  export type CreateMaterialParamsForLingZhu = {
    hidden?: boolean;
    meta?: Record<string, unknown>;
    /** 物料发布内容 */
    content?: Record<string, unknown>;
    /**
     *  物料标准协议
     *  https://components.corp.kuaishou.com/schema
     */
    schema: Omit<
      Module.Schema.Schema,
      | 'componentName'
      | 'componentBundleType'
      | 'libraryName'
      | 'instructionUrl'
      | 'gitUrl'
      | 'publishTime'
      | 'updateTime'
    >;
  };

  export type CreateComLibMaterialParamsForWebsite =
    CreateMaterialParamsForWebsite;

  export type UpdateMaterialParamsForWebsite = {
    updater: string;
    namespace: string;
  } & Omit<UpdateMaterialParams, 'id'>;

  export type CreateMaterialParamsForWebsiteDao = Omit<
    Material,
    'id' | 'create_time' | 'updater_id' | 'update_time' | 'status'
  > & { hidden?: boolean};
}

declare namespace Module.Meta {
  export interface Meta extends Module.Common.Creator, Module.Common.Status {
    id: number;
    title: string;
    business: Business;
    value: string;
    meta: string;
  }
  export type CreateMetaParams = Partial<Omit<Meta, 'id'>>;
  export type UpdateMetaParams = {
    id: number;
    title?: string;
    value?: string;
    status?: EffectStatus;
    meta?: string;
  };
  export type GetMetaListParams = {
    title?: string;
    business?: Business;
    status?: EffectStatus;
  } & Module.Common.Paging;

  export interface MetaValue
    extends Module.Common.Creator,
      Module.Common.Status {
    id: number;
    title: string;
    value: string;
    meta_id: number;
    order: number;
  }
  export type CreateMetaValueParams = Partial<Omit<MetaValue, 'id'>>;
  export type UpdateMetaValueParams = {
    id: number;
    title?: string;
    value?: string;
    order?: number;
    status?: EffectStatus;
  };
  export type GetMetaValueListParams = {
    title?: string;
    meta_id?: number;
    status?: EffectStatus;
  } & Module.Common.Paging;

  export interface MetaValueRelation
    extends Module.Common.Creator,
      Module.Common.Status {
    id: number;
    meta_value_id: number;
    material_id: number;
  }
  export type CreateMetaValueRelationParams = Partial<
    Omit<MetaValueRelation, 'id'>
  >;
  export type GetMetaValueRelationListParams = {
    material_id?: number;
    meta_value_id?: number;
    status: EffectStatus;
  };
  export type UpdateMetaRelationByMaterialIdParams = {
    id: number;
    value: Record<string, number[]>;
    creator_id: number;
  };
  export type UpdateMetaRelationByMaterialIdParamsForWebsite = {
    id: number;
    value: Record<string, string[]>;
    creator: string;
  };
}

declare namespace Module.BusinessConfig {
  export interface Config extends Module.Common.Updater, Module.Common.Status {
    id: number;
    business: Business;
    /** 配置信息 */
    meta: string;
  }
  export type CreateConfigParams = Omit<
    Config,
    'id' | 'status' | 'update_time' | 'meta'
  > & { meta: Record<string, unknown> };
  export type UpdateConfigParams = {
    id: number;
    meta?: Record<string, unknown>;
    status?: EffectStatus;
  };
  export type GetConfigListParams = {
    status?: EffectStatus;
    business?: Business;
  };
}

declare namespace Module.Website {
  export interface GetMaterialDetailParams extends Module.Common.Paging {
    code_type?: CodeType;
    namespace?: string;
    version?: string;
    id?: number;
    source_type?: string;
  }
}

declare namespace Module.ConvertScript {
  export interface ConvertScript
    extends Module.Common.Creator,
      Module.Common.Status {
    id: number;
    business: Business;
    script: string;
  }
  export type GetConvertScriptListParams = {
    business?: Business;
    status?: EffectStatus;
  };
  export type CreateConvertScriptParams = {
    business: Business;
    creator_id: number;
    /** 脚本地址 */
    script: string;
  };
  export type UpdateConvertScriptParams = {
    id: number;
    /** 脚本地址 */
    script: string;
  };
}

declare namespace Module.Convert {
  export interface Convert extends Module.Common.Creator, Module.Common.Status {
    id: number;
    material_id: number;
    version: string;
    business: Business;
    script: string;
    /** 产物地址 */
    bundle: string;
    /** 描述信息 */
    reason: string;
    /** 结果标识 */
    result: ConvertStatus;
  }
  export type GetConvertListParams = {
    material_id: number;
    business?: Business;
    status?: EffectStatus;
  } & Module.Common.Paging;
  export type CreateConvertParams = {
    material_id: number;
    version: string;
    business: Business;
    creator_id: number;
    /** 脚本地址 */
    script: string;
    bundle?: string;
    result?: number;
    reason?: string;
  };
  export type UpdateConvertParams = {
    id: number;
    bundle?: string;
    result?: number;
    reason?: string;
  };
  export type TriggerConvertParams = {
    material_id: number;
    version: string;
    business: Business;
    creator_id: number;
  };
  export type TriggerAutoConvertParams = {
    namespace: string;
    version: string;
    business: Business;
    creator: string;
  };
}

declare namespace Module.User {
  export interface User extends Module.Common.Status, Module.Common.Creator {
    id: number;
    /** 员工ID */
    user_id: number;
    /** 中文名 */
    name: string;
    /** 用户名 */
    user_name: string;
    /** 头像 */
    avatar: string;
    /** 所属部门 */
    department: string;
    /** 邮箱 */
    email: string;
  }
  export type CreateUserParams = {
    user_id?: number;
    department?: string;
    name: string;
    user_name: string;
    avatar: string;
    email: string;
  };
  export type GetUserInfoParams = {
    user_name?: string;
    email?: string;
  };
}

declare namespace Module.CDN {
  export type UploadParams = {
    str: string | Buffer;
    filename: string;
    path?: string;
  };
}

declare namespace Module.Report {
  export interface Report extends Module.Common.Status {
    id: number;
    /** 引用页对应的业务域 */
    business: Business;
    /** 报表指标类型 */
    type: ReportType;
    /** 指标值 */
    value: number;
    /** 源码开发(pro_code)/低码搭建(low_code) */
    code_type: string;
    /** 指标关联的数据ID，如：物料ID等 */
    relation_key?: number;
    /** 指标口径开始时间 */
    start_time: number;
    /** 指标口径结束时间 */
    end_time: number;
    /** 更新时间 */
    update_time: number;
  }
  export type CreateReportItem = Omit<Report, 'id' | 'update_time'>;
  export type CreateReportParams = {
    reports: Omit<CreateReportItem, 'status' | 'update_time'>[];
  };
  export type GetReportParams = Pick<Report, 'business' | 'code_type'>;
  export type GetReportOverviewParams = Pick<
    Report,
    | 'business'
    | 'code_type'
    | 'type'
    | 'start_time'
    | 'end_time'
    | 'relation_key'
  >;

  export interface Project extends Module.Common.Status {
    id: number;
    /** 所属BU */
    business: Business;
    /** 项目名 */
    title: string;
    /** 仓库地址 */
    git_url: string;
    /** 项目详细信息 */
    content: string;
    /** 指标口径开始时间 */
    start_time: number;
    /** 指标口径结束时间 */
    end_time: number;
    type: 'material' | 'profession';
    /** 更新时间 */
    update_time: number;
  }
  export type CreateProjectItem = Omit<
    Project,
    'id' | 'update_time' | 'status'
  > & { status?: EffectStatus };
  export type CreateProjectParams = { projects: CreateProjectItem[] };
  export type GetProjectParams = Pick<
    Project,
    'business' | 'start_time' | 'end_time' | 'type'
  > & { git_url?: string };
  export type UpdateProjectParams = Pick<Project, 'id' | 'content'>;

  export interface LowCodePage extends Module.Common.Status {
    id: number;
    business: Business;
    url: string;
    version: string;
    schema: string;
    content: string;
    update_time: number;
    status: EffectStatus;
  }
  export type CreateLowCodePageItem = Omit<
    LowCodePage,
    'id' | 'update_time' | 'status'
  >;
  export type UpdateLowCodePageItem = Pick<LowCodePage, 'id'> &
    Partial<LowCodePage, 'content' | 'schema'>;

  export interface LowCodeBuildReport extends Module.Common.Status {
    id: number;
    /** 低码页面信息记录ID */
    page_id: number;
    /** 报表指标类型 */
    type: string;
    /** 指标值 */
    value: number;
    update_time: number;
    status: EffectStatus;
  }
  export type CreateLowCodeBuildReportItem = Omit<
    LowCodeBuildReport,
    'id' | 'update_time' | 'status'
  >;
}

declare namespace Module.Scan {
  export interface ScanProject {
    /** 项目名 */
    title: string;
    /** 仓库地址 */
    git_url: string;
    /** 入口配置 */
    router_entry?: string[];
    /** 分支 */
    branch?: string;
    /** 黑名单 */
    black_list?: string[];
    /** 根据 package dep 依赖筛选 */
    pkg_filter?: string;
  }
}
