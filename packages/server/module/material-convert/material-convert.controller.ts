import { Body, Controller, Get, Inject, Post, Query } from '@nestjs/common';
import MaterialConvertService from './material-convert.service';

@Controller('api/manage/convert')
export default class MaterialConvertController {
  @Inject(MaterialConvertService)
  	materialConvertService: MaterialConvertService;

  @Post('/create')
  async create(@Body() body: Module.Convert.CreateConvertParams) {
	  if (!body.material_id || !body.script || !body.creator_id) {
		  return { code: -1, message: '参数 business、script、creator_id 不能为空' };
	  }

	  try {
		  return { code: 1, data: await this.materialConvertService.create(body), message: '转换记录新建成功' };
	  } catch (error) {
		  return { code: -1, message: '转换记录新建失败，错误原因是' + error.message };
	  }
  }

  @Post('/delete')
  async delete(@Body('id') id: number) {
  	await this.materialConvertService.delete(id);

  	return { code: 1, message: '转换记录删除成功' };
  }

  @Post('/update')
  async updated(@Body() body: Module.Convert.UpdateConvertParams) {
	  if (!body.id) {
		  return { code: -1, message: '参数 id 不能为空' };
	  }

  	try {
  		await this.materialConvertService.update(body);

  		return { code: 1, message: '转换记录更新成功' };
  	} catch (error) {
  		return { code: -1, message: '转换记录更新失败，错误原因是' + error.message };
  	}
  }

  @Get('/list')
  async getList(@Query() params: Module.Convert.GetConvertListParams) {
  	try {
  		return { code: 1, data: await this.materialConvertService.query(params) };
  	} catch (error) {
  		return { code: -1, message: '查询转换记录列表失败，错误原因是' + error.message };
  	}
  }

  @Get('/detail')
  async getDetail(@Query('id') id: number) {
  	if (!id) {
  		return { code: -1, message: '参数 id 不能为空' };
  	}

  	try {
  		return { code: 1, data: await this.materialConvertService.detail(id) };
  	} catch (error) {
  		return { code: -1, message: '查询转换记录详情失败，错误原因是' + error.message };
  	}
  }

  @Get('/status')
  async getConvertStatus(@Query('id') id: number) {
  	if (!id) {
  		return { code: -1, message: '参数 id 不能为空' };
  	}

  	try {
  		return { code: 1, data: await this.materialConvertService.getConvertStatus(id) };
  	} catch (error) {
  		return { code: -1, message: '查询转换记录状态失败，错误原因是' + error.message };
  	}
  }

	@Post('/trigger')
  async triggerConvert(@Body() body: Module.Convert.TriggerConvertParams) {
  	if (!body.material_id || !body.business || !body.version || !body.creator_id) {
  		return { code: -1, message: '参数 material_id、business、version、creator_id 不能为空' };
  	}

  	try {
  		return { code: 1, message: '转换记录更新成功', data: await this.materialConvertService.triggerConvert(body) };
  	} catch (error) {
  		return { code: -1, message: '转换记录更新失败，错误原因是' + error.message };
  	}
  }

	@Post('/trigger/auto')
	async triggerAutoConvert(@Body() body: Module.Convert.TriggerAutoConvertParams) {
  	if (!body.namespace || !body.business || !body.version || !body.creator) {
  		return { code: -1, message: '参数 namespace、business、version、creator 不能为空' };
  	}

  	try {
  		return { code: 1, message: '自动转换成功', data: await this.materialConvertService.triggerAutoConvertForAPI(body) };
  	} catch (error) {
  		return { code: -1, message: '自动转换失败，错误原因是' + (typeof error === 'string' ? error : error.message) };
  	}
	}

	@Post('/publish')
	async publishConvert(@Body() body: { id: number }) {
  	if (!body.id) {
  		return { code: -1, message: '参数 id 不能为空' };
  	}

  	try {
  		return { code: 1, message: '转换物料发布成功', data: await this.materialConvertService.publishConvert(body.id) };
  	} catch (error) {
  		return { code: -1, message: '转换物料发布失败，错误原因是' + error.message };
  	}
	}
}
