import { Inject, Injectable, OnModuleInit } from "@nestjs/common";
import { ModuleRef } from "@nestjs/core";
import axios from "axios";
import { isObject } from "lodash";
import { ALL_BUSINESS, ASSET_DOWNLOAD_TYPE, CONVERT_STATUS, CONVERT_STATUS_LABEL, flattenComponentField, PLATFORMS, SOURCE_CODE_BUSINESS_NAME_MAP } from '@global-material-middleoffice/server-v2/shared';
import MaterialConvertDao from "./material-convert.dao";
import {
  Business,
  EffectStatus,
  MaterialOperateAction,
  MaterialType,
  PAGE_NUM,
  PAGE_SIZE,
} from "../../common/const";
import { computeOffset } from "../../common/util/paging";
import MaterialDao from "../material/material.dao";
import MaterialConvertScriptDao from "../material-convert-script/material-convert-script.dao";
import MaterialPubDao from "../material-pub/material-pub.dao";
import CDNService from "../cdn/cdn.service";
import UserDao from "../user/user.dao";
import WebsiteDao from "../website/website.dao";
import MaterialTagRelationDao from "../material-tag/material-tag-relation.dao";
import MaterialMetaDao from "../material-meta/material-meta.dao";
import MaterialMetaValueDao from "../material-meta-value/material-meta-value.dao";
import MaterialMetaValueRelationDao from "../material-meta-value-relation/material-meta-value-relation.dao";
import {
  safeStringify,
  convertCDNUrl2IdcUrl,
  safeParse,
} from "../../common/util";
import NoticeService from "../notice/notice.service";
import MaterialTagDao from "../material-tag/material-tag.dao";

@Injectable()
export default class MaterialConvertService implements OnModuleInit {
  @Inject(MaterialConvertDao)
  materialConvertDao: MaterialConvertDao;
  @Inject(MaterialDao)
  materialDao: MaterialDao;
  @Inject(WebsiteDao)
  websiteDao: WebsiteDao;
  @Inject(MaterialConvertScriptDao)
  materialConvertScriptDao: MaterialConvertScriptDao;
  @Inject(MaterialPubDao)
  materialPubDao: MaterialPubDao;
  @Inject(MaterialTagRelationDao)
  materialTagRelationDao: MaterialTagRelationDao;
  @Inject(MaterialMetaDao)
  materialMetaDao: MaterialMetaDao;
  @Inject(MaterialMetaValueDao)
  materialMetaValueDao: MaterialMetaValueDao;
  @Inject(MaterialMetaValueRelationDao)
  materialMetaValueRelationDao: MaterialMetaValueRelationDao;
  @Inject(CDNService)
  cdnService: CDNService;
  @Inject(UserDao)
  userDao: UserDao;
  @Inject(MaterialTagDao)
  materialTagDao: MaterialTagDao;
  noticeService: NoticeService;

  constructor(private moduleRef: ModuleRef) {}

  onModuleInit() {
    this.noticeService = this.moduleRef.get(NoticeService, { strict: false });
  }

  async create(params: Module.Convert.CreateConvertParams) {
    return this.materialConvertDao.create(params);
  }

  async delete(id: number) {
    return this.materialConvertDao.delete(id);
  }

  async update(params: Module.Convert.UpdateConvertParams) {
    if (await this.exist(params.id)) {
      return this.materialConvertDao.update(params);
    } else {
      throw new Error("转换记录不存在");
    }
  }

  async query(params: Module.Convert.GetConvertListParams) {
    const mergedParams = {
      ...params,
      status: params.status ?? EffectStatus.EFFECT,
      pageNum: Number(params.pageNum || PAGE_NUM),
      pageSize: Number(params.pageSize || PAGE_SIZE),
      offset: computeOffset(params.pageNum, params.pageSize),
    };
    const [dataSource, total] = await Promise.all([
      this.materialConvertDao.query(mergedParams),
      this.materialConvertDao.queryListTotal(mergedParams),
    ]);

    return {
      pageSize: Number(params.pageSize || PAGE_SIZE),
      pageNum: mergedParams.pageNum,
      dataSource,
      total,
    };
  }

  async exist(id: number) {
    return !!(await this.materialConvertDao.detail(id));
  }

  async detail(id: number) {
    const convert = await this.materialConvertDao.detail(id);

    if (!convert) {
      throw Error("转换记录不存在");
    }

    return convert;
  }

  async getConvertStatus(id: number) {
    const convert = await this.materialConvertDao.detail(id);

    if (!convert) {
      throw Error("转换记录不存在");
    }

    return {
      result: convert.result,
      text: convert.reason ?? CONVERT_STATUS_LABEL[convert.result],
    };
  }

  async triggerConvert(body: Module.Convert.TriggerConvertParams) {
    const [user] = await this.userDao.queryById(body.creator_id);
    if (!user) {
      throw Error("用户不存在");
    }
    const material = await this.materialDao.detail(body.material_id);
    if (!material) {
      throw Error("物料不存在");
    }

    if (material.business === body.business) {
      throw Error("转换到的业务线不能与物料业务线相同");
    }

    const metaValueRelation = await this.materialMetaValueRelationDao.query({
      material_id: material.id,
      status: EffectStatus.EFFECT,
    });
    const metaValues = await this.materialMetaValueDao.queryByIds({
      ids: metaValueRelation.map((r) => r.meta_value_id),
    });
    const metas = await this.materialMetaDao.queryByIds({
      ids: metaValues.map((r) => r.meta_id),
    });

    const [materialVersion] = await this.materialPubDao.query({
      material_id: material.id,
      version: body.version,
    });
    if (!materialVersion) {
      throw Error("物料对应版本不存在");
    }

    if (!materialVersion.schema) {
      throw Error("物料标准 schema 不存在");
    }

    const parsedSchema = safeParse(materialVersion.schema);
    const parsedContent = safeParse(materialVersion.content);

    if (parsedSchema.assetDownloadType === ASSET_DOWNLOAD_TYPE.NPM) {
      throw Error("资源产物为 NPM 类型的物料暂不能进行转换");
    }
    if (!parsedSchema?.assetDownloadUrl) {
      throw Error("物料 assetDownloadUrl 资源不存在");
    }

    const [convertScript] = await this.materialConvertScriptDao.query({
      business: body.business,
      status: EffectStatus.EFFECT,
    });
    if (!convertScript || !convertScript.script) {
      throw Error("对应 BU 的转换脚本不存在");
    }

    const convertId = await this.materialConvertDao.create({
      ...body,
      script: convertScript.script,
      result: CONVERT_STATUS.LOADING,
    });
    const scriptText = await axios
      .get(convertCDNUrl2IdcUrl(convertScript.script))
      .then((res) => res.data);
    let newMaterialNamespace: string = "";
    eval(scriptText)(
      {
        ...material,
        ...materialVersion,
        platform: material.platform?.split(","),
        schema: parsedSchema,
        content: parsedContent,
        tags: (
          await this.materialTagRelationDao.queryByMaterialIds([material.id])
        ).map((t) => ({ id: t.id, title: t.title })),
        extra_metas: metas.map((meta) => {
          return {
            id: meta.id,
            title: meta.title,
            key: meta.value,
            values: metaValues
              .filter((v) => v.meta_id === meta.id)
              .map((v) => {
                return {
                  id: v.id,
                  title: v.title,
                  key: v.value,
                };
              }),
          };
        }),
      },
      {
        business: body.business,
        businessTitle: SOURCE_CODE_BUSINESS_NAME_MAP[body.business],
        creatorId: user.id,
        creatorName: user.user_name || user.name,
        axios,
        convertCDNUrl2IdcUrl,
      },
    )
      .then(async (bundle) => {
        if ("namespace" in bundle) {
          newMaterialNamespace = bundle.namespace;
        }
        const url = await this.cdnService.uploadToCDN({
          str: JSON.stringify(bundle, null, 2),
          path: `/convert/${convertId}`,
          filename: material.namespace + "-convert.json",
        });
        await this.materialConvertDao.update({
          id: convertId,
          bundle: url,
          result: CONVERT_STATUS.SUCCESS,
        });
      })
      .catch(async (error) => {
        await this.materialConvertDao.update({
          id: convertId,
          bundle: "",
          result: CONVERT_STATUS.FAILED,
          reason: "转换失败，失败原因是" + error.message,
        });
      });

    return { id: convertId, convertedMaterialNamespace: newMaterialNamespace };
  }

  async triggerAutoConvertForAPI(
    body: Module.Convert.TriggerAutoConvertParams,
  ) {
    const [user] = await this.userDao.queryByUserNameNotFill(body.creator);
    if (!user) {
      throw Error("用户不存在");
    }
    const [material] = await this.materialDao.queryByNamespaceAndVersion(
      body.namespace,
      body.version,
    );
    if (!material) {
      throw Error("物料不存在");
    }

    try {
      const { id: convertId } = await this.triggerConvert({
        material_id: material.id,
        version: body.version,
        business: body.business,
        creator_id: user.id,
      });
      let timer = null;

      const { materialId, materialPubId } = await new Promise<{
        materialId: number;
        materialPubId: number;
      }>((resolve, reject) => {
        const loop = () => {
          clearTimeout(timer);

          timer = setTimeout(async () => {
            try {
              const status = await this.getConvertStatus(convertId);

              if (status.result === CONVERT_STATUS.SUCCESS) {
                const { materialId, materialPubId } =
                  await this.publishConvert(convertId);
                resolve({ materialId, materialPubId });
                clearTimeout(timer);
              } else if (status.result === CONVERT_STATUS.FAILED) {
                reject(Error(body.business + "转换任务执行失败"));
                clearTimeout(timer);
              } else {
                loop();
              }
            } catch (error) {
              reject(error);
            }
          }, 2000);
        };
        loop();
      });

      return { id: convertId, materialId, materialPubId };
    } catch (e) {
      throw Error(e.message ?? e);
    }
  }

  async publishConvert(id: number) {
    const convert = await this.materialConvertDao.detail(id);

    if (!convert) {
      throw Error("转换记录不存在");
    }

    if (convert.result !== CONVERT_STATUS.SUCCESS) {
      throw Error("转换记录未达到成功状态");
    }

    const bundleJson = await axios
      .get(convertCDNUrl2IdcUrl(convert.bundle))
      .then((res) => res.data);
    const [version] = await this.websiteDao.queryByNamespaceAndVersion(
      bundleJson.namespace,
      bundleJson.version,
    );

    if (version) {
      throw Error("对应物料版本号已存在");
    }
    let parseMeta = {};
    try {
      parseMeta =
        typeof bundleJson.meta === "object"
          ? bundleJson.meta
          : safeParse(bundleJson.meta);
    } catch {}

    if (!bundleJson.schema) {
      throw Error("对应物料协议不能为空");
    }

    const [material] = await this.materialDao.queryByNamespaces({
      namespaces: [bundleJson.namespace],
    });
    let materialId = material?.id;
    let action;

    if (!bundleJson.schema.tags) {
      bundleJson.schema.tags = {};
    }
    bundleJson.schema.tags.business = convert.business;

    if (!material) {
      // TODO: 转换后应当继承更多的属性。eg: domain
      materialId = await this.materialDao.create({
        ...bundleJson,
        platform: (Array.isArray(bundleJson.platform)
          ? bundleJson.platform.join(",")
          : bundleJson.platform) as PLATFORMS,
        meta: safeStringify({
          ...parseMeta,
          ref_source: "convert",
          ref_material_id: convert.material_id,
        }),
        type: MaterialType.COMPONENT,
        business: convert.business,
      });
      action = MaterialOperateAction.CREATE;
    } else {
      await this.materialDao.update({
        meta: safeStringify({
          ...parseMeta,
          ref_source: "convert",
          ref_material_id: convert.material_id,
        }),
        version: bundleJson.version,
        description: bundleJson.description,
        git_url: bundleJson.gitUrl,
        id: materialId,
        updater_id: bundleJson.creator_id,
        title: bundleJson.title,
        business: convert.business,
        type: MaterialType.COMPONENT,
        platform: (Array.isArray(bundleJson.platform)
          ? bundleJson.platform.join(",")
          : bundleJson.platform) as PLATFORMS,
      });
      action = MaterialOperateAction.PUBLISH;
    }
    const pubId = await this.materialPubDao.create({
      material_id: materialId,
      version: bundleJson.version,
      preview_img: bundleJson.preview_img,
      readme: bundleJson.readme,
      schema: JSON.stringify(bundleJson.schema),
      content: JSON.stringify(bundleJson.content),
      creator_id: bundleJson.creator_id,
    });
    await this.materialConvertDao.update({
      id: convert.id,
      result: CONVERT_STATUS.PUBLISHED,
    });

    if (bundleJson.schema?.tags?.category) {
      const tags = await this.materialTagDao.queryByValues({
        values: Array.isArray(bundleJson.schema.tags.category)
          ? bundleJson.schema.tags.category
          : [bundleJson.schema.tags.category],
      });
      await this.materialTagRelationDao.deleteByMaterialId(materialId);

      if (tags.length) {
        for (const tag of tags) {
          await this.materialTagRelationDao.create({
            material_id: materialId,
            tag_id: tag.id,
            creator_id: bundleJson.creator_id,
          });
        }
      }
    }

    if (bundleJson.schema?.tags?.meta) {
      if (!isObject(bundleJson.schema.tags.meta))
        throw Error("tags.meta类型错误");
      const metaList = await this.materialMetaDao.queryByBusiness(
        convert.business,
      );
      const validMeta = Object.keys(bundleJson.schema.tags.meta)
        .map((value) => {
          const existMeta = metaList.find((meta) => meta.value === value);

          if (existMeta) {
            return { ...existMeta, values: bundleJson.schema.tags.meta[value] };
          }
        })
        .filter(Boolean);
      const metaValues = await this.materialMetaValueDao.queryByMetaIds({
        meta_ids: validMeta.map((it) => it.id),
      });
      const validValues = validMeta
        .map((meta) =>
          metaValues.filter(
            (it) => meta.values.includes(it.value) && it.meta_id === meta.id,
          ),
        )
        .flat();
      await this.materialMetaValueRelationDao.deleteByMaterialId(materialId);
      await Promise.all(
        validValues.map((it) =>
          this.materialMetaValueRelationDao.create({
            meta_value_id: it.id,
            material_id: materialId,
            creator_id: bundleJson.creator_id,
          }),
        ),
      );
    }

    await this.noticeService.notice(materialId, action);

    return { materialId, materialPubId: pubId };
  }

  async triggerAutoConvert(
    params: { material_id: number; version: string },
    defaultBusinessList?: Business[],
  ) {
    const allBusiness = ALL_BUSINESS
    const convertedMaterials = await this.materialDao.queryByConvertTag({
      convert_tag: `%"ref_source":"convert","ref_material_id":${params.material_id}%`,
    });
    const material = await this.materialDao.detail(params.material_id);

    if (!material) {
      throw Error("物料不存在");
    }
    const meta = safeParse(material.meta) as { autoConvert?: Business[] };
    const businessList = [
      ...new Set(convertedMaterials.map((m) => m.business)),
      ...(meta?.autoConvert ?? []),
      ...(defaultBusinessList ?? []),
    ].filter((bu) => allBusiness.includes(bu));
    const [materialVersion] = await this.materialPubDao.query({
      ...params,
      status: EffectStatus.EFFECT,
    });

    if (!materialVersion) {
      throw Error(`物料对应版本 ${params.version} 不存在`);
    }

    for (const business of businessList) {
      const { id: convertId } = await this.triggerConvert({
        ...params,
        business,
        creator_id: materialVersion.creator_id,
      });

      let timer = null;
      await new Promise((resolve, reject) => {
        const loop = () => {
          clearTimeout(timer);

          timer = setTimeout(async () => {
            try {
              const status = await this.getConvertStatus(convertId);

              if (status.result === CONVERT_STATUS.SUCCESS) {
                resolve(void 0);
                await this.publishConvert(convertId);
                clearTimeout(timer);
              } else if (status.result === CONVERT_STATUS.FAILED) {
                reject(Error(business + "转换任务执行失败"));
                clearTimeout(timer);
              } else {
                loop();
              }
            } catch (error) {
              reject(error);
            }
          }, 2000);
        };
        loop();
      });
    }
  }

  async triggerAutoConvertForComLib(params: {
    material_id: number;
    version: string;
  }) {
    const material = await this.materialDao.detail(params.material_id);

    if (!material) {
      throw Error("组件库物料不存在");
    }
    const [materialVersion] = await this.materialPubDao.query({
      ...params,
      status: EffectStatus.EFFECT,
    });

    if (!materialVersion) {
      throw Error(`组件库物料对应版本 ${params.version} 不存在`);
    }

    const schema = safeParse(materialVersion.schema);
    const newComponents = flattenComponentField(schema.components);
    const componentMaterials =
      await this.materialDao.queryByNamespaceAndVersionList(newComponents);
    for (const component of newComponents) {
      if (
        !componentMaterials.find(
          (item) =>
            item.namespace === component.namespace &&
            item.version === component.version,
        )
      ) {
        console.log(
          `组件库（${material.title}/${material.namespace}）自动转换：组件库中组件 ${component.namespace} 对应版本 ${component.version} 不存在`,
        );
      }
    }

    const meta = safeParse(material.meta) as { autoConvert?: Business[] };
    for (const componentMaterial of componentMaterials) {
      await this.triggerAutoConvert(
        {
          material_id: componentMaterial.id,
          version: componentMaterial.version,
        },
        meta.autoConvert ?? [],
      );
    }
  }
}
