import { Module } from '@nestjs/common';
import MaterialConvertController from './material-convert.controller';
import MaterialConvertService from './material-convert.service';
import MaterialConvertDao from './material-convert.dao';
import MaterialDao from '../material/material.dao';
import MaterialConvertScriptDao from '../material-convert-script/material-convert-script.dao';
import MaterialPubDao from '../material-pub/material-pub.dao';
import CDNService from '../cdn/cdn.service';
import UserDao from '../user/user.dao';
import WebsiteDao from '../website/website.dao';
import MaterialTagRelationDao from '../material-tag/material-tag-relation.dao';
import MaterialMetaDao from '../material-meta/material-meta.dao';
import MaterialMetaValueDao from '../material-meta-value/material-meta-value.dao';
import MaterialMetaValueRelationDao from '../material-meta-value-relation/material-meta-value-relation.dao';
import NoticeService from '../notice/notice.service';
import BusinessConfigDao from '../business-config/business-config.dao';
import MaterialTagDao from '../material-tag/material-tag.dao';

@Module({
	imports: [],
	controllers: [MaterialConvertController],
	providers: [
	MaterialConvertService,
	MaterialConvertDao,
	MaterialDao,
	MaterialConvertScriptDao,
	MaterialPubDao,
	CDNService,
	UserDao,
	WebsiteDao,
	MaterialTagRelationDao,
	MaterialMetaDao,
	MaterialMetaValueDao,
	MaterialMetaValueRelationDao,
	NoticeService,
	BusinessConfigDao,
	MaterialTagDao
	],
	exports: [MaterialConvertService, MaterialConvertDao],
	})
export class MaterialConvertModule {}
