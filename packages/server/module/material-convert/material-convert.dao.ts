import { DOBase } from '@mybricks/rocker-dao';
import { EffectStatus } from '../../common/const';
import { genMainIndexOfDB } from '../../common/util';

export default class MaterialConvertDao extends DOBase {
	public async create(params: Module.Convert.CreateConvertParams) {
		const result = await this.exe<{ insertId: number }>('material_convert:create', {
			...params,
			id: genMainIndexOfDB(),
			create_time: Date.now(),
			bundle: params.bundle ?? '',
			result: params.result ?? '',
			reason: params.reason ?? '',
			status: EffectStatus.EFFECT
		});

		return result.insertId;
	}

	public async delete(id: number) {
		return await this.exe('material_convert:delete', { id });
	}

	public async update(params: Module.Convert.UpdateConvertParams) {
		return await this.exe('material_convert:update', params);
	}

	public async detail(id: number) {
		return (await this.exe<Module.Convert.Convert[]>('material_convert:detail', { id }))?.[0];
	}

	public async query(params: Module.Convert.GetConvertListParams) {
		return await this.exe('material_convert:query', params);
	}

	public async queryListTotal(
		params: Omit<Module.Convert.GetConvertListParams, 'pageNum' | 'pageSize'>,
	) {
		const result = await this.exe<Array<{ total: number }>>('material_convert:queryListTotal', params);
		return result[0]?.total;
	}
}
