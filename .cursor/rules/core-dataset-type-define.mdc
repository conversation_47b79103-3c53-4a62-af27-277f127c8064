---
description:
globs:
alwaysApply: true
---
# 核心数据类型定义规范

## 目标
降低 TypeScript 类型编写和使用成本，避免前后端重复定义数据类型。

## 架构设计
- 前后端工程作为两个 workspace 存在于 pnpm monorepo 中
- 后端工程（`packages/server.v2`）作为前端工程（`packages/client`）的依赖
- 前端通过 `"@global-material-middleoffice/server-v2": "workspace:*"` 引用后端类型

## 类型定义规范

### 1. 文件组织
- 每个 NestJS module 对应一个 `.d.ts` 类型定义文件
- 文件命名：`{module-name}.d.ts`，与 `{module-name}.module.ts` 同级
- 例如：`user.module.ts` 对应 `user.d.ts`

### 2. 命名空间结构
使用 `declare namespace` 定义全局可用的类型，无需显式 import：

```typescript
declare namespace Service.{ModuleName} {
  // 数据模型
  export interface UserInfo {
    id: number
    name: string
    email: string
    phone: string
    avatar: string
    createdAt: string
  }

  // 请求参数
  export interface CreateUserParams {
    name: string
    email: string
  }

  // 响应数据
  export interface UserListResult {
    data: UserInfo[]
    pagination: Service.PaginationResult
  }
}
```

### 3. 命名空间层级
- 根命名空间：`Service`
- 模块命名空间：`Service.{ModuleName}`（如 `Service.User`、`Service.Material`）
- 子模块命名空间：`Service.{ModuleName}.{SubModuleName}`（如 `Service.Material.Tag`）

### 4. 类型分类
每个模块的类型定义应包含：
- **数据模型**：核心实体类型（通常对应数据库表结构）
- **请求参数**：API 接口的输入参数类型
- **响应数据**：API 接口的返回数据类型
- **业务逻辑类型**：特定业务场景的数据结构

## 使用规范

### 1. 前端使用
在前端代码中直接使用命名空间类型，无需 import：

```typescript
// 组件 props
interface Props {
  user: Service.User.UserInfo
}

// API 调用
const fetchUsers = (params: Service.User.ListParams) => {
  return api.get<Service.User.UserListResult>('/users', { params })
}

// 状态管理
const [users, setUsers] = useState<Service.User.UserInfo[]>([])
```

### 2. 后端使用
后端可以通过相同的命名空间访问类型定义，确保前后端类型一致性。

## 代码生成指导

当 Cursor 生成相关代码时，应遵循以下原则：

### 1. 接口代码生成
- 自动使用对应模块的类型定义
- 请求参数使用 `Service.{ModuleName}.{ActionName}Params` 格式
- 响应数据使用 `Service.{ModuleName}.{ActionName}Result` 格式

### 2. 前端组件生成
- 组件 props 优先使用已定义的 Service 类型
- 状态管理使用对应的数据模型类型
- 表单字段映射到请求参数类型

### 3. 类型推导
- 根据现有的命名空间结构推导新类型的命名
- 保持命名一致性和层级结构
- 复用已有的基础类型（如 `Service.PaginationResult`）

## 示例参考

项目中已有的类型定义示例：
- `Service.User.*` - 用户相关类型
- `Service.Material.*` - 物料相关类型
- `Service.Analysis.*` - 分析相关类型
- `Service.Forward.*` - 转发相关类型

在生成新代码时，请参考这些现有模式，保持代码风格和结构的一致性。
